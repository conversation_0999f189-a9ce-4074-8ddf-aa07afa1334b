aiohappyeyeballs==2.6.1
aiofiles==24.1.0
aiohttp==3.11.18
aiosignal==1.3.2
alembic==1.15.2
annotated-types==0.7.0
anyio==4.9.0
appdirs==1.4.4
asgiref==3.8.1
asttokens==3.0.0
attrs==25.3.0
auth0-python==4.9.0
Authlib==1.3.1
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.4
blinker==1.9.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.1
chroma-hnswlib==0.7.3
chromadb==0.4.24
click==8.1.8
cohere==5.15.0
coloredlogs==15.0.1
crewai==0.11.2
crewai-tools==0.0.1
cryptography==44.0.2
dataclasses-json==0.6.7
decorator==5.2.1
Deprecated==1.2.18
deprecation==2.1.0
diffusers==0.33.1
distro==1.9.0
docker==7.1.0
docstring_parser==0.15
durationpy==0.9
elevenlabs==1.57.0
# embedchain==0.0.73
et_xmlfile==2.0.0
executing==2.2.0
faiss-cpu==1.11.0
fastapi==0.115.9
fastavro==1.10.0
ffmpeg-python==0.2.0
filelock==3.18.0
flatbuffers==25.2.10
frozenlist==1.6.0
fsspec==2025.3.2
future==1.0.0
gemini-api==0.1.6
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc0
google-api-python-client==2.169.0
google-auth==2.39.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
gptcache==0.1.44
grpcio==1.71.0
grpcio-health-checking==1.71.0
grpcio-status==1.71.0
grpcio-tools==1.71.0
gunicorn==23.0.0
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
imageio==2.37.0
imageio-ffmpeg==0.6.0
importlib-metadata==8.6.1
importlib-resources==6.5.2
instructor==0.5.2
ipython==9.2.0
ipython_pygments_lexers==1.1.1
jedi==0.19.2
Jinja2==3.1.6
jiter==0.8.2
json5==0.12.0
json_repair==0.44.0
jsonpatch==1.33
jsonpickle==4.0.5
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
kubernetes==32.0.1
lancedb==0.22.0
langsmith==0.3.38
litellm==1.67.2
Mako==1.3.10
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.94
mmh3==5.1.0
monotonic==1.6
moviepy==2.1.2
mpmath==1.3.0
multidict==6.4.3
mypy_extensions==1.1.0
networkx==3.4.2
nodeenv==1.9.1
numpy==2.2.5
oauthlib==3.2.2
onnxruntime==1.21.1
openai==1.76.2
opencv-python==*********
openpyxl==3.1.5
opentelemetry-api==1.32.1
opentelemetry-exporter-otlp-proto-common==1.32.1
opentelemetry-exporter-otlp-proto-grpc==1.32.1
opentelemetry-exporter-otlp-proto-http==1.32.1
opentelemetry-instrumentation==0.53b1
opentelemetry-instrumentation-asgi==0.53b1
opentelemetry-instrumentation-fastapi==0.53b1
opentelemetry-proto==1.32.1
opentelemetry-sdk==1.32.1
opentelemetry-semantic-conventions==0.53b1
opentelemetry-util-http==0.53b1
orjson==3.10.18
overrides==7.7.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pdfminer.six==20250327
pdfplumber==0.11.6
pexpect==4.9.0
pillow==10.4.0
portalocker==2.10.1
posthog==3.25.0
proglog==0.1.11
prompt_toolkit==3.0.51
propcache==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pyav==14.2.1
pycparser==2.22
pydantic==2.11.4
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
PyJWT==2.10.1
pyparsing==3.2.3
pypdf==5.4.0
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyright==1.1.400
pysbd==0.3.4
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-magic==0.4.27
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
PyYAML==6.0.2
qdrant-client==1.14.2
referencing==0.36.2
regex==2023.12.25
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.24.0
rsa==4.9.1
safetensors==0.5.3
schema==0.7.7
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.40
stability-sdk==0.2.2
stack-data==0.6.3
starlette==0.45.3
sympy==1.14.0
tabulate==0.9.0
tenacity==8.2.3
tokenizers==0.20.3
tomli==2.2.1
tomli_w==1.2.0
tqdm==4.67.1
traitlets==5.14.3
typer==0.9.0
types-requests==2.32.0.20250328
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.4.0
uv==0.7.1
uvicorn==0.34.2
uvloop==0.21.0
validators==0.34.0
watchfiles==1.0.5
wcwidth==0.2.13
weaviate-client==4.14.1
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
yarl==1.20.0
zipp==3.21.0
zstandard==0.23.0

# Premium features dependencies (pytz already included above)

# Additional dependencies for real SEO intelligence
nltk==3.8.1
textstat==0.7.3
scikit-learn==1.3.2
