#!/usr/bin/env python3
"""
Emma Studio Backend - Inicio FORZADO en puerto 8001
Este script SIEMPRE inicia el backend en puerto 8001, sin excepciones.
"""

import os
import sys
import subprocess
import signal
import time

# PUERTO FORZADO - NO NEGOCIABLE
BACKEND_PORT = 8001
BACKEND_HOST = "0.0.0.0"

def kill_port_processes(port):
    """Mata todos los procesos que estén usando el puerto especificado"""
    try:
        # Buscar procesos usando el puerto
        result = subprocess.run(
            ["lsof", "-ti", f":{port}"], 
            capture_output=True, 
            text=True
        )
        
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid:
                    try:
                        os.kill(int(pid), signal.SIGKILL)
                        print(f"🔪 Proceso {pid} eliminado del puerto {port}")
                    except ProcessLookupError:
                        pass  # El proceso ya no existe
                    except Exception as e:
                        print(f"⚠️  Error eliminando proceso {pid}: {e}")
        else:
            print(f"✅ Puerto {port} está libre")
            
    except FileNotFoundError:
        print("⚠️  lsof no disponible, continuando...")
    except Exception as e:
        print(f"⚠️  Error limpiando puerto {port}: {e}")

def main():
    print("🚀 Emma Studio Backend - Inicio FORZADO")
    print("=" * 50)
    print(f"🎯 Puerto FORZADO: {BACKEND_PORT}")
    print(f"🌐 Host: {BACKEND_HOST}")
    print("")
    
    # Limpiar puerto
    print("🧹 Limpiando puerto 8001...")
    kill_port_processes(BACKEND_PORT)
    time.sleep(1)
    
    # Configurar variables de entorno FORZADAS
    os.environ["PORT"] = str(BACKEND_PORT)
    os.environ["HOST"] = BACKEND_HOST
    os.environ["UVICORN_PORT"] = str(BACKEND_PORT)
    os.environ["UVICORN_HOST"] = BACKEND_HOST
    
    print("🐍 Iniciando backend...")
    print(f"📍 URL: http://{BACKEND_HOST}:{BACKEND_PORT}")
    print(f"📚 Docs: http://{BACKEND_HOST}:{BACKEND_PORT}/docs")
    print("")
    
    # Comando uvicorn con parámetros FORZADOS
    cmd = [
        "python", "-m", "uvicorn", 
        "app.main:app",
        "--reload",
        "--host", BACKEND_HOST,
        "--port", str(BACKEND_PORT)
    ]
    
    try:
        # Ejecutar uvicorn
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 Backend detenido por el usuario")
        sys.exit(0)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error iniciando backend: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
