#!/usr/bin/env python3
"""
Test script to verify the JWT authentication fix is working correctly.
This script tests the new JWT verification method against the old problematic method.
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.auth import verify_jwt_token, get_current_user_from_token
from app.core.supabase import supabase

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test JWT token (you'll need to replace this with a real token from your frontend)
TEST_TOKEN = "your_test_jwt_token_here"

async def test_old_method(token: str) -> Dict[str, Any]:
    """Test the old problematic method that was failing."""
    try:
        logger.info("🔍 Testing old method: supabase.auth.get_user(token)")
        user_response = supabase.auth.get_user(token)
        
        if user_response.user:
            logger.info("✅ Old method succeeded (unexpected)")
            return {
                "success": True,
                "user_id": user_response.user.id,
                "email": user_response.user.email,
                "method": "old"
            }
        else:
            logger.warning("❌ Old method failed: user_response.user is None")
            return {"success": False, "error": "user_response.user is None", "method": "old"}
            
    except Exception as e:
        logger.error(f"❌ Old method failed with exception: {str(e)}")
        return {"success": False, "error": str(e), "method": "old"}

async def test_new_method(token: str) -> Dict[str, Any]:
    """Test the new JWT verification method."""
    try:
        logger.info("🔍 Testing new method: verify_jwt_token(token)")
        payload = await verify_jwt_token(token)
        
        logger.info("✅ New method succeeded")
        return {
            "success": True,
            "user_id": payload.get("sub"),
            "email": payload.get("email"),
            "method": "new",
            "payload_keys": list(payload.keys())
        }
        
    except Exception as e:
        logger.error(f"❌ New method failed: {str(e)}")
        return {"success": False, "error": str(e), "method": "new"}

async def test_full_auth_flow(token: str) -> Dict[str, Any]:
    """Test the complete authentication flow as used in endpoints."""
    try:
        logger.info("🔍 Testing full auth flow: get_current_user_from_token")
        
        # Simulate the authorization header
        authorization = f"Bearer {token}"
        
        # This would normally be called by FastAPI with the header
        # For testing, we'll call it directly (note: this won't work exactly the same)
        # but we can test the token extraction and verification logic
        
        if not authorization.startswith("Bearer "):
            raise Exception("Invalid authorization header format")
            
        extracted_token = authorization.split(" ")[1]
        payload = await verify_jwt_token(extracted_token)
        
        user_id = payload.get("sub")
        email = payload.get("email")
        user_metadata = payload.get("user_metadata", {})
        
        if not user_id or not email:
            raise Exception("Invalid token payload. Missing user information.")
            
        result = {
            "user_id": user_id,
            "username": user_metadata.get("full_name") or email.split("@")[0],
            "email": email,
            "metadata": user_metadata,
            "jwt_token": extracted_token
        }
        
        logger.info("✅ Full auth flow succeeded")
        return {"success": True, "user_data": result, "method": "full_flow"}
        
    except Exception as e:
        logger.error(f"❌ Full auth flow failed: {str(e)}")
        return {"success": False, "error": str(e), "method": "full_flow"}

async def main():
    """Main test function."""
    print("🧪 JWT Authentication Fix Test")
    print("=" * 50)
    
    if TEST_TOKEN == "your_test_jwt_token_here":
        print("⚠️  Please replace TEST_TOKEN with a real JWT token from your frontend")
        print("   You can get this from:")
        print("   1. Login to your frontend")
        print("   2. Open browser dev tools")
        print("   3. Go to Application/Storage > Local Storage")
        print("   4. Look for the Supabase auth token")
        print("   5. Copy the access_token value")
        return
    
    print(f"🔑 Testing with token: {TEST_TOKEN[:20]}...")
    print()
    
    # Test old method
    print("1️⃣ Testing Old Method (Expected to Fail)")
    print("-" * 40)
    old_result = await test_old_method(TEST_TOKEN)
    print(f"Result: {old_result}")
    print()
    
    # Test new method
    print("2️⃣ Testing New Method (Should Succeed)")
    print("-" * 40)
    new_result = await test_new_method(TEST_TOKEN)
    print(f"Result: {new_result}")
    print()
    
    # Test full flow
    print("3️⃣ Testing Full Authentication Flow")
    print("-" * 40)
    full_result = await test_full_auth_flow(TEST_TOKEN)
    print(f"Result: {full_result}")
    print()
    
    # Summary
    print("📊 Test Summary")
    print("=" * 50)
    
    if old_result["success"]:
        print("⚠️  Old method unexpectedly succeeded - this might indicate a different issue")
    else:
        print("✅ Old method failed as expected")
    
    if new_result["success"]:
        print("✅ New method succeeded - JWT verification is working!")
    else:
        print("❌ New method failed - there may be an issue with the implementation")
    
    if full_result["success"]:
        print("✅ Full auth flow succeeded - endpoints should work correctly!")
        print(f"   User: {full_result['user_data']['username']} ({full_result['user_data']['email']})")
    else:
        print("❌ Full auth flow failed - endpoints may still have issues")
    
    print()
    print("🎯 Next Steps:")
    if new_result["success"] and full_result["success"]:
        print("✅ Authentication fix is working! You can now:")
        print("   1. Restart your backend server")
        print("   2. Test the frontend authentication")
        print("   3. Try accessing protected endpoints")
    else:
        print("🔧 Issues found. Please:")
        print("   1. Check the error messages above")
        print("   2. Verify your Supabase configuration")
        print("   3. Ensure the JWT token is valid and not expired")

if __name__ == "__main__":
    asyncio.run(main())
