"""
Enhanced health monitoring endpoints for Emma Studio backend stability.
"""

import logging
import time
import os
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException

from app.services.external_service_health import external_service_monitor

logger = logging.getLogger(__name__)
router = APIRouter()

# Track startup time for uptime calculation
STARTUP_TIME = time.time()

@router.get("/health/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """
    Comprehensive health check including system resources and service status.
    """
    try:
        # Basic health info
        current_time = time.time()
        uptime_seconds = current_time - STARTUP_TIME
        uptime_formatted = str(timedelta(seconds=int(uptime_seconds)))
        
        # Memory usage
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024

        # CPU usage
        cpu_percent = process.cpu_percent()

        # System memory
        system_memory = psutil.virtual_memory()
        
        # Check critical services
        service_status = await check_critical_services()

        # Get external service status
        external_services = external_service_monitor.get_all_statuses()

        # Determine overall health status
        internal_healthy = all(service_status.values())
        external_healthy = all(
            status["status"] in ["healthy", "unknown"]
            for status in external_services.values()
        )
        overall_status = "healthy" if internal_healthy and external_healthy else "degraded"

        health_data = {
            "status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "uptime": uptime_formatted,
            "uptime_seconds": int(uptime_seconds),
            "process": {
                "pid": os.getpid(),
                "memory_mb": round(memory_mb, 2),
                "cpu_percent": cpu_percent,
                "threads": process.num_threads()
            },
            "system": {
                "memory_total_gb": round(system_memory.total / 1024 / 1024 / 1024, 2),
                "memory_available_gb": round(system_memory.available / 1024 / 1024 / 1024, 2),
                "memory_percent": system_memory.percent,
                "cpu_count": psutil.cpu_count()
            },
            "services": service_status,
            "external_services": external_services,
            "environment": {
                "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
                "platform": os.name,
                "reload_mode": "--reload" in os.sys.argv
            }
        }
        
        return health_data
        
    except Exception as e:
        logger.error(f"Error in detailed health check: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/health/services")
async def service_health_check() -> Dict[str, Any]:
    """
    Check the health of critical Emma Studio services.
    """
    try:
        service_status = await check_critical_services()
        
        return {
            "status": "healthy" if all(service_status.values()) else "degraded",
            "services": service_status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in service health check: {e}")
        raise HTTPException(status_code=500, detail=f"Service health check failed: {str(e)}")

@router.get("/health/memory")
async def memory_health_check() -> Dict[str, Any]:
    """
    Detailed memory usage analysis.
    """
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()

        # Get memory details
        memory_data = {
            "status": "healthy",
            "process_memory": {
                "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
                "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
                "percent": round(process.memory_percent(), 2)
            },
            "system_memory": {
                "total_gb": round(psutil.virtual_memory().total / 1024 / 1024 / 1024, 2),
                "available_gb": round(psutil.virtual_memory().available / 1024 / 1024 / 1024, 2),
                "used_percent": psutil.virtual_memory().percent
            },
            "timestamp": datetime.now().isoformat()
        }
        
        # Check for memory issues
        if memory_data["process_memory"]["rss_mb"] > 1000:  # > 1GB
            memory_data["status"] = "warning"
            memory_data["warning"] = "High memory usage detected"
        
        if memory_data["system_memory"]["used_percent"] > 90:
            memory_data["status"] = "critical"
            memory_data["error"] = "System memory critically low"
        
        return memory_data
        
    except Exception as e:
        logger.error(f"Error in memory health check: {e}")
        raise HTTPException(status_code=500, detail=f"Memory health check failed: {str(e)}")

async def check_critical_services() -> Dict[str, bool]:
    """
    Check if critical services are properly initialized and working.
    """
    service_status = {}
    
    try:
        # Check database connection
        try:
            from app.db.session import engine
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            service_status["database"] = True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            service_status["database"] = False
        
        # Check Gemini AI service
        try:
            from app.core.config import settings
            service_status["gemini_api_key"] = bool(settings.GEMINI_API_KEY)
        except Exception as e:
            logger.error(f"Gemini API key check failed: {e}")
            service_status["gemini_api_key"] = False
        
        # Check Ideogram service
        try:
            from app.core.config import settings
            service_status["ideogram_api_key"] = bool(settings.IDEOGRAM_API_KEY)
        except Exception as e:
            logger.error(f"Ideogram API key check failed: {e}")
            service_status["ideogram_api_key"] = False
        
        # Check if we're in reload mode (problematic)
        service_status["stable_mode"] = "--reload" not in os.sys.argv
        
        # Check for global service instances (potential memory leaks)
        global_services_count = 0
        try:
            import sys
            for module_name, module in sys.modules.items():
                if module_name.startswith('app.services.') and hasattr(module, '__dict__'):
                    for attr_name, attr_value in module.__dict__.items():
                        if attr_name.endswith('_service') and not attr_name.startswith('_'):
                            global_services_count += 1
            
            service_status["global_services_count"] = global_services_count
            service_status["global_services_warning"] = global_services_count > 10
        except Exception as e:
            logger.error(f"Global services check failed: {e}")
            service_status["global_services_count"] = -1
        
    except Exception as e:
        logger.error(f"Error checking critical services: {e}")
    
    return service_status

@router.get("/health/stability")
async def stability_check() -> Dict[str, Any]:
    """
    Check for known stability issues and provide recommendations.
    """
    try:
        issues = []
        recommendations = []
        
        # Check for auto-reload mode
        if "--reload" in os.sys.argv:
            issues.append("Auto-reload mode detected (causes instability)")
            recommendations.append("Use ./emma-stable.sh for production stability")
        
        # Check memory usage
        process = psutil.Process(os.getpid())
        memory_mb = process.memory_info().rss / 1024 / 1024

        if memory_mb > 500:
            issues.append(f"High memory usage: {memory_mb:.1f}MB")
            recommendations.append("Consider restarting the backend")
        
        # Check uptime
        uptime_hours = (time.time() - STARTUP_TIME) / 3600
        if uptime_hours > 24:
            issues.append(f"Long uptime: {uptime_hours:.1f} hours")
            recommendations.append("Consider periodic restarts for optimal performance")
        
        # Check for global services
        service_status = await check_critical_services()
        if service_status.get("global_services_warning", False):
            issues.append("High number of global service instances detected")
            recommendations.append("Consider implementing dependency injection")
        
        stability_status = "stable" if not issues else "unstable"
        
        return {
            "status": stability_status,
            "issues": issues,
            "recommendations": recommendations,
            "uptime_hours": round(uptime_hours, 2),
            "memory_mb": round(memory_mb, 2),
            "reload_mode": "--reload" in os.sys.argv,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in stability check: {e}")
        raise HTTPException(status_code=500, detail=f"Stability check failed: {str(e)}")

@router.get("/health/external-services")
async def external_services_health() -> Dict[str, Any]:
    """
    Check health of all external services with circuit breaker status.
    """
    try:
        # Trigger immediate health check
        await external_service_monitor.check_all_services()

        # Get all service statuses
        services = external_service_monitor.get_all_statuses()

        # Calculate summary statistics
        total_services = len(services)
        healthy_services = sum(1 for s in services.values() if s["status"] == "healthy")
        degraded_services = sum(1 for s in services.values() if s["status"] == "degraded")
        unhealthy_services = sum(1 for s in services.values() if s["status"] == "unhealthy")
        circuit_broken_services = sum(1 for s in services.values() if s["circuit_breaker_state"] == "open")

        overall_status = "healthy"
        if unhealthy_services > 0 or circuit_broken_services > 0:
            overall_status = "unhealthy"
        elif degraded_services > 0:
            overall_status = "degraded"

        return {
            "status": overall_status,
            "summary": {
                "total_services": total_services,
                "healthy": healthy_services,
                "degraded": degraded_services,
                "unhealthy": unhealthy_services,
                "circuit_broken": circuit_broken_services
            },
            "services": services,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error checking external services health: {e}")
        raise HTTPException(status_code=500, detail=f"External services health check failed: {str(e)}")

@router.get("/health/external-services/{service_name}")
async def external_service_health(service_name: str) -> Dict[str, Any]:
    """
    Check health of a specific external service.
    """
    try:
        # Check if service exists
        service_info = external_service_monitor.get_service_status(service_name)
        if not service_info:
            raise HTTPException(status_code=404, detail=f"Service '{service_name}' not found")

        # Trigger immediate health check for this service
        await external_service_monitor._check_service_health(service_name)

        # Get updated status
        service_info = external_service_monitor.get_service_status(service_name)

        return {
            "status": "success",
            "service": service_info.to_dict(),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking health of service {service_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Service health check failed: {str(e)}")

@router.post("/health/external-services/start-monitoring")
async def start_external_monitoring() -> Dict[str, Any]:
    """
    Start periodic monitoring of external services.
    """
    try:
        await external_service_monitor.start_monitoring()
        return {
            "status": "success",
            "message": "External service monitoring started",
            "monitoring_interval_seconds": external_service_monitor.health_check_interval,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error starting external service monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start monitoring: {str(e)}")

@router.post("/health/external-services/stop-monitoring")
async def stop_external_monitoring() -> Dict[str, Any]:
    """
    Stop periodic monitoring of external services.
    """
    try:
        await external_service_monitor.stop_monitoring()
        return {
            "status": "success",
            "message": "External service monitoring stopped",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error stopping external service monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop monitoring: {str(e)}")
