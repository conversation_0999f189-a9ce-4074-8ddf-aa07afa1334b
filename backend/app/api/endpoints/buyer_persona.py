"""API endpoints for buyer persona generation."""

import logging
import time
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse

from app.schemas.buyer_persona import (
    BuyerPersonaRequest,
    BuyerPersonaResponse,
    TextAssistRequest,
    TextAssistResponse
)
from app.services.buyer_persona_service import buyer_persona_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/generate-buyer-personas", response_model=BuyerPersonaResponse)
async def generate_buyer_personas(
    request: BuyerPersonaRequest,
    http_request: Request
) -> BuyerPersonaResponse:
    """
    Generate detailed buyer personas based on product description and business context.

    This endpoint creates comprehensive buyer personas including:
    - Demographic and psychographic profiles
    - Job information and responsibilities
    - Goals, challenges, and pain points
    - Buying process and decision factors
    - Communication preferences and influences
    - Marketing recommendations and strategies
    - General market insights

    Args:
        request: Buyer persona generation parameters
        http_request: FastAPI request object

    Returns:
        Detailed buyer personas with marketing recommendations
    """
    start_time = time.time()
    request_id = http_request.headers.get("X-Request-ID", f"req_{int(time.time()*1000)}")

    logger.info(f"🚀 Buyer persona generation request {request_id}: {request.product_description[:100] if request.product_description else 'No description'}...")
    logger.info(f"📊 Request details: num_personas={request.num_personas}, industry={request.industry}, target_market={request.target_market}")
    logger.info(f"🔧 Full request payload: {request.dict()}")

    try:
        logger.info(f"✅ Starting validation for request {request_id}")

        # Enhanced validation for both legacy and smart form data
        if not request.product_description and not request.smart_form_data:
            logger.error(f"❌ Validation failed: No product description or smart form data provided")
            raise HTTPException(
                status_code=400,
                detail="Either product_description or smart_form_data must be provided"
            )

        # Validate product description if provided
        if request.product_description and len(request.product_description.strip()) < 10:
            logger.error(f"❌ Validation failed: Product description too short ({len(request.product_description.strip())} chars)")
            raise HTTPException(
                status_code=400,
                detail="Product description must be at least 10 characters long"
            )

        # Validate smart form data if provided
        if request.smart_form_data:
            try:
                logger.info(f"✅ Smart form data provided and valid")
                # Pydantic will handle validation automatically
                pass
            except Exception as e:
                logger.error(f"❌ Smart form validation failed: {e}")
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid smart form data: {str(e)}"
                )

        # Validate number of personas
        if request.num_personas < 1 or request.num_personas > 5:
            logger.error(f"❌ Validation failed: Invalid number of personas ({request.num_personas})")
            raise HTTPException(
                status_code=400,
                detail="Number of personas must be between 1 and 5"
            )

        logger.info(f"✅ All validations passed for request {request_id}")

        # Generate buyer personas
        logger.info(f"🤖 Calling buyer_persona_service.generate_buyer_personas for request {request_id}")
        response = await buyer_persona_service.generate_buyer_personas(request)
        logger.info(f"✅ Service call completed successfully for request {request_id}")

        # Log execution time
        execution_time = time.time() - start_time
        logger.info(f"Buyer persona generation completed in {execution_time:.2f}s for request {request_id}")

        return response

    except HTTPException as http_exc:
        # Log HTTP exceptions but re-raise them to maintain proper status codes
        execution_time = time.time() - start_time
        logger.error(f"HTTP error generating buyer personas for request {request_id}: {http_exc.detail} (status: {http_exc.status_code})")
        raise
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"Unexpected error generating buyer personas for request {request_id}: {e}", exc_info=True)

        # Return a proper error response instead of raising an exception
        return BuyerPersonaResponse(
            status="error",
            buyer_personas=[],
            marketing_recommendations=[],
            general_insights=[],
            timestamp=datetime.now().isoformat(),
            request_id=request_id,
            error_message=f"Generation failed: {str(e)}. Please try again.",
            generation_time_seconds=execution_time,
            ai_model_used="error"
        )


@router.post("/text-assist", response_model=TextAssistResponse)
async def text_assist(
    request: TextAssistRequest,
    http_request: Request
) -> TextAssistResponse:
    """
    Improve text content using AI assistance for better buyer persona generation.

    This endpoint provides intelligent text improvements including:
    - Enhanced clarity and specificity
    - Professional tone optimization
    - Marketing-focused language
    - Industry-specific terminology
    - Call-to-action improvements

    Args:
        request: Text assistance parameters
        http_request: FastAPI request object

    Returns:
        Improved text suggestions
    """
    start_time = time.time()
    request_id = http_request.headers.get("X-Request-ID", f"req_{int(time.time()*1000)}")

    logger.info(f"Text assistance request {request_id}: {request.content[:50]}...")

    try:
        # Validate request
        if not request.content.strip():
            raise HTTPException(
                status_code=400,
                detail="Content is required and cannot be empty"
            )

        # Improve text
        response = await buyer_persona_service.improve_text(request)

        # Log execution time
        execution_time = time.time() - start_time
        logger.info(f"Text assistance completed in {execution_time:.2f}s for request {request_id}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"Error in text assistance for request {request_id}: {e}", exc_info=True)

        return TextAssistResponse(
            status="error",
            suggestions=[],
            original_content=request.content,
            error_message=f"Internal server error: {str(e)}"
        )


@router.get("/health")
async def buyer_persona_health() -> Dict[str, Any]:
    """Health check for buyer persona service."""
    try:
        # Check if service is available using the correct method
        service_status = buyer_persona_service.get_service_status()

        return {
            "status": "healthy",
            "service": "buyer_persona",
            "ai_status": "available" if service_status["ai_available"] else "mock_mode",
            "model_name": service_status["model_name"],
            "services": service_status["services"],
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": "buyer_persona",
            "error": str(e),
            "timestamp": time.time()
        }
