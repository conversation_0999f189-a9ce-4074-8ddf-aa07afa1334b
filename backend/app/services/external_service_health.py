"""
External Service Health Monitoring for Emma Studio
Provides health checks, circuit breaker patterns, and status monitoring for external APIs.
"""

import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from enum import Enum
import httpx
import google.generativeai as genai

from app.core.config import settings

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    """Service health status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

class CircuitBreakerState(Enum):
    """Circuit breaker state enumeration."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Service disabled due to failures
    HALF_OPEN = "half_open"  # Testing if service recovered

class ServiceHealthInfo:
    """Health information for a single external service."""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.status = ServiceStatus.UNKNOWN
        self.last_check = None
        self.last_success = None
        self.consecutive_failures = 0
        self.total_requests = 0
        self.successful_requests = 0
        self.error_message = None
        self.response_time_ms = None
        self.circuit_breaker_state = CircuitBreakerState.CLOSED
        self.circuit_breaker_opened_at = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "service_name": self.service_name,
            "status": self.status.value,
            "last_check": self.last_check.isoformat() if self.last_check else None,
            "last_success": self.last_success.isoformat() if self.last_success else None,
            "consecutive_failures": self.consecutive_failures,
            "success_rate": self.successful_requests / max(self.total_requests, 1),
            "error_message": self.error_message,
            "response_time_ms": self.response_time_ms,
            "circuit_breaker_state": self.circuit_breaker_state.value,
            "circuit_breaker_opened_at": self.circuit_breaker_opened_at.isoformat() if self.circuit_breaker_opened_at else None
        }

class ExternalServiceHealthMonitor:
    """Monitor health of external services with circuit breaker pattern."""
    
    def __init__(self):
        self.services: Dict[str, ServiceHealthInfo] = {}
        self.health_check_interval = 300  # 5 minutes
        self.circuit_breaker_failure_threshold = 3
        self.circuit_breaker_recovery_timeout = 300  # 5 minutes
        self.health_check_timeout = 10  # 10 seconds
        self._monitoring_task = None
        self._is_monitoring = False
        
        # Initialize service health info
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialize health info for all external services."""
        services = [
            "gemini_ai",
            "ideogram_api", 
            "stability_ai",
            "openai_api",
            "serper_api"
        ]
        
        for service in services:
            self.services[service] = ServiceHealthInfo(service)
    
    async def start_monitoring(self):
        """Start periodic health monitoring."""
        if self._is_monitoring:
            return
            
        self._is_monitoring = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("🔍 External service health monitoring started")
    
    async def stop_monitoring(self):
        """Stop periodic health monitoring."""
        self._is_monitoring = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 External service health monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while self._is_monitoring:
            try:
                await self.check_all_services()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def check_all_services(self) -> Dict[str, ServiceHealthInfo]:
        """Check health of all external services."""
        logger.info("🔍 Checking health of all external services...")
        
        # Run all health checks concurrently
        tasks = []
        for service_name in self.services.keys():
            task = asyncio.create_task(self._check_service_health(service_name))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Log summary
        healthy_count = sum(1 for s in self.services.values() if s.status == ServiceStatus.HEALTHY)
        total_count = len(self.services)
        logger.info(f"✅ Health check complete: {healthy_count}/{total_count} services healthy")
        
        return self.services
    
    async def _check_service_health(self, service_name: str):
        """Check health of a specific service."""
        service_info = self.services[service_name]
        start_time = time.time()
        
        try:
            # Check circuit breaker state
            if service_info.circuit_breaker_state == CircuitBreakerState.OPEN:
                # Check if recovery timeout has passed
                if (service_info.circuit_breaker_opened_at and 
                    datetime.now() - service_info.circuit_breaker_opened_at > timedelta(seconds=self.circuit_breaker_recovery_timeout)):
                    service_info.circuit_breaker_state = CircuitBreakerState.HALF_OPEN
                    logger.info(f"🔄 Circuit breaker for {service_name} moved to HALF_OPEN")
                else:
                    # Still in open state, skip health check
                    return
            
            # Perform actual health check
            is_healthy = await self._perform_health_check(service_name)
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            # Update service info
            service_info.last_check = datetime.now()
            service_info.response_time_ms = response_time
            service_info.total_requests += 1
            
            if is_healthy:
                service_info.status = ServiceStatus.HEALTHY
                service_info.last_success = datetime.now()
                service_info.successful_requests += 1
                service_info.consecutive_failures = 0
                service_info.error_message = None
                
                # Close circuit breaker if it was open
                if service_info.circuit_breaker_state != CircuitBreakerState.CLOSED:
                    service_info.circuit_breaker_state = CircuitBreakerState.CLOSED
                    service_info.circuit_breaker_opened_at = None
                    logger.info(f"✅ Circuit breaker for {service_name} CLOSED - service recovered")
            else:
                service_info.status = ServiceStatus.UNHEALTHY
                service_info.consecutive_failures += 1
                
                # Open circuit breaker if failure threshold reached
                if (service_info.consecutive_failures >= self.circuit_breaker_failure_threshold and
                    service_info.circuit_breaker_state == CircuitBreakerState.CLOSED):
                    service_info.circuit_breaker_state = CircuitBreakerState.OPEN
                    service_info.circuit_breaker_opened_at = datetime.now()
                    logger.warning(f"🚨 Circuit breaker for {service_name} OPENED - too many failures")
        
        except Exception as e:
            logger.error(f"Error checking health of {service_name}: {e}")
            service_info.status = ServiceStatus.UNKNOWN
            service_info.error_message = str(e)
    
    async def _perform_health_check(self, service_name: str) -> bool:
        """Perform actual health check for a specific service."""
        try:
            if service_name == "gemini_ai":
                return await self._check_gemini_health()
            elif service_name == "ideogram_api":
                return await self._check_ideogram_health()
            elif service_name == "stability_ai":
                return await self._check_stability_health()
            elif service_name == "openai_api":
                return await self._check_openai_health()
            elif service_name == "serper_api":
                return await self._check_serper_health()
            else:
                return False
        except Exception as e:
            logger.error(f"Health check failed for {service_name}: {e}")
            return False
    
    async def _check_gemini_health(self) -> bool:
        """Check Gemini AI health."""
        if not settings.GEMINI_API_KEY:
            return False
        
        try:
            # Simple test generation
            genai.configure(api_key=settings.GEMINI_API_KEY)
            model = genai.GenerativeModel('gemini-1.5-flash')
            response = await asyncio.to_thread(
                model.generate_content,
                "Test",
                generation_config=genai.types.GenerationConfig(max_output_tokens=10)
            )
            return bool(response.text)
        except Exception:
            return False
    
    async def _check_ideogram_health(self) -> bool:
        """Check Ideogram API health."""
        if not settings.IDEOGRAM_API_KEY:
            return False
        
        try:
            async with httpx.AsyncClient(timeout=self.health_check_timeout) as client:
                response = await client.get(
                    "https://api.ideogram.ai/v1/models",
                    headers={"Api-Key": settings.IDEOGRAM_API_KEY}
                )
                return response.status_code == 200
        except Exception:
            return False
    
    async def _check_stability_health(self) -> bool:
        """Check Stability AI health."""
        if not settings.STABILITY_API_KEY:
            return False
        
        try:
            async with httpx.AsyncClient(timeout=self.health_check_timeout) as client:
                response = await client.get(
                    f"{settings.STABILITY_API_URL}/v1/user/account",
                    headers={"Authorization": f"Bearer {settings.STABILITY_API_KEY}"}
                )
                return response.status_code == 200
        except Exception:
            return False
    
    async def _check_openai_health(self) -> bool:
        """Check OpenAI API health."""
        if not settings.OPENAI_API_KEY:
            return False
        
        try:
            async with httpx.AsyncClient(timeout=self.health_check_timeout) as client:
                response = await client.get(
                    "https://api.openai.com/v1/models",
                    headers={"Authorization": f"Bearer {settings.OPENAI_API_KEY}"}
                )
                return response.status_code == 200
        except Exception:
            return False
    
    async def _check_serper_health(self) -> bool:
        """Check Serper API health."""
        if not settings.SERPER_API_KEY:
            return False
        
        try:
            async with httpx.AsyncClient(timeout=self.health_check_timeout) as client:
                response = await client.post(
                    "https://google.serper.dev/search",
                    headers={"X-API-KEY": settings.SERPER_API_KEY},
                    json={"q": "test", "num": 1}
                )
                return response.status_code == 200
        except Exception:
            return False
    
    def is_service_available(self, service_name: str) -> bool:
        """Check if a service is available (not circuit broken)."""
        if service_name not in self.services:
            return False
        
        service_info = self.services[service_name]
        return service_info.circuit_breaker_state != CircuitBreakerState.OPEN
    
    def get_service_status(self, service_name: str) -> Optional[ServiceHealthInfo]:
        """Get status of a specific service."""
        return self.services.get(service_name)
    
    def get_all_statuses(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all services."""
        return {name: info.to_dict() for name, info in self.services.items()}

# Global instance
external_service_monitor = ExternalServiceHealthMonitor()
