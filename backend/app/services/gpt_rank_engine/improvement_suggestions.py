"""
Improvement Suggestions Module
Generates specific improvement suggestions based on analysis results
"""

import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class ImprovementSuggestionGenerator:
    """Generates specific improvement suggestions based on component analysis."""
    
    def __init__(self):
        logger.info("✅ Improvement Suggestion Generator initialized successfully")
    
    def generate_improvement_suggestions(
        self,
        component_scores: Dict[str, float],
        content: str,
        topic: str
    ) -> List[str]:
        """
        Generate specific improvement suggestions based on component scores.

        Args:
            component_scores: Scores for each component
            content: Original content
            topic: Main topic

        Returns:
            List of specific improvement suggestions as strings
        """
        try:
            suggestions = []
            content_analysis = self._analyze_content_specifics(content)

            # Generate specific suggestions based on component scores
            for component, score in component_scores.items():
                if score < 70:  # Needs improvement
                    specific_suggestions = self._get_specific_suggestions(
                        component, score, content, topic, content_analysis
                    )
                    suggestions.extend(specific_suggestions)

            # Add content-specific suggestions
            content_suggestions = self._get_content_specific_suggestions(content, content_analysis)
            suggestions.extend(content_suggestions)

            # Remove duplicates and limit to top 5 most actionable
            unique_suggestions = list(dict.fromkeys(suggestions))
            return unique_suggestions[:5]

        except Exception as e:
            logger.error(f"❌ Error generating improvement suggestions: {str(e)}")
            return ["Mejora la estructura del contenido", "Agrega más detalles específicos", "Incluye ejemplos prácticos"]

    def _analyze_content_specifics(self, content: str) -> Dict[str, Any]:
        """Analyze specific characteristics of the content."""
        words = content.split()
        sentences = content.split('.')

        return {
            'word_count': len(words),
            'sentence_count': len([s for s in sentences if s.strip()]),
            'has_questions': '?' in content,
            'has_lists': any(marker in content for marker in ['•', '-', '1.', '2.', '*']),
            'has_numbers': any(char.isdigit() for char in content),
            'has_technical_terms': len([w for w in words if len(w) > 8]) > 2,
            'paragraph_count': len([p for p in content.split('\n\n') if p.strip()]),
            'keywords_mentioned': self._extract_keywords(content)
        }

    def _extract_keywords(self, content: str) -> List[str]:
        """Extract potential keywords from content."""
        words = content.lower().split()
        # Simple keyword extraction - words longer than 4 characters, not common words
        common_words = {'para', 'este', 'esta', 'como', 'pero', 'todo', 'más', 'muy', 'bien', 'hacer', 'puede', 'debe'}
        keywords = [w for w in words if len(w) > 4 and w not in common_words]
        return list(set(keywords))[:5]

    def _get_specific_suggestions(
        self,
        component: str,
        score: float,
        content: str,
        topic: str,
        content_analysis: Dict[str, Any]
    ) -> List[str]:
        """Generate specific suggestions based on component and content analysis."""
        suggestions = []

        if component == "semantic_similarity" and score < 70:
            if not content_analysis['has_technical_terms']:
                suggestions.append(f"Incluye terminología más técnica relacionada con {topic}")
            if content_analysis['word_count'] < 100:
                suggestions.append("Expande el contenido con más detalles específicos y ejemplos")
            suggestions.append(f"Define claramente qué es {topic} y sus características principales")

        elif component == "logical_coherence" and score < 70:
            if content_analysis['paragraph_count'] < 2:
                suggestions.append("Divide el contenido en párrafos más estructurados")
            if not any(word in content.lower() for word in ['además', 'por tanto', 'sin embargo', 'finalmente']):
                suggestions.append("Agrega conectores lógicos: 'además', 'por tanto', 'sin embargo'")
            suggestions.append("Organiza las ideas en orden lógico: introducción → desarrollo → conclusión")

        elif component == "authority_signals" and score < 70:
            if not content_analysis['has_numbers']:
                suggestions.append("Incluye datos específicos, estadísticas o porcentajes")
            suggestions.append("Menciona fuentes confiables o estudios relacionados")
            suggestions.append("Usa frases como 'según estudios', 'los datos muestran', 'la investigación confirma'")

        elif component == "citability_score" and score < 70:
            if not content_analysis['has_lists']:
                suggestions.append("Estructura información en listas o puntos clave")
            suggestions.append("Crea declaraciones factuales específicas y verificables")
            suggestions.append("Evita lenguaje ambiguo, usa afirmaciones claras y directas")

        elif component == "clarity_score" and score < 70:
            if content_analysis['sentence_count'] > 0:
                avg_words_per_sentence = content_analysis['word_count'] / content_analysis['sentence_count']
                if avg_words_per_sentence > 20:
                    suggestions.append("Acorta las oraciones para mejorar la legibilidad")
            if not content_analysis['has_questions']:
                suggestions.append("Incluye preguntas retóricas para mejorar el engagement")

        elif component == "completeness_score" and score < 70:
            suggestions.append(f"Agrega más aspectos importantes sobre {topic}")
            if content_analysis['word_count'] < 200:
                suggestions.append("Expande el contenido con ejemplos prácticos y casos de uso")
            suggestions.append("Incluye beneficios, desafíos y mejores prácticas")

        return suggestions

    def _get_content_specific_suggestions(self, content: str, content_analysis: Dict[str, Any]) -> List[str]:
        """Generate suggestions specific to the content characteristics."""
        suggestions = []

        # Word count suggestions
        if content_analysis['word_count'] < 150:
            suggestions.append("Expande el contenido a al menos 300 palabras para mejor SEO")

        # Structure suggestions
        if not content_analysis['has_lists'] and content_analysis['word_count'] > 100:
            suggestions.append("Organiza información en listas numeradas o con viñetas")

        # Engagement suggestions
        if not content_analysis['has_questions']:
            suggestions.append("Incluye preguntas para aumentar el engagement del lector")

        # Keywords suggestions
        if len(content_analysis['keywords_mentioned']) < 3:
            suggestions.append("Incluye más palabras clave relevantes al tema principal")

        return suggestions

    def _get_component_suggestion(
        self, 
        component: str, 
        score: float, 
        content: str, 
        topic: str
    ) -> Optional[Dict[str, Any]]:
        """Get specific suggestion for a component."""
        suggestions_map = {
            "semantic_similarity": {
                "title": "Mejorar Similitud Semántica",
                "description": "Usa terminología más técnica y estructura similar a fuentes autoritativas",
                "actions": [
                    "Incluye definiciones precisas y técnicas",
                    "Usa lenguaje más formal y académico",
                    "Estructura el contenido como Wikipedia",
                    "Agrega referencias a conceptos establecidos"
                ],
                "examples": [
                    f"Define claramente qué es {topic}",
                    "Usa terminología estándar del campo",
                    "Incluye clasificaciones y categorías"
                ]
            },
            "logical_coherence": {
                "title": "Mejorar Coherencia Lógica",
                "description": "Mejora el flujo y la estructura lógica del contenido",
                "actions": [
                    "Agrega palabras de transición entre párrafos",
                    "Organiza ideas en orden lógico",
                    "Conecta mejor los conceptos",
                    "Crea una progresión clara de ideas"
                ],
                "examples": [
                    "Usa 'además', 'por otro lado', 'sin embargo'",
                    "Estructura: introducción → desarrollo → conclusión",
                    "Cada párrafo debe conectar con el anterior"
                ]
            },
            "authority_signals": {
                "title": "Aumentar Señales de Autoridad",
                "description": "Incluye más indicadores de expertise y credibilidad",
                "actions": [
                    "Agrega datos y estadísticas verificables",
                    "Menciona fuentes confiables y estudios",
                    "Usa lenguaje más autoritativo",
                    "Incluye evidencia científica"
                ],
                "examples": [
                    "Según estudios de [institución]...",
                    "Los datos muestran que...",
                    "La investigación confirma..."
                ]
            },
            "citability_score": {
                "title": "Mejorar Citabilidad",
                "description": "Crea contenido más fácil de citar por IA",
                "actions": [
                    "Incluye definiciones claras y concisas",
                    "Crea declaraciones factuales específicas",
                    "Estructura información en puntos clave",
                    "Evita lenguaje ambiguo"
                ],
                "examples": [
                    f"{topic} se define como...",
                    "Los principales beneficios incluyen...",
                    "Es importante destacar que..."
                ]
            },
            "clarity_score": {
                "title": "Mejorar Claridad",
                "description": "Hace el contenido más claro y legible",
                "actions": [
                    "Simplifica oraciones largas",
                    "Usa párrafos más cortos",
                    "Agrega listas y estructura visual",
                    "Elimina jerga innecesaria"
                ],
                "examples": [
                    "Oraciones de 15-25 palabras",
                    "Párrafos de 3-5 oraciones",
                    "Usa listas numeradas o con viñetas"
                ]
            },
            "completeness_score": {
                "title": "Aumentar Completitud",
                "description": "Cubre más aspectos del tema",
                "actions": [
                    "Responde más preguntas básicas (qué, cómo, por qué)",
                    "Agrega ejemplos prácticos",
                    "Incluye más contexto e información de fondo",
                    "Cubre casos de uso específicos"
                ],
                "examples": [
                    f"¿Qué es {topic}?",
                    f"¿Cómo funciona {topic}?",
                    f"¿Cuándo usar {topic}?"
                ]
            }
        }
        
        if component in suggestions_map:
            suggestion = suggestions_map[component].copy()
            suggestion.update({
                "component": component,
                "current_score": round(score, 1),
                "priority": "high" if score < 50 else "medium" if score < 70 else "low",
                "impact": "high" if component in ["semantic_similarity", "authority_signals"] else "medium"
            })
            return suggestion
        
        return None
    
    def _get_general_improvement_suggestions(self, content: str, topic: str) -> List[Dict[str, Any]]:
        """Get general improvement suggestions for low-scoring content."""
        general_suggestions = []
        
        word_count = len(content.split())
        
        # Content length suggestions
        if word_count < 200:
            general_suggestions.append({
                "title": "Expandir Contenido",
                "description": "El contenido es demasiado corto para ser completo",
                "actions": [
                    "Agrega más detalles y explicaciones",
                    "Incluye ejemplos adicionales",
                    "Desarrolla más los conceptos principales"
                ],
                "component": "general",
                "current_score": 30,
                "priority": "high",
                "impact": "high"
            })
        
        # Structure suggestions
        if '\n\n' not in content:
            general_suggestions.append({
                "title": "Mejorar Estructura",
                "description": "El contenido necesita mejor organización",
                "actions": [
                    "Divide el contenido en párrafos",
                    "Agrega subtítulos si es necesario",
                    "Organiza la información lógicamente"
                ],
                "component": "general",
                "current_score": 35,
                "priority": "medium",
                "impact": "medium"
            })
        
        # Topic focus suggestions
        if topic and topic.lower() not in content.lower():
            general_suggestions.append({
                "title": "Enfocar en el Tema",
                "description": f"El contenido debe centrarse más en {topic}",
                "actions": [
                    f"Menciona {topic} más frecuentemente",
                    f"Relaciona todos los puntos con {topic}",
                    "Mantén el foco en el tema principal"
                ],
                "component": "general",
                "current_score": 25,
                "priority": "high",
                "impact": "high"
            })
        
        return general_suggestions
    
    def prioritize_suggestions(self, suggestions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize suggestions based on impact and current score."""
        try:
            # Define priority weights
            priority_weights = {"high": 3, "medium": 2, "low": 1}
            impact_weights = {"high": 3, "medium": 2, "low": 1}
            
            # Calculate priority score for each suggestion
            for suggestion in suggestions:
                priority_score = priority_weights.get(suggestion.get("priority", "medium"), 2)
                impact_score = impact_weights.get(suggestion.get("impact", "medium"), 2)
                current_score = suggestion.get("current_score", 50)
                
                # Lower current score = higher priority
                score_factor = (100 - current_score) / 100
                
                suggestion["priority_score"] = (priority_score + impact_score) * score_factor
            
            # Sort by priority score (descending)
            return sorted(suggestions, key=lambda x: x.get("priority_score", 0), reverse=True)
            
        except Exception as e:
            logger.error(f"❌ Suggestion prioritization failed: {str(e)}")
            return suggestions
    
    def generate_quick_wins(self, suggestions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate quick win suggestions that are easy to implement."""
        quick_wins = []
        
        for suggestion in suggestions:
            component = suggestion.get("component", "")
            
            # Identify quick wins based on component
            if component == "clarity_score":
                quick_wins.append({
                    "title": "Dividir Oraciones Largas",
                    "description": "Mejora inmediata de claridad",
                    "effort": "low",
                    "impact": "medium",
                    "time_estimate": "5-10 minutos"
                })
            
            elif component == "completeness_score":
                quick_wins.append({
                    "title": "Agregar Ejemplos",
                    "description": "Incluye 1-2 ejemplos prácticos",
                    "effort": "low",
                    "impact": "medium",
                    "time_estimate": "10-15 minutos"
                })
            
            elif component == "semantic_similarity":
                quick_wins.append({
                    "title": "Usar Terminología Técnica",
                    "description": "Reemplaza palabras comunes con términos técnicos",
                    "effort": "medium",
                    "impact": "high",
                    "time_estimate": "15-20 minutos"
                })
        
        return quick_wins[:3]  # Return top 3 quick wins
    
    def generate_action_plan(self, suggestions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate a structured action plan based on suggestions."""
        try:
            prioritized_suggestions = self.prioritize_suggestions(suggestions)
            quick_wins = self.generate_quick_wins(suggestions)
            
            # Group suggestions by priority
            high_priority = [s for s in prioritized_suggestions if s.get("priority") == "high"]
            medium_priority = [s for s in prioritized_suggestions if s.get("priority") == "medium"]
            
            action_plan = {
                "immediate_actions": quick_wins,
                "high_priority_improvements": high_priority[:3],
                "medium_priority_improvements": medium_priority[:3],
                "estimated_total_time": self._estimate_total_time(prioritized_suggestions[:6]),
                "expected_score_improvement": self._estimate_score_improvement(prioritized_suggestions[:6])
            }
            
            return action_plan
            
        except Exception as e:
            logger.error(f"❌ Action plan generation failed: {str(e)}")
            return {"error": str(e)}
    
    def _estimate_total_time(self, suggestions: List[Dict[str, Any]]) -> str:
        """Estimate total time needed for improvements."""
        # Simple estimation based on number and type of suggestions
        total_minutes = len(suggestions) * 15  # 15 minutes per suggestion average
        
        if total_minutes < 60:
            return f"{total_minutes} minutos"
        else:
            hours = total_minutes // 60
            minutes = total_minutes % 60
            return f"{hours}h {minutes}m"
    
    def _estimate_score_improvement(self, suggestions: List[Dict[str, Any]]) -> float:
        """Estimate potential score improvement."""
        # Conservative estimate: each suggestion can improve score by 5-15 points
        total_improvement = 0
        
        for suggestion in suggestions:
            current_score = suggestion.get("current_score", 50)
            impact = suggestion.get("impact", "medium")
            
            if impact == "high":
                improvement = min(15, 100 - current_score)
            elif impact == "medium":
                improvement = min(10, 100 - current_score)
            else:
                improvement = min(5, 100 - current_score)
            
            total_improvement += improvement
        
        return min(total_improvement * 0.7, 40)  # Conservative estimate with cap
