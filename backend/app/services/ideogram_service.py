"""
Ideogram.ai Service - Specialized service for generating advertisements using Ideogram.ai
Perfect for text-heavy marketing materials with professional quality at low cost.
"""

import logging
import httpx
import os
from typing import Dict, Any
from fastapi import UploadFile
from app.core.config import settings

logger = logging.getLogger(__name__)


class IdeogramService:
    """Service for generating advertisements using Ideogram.ai - optimized for text and marketing."""
    
    def __init__(self):
        """Initialize the Ideogram service."""
        self.api_key = os.getenv("IDEOGRAM_API_KEY") or settings.IDEOGRAM_API_KEY
        self.base_url = "https://api.ideogram.ai/v1"
        
    async def generate_image(self, prompt: str, dimensions: Dict[str, int] = None) -> Dict[str, Any]:
        """
        Generate an image using Ideogram.ai - main method for post generation.

        Args:
            prompt: Description of the image to create
            dimensions: Dictionary with width and height (e.g., {"width": 1080, "height": 1080})

        Returns:
            Dict with success status, image data, and metadata
        """
        # Convert dimensions to size string
        if dimensions:
            width = dimensions.get("width", 1024)
            height = dimensions.get("height", 1024)
            size = f"{width}x{height}"
        else:
            size = "1024x1024"

        # Use the existing generate_ad method
        return await self.generate_ad(prompt, size)

    async def generate_ad(self, prompt: str, size: str = "1024x1024", mode: str = "general", reference_image: bytes = None) -> Dict[str, Any]:
        """
        Generate an image using Ideogram.ai.

        Args:
            prompt: Description of the image to create
            size: Image size (1024x1024, 1024x1792, 1792x1024)
            mode: Generation mode ("general" for products/ads, "portrait" for human portraits)
            reference_image: Optional reference image bytes for product integration

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Use the prompt directly as Ideogram recommends - don't over-complicate
            enhanced_prompt = prompt

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Configure based on mode
            if mode == "portrait":
                # Portrait mode: Allow realistic humans, optimize for portrait photography
                style_type = "REALISTIC"
                negative_prompt = "cartoon, anime, illustration, drawing, painting, sketch, 3d render, cgi, artificial, fake, distorted anatomy, multiple heads, extra limbs, deformed face, blurry, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution, nude, nsfw"
            else:
                # General mode: Avoid realistic humans for product/ad generation
                style_type = "DESIGN"
                negative_prompt = "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"

            # Prepare multipart form data for Ideogram 3.0 API
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, style_type),
                "negative_prompt": (None, negative_prompt),
                "num_images": (None, "1")  # Single image for individual calls
            }

            # Add reference image if provided
            if reference_image:
                files["image"] = ("reference.jpg", reference_image, "image/jpeg")

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating advertisement with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", "GENERAL")
                            }
                        }
                    else:
                        logger.error(f"No image URL found in response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}
                    
        except Exception as e:
            logger.error(f"Error generating advertisement with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_multiple_ads(self, prompt: str, num_images: int = 3, size: str = "1024x1024", reference_image: UploadFile = None, use_product_image: bool = False) -> Dict[str, Any]:
        """
        Generate multiple advertisements in a single API call using Ideogram.ai.
        Uses best practices: QUALITY rendering, AUTO magic prompt, DESIGN style.

        Args:
            prompt: Description of the advertisement to create
            num_images: Number of images to generate (1-8, default 3)
            size: Image size
            reference_image: Optional reference image
            use_product_image: If True, incorporates the reference_image as the actual product in the ad (REMIX mode)
                              If False, uses reference_image only for style guidance (GENERATE mode)

        Returns:
            Dict with success status, image URLs list, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        # Limit to API maximum
        num_images = min(max(num_images, 1), 8)

        try:
            # Use the prompt directly as Ideogram recommends - don't over-complicate
            enhanced_prompt = prompt

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data for multiple images
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Use QUALITY for best results
                "magic_prompt": (None, "AUTO"),  # Let Ideogram enhance prompts automatically
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, str(num_images))  # Generate multiple images
            }

            # Decide between PRODUCT INCORPORATION or STYLE REFERENCE
            if reference_image and reference_image.filename and use_product_image:
                # PRODUCT MODE: Incorpora el producto en el anuncio usando style_reference_images
                logger.info("🎯 PRODUCT MODE: Incorporating product into advertisement using style reference")
                return await self._generate_with_product_reference(enhanced_prompt, reference_image, num_images, ideogram_size)
            elif reference_image and reference_image.filename:
                # STYLE REFERENCE MODE: Use reference image for style guidance only
                logger.info("📸 Adding style reference image to batch generation")
                # Read the image content
                image_content = await reference_image.read()
                # Reset file pointer for potential future reads
                await reference_image.seek(0)

                # Add style reference image to the request (replaces style_type)
                files["style_reference_images"] = (reference_image.filename, image_content, reference_image.content_type or "image/png")
                logger.info("🎨 Using style_reference_images instead of style_type (mutually exclusive)")
            else:
                # Only add style_type if no reference image is provided
                files["style_type"] = (None, "DESIGN")  # Use DESIGN style for better text integration
                logger.info("🎨 Using style_type=DESIGN (no reference image provided)")

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating {num_images} advertisements with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Process multiple images from response
                if "data" in result and len(result["data"]) > 0:
                    images = []
                    for i, image_data in enumerate(result["data"]):
                        image_url = image_data.get("url")
                        if image_url:
                            images.append({
                                "image_url": image_url,
                                "revised_prompt": image_data.get("prompt"),
                                "metadata": {
                                    "model": "ideogram-3.0-quality",
                                    "size": size,
                                    "resolution": ideogram_size,
                                    "original_prompt": prompt,
                                    "enhanced_prompt": enhanced_prompt,
                                    "seed": image_data.get("seed"),
                                    "variation": i + 1,
                                    "is_image_safe": image_data.get("is_image_safe", True),
                                    "style_type": image_data.get("style_type", "GENERAL")
                                }
                            })

                    if images:
                        # Extract URLs for compatibility
                        image_urls = [img["image_url"] for img in images]

                        return {
                            "success": True,
                            "image_url": image_urls[0],  # First image as primary
                            "all_images": image_urls,
                            "num_generated": len(images),
                            "revised_prompt": images[0]["revised_prompt"],
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "num_requested": num_images,
                                "num_generated": len(images),
                                "type": "premium_batch_generation",
                                "rendering_speed": "QUALITY",
                                "magic_prompt": "AUTO",
                                "style_type": "DESIGN",
                                "all_images_data": images
                            }
                        }
                    else:
                        return {"success": False, "error": "No valid images in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating multiple advertisements with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_with_reference(self, prompt: str, reference_image: UploadFile, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate advertisement using a reference image with Ideogram.ai.
        
        Args:
            prompt: Description of the advertisement to create
            reference_image: Reference image to guide the generation
            size: Image size
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Read reference image content
            image_content = await reference_image.read()
            
            # Enhanced prompt for reference-based generation (optimized for mockups)
            enhanced_prompt = self._create_mockup_prompt_with_reference(prompt)

            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with reference image for Ideogram 3.0 API
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, "GENERAL"),
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, illegible text, low resolution"),
                "num_images": (None, "1"),
                "style_reference_images": ("reference.png", image_content, "image/png")
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🖼️ Generating with reference using Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram reference error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()
                
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "type": "reference_based",
                                "reference_used": True,
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", "GENERAL")
                            }
                        }
                    else:
                        return {"success": False, "error": "No image URL in reference response"}
                else:
                    return {"success": False, "error": "No image data in reference response"}
                    
        except Exception as e:
            logger.error(f"Error in reference generation with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_similar_image_with_reference(self, prompt: str, reference_image_url: str, reference_metadata: Dict[str, Any], size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an image similar to a reference using Ideogram's style_reference_images parameter.
        Uses /generate endpoint with style_reference_images for similar (not remix) generation.

        Args:
            prompt: Description of the image to create
            reference_image_url: URL of the reference image to use as style guide
            reference_metadata: Metadata from the reference image
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Try to get image from local storage first
            from app.services.image_storage_service import image_storage_service

            reference_image_content = image_storage_service.read_stored_image(reference_image_url)

            if reference_image_content:
                logger.info(f"📁 Using locally stored reference image")
            else:
                # Fallback: try to download directly (will likely fail due to expiration)
                logger.warning(f"⚠️ Reference image not found in local storage, trying direct download")
                import httpx
                async with httpx.AsyncClient() as client:
                    response = await client.get(reference_image_url)
                    if response.status_code != 200:
                        logger.error(f"Failed to download reference image: {response.status_code}")
                        return {"success": False, "error": "Failed to download reference image - URL may have expired"}
                    reference_image_content = response.content

            # Extract reference parameters
            reference_style_type = reference_metadata.get("style_type", "DESIGN")

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with style_reference_images (CORRECT WAY FOR SIMILAR)
            files = {
                "prompt": (None, prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, reference_style_type),
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, "1"),
                "style_reference_images": ("reference.png", reference_image_content, "image/png")  # Style reference for similar images
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating SIMILAR image using style_reference_images: {prompt[:100]}...")
            logger.info(f"🖼️ Using /generate with style_reference_images (official documentation)")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",  # Use /generate endpoint for similar
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram similar image error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "seed": image_data.get("seed"),
                                "type": "style_reference_based",
                                "reference_used": True,
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", reference_style_type),
                                "similarity_method": "style_reference_images"
                            }
                        }
                    else:
                        return {"success": False, "error": "No image URL in similar image response"}
                else:
                    return {"success": False, "error": "No image data in similar image response"}

        except Exception as e:
            logger.error(f"Error generating similar image with style reference: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_image_with_seed(self, prompt: str, seed: int, style_type: str = "DESIGN", size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an image using a specific seed for consistency.
        This is a more reliable approach for generating similar images.

        Args:
            prompt: Description of the image to create
            seed: Seed value for consistent generation
            style_type: Style type (DESIGN, GENERAL, etc.)
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with seed
            files = {
                "prompt": (None, prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, style_type),
                "seed": (None, str(seed)),  # Use specific seed for similarity
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, "1")
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🌱 Generating image with seed {seed}: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram seed-based error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "seed": image_data.get("seed", seed),
                                "style_type": image_data.get("style_type", style_type),
                                "is_similar_to_reference": True,
                                "similarity_method": "seed_based",
                                "is_image_safe": image_data.get("is_image_safe", True)
                            }
                        }
                    else:
                        logger.error(f"No image URL found in seed-based response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in seed-based response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating image with seed: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def _generate_with_product_reference(self, prompt: str, product_image: UploadFile, num_images: int, size: str) -> Dict[str, Any]:
        """
        Generate advertisements using PRODUCT REFERENCE mode - incorporates the product using style_reference_images.

        Usa el endpoint GENERATE con style_reference_images para crear anuncios que incorporen el producto.

        Args:
            prompt: Advertisement description
            product_image: The product image to incorporate as reference
            num_images: Number of variations to generate
            size: Image resolution

        Returns:
            Dict with success status, image URLs list, and metadata
        """
        try:
            logger.info(f"🎨 PRODUCT REFERENCE: Creating {num_images} ads incorporating product image")

            # Mejorar el prompt para incorporar el producto como referencia
            enhanced_prompt = f"Create a professional advertisement that prominently features and showcases the product from the provided image. {prompt}. The product must be the main focus and clearly visible in the center of the advertisement. Include compelling marketing text and call-to-action. High quality marketing design with clear readable text, modern layout, eye-catching colors, commercial style, professional typography, clean composition, brand-focused design, premium quality."

            # Read the product image
            image_content = await product_image.read()
            await product_image.seek(0)  # Reset for potential future reads

            # Prepare the generate request with style_reference_images
            files = {
                "prompt": (None, enhanced_prompt),
                "aspect_ratio": (None, "1x1" if size == "1024x1024" else "16x9"),
                "model": (None, "V_3"),
                "magic_prompt": (None, "AUTO"),
                "num_images": (None, str(num_images)),
                "image_prompts": (product_image.filename or "product.png", image_content, product_image.content_type or "image/png"),
                "image_prompt_strength": (None, "80")  # High strength to incorporate the actual product
            }

            headers = {
                "Api-Key": self.api_key
                # Don't set Content-Type, let httpx handle multipart
            }

            # Use the GENERATE endpoint with style_reference_images
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram GENERATE error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram GENERATE error: {error_text}"}

                result = response.json()

                if "data" in result and result["data"]:
                    image_urls = []
                    for image_data in result["data"]:
                        if "url" in image_data:
                            image_urls.append(image_data["url"])

                    if image_urls:
                        logger.info(f"✅ PRODUCT REFERENCE: Successfully generated {len(image_urls)} product advertisements")
                        return {
                            "success": True,
                            "image_url": image_urls[0],  # Primary image
                            "all_images": image_urls,    # All generated variations
                            "num_generated": len(image_urls),
                            "message": f"Generated {len(image_urls)} product advertisements using PRODUCT REFERENCE mode",
                            "has_product_image": True,
                            "mode": "PRODUCT_REFERENCE"
                        }
                    else:
                        logger.error("No image URLs found in GENERATE response")
                        return {"success": False, "error": "No image URLs in GENERATE response"}
                else:
                    logger.error(f"No data in GENERATE response: {result}")
                    return {"success": False, "error": "No image data in GENERATE response"}

        except Exception as e:
            logger.error(f"Error in PRODUCT REFERENCE generation: {e}")
            return {"success": False, "error": f"PRODUCT REFERENCE error: {str(e)}"}

    async def generate_batch_images(self, prompt: str, num_images: int = 4, size: str = "1024x1024") -> Dict[str, Any]:
        """
        🚀 BATCH GENERATION: Generate multiple images at once for faster post creation.

        Args:
            prompt: Description of the images to create
            num_images: Number of images to generate (1-10)
            size: Image size

        Returns:
            Dict with success status and list of image URLs
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Limit to reasonable batch size
            num_images = min(max(1, num_images), 10)

            logger.info(f"🚀 BATCH GENERATING {num_images} images with Ideogram.ai: {prompt[:100]}...")

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare form data for batch generation
            data = {
                "prompt": (None, prompt),
                "size": (None, ideogram_size),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, "DESIGN"),
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, str(num_images))  # 🚀 BATCH SIZE
            }

            headers = {
                "Api-Key": self.api_key
            }

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    "https://api.ideogram.ai/v1/ideogram-v3/generate",
                    files=data,
                    headers=headers
                )

                if response.status_code == 200:
                    result = response.json()

                    if result.get("data") and len(result["data"]) > 0:
                        image_urls = []
                        metadata_list = []

                        for item in result["data"]:
                            if item.get("url"):
                                image_urls.append(item["url"])
                                metadata_list.append({
                                    "seed": item.get("seed"),
                                    "style_type": "DESIGN",
                                    "prompt": prompt,
                                    "size": ideogram_size
                                })

                        logger.info(f"✅ BATCH GENERATED {len(image_urls)} images successfully!")

                        return {
                            "success": True,
                            "image_urls": image_urls,
                            "metadata_list": metadata_list,
                            "total_generated": len(image_urls),
                            "prompt_used": prompt,
                            "size": ideogram_size
                        }
                    else:
                        logger.error("No images in Ideogram response")
                        return {"success": False, "error": "No images generated"}
                else:
                    logger.error(f"Ideogram API error: {response.status_code} - {response.text}")
                    return {"success": False, "error": f"API error: {response.status_code}"}

        except Exception as e:
            logger.error(f"Error in batch image generation: {e}")
            return {"success": False, "error": f"Batch generation failed: {str(e)}"}

    def _create_mockup_prompt_with_reference(self, user_prompt: str) -> str:
        """
        Create optimized prompt for mockup generation with reference images.
        Prevents text overlay issues and ensures proper product placement.
        """
        # Clean and analyze user prompt
        clean_prompt = user_prompt.strip()

        # Detect if this is a product placement request
        placement_keywords = ['in', 'on', 'at', 'inside', 'within', 'surrounded by', 'placed in', 'en', 'sobre', 'dentro']
        is_placement_request = any(keyword in clean_prompt.lower() for keyword in placement_keywords)

        if is_placement_request:
            # For placement requests, focus on environmental integration
            enhanced_prompt = f"""Professional product photography: Take the exact product shown in the reference image and naturally place it {clean_prompt}.

CRITICAL REQUIREMENTS:
- Use the EXACT product from the reference image as the main subject
- Seamlessly integrate the product into the described environment
- Maintain the product's original appearance, colors, and branding
- Position the product naturally within the scene as the focal point
- Create realistic lighting that works for both product and environment

ENVIRONMENT SETUP:
- Create a high-quality, realistic {clean_prompt} setting
- Use professional photography lighting and composition
- Ensure natural shadows, reflections, and depth of field
- Maintain photorealistic quality throughout

STRICT RESTRICTIONS:
- NO text overlays, labels, or written descriptions in the image
- NO graphic design elements, typography, or text of any kind
- NO "my supplement" or literal text interpretations
- Focus purely on photographic realism
- Commercial product photography quality only

The result must look like a professional product photograph where the item from the reference image has been naturally placed in the described environment."""

        else:
            # For general enhancement requests
            enhanced_prompt = f"""Professional product photography: {clean_prompt}

PRODUCT FOCUS:
- Use the exact product from the reference image as the main subject
- Enhance the product presentation while maintaining its original characteristics
- Apply the described styling or context to the product naturally

PHOTOGRAPHY REQUIREMENTS:
- Professional commercial photography quality
- Optimal lighting, composition, and focus
- Realistic shadows, reflections, and depth
- High detail and sharp focus throughout

STRICT RESTRICTIONS:
- NO text overlays, labels, or written descriptions
- NO graphic elements, typography, or text of any kind
- Pure photographic approach only
- Maintain product authenticity and realism

Create a professional product photograph that incorporates the reference product with the described enhancements."""

        logger.info(f"🎯 Mockup prompt created: {enhanced_prompt[:100]}...")
        return enhanced_prompt


# Global service instance
ideogram_service = IdeogramService()
