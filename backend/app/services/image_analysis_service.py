"""
Image Analysis Service for Product Recognition and Description
Analyzes uploaded product images to extract meaningful descriptions for ad generation
"""

import logging
import base64
from typing import Dict, Any, Optional, List
from fastapi import UploadFile
import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class ImageAnalysisService:
    """Service for analyzing product images and extracting descriptions"""
    
    def __init__(self):
        """Initialize the image analysis service with Gemini Vision"""
        self.model = None
        self._initialize_gemini()
    
    def _initialize_gemini(self):
        """Initialize Gemini for image analysis"""
        try:
            if settings.GEMINI_API_KEY:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Gemini Vision initialized for image analysis")
            else:
                logger.warning("⚠️ Gemini API key not configured - image analysis disabled")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini Vision: {e}")
            self.model = None
    
    async def analyze_product_image(self, image_file: UploadFile, user_prompt: str = "") -> Dict[str, Any]:
        """
        Analyze a product image to extract detailed description for ad generation
        
        Args:
            image_file: The uploaded image file
            user_prompt: User's description to provide context
            
        Returns:
            Dict with product analysis results
        """
        if not self.model:
            logger.warning("🔍 Image analysis not available - using fallback")
            return self._fallback_analysis(user_prompt)
        
        try:
            # Read image content
            image_content = await image_file.read()
            await image_file.seek(0)  # Reset for potential future reads
            
            # Prepare the analysis prompt
            analysis_prompt = self._build_analysis_prompt(user_prompt)
            
            # Analyze with Gemini Vision
            response = self.model.generate_content([
                analysis_prompt,
                {"mime_type": image_file.content_type or "image/png", "data": image_content}
            ])
            
            # Parse the response
            analysis_text = response.text if response.text else ""
            
            # Extract structured information
            analysis_result = self._parse_analysis_response(analysis_text, user_prompt)
            
            logger.info(f"✅ Image analysis completed: {analysis_result['product_type']}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ Image analysis failed: {e}")
            return self._fallback_analysis(user_prompt)
    
    def _build_analysis_prompt(self, user_context: str = "") -> str:
        """Build the analysis prompt for Gemini Vision"""
        return f"""
        Analyze this product image for marketing advertisement creation. Provide a detailed analysis in the following format:

        PRODUCT_TYPE: [Category like: supplement, app, clothing, food, service, etc.]
        PRODUCT_NAME: [Specific product name if visible, or generic name]
        VISUAL_DESCRIPTION: [Detailed description of what you see - colors, packaging, design, etc.]
        TARGET_AUDIENCE: [Who this product is likely for - age, gender, lifestyle, etc.]
        INDUSTRY_STYLE: [Marketing style that fits this industry - modern, premium, playful, medical, etc.]
        KEY_FEATURES: [Visible features, benefits, or selling points]
        BRAND_AESTHETIC: [Visual brand style - minimalist, bold, elegant, sporty, etc.]
        SUGGESTED_TONE: [Marketing tone - professional, friendly, urgent, luxurious, etc.]
        
        User context: {user_context}
        
        Be specific and detailed. Focus on elements that would be important for creating an effective marketing advertisement.
        """
    
    def _parse_analysis_response(self, analysis_text: str, user_prompt: str) -> Dict[str, Any]:
        """Parse the Gemini analysis response into structured data"""
        
        # Default values
        result = {
            "product_type": "product",
            "product_name": "product",
            "visual_description": "",
            "target_audience": "general audience",
            "industry_style": "modern professional",
            "key_features": [],
            "brand_aesthetic": "clean modern",
            "suggested_tone": "professional friendly",
            "full_analysis": analysis_text,
            "has_analysis": True
        }
        
        try:
            # Extract information using simple parsing
            lines = analysis_text.split('\n')
            
            for line in lines:
                line = line.strip()
                if line.startswith('PRODUCT_TYPE:'):
                    result["product_type"] = line.replace('PRODUCT_TYPE:', '').strip()
                elif line.startswith('PRODUCT_NAME:'):
                    result["product_name"] = line.replace('PRODUCT_NAME:', '').strip()
                elif line.startswith('VISUAL_DESCRIPTION:'):
                    result["visual_description"] = line.replace('VISUAL_DESCRIPTION:', '').strip()
                elif line.startswith('TARGET_AUDIENCE:'):
                    result["target_audience"] = line.replace('TARGET_AUDIENCE:', '').strip()
                elif line.startswith('INDUSTRY_STYLE:'):
                    result["industry_style"] = line.replace('INDUSTRY_STYLE:', '').strip()
                elif line.startswith('KEY_FEATURES:'):
                    features_text = line.replace('KEY_FEATURES:', '').strip()
                    result["key_features"] = [f.strip() for f in features_text.split(',') if f.strip()]
                elif line.startswith('BRAND_AESTHETIC:'):
                    result["brand_aesthetic"] = line.replace('BRAND_AESTHETIC:', '').strip()
                elif line.startswith('SUGGESTED_TONE:'):
                    result["suggested_tone"] = line.replace('SUGGESTED_TONE:', '').strip()
            
            # Fallback to user prompt if no product name found
            if not result["product_name"] or result["product_name"] == "product":
                if user_prompt:
                    result["product_name"] = user_prompt[:50]  # First 50 chars
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to parse analysis response: {e}")
        
        return result
    
    def _fallback_analysis(self, user_prompt: str) -> Dict[str, Any]:
        """Fallback analysis when Gemini is not available"""
        
        # Try to infer product type from user prompt
        product_type = self._infer_product_type(user_prompt)
        
        return {
            "product_type": product_type,
            "product_name": user_prompt[:50] if user_prompt else "product",
            "visual_description": "product image",
            "target_audience": "general audience",
            "industry_style": self._get_industry_style(product_type),
            "key_features": [],
            "brand_aesthetic": "modern professional",
            "suggested_tone": "professional friendly",
            "full_analysis": f"Fallback analysis for {product_type}",
            "has_analysis": False
        }
    
    def _infer_product_type(self, prompt: str) -> str:
        """Infer product type from user prompt"""
        prompt_lower = prompt.lower() if prompt else ""
        
        # Product type keywords
        type_keywords = {
            "supplement": ["suplemento", "supplement", "vitamina", "protein", "pills", "capsules"],
            "app": ["app", "aplicación", "software", "platform", "saas"],
            "clothing": ["ropa", "clothing", "shirt", "dress", "shoes", "fashion"],
            "food": ["comida", "food", "restaurant", "meal", "recipe", "cooking"],
            "service": ["servicio", "service", "consulting", "consultoría", "coaching"],
            "fitness": ["fitness", "gym", "workout", "exercise", "training"],
            "beauty": ["beauty", "cosmetic", "skincare", "makeup", "belleza"],
            "tech": ["technology", "tech", "gadget", "device", "electronic"]
        }
        
        for product_type, keywords in type_keywords.items():
            if any(keyword in prompt_lower for keyword in keywords):
                return product_type
        
        return "product"
    
    def _get_industry_style(self, product_type: str) -> str:
        """Get appropriate industry style for product type"""
        style_mapping = {
            "supplement": "medical professional clean",
            "app": "modern tech sleek",
            "clothing": "fashion stylish trendy",
            "food": "appetizing warm inviting",
            "service": "professional trustworthy",
            "fitness": "energetic motivational bold",
            "beauty": "elegant luxurious soft",
            "tech": "innovative futuristic clean"
        }
        
        return style_mapping.get(product_type, "modern professional")
    
    def get_style_recommendations(self, analysis: Dict[str, Any]) -> Dict[str, str]:
        """Get style recommendations based on analysis"""
        
        product_type = analysis.get("product_type", "product")
        
        # Style recommendations by product type
        recommendations = {
            "supplement": {
                "style_type": "DESIGN",
                "color_scheme": "medical blues and whites, trust-building colors",
                "typography": "clean, readable, professional fonts",
                "layout": "organized, clinical, evidence-based"
            },
            "app": {
                "style_type": "DESIGN", 
                "color_scheme": "modern tech colors, gradients, vibrant accents",
                "typography": "modern sans-serif, tech-forward",
                "layout": "sleek, minimal, interface-inspired"
            },
            "clothing": {
                "style_type": "DESIGN",
                "color_scheme": "fashion-forward, seasonal, brand-aligned",
                "typography": "stylish, trendy, brand-specific",
                "layout": "lifestyle-focused, aspirational"
            },
            "food": {
                "style_type": "DESIGN",
                "color_scheme": "appetizing, warm, natural tones",
                "typography": "friendly, approachable, readable",
                "layout": "mouth-watering, inviting, comfort-focused"
            }
        }
        
        return recommendations.get(product_type, {
            "style_type": "DESIGN",
            "color_scheme": "professional, trustworthy",
            "typography": "clean, readable",
            "layout": "organized, clear"
        })
