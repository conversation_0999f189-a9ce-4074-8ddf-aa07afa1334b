"""SEO Analysis Service using Jina AI + Gemini for comprehensive website SEO analysis."""

import logging
import asyncio
import requests
import re
from typing import Dict, Any, List, Optional
from urllib.parse import urljoin, urlparse
import time
import json

import google.generativeai as genai
from app.core.config import settings

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

logger = logging.getLogger(__name__)

class SEOAnalyzer:
    """Comprehensive SEO analysis service using Jina AI for content extraction and Gemini for intelligent analysis."""
    
    def __init__(self):
        self.jina_base_url = "https://r.jina.ai/"
        self.gemini_model = None

        # Initialize Gemini AI
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("Gemini AI initialized successfully for SEO analysis")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("GEMINI_API_KEY not found. SEO analysis will use rule-based recommendations only.")
        
    async def analyze_website(self, url: str) -> Dict[str, Any]:
        """
        Perform comprehensive SEO analysis of a website.
        
        Args:
            url: Website URL to analyze
            
        Returns:
            Complete SEO analysis results
        """
        try:
            start_time = time.time()
            logger.info(f"Starting comprehensive SEO analysis for: {url}")
            
            # Step 1: Extract content and HTML using Jina AI
            content_data = await self._extract_website_data(url)
            if not content_data:
                # Fallback: Try basic HTML extraction without Jina AI
                logger.warning(f"Jina AI extraction failed for {url}, trying fallback method")
                content_data = await self._extract_basic_html(url)
                if not content_data:
                    raise Exception("Failed to extract website data using both primary and fallback methods")
            
            # Step 2: Parse HTML for technical SEO analysis
            technical_analysis = await self._analyze_technical_seo(content_data, url)
            
            # Step 3: Analyze content structure and keywords
            content_analysis = await self._analyze_content_seo(content_data)

            # Step 3.5: Analyze real ranking keywords using Serper API
            ranking_keywords = await self._analyze_ranking_keywords(url, technical_analysis, content_analysis)
            content_analysis["ranking_keywords"] = ranking_keywords

            # Step 4: Generate AI-powered recommendations using Gemini
            ai_recommendations = await self._generate_ai_recommendations(
                url, technical_analysis, content_analysis, content_data
            )
            
            # Step 5: Calculate overall SEO score
            seo_score = self._calculate_seo_score(technical_analysis, content_analysis)
            
            # Step 6: Generate preview data for social media
            preview_data = self._generate_preview_data(technical_analysis, url)
            
            processing_time = time.time() - start_time
            
            logger.info(f"SEO analysis completed for {url} in {processing_time:.2f}s")

            # Generate achievements (positive reinforcement)
            achievements = self._generate_achievements(
                technical_analysis.get("basic_info", {}),
                content_analysis,
                technical_analysis.get("technical_audit", {}),
                technical_analysis.get("open_graph", {})
            )



            return {
                "status": "success",
                "url": url,
                "basic_info": technical_analysis.get("basic_info", {}),
                "open_graph": technical_analysis.get("open_graph", {}),
                "twitter_card": technical_analysis.get("twitter_card", {}),
                "technical_audit": technical_analysis.get("technical_audit", {}),
                "performance_metrics": technical_analysis.get("performance_metrics", {}),
                "content_analysis": content_analysis,
                "seo_checks": seo_score.get("checks", {}),
                "preview_data": preview_data,
                "recommendations": ai_recommendations,
                "achievements": achievements,
                "ai_enhanced": True,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"SEO analysis failed for {url}: {str(e)}")
            return {
                "status": "error",
                "error_message": f"SEO analysis failed: {str(e)}",
                "url": url,
                "ai_enhanced": False
            }
    
    async def _extract_website_data(self, url: str) -> Optional[Dict[str, Any]]:
        """Extract website content and HTML using Jina AI Reader."""
        if not AIOHTTP_AVAILABLE:
            logger.warning("aiohttp not available - cannot extract website data")
            return None

        try:
            jina_url = f"{self.jina_base_url}{url}"

            async with aiohttp.ClientSession() as session:
                # Get clean content
                async with session.get(jina_url, timeout=60) as response:
                    if response.status != 200:
                        logger.error(f"Jina AI returned status {response.status} for {url}")
                        return None
                    
                    clean_content = await response.text()
                
                # Get raw HTML for technical analysis
                async with session.get(url, timeout=60) as response:
                    if response.status != 200:
                        logger.warning(f"Could not fetch raw HTML for {url}, status: {response.status}")
                        raw_html = ""
                        status_code = response.status
                        response_time = 0
                    else:
                        raw_html = await response.text()
                        status_code = response.status
                        response_time = response.headers.get('X-Response-Time', 0)
                
                return {
                    "clean_content": clean_content,
                    "raw_html": raw_html,
                    "status_code": status_code,
                    "response_time": response_time,
                    "is_https": url.startswith("https://")
                }
                
        except asyncio.TimeoutError:
            logger.error(f"Timeout extracting data from {url}")
            return None
        except Exception as e:
            logger.error(f"Error extracting website data: {str(e)}")
            return None

    async def _extract_basic_html(self, url: str) -> Optional[Dict[str, Any]]:
        """Fallback method to extract basic HTML without Jina AI."""
        try:
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                async with session.get(url, timeout=45) as response:
                    if response.status != 200:
                        logger.warning(f"Could not fetch HTML for {url}, status: {response.status}")
                        return None

                    raw_html = await response.text()
                    response_time = time.time() - start_time

                    return {
                        "clean_content": raw_html[:5000],  # Truncate for basic analysis
                        "raw_html": raw_html,
                        "status_code": response.status,
                        "response_time": response_time,
                        "is_https": url.startswith("https://")
                    }

        except asyncio.TimeoutError:
            logger.error(f"Timeout in fallback extraction for {url}")
            return None
        except Exception as e:
            logger.error(f"Error in fallback extraction: {str(e)}")
            return None

    async def _analyze_technical_seo(self, content_data: Dict[str, Any], url: str) -> Dict[str, Any]:
        """Analyze technical SEO aspects from HTML."""
        try:
            html = content_data.get("raw_html", "")
            if not html:
                logger.warning("No HTML content available for technical analysis")
                return self._get_default_technical_analysis(content_data, url)

            if not BS4_AVAILABLE:
                logger.warning("BeautifulSoup not available - technical analysis will be limited")
                return self._get_default_technical_analysis(content_data, url)

            soup = BeautifulSoup(html, 'html.parser')

            # Analyze Core Web Vitals and Performance
            try:
                performance_data = await self._analyze_performance_metrics(url)
            except Exception as e:
                logger.error(f"Error in performance metrics analysis: {str(e)}")
                performance_data = self._get_default_performance_metrics()

            # Basic info analysis
            title_tag = soup.find('title')
            title = title_tag.get_text().strip() if title_tag else ""

            meta_desc = soup.find('meta', attrs={'name': 'description'})
            meta_description = meta_desc.get('content', '').strip() if meta_desc else None

            # H1 tags analysis
            h1_tags = [h1.get_text().strip() for h1 in soup.find_all('h1')]

            # Meta tags analysis
            canonical = soup.find('link', attrs={'rel': 'canonical'})
            canonical_url = canonical.get('href') if canonical else None

            lang_attr = soup.find('html')
            language = lang_attr.get('lang') if lang_attr else None

            viewport = soup.find('meta', attrs={'name': 'viewport'})
            has_viewport = viewport is not None
            viewport_content = viewport.get('content') if viewport else None

            robots = soup.find('meta', attrs={'name': 'robots'})
            meta_robots = robots.get('content') if robots else None

            # Open Graph analysis
            og_data = {}
            for og_tag in soup.find_all('meta', property=lambda x: x and x.startswith('og:')):
                property_name = og_tag.get('property', '').replace('og:', '')
                og_data[property_name] = og_tag.get('content', '')

            # Twitter Card analysis
            twitter_data = {}
            for twitter_tag in soup.find_all('meta', attrs={'name': lambda x: x and x.startswith('twitter:')}):
                name = twitter_tag.get('name', '').replace('twitter:', '')
                twitter_data[name] = twitter_tag.get('content', '')

            return {
                "basic_info": {
                    "title": title,
                    "title_length": len(title),
                    "meta_description": meta_description,
                    "meta_description_length": len(meta_description) if meta_description else 0,
                    "h1_tags": h1_tags,
                    "h1_count": len(h1_tags),
                    "canonical_url": canonical_url,
                    "language": language,
                    "has_viewport": has_viewport,
                    "viewport_content": viewport_content,
                    "meta_robots": meta_robots
                },
                "open_graph": og_data,
                "twitter_card": twitter_data,
                "technical_audit": {
                    "is_https": content_data.get("is_https", False),
                    "status_code": content_data.get("status_code", 0),
                    "response_time": content_data.get("response_time", 0)
                },
                "performance_metrics": performance_data,
                "schema_markup": await self._analyze_schema_markup(soup),
                "mobile_usability": await self._analyze_mobile_usability(soup, url)
            }

        except Exception as e:
            logger.error(f"Error in technical SEO analysis: {str(e)}")
            return self._get_default_technical_analysis(content_data, url)

    def _get_default_technical_analysis(self, content_data: Dict[str, Any], url: str) -> Dict[str, Any]:
        """Return default technical analysis when HTML parsing fails."""
        return {
            "basic_info": {
                "title": "Unable to extract title",
                "title_length": 0,
                "meta_description": None,
                "meta_description_length": 0,
                "h1_tags": [],
                "h1_count": 0,
                "canonical_url": None,
                "language": None,
                "has_viewport": False,
                "viewport_content": None,
                "meta_robots": None
            },
            "open_graph": {},
            "twitter_card": {},
            "technical_audit": {
                "is_https": content_data.get("is_https", False),
                "status_code": content_data.get("status_code", 0),
                "response_time": content_data.get("response_time", 0)
            },
            "performance_metrics": self._get_default_performance_metrics(),
            "schema_markup": {
                "json_ld_scripts": [],
                "microdata_items": [],
                "rdfa_properties": [],
                "total_schemas": 0,
                "schema_types": []
            },
            "mobile_usability": {
                "has_viewport": False,
                "viewport_content": None,
                "has_responsive_images": False,
                "has_touch_friendly_elements": False,
                "font_size_issues": [],
                "mobile_score": 0
            }
        }

    async def _analyze_content_seo(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content structure and SEO factors."""
        try:
            clean_content = content_data.get("clean_content", "")
            html = content_data.get("raw_html", "")

            if not clean_content:
                logger.warning("No clean content available for analysis")
                return self._get_default_content_analysis()

            # Word count and reading time
            words = clean_content.split()
            word_count = len(words)
            reading_time = max(1, word_count // 200)  # Average reading speed

            # Analyze HTML structure if available
            headings_structure = {}
            images_info = {"total": 0, "without_alt": 0, "without_alt_percentage": 0}
            links_info = {
                "total": 0, "internal_count": 0, "external_count": 0,
                "internal_links": [], "external_links": []
            }

            if html:
                soup = BeautifulSoup(html, 'html.parser')

                # Headings structure
                for level in range(1, 7):
                    headings = soup.find_all(f'h{level}')
                    if headings:
                        headings_structure[f'h{level}'] = [h.get_text().strip() for h in headings]

                # Images analysis with detailed information
                images = soup.find_all('img')
                images_info["total"] = len(images)

                # Detailed analysis of images without alt text
                images_without_alt_details = []
                without_alt_count = 0

                for i, img in enumerate(images, 1):
                    alt_text = img.get('alt', '').strip()
                    if not alt_text:
                        without_alt_count += 1

                        # Extract detailed information about problematic images
                        img_details = {
                            "position": i,
                            "src": img.get('src', ''),
                            "alt_status": "missing" if img.get('alt') is None else "empty",
                            "width": img.get('width', ''),
                            "height": img.get('height', ''),
                            "class": ' '.join(img.get('class', [])) if img.get('class') else '',
                            "title": img.get('title', ''),
                            "html_snippet": str(img)[:200] + '...' if len(str(img)) > 200 else str(img),
                            "image_type": self._classify_image_type(img)
                        }
                        images_without_alt_details.append(img_details)

                images_info["without_alt"] = without_alt_count
                images_info["without_alt_percentage"] = (without_alt_count / len(images) * 100) if images else 0
                images_info["without_alt_details"] = images_without_alt_details

                # Links analysis
                links = soup.find_all('a', href=True)
                links_info["total"] = len(links)

                for link in links:
                    href = link.get('href', '')
                    if href.startswith('http'):
                        links_info["external_links"].append(href)
                        links_info["external_count"] += 1
                    elif href.startswith('/') or not href.startswith('#'):
                        links_info["internal_links"].append(href)
                        links_info["internal_count"] += 1

            # Keyword analysis (simple frequency count)
            top_keywords = self._extract_top_keywords(clean_content)

            return {
                "word_count": word_count,
                "reading_time_minutes": reading_time,
                "headings_structure": headings_structure,
                "images": images_info,
                "links": links_info,
                "top_keywords": top_keywords
            }

        except Exception as e:
            logger.error(f"Error in content SEO analysis: {str(e)}")
            return self._get_default_content_analysis()

    def _get_default_content_analysis(self) -> Dict[str, Any]:
        """Return default content analysis when parsing fails."""
        return {
            "word_count": 0,
            "reading_time_minutes": 0,
            "headings_structure": {},
            "images": {"total": 0, "without_alt": 0, "without_alt_percentage": 0, "without_alt_details": []},
            "links": {
                "total": 0, "internal_count": 0, "external_count": 0,
                "internal_links": [], "external_links": []
            },
            "top_keywords": []
        }

    def _extract_top_keywords(self, content: str, max_keywords: int = 10) -> List[Dict[str, Any]]:
        """Extract top keywords from content with improved filtering."""
        try:
            # Enhanced keyword extraction with better filtering
            words = re.findall(r'\b[a-zA-ZáéíóúñüÁÉÍÓÚÑÜ]{3,}\b', content.lower())

            # Comprehensive stop words including technical terms
            stop_words = {
                # Common English stop words
                'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our',
                'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two',
                'who', 'boy', 'did', 'man', 'way', 'she', 'use', 'your', 'said', 'each', 'which', 'their', 'time',
                'will', 'about', 'would', 'there', 'could', 'other', 'after', 'first', 'well', 'water', 'been',
                'call', 'who', 'oil', 'sit', 'now', 'find', 'long', 'down', 'day', 'did', 'get', 'come', 'made',
                'may', 'part', 'over', 'new', 'sound', 'take', 'only', 'little', 'work', 'know', 'place', 'year',
                'live', 'back', 'give', 'most', 'very', 'after', 'thing', 'our', 'just', 'name', 'good',
                'sentence', 'man', 'think', 'say', 'great', 'where', 'help', 'through', 'much', 'before', 'line',
                'right', 'too', 'mean', 'old', 'any', 'same', 'tell', 'boy', 'follow', 'came', 'want', 'show',
                'also', 'around', 'form', 'three', 'small', 'set', 'put', 'end', 'why', 'again', 'turn', 'here',
                'why', 'ask', 'went', 'men', 'read', 'need', 'land', 'different', 'home', 'move', 'try',
                'kind', 'hand', 'picture', 'again', 'change', 'off', 'play', 'spell', 'air', 'away', 'animal',
                'house', 'point', 'page', 'letter', 'mother', 'answer', 'found', 'study', 'still', 'learn',
                'should', 'america', 'world', 'make', 'look', 'into', 'more', 'than', 'what', 'when', 'them',
                'some', 'like', 'have', 'from', 'they', 'been', 'were', 'said', 'each', 'which', 'their',
                'this', 'that', 'with', 'will', 'would', 'there', 'could', 'other', 'then', 'them', 'these',
                'those', 'such', 'only', 'both', 'many', 'much', 'more', 'most', 'some', 'few', 'little',

                # Common Spanish stop words
                'que', 'del', 'las', 'los', 'una', 'con', 'por', 'para', 'como', 'más', 'pero', 'sus', 'les',
                'una', 'sus', 'les', 'muy', 'sin', 'sobre', 'también', 'hasta', 'donde', 'mientras', 'cuando',
                'desde', 'entre', 'durante', 'antes', 'después', 'aunque', 'porque', 'sino', 'hacia', 'bajo',
                'según', 'contra', 'mediante', 'durante', 'excepto', 'salvo', 'incluso', 'además', 'entonces',
                'embargo', 'tanto', 'cuanto', 'poco', 'mucho', 'todo', 'cada', 'otro', 'mismo', 'tal', 'cual',

                # Technical/Web terms that are not useful keywords
                'https', 'http', 'www', 'com', 'org', 'net', 'html', 'css', 'javascript', 'js', 'php', 'asp',
                'xml', 'json', 'api', 'url', 'uri', 'href', 'src', 'alt', 'title', 'meta', 'div', 'span',
                'class', 'style', 'script', 'link', 'head', 'body', 'header', 'footer', 'nav', 'main',
                'section', 'article', 'aside', 'figure', 'img', 'video', 'audio', 'iframe', 'form', 'input',
                'button', 'select', 'option', 'textarea', 'label', 'fieldset', 'legend', 'table', 'thead',
                'tbody', 'tfoot', 'caption', 'colgroup', 'col', 'tr', 'th', 'td', 'ul', 'ol', 'li', 'dl',
                'dt', 'dd', 'blockquote', 'cite', 'code', 'pre', 'kbd', 'samp', 'var', 'sub', 'sup', 'small',
                'strong', 'em', 'mark', 'del', 'ins', 'abbr', 'dfn', 'time', 'progress', 'meter', 'details',
                'summary', 'menu', 'menuitem', 'dialog', 'canvas', 'svg', 'math', 'noscript', 'template',
                'slot', 'content', 'element', 'attribute', 'property', 'value', 'data', 'type', 'name',
                'version', 'charset', 'encoding', 'language', 'locale', 'timezone', 'format', 'protocol',
                'domain', 'subdomain', 'path', 'query', 'fragment', 'parameter', 'argument', 'variable',
                'function', 'method', 'object', 'array', 'string', 'number', 'boolean', 'null', 'undefined',
                'true', 'false', 'yes', 'no', 'on', 'off', 'enable', 'disable', 'active', 'inactive',
                'visible', 'hidden', 'show', 'hide', 'open', 'close', 'start', 'stop', 'begin', 'end',
                'first', 'last', 'next', 'previous', 'prev', 'current', 'default', 'auto', 'manual',
                'public', 'private', 'protected', 'static', 'dynamic', 'global', 'local', 'external',
                'internal', 'inline', 'block', 'none', 'inherit', 'initial', 'unset', 'revert',

                # Generic/meaningless terms
                'click', 'here', 'more', 'info', 'information', 'details', 'description', 'text', 'content',
                'item', 'items', 'list', 'lists', 'menu', 'menus', 'option', 'options', 'choice', 'choices',
                'selection', 'selections', 'example', 'examples', 'sample', 'samples', 'demo', 'demos',
                'test', 'tests', 'trial', 'trials', 'preview', 'previews', 'view', 'views', 'display',
                'displays', 'screen', 'screens', 'window', 'windows', 'tab', 'tabs', 'panel', 'panels',
                'section', 'sections', 'part', 'parts', 'piece', 'pieces', 'bit', 'bits', 'chunk', 'chunks'
            }

            # Technical patterns to exclude (regex patterns)
            technical_patterns = [
                r'^[0-9]+$',  # Pure numbers
                r'^[a-f0-9]{8,}$',  # Hex codes/IDs
                r'^\w{1,3}$',  # Very short abbreviations
                r'^(px|em|rem|vh|vw|pt|pc|in|cm|mm|ex|ch)$',  # CSS units
                r'^(rgb|rgba|hsl|hsla|hex)$',  # Color formats
                r'^(get|post|put|delete|patch|head|options)$',  # HTTP methods
                r'^(json|xml|csv|pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|tar|gz)$',  # File extensions
            ]

            # Filter words
            filtered_words = []
            for word in words:
                # Skip if in stop words
                if word in stop_words:
                    continue

                # Skip if too short or too long
                if len(word) < 4 or len(word) > 25:
                    continue

                # Skip if matches technical patterns
                if any(re.match(pattern, word) for pattern in technical_patterns):
                    continue

                # Skip if contains numbers (likely technical terms)
                if re.search(r'\d', word):
                    continue

                # Skip if all uppercase (likely acronyms)
                if word.isupper() and len(word) > 1:
                    continue

                filtered_words.append(word)

            # Count frequency
            word_freq = {}
            for word in filtered_words:
                word_freq[word] = word_freq.get(word, 0) + 1

            # Filter out words that appear only once (likely not important)
            word_freq = {word: count for word, count in word_freq.items() if count > 1}

            # Sort by frequency and return top keywords
            sorted_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)

            return [
                {"word": word, "count": count}
                for word, count in sorted_keywords[:max_keywords]
            ]

        except Exception as e:
            logger.error(f"Error extracting keywords: {str(e)}")
            return []

    async def _analyze_ranking_keywords(self, url: str, technical_analysis: Dict[str, Any], content_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze real ranking keywords using Serper API to find what keywords the page actually ranks for."""
        try:
            serper_api_key = getattr(settings, 'SERPER_API_KEY', None)
            if not serper_api_key:
                logger.warning("Serper API key not configured - skipping ranking keywords analysis")
                return self._get_default_ranking_keywords()

            # Extract potential keywords from content and meta data
            title = technical_analysis.get("basic_info", {}).get("title", "")
            meta_description = technical_analysis.get("basic_info", {}).get("meta_description", "")
            h1_tags = technical_analysis.get("basic_info", {}).get("h1_tags", [])
            content_keywords = content_analysis.get("top_keywords", [])

            # Generate search queries based on content
            search_queries = self._generate_search_queries(title, meta_description, h1_tags, content_keywords)

            ranking_results = []
            domain = urlparse(url).netloc

            logger.info(f"Analyzing ranking keywords for {url} using {len(search_queries)} search queries")

            # Search for each query and check if the URL appears in results
            for query in search_queries[:10]:  # Limit to 10 queries to avoid API limits
                try:
                    ranking_data = await self._search_keyword_ranking(query, domain, url)
                    if ranking_data:
                        ranking_results.append(ranking_data)
                        logger.info(f"Found ranking for '{query}': position {ranking_data['position']}")

                    # Small delay to respect API limits
                    await asyncio.sleep(0.5)

                except Exception as e:
                    logger.warning(f"Error searching for keyword '{query}': {str(e)}")
                    continue

            # Analyze and categorize results
            analyzed_keywords = self._analyze_keyword_performance(ranking_results)

            return {
                "total_keywords_found": len(ranking_results),
                "ranking_keywords": ranking_results,
                "keyword_analysis": analyzed_keywords,
                "search_queries_tested": len(search_queries),
                "domain": domain
            }

        except Exception as e:
            logger.error(f"Error analyzing ranking keywords: {str(e)}")
            return self._get_default_ranking_keywords()

    def _generate_search_queries(self, title: str, meta_description: str, h1_tags: List[str], content_keywords: List[Dict]) -> List[str]:
        """Generate relevant search queries based on page content."""
        queries = []

        # Extract meaningful phrases from title
        if title:
            # Split title into meaningful parts
            title_words = re.findall(r'\b[a-zA-ZáéíóúñüÁÉÍÓÚÑÜ]{3,}\b', title.lower())
            if len(title_words) >= 2:
                queries.append(' '.join(title_words[:4]))  # First 4 words
                queries.append(' '.join(title_words[-3:]))  # Last 3 words

        # Extract from H1 tags
        for h1 in h1_tags[:2]:  # Only first 2 H1s
            h1_words = re.findall(r'\b[a-zA-ZáéíóúñüÁÉÍÓÚÑÜ]{3,}\b', h1.lower())
            if len(h1_words) >= 2:
                queries.append(' '.join(h1_words[:3]))

        # Use top content keywords to create combinations
        if content_keywords:
            top_words = [kw['word'] for kw in content_keywords[:5]]
            # Create 2-3 word combinations
            for i in range(len(top_words) - 1):
                if i < 3:  # Limit combinations
                    queries.append(f"{top_words[i]} {top_words[i+1]}")

        # Extract from meta description
        if meta_description:
            meta_words = re.findall(r'\b[a-zA-ZáéíóúñüÁÉÍÓÚÑÜ]{4,}\b', meta_description.lower())
            if len(meta_words) >= 2:
                queries.append(' '.join(meta_words[:3]))

        # Remove duplicates and filter
        unique_queries = []
        for query in queries:
            if query not in unique_queries and len(query.split()) >= 2 and len(query) <= 50:
                unique_queries.append(query)

        return unique_queries[:15]  # Limit to 15 queries

    async def _search_keyword_ranking(self, query: str, domain: str, target_url: str) -> Optional[Dict[str, Any]]:
        """Search for a keyword and check if the domain/URL appears in results."""
        try:
            serper_url = "https://google.serper.dev/search"
            headers = {
                'X-API-KEY': settings.SERPER_API_KEY,
                'Content-Type': 'application/json'
            }
            payload = {
                "q": query,
                "num": 20,  # Check top 20 results
                "gl": "us",
                "hl": "en"
            }

            # Use requests for synchronous call (wrapped in async)
            response = requests.post(serper_url, headers=headers, json=payload, timeout=10)
            response.raise_for_status()
            data = response.json()

            # Check if our domain appears in organic results
            organic_results = data.get('organic', [])
            for position, result in enumerate(organic_results, 1):
                result_url = result.get('link', '')
                result_domain = urlparse(result_url).netloc

                # Check if it's the same domain or exact URL
                if domain in result_domain or result_url == target_url:
                    return {
                        "keyword": query,
                        "position": position,
                        "url": result_url,
                        "title": result.get('title', ''),
                        "snippet": result.get('snippet', ''),
                        "exact_match": result_url == target_url,
                        "domain_match": domain in result_domain
                    }

            return None  # Not found in top 20

        except Exception as e:
            logger.error(f"Error searching for keyword '{query}': {str(e)}")
            return None

    def _analyze_keyword_performance(self, ranking_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze keyword performance and categorize results."""
        if not ranking_results:
            return {
                "top_positions": [],
                "opportunities": [],
                "average_position": 0,
                "total_rankings": 0
            }

        # Categorize by position
        top_positions = [r for r in ranking_results if r['position'] <= 3]
        good_positions = [r for r in ranking_results if 4 <= r['position'] <= 10]
        opportunities = [r for r in ranking_results if r['position'] > 10]

        # Calculate average position
        avg_position = sum(r['position'] for r in ranking_results) / len(ranking_results)

        return {
            "top_positions": top_positions,
            "good_positions": good_positions,
            "opportunities": opportunities,
            "average_position": round(avg_position, 1),
            "total_rankings": len(ranking_results),
            "best_position": min(r['position'] for r in ranking_results),
            "worst_position": max(r['position'] for r in ranking_results)
        }

    def _get_default_ranking_keywords(self) -> Dict[str, Any]:
        """Return default ranking keywords data when API is not available."""
        return {
            "total_keywords_found": 0,
            "ranking_keywords": [],
            "keyword_analysis": {
                "top_positions": [],
                "good_positions": [],
                "opportunities": [],
                "average_position": 0,
                "total_rankings": 0
            },
            "search_queries_tested": 0,
            "domain": "",
            "error": "Serper API not configured"
        }

    def _calculate_seo_score(self, technical_analysis: Dict[str, Any], content_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate SEO score based on various factors."""
        checks = {}

        # Basic info checks
        basic_info = technical_analysis.get("basic_info", {})

        # Title checks
        title = basic_info.get("title", "")
        title_length = basic_info.get("title_length", 0)
        checks["has_title"] = bool(title.strip())
        checks["title_length_optimal"] = 30 <= title_length <= 60

        # Meta description checks
        meta_desc = basic_info.get("meta_description")
        meta_desc_length = basic_info.get("meta_description_length", 0)
        checks["has_meta_description"] = bool(meta_desc)
        checks["meta_description_length_optimal"] = 120 <= meta_desc_length <= 160 if meta_desc else False

        # H1 checks
        h1_count = basic_info.get("h1_count", 0)
        checks["has_h1"] = h1_count > 0
        checks["single_h1"] = h1_count == 1

        # Technical checks
        technical_audit = technical_analysis.get("technical_audit", {})
        checks["is_https"] = technical_audit.get("is_https", False)
        checks["has_canonical"] = bool(basic_info.get("canonical_url"))
        checks["has_viewport"] = basic_info.get("has_viewport", False)
        checks["has_language"] = bool(basic_info.get("language"))

        # Content checks
        word_count = content_analysis.get("word_count", 0)
        checks["sufficient_content"] = word_count >= 300

        # Images checks
        images = content_analysis.get("images", {})
        total_images = images.get("total", 0)
        without_alt_percentage = images.get("without_alt_percentage", 0)
        checks["images_have_alt"] = without_alt_percentage < 20 if total_images > 0 else True

        # Links checks
        links = content_analysis.get("links", {})
        checks["has_internal_links"] = links.get("internal_count", 0) > 0

        # Open Graph checks
        og_data = technical_analysis.get("open_graph", {})
        checks["has_og_title"] = bool(og_data.get("title"))
        checks["has_og_description"] = bool(og_data.get("description"))
        checks["has_og_image"] = bool(og_data.get("image"))

        return {"checks": checks}

    async def _generate_ai_recommendations(
        self,
        url: str,
        technical_analysis: Dict[str, Any],
        content_analysis: Dict[str, Any],
        content_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate AI-powered SEO recommendations using Gemini."""
        try:
            # Prepare analysis summary for Gemini
            analysis_summary = {
                "url": url,
                "title": technical_analysis.get("basic_info", {}).get("title", ""),
                "title_length": technical_analysis.get("basic_info", {}).get("title_length", 0),
                "meta_description": technical_analysis.get("basic_info", {}).get("meta_description"),
                "meta_description_length": technical_analysis.get("basic_info", {}).get("meta_description_length", 0),
                "h1_count": technical_analysis.get("basic_info", {}).get("h1_count", 0),
                "word_count": content_analysis.get("word_count", 0),
                "images_total": content_analysis.get("images", {}).get("total", 0),
                "images_without_alt": content_analysis.get("images", {}).get("without_alt", 0),
                "internal_links": content_analysis.get("links", {}).get("internal_count", 0),
                "external_links": content_analysis.get("links", {}).get("external_count", 0),
                "is_https": technical_analysis.get("technical_audit", {}).get("is_https", False),
                "has_canonical": bool(technical_analysis.get("basic_info", {}).get("canonical_url")),
                "has_viewport": technical_analysis.get("basic_info", {}).get("has_viewport", False),
                "top_keywords": content_analysis.get("top_keywords", [])[:5]
            }

            prompt = f"""Eres un consultor SEO experto analizando un sitio web. Proporciona recomendaciones específicas, accionables y fáciles de entender para propietarios de sitios web que pueden no tener conocimientos técnicos profundos.

URL ANALIZADA: {url}

DATOS TÉCNICOS DEL ANÁLISIS:
- Título: "{analysis_summary['title']}" (Longitud: {analysis_summary['title_length']} caracteres)
- Meta descripción: {"Presente" if analysis_summary['meta_description'] else "Ausente"} (Longitud: {analysis_summary['meta_description_length']} caracteres)
- Etiquetas H1: {analysis_summary['h1_count']} encontradas
- Contenido: {analysis_summary['word_count']} palabras
- Imágenes: {analysis_summary['images_total']} total, {analysis_summary['images_without_alt']} sin texto alternativo
- Enlaces: {analysis_summary['internal_links']} internos, {analysis_summary['external_links']} externos
- HTTPS: {"Sí" if analysis_summary['is_https'] else "No"}
- URL canónica: {"Presente" if analysis_summary['has_canonical'] else "Ausente"}
- Viewport: {"Presente" if analysis_summary['has_viewport'] else "Ausente"}

PALABRAS CLAVE PRINCIPALES DETECTADAS:
{', '.join([kw['word'] for kw in analysis_summary['top_keywords']])}

INSTRUCCIONES PARA GENERAR RECOMENDACIONES:

1. Identifica los 5-8 problemas más críticos que afectan el SEO
2. Para cada problema, proporciona una recomendación específica y accionable
3. Usa un lenguaje claro y evita jerga técnica excesiva
4. Incluye el impacto esperado de cada mejora
5. Prioriza según el impacto en el ranking de búsqueda

CRITERIOS ESTRICTOS PARA EVITAR CONTRADICCIONES:

TÍTULOS:
- Solo recomienda si: longitud < 30 caracteres O longitud > 65 caracteres O ausente
- NO recomiendes si: 30-65 caracteres (esto se considera aceptable)

META DESCRIPCIÓN:
- Solo recomienda si: ausente O longitud < 120 caracteres O longitud > 170 caracteres
- NO recomiendes si: 120-170 caracteres (esto se considera aceptable)

ETIQUETAS H1:
- Solo recomienda si: 0 H1s O más de 1 H1
- NO recomiendes si: exactamente 1 H1 (esto es perfecto)

IMÁGENES ALT TEXT:
- Solo recomienda si: menos del 70% de imágenes tienen alt text
- NO recomiendes si: 70% o más tienen alt text (esto se considera aceptable o bueno)

CATEGORÍAS DISPONIBLES:
- "Meta Tags y Títulos" - Problemas con títulos, meta descripciones, etc.
- "Estructura de Contenido" - Problemas con H1, H2, organización del contenido
- "Optimización Técnica" - HTTPS, velocidad, URLs, canonical tags
- "Imágenes y Multimedia" - Alt text, optimización de imágenes
- "Enlaces y Navegación" - Enlaces internos, externos, estructura de navegación
- "Experiencia de Usuario" - Viewport, responsive design, usabilidad
- "Contenido y Keywords" - Longitud del contenido, densidad de palabras clave

NIVELES DE IMPORTANCIA:
- "crítica" - Problemas que impactan severamente el SEO (ej: sin título, sin HTTPS)
- "alta" - Problemas importantes que afectan el ranking (ej: meta descripción faltante)
- "media" - Mejoras que optimizan el SEO (ej: optimización de imágenes)
- "baja" - Mejoras menores pero beneficiosas

FORMATO DE RESPUESTA (JSON):
[
  {{
    "category": "Categoría específica de la lista anterior",
    "issue": "Descripción clara del problema encontrado",
    "importance": "crítica|alta|media|baja",
    "recommendation": "Instrucción específica y accionable de cómo solucionarlo, incluyendo el beneficio esperado"
  }}
]

EJEMPLOS DE BUENAS RECOMENDACIONES:
- "Agrega una meta descripción de 120-160 caracteres que resuma el contenido de la página y incluya tu palabra clave principal. Esto mejorará el CTR en los resultados de búsqueda."
- "Reduce el título a 50-60 caracteres para evitar que se corte en Google. Incluye tu palabra clave principal al inicio para mayor relevancia."
- "Agrega texto alternativo descriptivo a las {analysis_summary['images_without_alt']} imágenes sin alt text. Esto mejora la accesibilidad y ayuda a Google a entender el contenido visual."

Genera exactamente 5-8 recomendaciones priorizadas por impacto en SEO:"""

            # Generate recommendations using Gemini
            if self.gemini_model:
                try:
                    response = self.gemini_model.generate_content(
                        prompt,
                        generation_config=genai.types.GenerationConfig(
                            temperature=0.3,
                            top_p=0.8,
                            top_k=40,
                            max_output_tokens=2048,
                        )
                    )

                    content = response.text

                    # Try to extract JSON from the response
                    try:
                        # Find JSON array in the response
                        start_idx = content.find('[')
                        end_idx = content.rfind(']') + 1

                        if start_idx != -1 and end_idx != 0:
                            json_str = content[start_idx:end_idx]
                            recommendations = json.loads(json_str)

                            # Validate structure and filter contradictions
                            if isinstance(recommendations, list):
                                # Filter out contradictory recommendations
                                filtered_recommendations = self._filter_contradictory_recommendations(
                                    recommendations, analysis_summary
                                )
                                return filtered_recommendations

                    except (json.JSONDecodeError, ValueError) as e:
                        logger.warning(f"Could not parse JSON recommendations: {str(e)}")

                except Exception as e:
                    logger.error(f"Error calling Gemini API: {str(e)}")

            # Fallback to rule-based recommendations
            return self._generate_fallback_recommendations(analysis_summary)

        except Exception as e:
            logger.error(f"Error generating AI recommendations: {str(e)}")
            return self._generate_fallback_recommendations(dict())

    def _generate_fallback_recommendations(self, analysis_summary: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate fallback recommendations when AI fails. Uses strict evaluation to avoid contradictions."""
        recommendations = []

        # Title recommendations - Using strict evaluation
        title_eval = self._evaluate_seo_element(
            'title',
            title=analysis_summary.get("title", ""),
            title_length=analysis_summary.get("title_length", 0)
        )

        if title_eval['status'] in ['missing', 'poor']:
            recommendations.append({
                "category": "Meta Tags y Títulos",
                "issue": title_eval['details']['issue'],
                "importance": title_eval['details']['severity'],
                "recommendation": title_eval['details']['recommendation']
            })

        # Meta description recommendations - Using strict evaluation
        meta_eval = self._evaluate_seo_element(
            'meta_description',
            meta_description=analysis_summary.get("meta_description", ""),
            meta_description_length=analysis_summary.get("meta_description_length", 0)
        )

        if meta_eval['status'] in ['missing', 'poor']:
            recommendations.append({
                "category": "Meta Tags y Títulos",
                "issue": meta_eval['details']['issue'],
                "importance": meta_eval['details']['severity'],
                "recommendation": meta_eval['details']['recommendation']
            })

        # H1 recommendations - Using strict evaluation
        h1_eval = self._evaluate_seo_element(
            'h1_tags',
            h1_count=analysis_summary.get("h1_count", 0)
        )

        if h1_eval['status'] in ['missing', 'poor']:
            recommendations.append({
                "category": "Estructura de Contenido",
                "issue": h1_eval['details']['issue'],
                "importance": h1_eval['details']['severity'],
                "recommendation": h1_eval['details']['recommendation']
            })

        # Content recommendations
        word_count = analysis_summary.get("word_count", 0)
        if word_count < 300:
            recommendations.append({
                "category": "Contenido y Keywords",
                "issue": f"Contenido muy escaso ({word_count} palabras)",
                "importance": "alta",
                "recommendation": f"Expande el contenido a mínimo 300 palabras (actualmente {word_count}). Agrega información valiosa, ejemplos, y detalles relevantes. El contenido más extenso y útil tiende a posicionarse mejor en Google."
            })
        elif word_count > 2000:
            recommendations.append({
                "category": "Contenido y Keywords",
                "issue": f"Contenido muy extenso ({word_count} palabras)",
                "importance": "baja",
                "recommendation": f"Considera dividir este contenido extenso en múltiples páginas o secciones para mejorar la experiencia de usuario y facilitar la navegación."
            })

        # Images recommendations - Using strict evaluation
        images_eval = self._evaluate_seo_element(
            'images_alt',
            total_images=analysis_summary.get("images_total", 0),
            without_alt=analysis_summary.get("images_without_alt", 0)
        )

        if images_eval['status'] == 'poor':
            recommendations.append({
                "category": "Imágenes y Multimedia",
                "issue": images_eval['details']['issue'],
                "importance": images_eval['details']['severity'],
                "recommendation": images_eval['details']['recommendation']
            })

        # HTTPS recommendation
        if not analysis_summary.get("is_https", False):
            recommendations.append({
                "category": "Optimización Técnica",
                "issue": "Sitio web sin certificado SSL (HTTP)",
                "importance": "crítica",
                "recommendation": "Implementa HTTPS inmediatamente instalando un certificado SSL. Google penaliza sitios HTTP y los navegadores muestran advertencias de seguridad, lo que reduce drásticamente la confianza del usuario y el ranking."
            })

        # Canonical URL recommendation
        if not analysis_summary.get("has_canonical", False):
            recommendations.append({
                "category": "Optimización Técnica",
                "issue": "URL canónica faltante",
                "importance": "media",
                "recommendation": "Agrega una etiqueta canonical para evitar problemas de contenido duplicado. Esto ayuda a Google a identificar la versión preferida de la página."
            })

        # Viewport recommendation
        if not analysis_summary.get("has_viewport", False):
            recommendations.append({
                "category": "Experiencia de Usuario",
                "issue": "Meta viewport ausente - no optimizado para móviles",
                "importance": "alta",
                "recommendation": "Agrega la etiqueta meta viewport para asegurar que tu sitio se vea correctamente en dispositivos móviles. Google prioriza sitios mobile-friendly en sus rankings."
            })

        return recommendations

    async def analyze_website_exhaustive(self, url: str, progress_callback=None) -> Dict[str, Any]:
        """
        Perform exhaustive website analysis with progress tracking.

        Args:
            url: Base URL to analyze
            progress_callback: Optional callback function to report progress

        Returns:
            Complete website analysis results
        """
        try:
            start_time = time.time()
            logger.info(f"Starting exhaustive website analysis for: {url}")

            # Phase 1: Discovery
            if progress_callback:
                progress_callback({
                    "phase": "discovery",
                    "status": "Descubriendo páginas del sitio web...",
                    "current_page": 0,
                    "total_pages": 0,
                    "processed_urls": [],
                    "failed_urls": []
                })

            # Discover all pages
            pages_to_analyze = await self._discover_website_pages(url, max_pages=50)
            total_pages = len(pages_to_analyze)

            logger.info(f"Discovery complete: Found {total_pages} pages to analyze")

            if progress_callback:
                progress_callback({
                    "phase": "analysis",
                    "status": f"Encontradas {total_pages} páginas. Iniciando análisis...",
                    "current_page": 0,
                    "total_pages": total_pages,
                    "processed_urls": [],
                    "failed_urls": []
                })

            # Phase 2: Analyze each page
            page_analyses = []
            processed_urls = []
            failed_urls = []

            for i, page_url in enumerate(pages_to_analyze, 1):
                try:
                    if progress_callback:
                        progress_callback({
                            "phase": "analysis",
                            "status": f"Analizando página {i}/{total_pages}: {page_url}",
                            "current_page": i,
                            "total_pages": total_pages,
                            "processed_urls": processed_urls[-5:],  # Keep last 5
                            "failed_urls": failed_urls,
                            "current_url": page_url
                        })

                    # Analyze individual page
                    page_analysis = await self.analyze_website(page_url)

                    if page_analysis["status"] == "success":
                        page_analyses.append(page_analysis)
                        processed_urls.append(page_url)
                        logger.info(f"✅ Page {i}/{total_pages} analyzed successfully: {page_url}")
                    else:
                        failed_urls.append(page_url)
                        logger.warning(f"❌ Page {i}/{total_pages} analysis failed: {page_url}")

                except Exception as e:
                    failed_urls.append(page_url)
                    logger.warning(f"❌ Failed to analyze page {i}/{total_pages} {page_url}: {str(e)}")
                    continue

            # Phase 3: Generate recommendations
            if progress_callback:
                progress_callback({
                    "phase": "recommendations",
                    "status": "Generando recomendaciones finales...",
                    "current_page": total_pages,
                    "total_pages": total_pages,
                    "processed_urls": processed_urls[-5:],
                    "failed_urls": failed_urls,
                    "current_url": None
                })

            # Aggregate results and generate website-level recommendations
            if page_analyses:
                aggregated_results = self._aggregate_website_analysis(page_analyses, url)
                website_recommendations = await self._generate_website_recommendations(
                    aggregated_results, page_analyses, url
                )

                result = {
                    "status": "success",
                    "url": url,
                    "basic_info": aggregated_results.get("basic_info", {}),
                    "open_graph": aggregated_results.get("open_graph", {}),
                    "twitter_card": aggregated_results.get("twitter_card", {}),
                    "technical_audit": aggregated_results.get("technical_audit", {}),
                    "content_analysis": aggregated_results.get("content_analysis", {}),
                    "seo_checks": aggregated_results.get("seo_checks", {}),
                    "preview_data": aggregated_results.get("preview_data", {}),
                    "recommendations": website_recommendations,
                    "ai_enhanced": True,
                    "processing_time": time.time() - start_time,
                    "pages_analyzed": len(page_analyses),
                    "total_pages_found": total_pages
                }
            else:
                result = {
                    "status": "error",
                    "error_message": "No se pudieron analizar páginas del sitio web",
                    "url": url,
                    "ai_enhanced": False,
                    "processing_time": time.time() - start_time,
                    "pages_analyzed": 0,
                    "total_pages_found": total_pages
                }

            # Final progress update
            if progress_callback:
                progress_callback({
                    "phase": "complete",
                    "status": "Análisis exhaustivo completado",
                    "current_page": total_pages,
                    "total_pages": total_pages,
                    "processed_urls": processed_urls[-5:],
                    "failed_urls": failed_urls,
                    "pages_analyzed": len(page_analyses),
                    "total_pages_found": total_pages
                })

            logger.info(f"Exhaustive website analysis completed for: {url}")
            return result

        except Exception as e:
            logger.error(f"Exhaustive website analysis failed for {url}: {str(e)}")
            return {
                "status": "error",
                "error_message": f"Exhaustive website analysis failed: {str(e)}",
                "url": url,
                "ai_enhanced": False
            }

    def _generate_preview_data(self, technical_analysis: Dict[str, Any], url: str) -> Dict[str, Any]:
        """Generate preview data for social media platforms."""
        basic_info = technical_analysis.get("basic_info", {})
        og_data = technical_analysis.get("open_graph", {})
        twitter_data = technical_analysis.get("twitter_card", {})

        title = basic_info.get("title", "")
        description = basic_info.get("meta_description", "")

        return {
            "title": title,
            "description": description or "Sin descripción disponible",
            "url": url,
            "og_title": og_data.get("title", title),
            "og_description": og_data.get("description", description or ""),
            "og_image": og_data.get("image", ""),
            "twitter_title": twitter_data.get("title", title),
            "twitter_description": twitter_data.get("description", description or ""),
            "twitter_image": twitter_data.get("image", og_data.get("image", ""))
        }

    async def analyze_complete_website(self, url: str) -> Dict[str, Any]:
        """
        Perform comprehensive SEO analysis of an entire website.

        Args:
            url: Website URL to analyze (will be used as base URL)

        Returns:
            Complete website SEO analysis results
        """
        try:
            start_time = time.time()
            logger.info(f"Starting complete website SEO analysis for: {url}")

            # Step 1: Discover pages to analyze
            pages_to_analyze = await self._discover_website_pages(url)
            logger.info(f"Discovered {len(pages_to_analyze)} pages to analyze")

            # Step 2: Analyze each page with detailed progress logging
            page_analyses = []
            total_pages = len(pages_to_analyze)

            logger.info(f"Starting analysis of {total_pages} discovered pages...")

            for i, page_url in enumerate(pages_to_analyze, 1):
                try:
                    logger.info(f"Analyzing page {i}/{total_pages}: {page_url}")
                    page_analysis = await self.analyze_website(page_url)
                    if page_analysis["status"] == "success":
                        page_analyses.append(page_analysis)
                        logger.info(f"✅ Page {i}/{total_pages} analyzed successfully: {page_url}")
                    else:
                        logger.warning(f"❌ Page {i}/{total_pages} analysis failed: {page_url}")
                except Exception as e:
                    logger.warning(f"❌ Failed to analyze page {i}/{total_pages} {page_url}: {str(e)}")
                    continue

            if not page_analyses:
                raise Exception("No pages could be analyzed successfully")

            # Step 3: Aggregate results from all pages
            aggregated_results = self._aggregate_website_analysis(page_analyses, url)

            # Step 4: Generate website-wide recommendations
            website_recommendations = await self._generate_website_recommendations(
                aggregated_results, page_analyses, url
            )

            processing_time = time.time() - start_time

            logger.info(f"Complete website SEO analysis completed for {url} in {processing_time:.2f}s")

            return {
                "status": "success",
                "url": url,
                "basic_info": aggregated_results.get("basic_info", {}),
                "open_graph": aggregated_results.get("open_graph", {}),
                "twitter_card": aggregated_results.get("twitter_card", {}),
                "technical_audit": aggregated_results.get("technical_audit", {}),
                "content_analysis": aggregated_results.get("content_analysis", {}),
                "seo_checks": aggregated_results.get("seo_checks", {}),
                "preview_data": aggregated_results.get("preview_data", {}),
                "recommendations": website_recommendations,
                "ai_enhanced": True,
                "processing_time": processing_time,
                "pages_analyzed": len(page_analyses),
                "total_pages_found": len(pages_to_analyze)
            }

        except Exception as e:
            logger.error(f"Complete website SEO analysis failed for {url}: {str(e)}")
            return {
                "status": "error",
                "error_message": f"Website SEO analysis failed: {str(e)}",
                "url": url,
                "ai_enhanced": False
            }

    async def _discover_website_pages(self, base_url: str, max_pages: int = 500) -> List[str]:
        """Discover ALL pages of a website to analyze - EXHAUSTIVE crawling."""
        try:
            from urllib.parse import urljoin, urlparse
            import asyncio

            logger.info(f"Starting EXHAUSTIVE website discovery for: {base_url}")

            pages = set([base_url])  # Use set to avoid duplicates
            discovered_pages = set()

            # Get the domain for filtering
            parsed_base = urlparse(base_url)
            base_domain = f"{parsed_base.scheme}://{parsed_base.netloc}"

            # Step 1: Get ALL URLs from sitemap.xml (no limits)
            logger.info("Step 1: Extracting ALL URLs from sitemap.xml...")
            sitemap_urls = await self._get_all_sitemap_urls(base_url)
            if sitemap_urls:
                pages.update(sitemap_urls)
                logger.info(f"Found {len(sitemap_urls)} URLs from sitemap.xml")

            # Step 2: Crawl robots.txt for additional sitemaps
            logger.info("Step 2: Checking robots.txt for additional sitemaps...")
            robots_sitemaps = await self._get_robots_sitemaps(base_url)
            for sitemap_url in robots_sitemaps:
                additional_urls = await self._get_all_sitemap_urls(sitemap_url)
                pages.update(additional_urls)
                logger.info(f"Found {len(additional_urls)} additional URLs from {sitemap_url}")

            # Step 3: Comprehensive common pages check
            logger.info("Step 3: Checking comprehensive list of common pages...")
            comprehensive_pages = [
                # Main pages
                "/", "/home", "/inicio",
                "/about", "/about-us", "/nosotros", "/acerca-de", "/quienes-somos", "/empresa",
                "/contact", "/contacto", "/contact-us", "/contactanos", "/contactenos",
                "/services", "/servicios", "/productos", "/products", "/catalogo", "/tienda",

                # Content pages
                "/blog", "/news", "/noticias", "/articulos", "/posts", "/novedades",
                "/faq", "/preguntas-frecuentes", "/ayuda", "/help", "/support", "/soporte",

                # Legal pages
                "/privacy", "/privacidad", "/privacy-policy", "/politica-privacidad",
                "/terms", "/terminos", "/terms-of-service", "/condiciones", "/legal",
                "/cookies", "/politica-cookies", "/aviso-legal",

                # Business pages
                "/portfolio", "/portafolio", "/trabajos", "/proyectos", "/galeria", "/gallery",
                "/testimonials", "/testimonios", "/clientes", "/casos-exito",
                "/team", "/equipo", "/staff", "/personal",

                # E-commerce pages
                "/shop", "/tienda", "/store", "/catalogo", "/productos", "/products",
                "/cart", "/carrito", "/checkout", "/pago", "/comprar",
                "/account", "/cuenta", "/perfil", "/login", "/registro",

                # Location pages
                "/locations", "/ubicaciones", "/sucursales", "/oficinas", "/direcciones",
                "/mapa", "/map", "/donde-estamos",

                # Resources
                "/downloads", "/descargas", "/recursos", "/documentos",
                "/press", "/prensa", "/media", "/medios",
                "/careers", "/trabajos", "/empleo", "/vacantes", "/bolsa-trabajo"
            ]

            # Check all comprehensive pages
            for page_path in comprehensive_pages:
                page_url = urljoin(base_domain, page_path)
                if await self._check_url_exists(page_url):
                    pages.add(page_url)

            # Step 4: Crawl homepage for internal links
            logger.info("Step 4: Crawling homepage for internal links...")
            homepage_links = await self._extract_internal_links(base_url, base_domain)
            pages.update(homepage_links)
            logger.info(f"Found {len(homepage_links)} internal links from homepage")

            # Step 5: Deep crawl - analyze found pages for more links (limited to prevent infinite loops)
            logger.info("Step 5: Deep crawling discovered pages for more links...")
            crawled_count = 0
            max_crawl_depth = min(20, len(pages))  # Limit deep crawling to prevent infinite loops

            for page_url in list(pages)[:max_crawl_depth]:
                if crawled_count >= max_crawl_depth:
                    break

                try:
                    page_links = await self._extract_internal_links(page_url, base_domain)
                    new_links = page_links - pages  # Only new links
                    pages.update(new_links)
                    crawled_count += 1

                    if new_links:
                        logger.info(f"Found {len(new_links)} new links from {page_url}")

                except Exception as e:
                    logger.warning(f"Failed to crawl {page_url}: {str(e)}")
                    continue

            # Convert back to list and remove duplicates
            unique_pages = list(pages)

            # Filter out unwanted URLs (admin, api, etc.)
            filtered_pages = []
            exclude_patterns = [
                '/admin', '/wp-admin', '/api/', '/ajax/', '/json/',
                '.pdf', '.doc', '.zip', '.jpg', '.png', '.gif',
                '/feed', '/rss', '/xml', '?', '#'
            ]

            for page in unique_pages:
                if not any(pattern in page.lower() for pattern in exclude_patterns):
                    filtered_pages.append(page)

            # Limit to max_pages but allow more for exhaustive analysis
            final_pages = filtered_pages[:max_pages]

            logger.info(f"EXHAUSTIVE DISCOVERY COMPLETE: Found {len(final_pages)} pages to analyze")
            logger.info(f"Sample pages: {final_pages[:10]}")

            return final_pages

        except Exception as e:
            logger.error(f"Error in exhaustive website discovery: {str(e)}")
            return [base_url]  # Fallback to just the base URL

    async def _get_all_sitemap_urls(self, base_url: str) -> List[str]:
        """Get ALL URLs from sitemap.xml - NO LIMITS for exhaustive analysis."""
        try:
            from urllib.parse import urljoin
            import xml.etree.ElementTree as ET

            sitemap_url = urljoin(base_url, "/sitemap.xml") if not base_url.endswith('.xml') else base_url

            async with aiohttp.ClientSession() as session:
                async with session.get(sitemap_url, timeout=30) as response:
                    if response.status != 200:
                        return []

                    content = await response.text()

                    # Parse XML
                    root = ET.fromstring(content)

                    # Handle different sitemap formats
                    urls = []

                    # Standard sitemap
                    for url_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                        loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                        if loc_elem is not None and loc_elem.text:
                            urls.append(loc_elem.text)

                    # Sitemap index - recursively get ALL URLs from sub-sitemaps
                    for sitemap_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}sitemap'):
                        loc_elem = sitemap_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                        if loc_elem is not None and loc_elem.text:
                            # Recursively get ALL URLs from sub-sitemaps
                            sub_urls = await self._get_all_sitemap_urls(loc_elem.text)
                            urls.extend(sub_urls)  # NO LIMITS - get everything

                    logger.info(f"Extracted {len(urls)} URLs from sitemap: {sitemap_url}")
                    return urls  # Return ALL URLs found

        except Exception as e:
            logger.warning(f"Could not parse sitemap for {base_url}: {str(e)}")
            return []

    async def _get_robots_sitemaps(self, base_url: str) -> List[str]:
        """Extract sitemap URLs from robots.txt."""
        try:
            from urllib.parse import urljoin

            robots_url = urljoin(base_url, "/robots.txt")

            async with aiohttp.ClientSession() as session:
                async with session.get(robots_url, timeout=10) as response:
                    if response.status != 200:
                        return []

                    content = await response.text()

                    # Extract sitemap URLs from robots.txt
                    sitemap_urls = []
                    for line in content.split('\n'):
                        line = line.strip()
                        if line.lower().startswith('sitemap:'):
                            sitemap_url = line.split(':', 1)[1].strip()
                            sitemap_urls.append(sitemap_url)

                    logger.info(f"Found {len(sitemap_urls)} sitemaps in robots.txt")
                    return sitemap_urls

        except Exception as e:
            logger.warning(f"Could not parse robots.txt for {base_url}: {str(e)}")
            return []

    async def _extract_internal_links(self, page_url: str, base_domain: str) -> set:
        """Extract all internal links from a page."""
        try:
            from urllib.parse import urljoin, urlparse

            async with aiohttp.ClientSession() as session:
                async with session.get(page_url, timeout=15) as response:
                    if response.status != 200:
                        return set()

                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    internal_links = set()

                    # Find all links
                    for link in soup.find_all('a', href=True):
                        href = link.get('href', '').strip()

                        if not href or href.startswith('#') or href.startswith('mailto:') or href.startswith('tel:'):
                            continue

                        # Convert relative URLs to absolute
                        if href.startswith('/'):
                            full_url = urljoin(base_domain, href)
                        elif href.startswith('http'):
                            full_url = href
                        else:
                            full_url = urljoin(page_url, href)

                        # Check if it's internal (same domain)
                        parsed_url = urlparse(full_url)
                        if parsed_url.netloc == urlparse(base_domain).netloc:
                            # Clean URL (remove fragments and query params for crawling)
                            clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
                            if clean_url.endswith('/'):
                                clean_url = clean_url[:-1]
                            if clean_url and clean_url != base_domain.rstrip('/'):
                                internal_links.add(clean_url)

                    return internal_links

        except Exception as e:
            logger.warning(f"Could not extract links from {page_url}: {str(e)}")
            return set()

    async def _get_sitemap_urls(self, base_url: str) -> List[str]:
        """Legacy method - now calls the exhaustive version."""
        return await self._get_all_sitemap_urls(base_url)

    async def _check_url_exists(self, url: str) -> bool:
        """Check if a URL exists and is accessible."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.head(url, timeout=5) as response:
                    return response.status < 400
        except:
            return False

    def _aggregate_website_analysis(self, page_analyses: List[Dict[str, Any]], base_url: str) -> Dict[str, Any]:
        """Aggregate analysis results from multiple pages."""
        try:
            if not page_analyses:
                return {}

            # Use the first page (usually homepage) as base
            base_analysis = page_analyses[0]

            # Aggregate content analysis
            total_word_count = sum(analysis.get("content_analysis", {}).get("word_count", 0) for analysis in page_analyses)
            total_images = sum(analysis.get("content_analysis", {}).get("images", {}).get("total", 0) for analysis in page_analyses)
            total_images_without_alt = sum(analysis.get("content_analysis", {}).get("images", {}).get("without_alt", 0) for analysis in page_analyses)

            # Aggregate links
            all_internal_links = set()
            all_external_links = set()
            for analysis in page_analyses:
                links = analysis.get("content_analysis", {}).get("links", {})
                all_internal_links.update(links.get("internal_links", []))
                all_external_links.update(links.get("external_links", []))

            # Aggregate keywords
            all_keywords = {}
            for analysis in page_analyses:
                keywords = analysis.get("content_analysis", {}).get("top_keywords", [])
                for kw in keywords:
                    word = kw.get("word", "")
                    count = kw.get("count", 0)
                    all_keywords[word] = all_keywords.get(word, 0) + count

            top_keywords = [{"word": word, "count": count} for word, count in
                           sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)[:10]]

            # Calculate aggregated SEO checks
            seo_checks = {}
            for check_name in base_analysis.get("seo_checks", {}):
                # A check passes if it passes on the majority of pages
                passing_pages = sum(1 for analysis in page_analyses
                                  if analysis.get("seo_checks", {}).get(check_name, False))
                seo_checks[check_name] = passing_pages > len(page_analyses) / 2

            return {
                "basic_info": base_analysis.get("basic_info", {}),
                "open_graph": base_analysis.get("open_graph", {}),
                "twitter_card": base_analysis.get("twitter_card", {}),
                "technical_audit": base_analysis.get("technical_audit", {}),
                "content_analysis": {
                    "word_count": total_word_count,
                    "reading_time_minutes": max(1, total_word_count // 200),
                    "headings_structure": base_analysis.get("content_analysis", {}).get("headings_structure", {}),
                    "images": {
                        "total": total_images,
                        "without_alt": total_images_without_alt,
                        "without_alt_percentage": (total_images_without_alt / total_images * 100) if total_images > 0 else 0
                    },
                    "links": {
                        "total": len(all_internal_links) + len(all_external_links),
                        "internal_count": len(all_internal_links),
                        "external_count": len(all_external_links),
                        "internal_links": list(all_internal_links)[:50],  # Limit for response size
                        "external_links": list(all_external_links)[:50]
                    },
                    "top_keywords": top_keywords
                },
                "seo_checks": seo_checks,
                "preview_data": base_analysis.get("preview_data", {})
            }

        except Exception as e:
            logger.error(f"Error aggregating website analysis: {str(e)}")
            return page_analyses[0] if page_analyses else {}

    async def _generate_website_recommendations(
        self,
        aggregated_results: Dict[str, Any],
        page_analyses: List[Dict[str, Any]],
        base_url: str
    ) -> List[Dict[str, Any]]:
        """Generate AI-powered recommendations for the entire website."""
        try:
            if not self.gemini_model:
                return self._generate_website_fallback_recommendations(aggregated_results, page_analyses)

            # Prepare website analysis summary for Gemini
            website_summary = {
                "base_url": base_url,
                "pages_analyzed": len(page_analyses),
                "total_word_count": aggregated_results.get("content_analysis", {}).get("word_count", 0),
                "total_images": aggregated_results.get("content_analysis", {}).get("images", {}).get("total", 0),
                "images_without_alt": aggregated_results.get("content_analysis", {}).get("images", {}).get("without_alt", 0),
                "total_internal_links": aggregated_results.get("content_analysis", {}).get("links", {}).get("internal_count", 0),
                "total_external_links": aggregated_results.get("content_analysis", {}).get("links", {}).get("external_count", 0),
                "top_keywords": aggregated_results.get("content_analysis", {}).get("top_keywords", [])[:5],
                "seo_checks_summary": aggregated_results.get("seo_checks", {})
            }

            # Analyze consistency across pages
            consistency_issues = self._analyze_page_consistency(page_analyses)

            prompt = f"""Eres un consultor SEO experto realizando una auditoría completa de sitio web. Analiza los datos agregados de todas las páginas y proporciona recomendaciones estratégicas a nivel de sitio que mejoren el posicionamiento general.

RESUMEN DEL SITIO WEB ANALIZADO:
- URL base: {base_url}
- Páginas analizadas: {website_summary['pages_analyzed']}
- Contenido total: {website_summary['total_word_count']} palabras
- Promedio de palabras por página: {website_summary['total_word_count'] // website_summary['pages_analyzed'] if website_summary['pages_analyzed'] > 0 else 0}
- Imágenes totales: {website_summary['total_images']} ({website_summary['images_without_alt']} sin texto alternativo)
- Enlaces internos totales: {website_summary['total_internal_links']}
- Enlaces externos totales: {website_summary['total_external_links']}

PALABRAS CLAVE PRINCIPALES DEL SITIO:
{', '.join([kw['word'] for kw in website_summary['top_keywords']])}

PROBLEMAS DE CONSISTENCIA DETECTADOS:
{consistency_issues}

ESTADO TÉCNICO GENERAL:
{json.dumps(website_summary['seo_checks_summary'], indent=2)}

INSTRUCCIONES PARA RECOMENDACIONES DE SITIO COMPLETO:

1. Enfócate en problemas que afectan múltiples páginas o la arquitectura general
2. Prioriza mejoras que tengan impacto en todo el sitio web
3. Proporciona recomendaciones estratégicas, no correcciones de páginas individuales
4. Incluye el beneficio esperado para el SEO general del sitio
5. Considera la experiencia del usuario en toda la navegación

CATEGORÍAS PARA SITIO COMPLETO:
- "Arquitectura y Navegación" - Estructura del sitio, menús, enlaces internos
- "Consistencia de Contenido" - Uniformidad en títulos, meta descripciones, estructura
- "Optimización Técnica Global" - Configuraciones que afectan todo el sitio
- "Estrategia de Contenido" - Planificación y organización del contenido general
- "Enlaces Internos y Autoridad" - Distribución de PageRank, estructura de enlaces
- "Experiencia de Usuario Global" - Usabilidad, navegación, responsive design
- "SEO Internacional" - Idiomas, regiones, configuraciones globales

NIVELES DE IMPORTANCIA:
- "crítica" - Problemas estructurales que afectan severamente todo el sitio
- "alta" - Problemas importantes que impactan el ranking general
- "media" - Mejoras que optimizan el rendimiento SEO del sitio
- "baja" - Optimizaciones menores pero beneficiosas

FORMATO DE RESPUESTA (JSON):
[
  {{
    "category": "Categoría específica de la lista anterior",
    "issue": "Problema identificado a nivel de sitio web completo",
    "importance": "crítica|alta|media|baja",
    "recommendation": "Estrategia específica para mejorar todo el sitio, incluyendo pasos concretos y beneficios esperados"
  }}
]

EJEMPLOS DE BUENAS RECOMENDACIONES DE SITIO:
- "Implementa una estrategia de enlaces internos más robusta conectando páginas relacionadas. Esto distribuirá mejor la autoridad de página y mejorará la navegación para usuarios y bots."
- "Estandariza la estructura de títulos en todas las páginas usando un formato consistente: [Palabra Clave] | [Nombre de la Empresa]. Esto mejorará el reconocimiento de marca y la coherencia SEO."
- "Crea un mapa del sitio XML actualizado que incluya todas las {website_summary['pages_analyzed']} páginas analizadas y configura la actualización automática para nuevas páginas."

Genera exactamente 6-10 recomendaciones estratégicas para todo el sitio web:"""

            try:
                response = self.gemini_model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.3,
                        top_p=0.8,
                        top_k=40,
                        max_output_tokens=2048,
                    )
                )

                content = response.text

                # Try to extract JSON from the response
                start_idx = content.find('[')
                end_idx = content.rfind(']') + 1

                if start_idx != -1 and end_idx != 0:
                    json_str = content[start_idx:end_idx]
                    recommendations = json.loads(json_str)

                    if isinstance(recommendations, list):
                        return recommendations

            except Exception as e:
                logger.error(f"Error calling Gemini API for website recommendations: {str(e)}")

            # Fallback to rule-based recommendations
            return self._generate_website_fallback_recommendations(aggregated_results, page_analyses)

        except Exception as e:
            logger.error(f"Error generating website recommendations: {str(e)}")
            return self._generate_website_fallback_recommendations(aggregated_results, page_analyses)

    def _analyze_page_consistency(self, page_analyses: List[Dict[str, Any]]) -> str:
        """Analyze consistency issues across pages."""
        issues = []

        # Check title consistency
        titles = [analysis.get("basic_info", {}).get("title", "") for analysis in page_analyses]
        if len(set(titles)) < len(titles) * 0.8:  # If less than 80% unique titles
            issues.append("- Títulos duplicados o muy similares entre páginas")

        # Check meta description consistency
        meta_descriptions = [analysis.get("basic_info", {}).get("meta_description", "") for analysis in page_analyses if analysis.get("basic_info", {}).get("meta_description")]
        if len(meta_descriptions) < len(page_analyses) * 0.7:  # If less than 70% have meta descriptions
            issues.append("- Meta descripciones faltantes en múltiples páginas")

        # Check H1 consistency
        h1_counts = [analysis.get("basic_info", {}).get("h1_count", 0) for analysis in page_analyses]
        if any(count != 1 for count in h1_counts):
            issues.append("- Problemas con etiquetas H1 (múltiples o faltantes) en varias páginas")

        return "\n".join(issues) if issues else "- No se detectaron problemas de consistencia importantes"

    def _generate_website_fallback_recommendations(
        self,
        aggregated_results: Dict[str, Any],
        page_analyses: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate fallback recommendations for website analysis."""
        recommendations = []

        # Site architecture recommendations
        total_pages = len(page_analyses)
        if total_pages < 5:
            recommendations.append({
                "category": "Arquitectura del Sitio",
                "issue": "Sitio web con pocas páginas",
                "importance": "media",
                "recommendation": f"El sitio tiene solo {total_pages} páginas analizadas. Considera expandir el contenido con más páginas relevantes como servicios, blog, o páginas de productos."
            })

        # Content recommendations
        total_words = aggregated_results.get("content_analysis", {}).get("word_count", 0)
        avg_words_per_page = total_words / total_pages if total_pages > 0 else 0
        if avg_words_per_page < 300:
            recommendations.append({
                "category": "Contenido Global",
                "issue": "Contenido insuficiente por página",
                "importance": "alta",
                "recommendation": f"El promedio de palabras por página es {avg_words_per_page:.0f}. Expande el contenido de cada página a al menos 300 palabras para mejorar el SEO."
            })

        # Images recommendations
        images_info = aggregated_results.get("content_analysis", {}).get("images", {})
        if images_info.get("without_alt_percentage", 0) > 20:
            recommendations.append({
                "category": "Técnico",
                "issue": "Imágenes sin texto alternativo en todo el sitio",
                "importance": "alta",
                "recommendation": f"{images_info.get('without_alt', 0)} de {images_info.get('total', 0)} imágenes no tienen texto alternativo. Implementa alt text descriptivo en todas las imágenes del sitio."
            })

        # Internal linking
        internal_links = aggregated_results.get("content_analysis", {}).get("links", {}).get("internal_count", 0)
        if internal_links < total_pages * 2:
            recommendations.append({
                "category": "Enlaces Internos",
                "issue": "Estructura de enlaces internos débil",
                "importance": "media",
                "recommendation": "Mejora la estructura de enlaces internos conectando páginas relacionadas para distribuir mejor el PageRank y mejorar la navegación."
            })

        return recommendations[:8]  # Limit to 8 recommendations

    async def _analyze_performance_metrics(self, url: str) -> Dict[str, Any]:
        """Analyze Core Web Vitals and performance metrics using Google PageSpeed Insights API."""
        try:
            # Google PageSpeed Insights API
            api_key = getattr(settings, 'GOOGLE_PAGESPEED_API_KEY', None)
            if not api_key:
                logger.warning("Google PageSpeed API key not configured")
                return self._get_default_performance_metrics()

            pagespeed_url = f"https://www.googleapis.com/pagespeedonline/v5/runPagespeed"
            params = {
                'url': url,
                'key': api_key,
                'category': 'PERFORMANCE',  # Focus on performance for Core Web Vitals
                'strategy': 'MOBILE'  # Mobile-first indexing
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(pagespeed_url, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.warning(f"PageSpeed API returned status {response.status}")
                        return self._get_default_performance_metrics()

                    data = await response.json()
                    lighthouse_result = data.get('lighthouseResult', {})
                    audits = lighthouse_result.get('audits', {})
                    categories = lighthouse_result.get('categories', {})

                    # Extract Core Web Vitals
                    core_web_vitals = {
                        'largest_contentful_paint': self._extract_metric_value(audits.get('largest-contentful-paint')),
                        'first_input_delay': self._extract_metric_value(audits.get('max-potential-fid')),
                        'cumulative_layout_shift': self._extract_metric_value(audits.get('cumulative-layout-shift')),
                        'first_contentful_paint': self._extract_metric_value(audits.get('first-contentful-paint')),
                        'speed_index': self._extract_metric_value(audits.get('speed-index')),
                        'time_to_interactive': self._extract_metric_value(audits.get('interactive'))
                    }

                    # Extract Lighthouse scores
                    lighthouse_scores = {
                        'performance': categories.get('performance', {}).get('score', 0) * 100,
                        'accessibility': categories.get('accessibility', {}).get('score', 0) * 100,
                        'best_practices': categories.get('best-practices', {}).get('score', 0) * 100,
                        'seo': categories.get('seo', {}).get('score', 0) * 100
                    }

                    return {
                        'core_web_vitals': core_web_vitals,
                        'lighthouse_scores': lighthouse_scores,
                        'overall_performance_score': lighthouse_scores['performance']
                    }

        except Exception as e:
            logger.error(f"Error analyzing performance metrics: {str(e)}")
            return self._get_default_performance_metrics()

    def _extract_metric_value(self, audit: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metric value and score from Lighthouse audit."""
        if not audit:
            return {'value': None, 'display_value': 'No disponible', 'score': 0, 'rating': 'poor'}

        return {
            'value': audit.get('numericValue'),
            'display_value': audit.get('displayValue', 'No disponible'),
            'score': audit.get('score', 0),
            'rating': self._get_performance_rating(audit.get('score', 0))
        }

    def _get_performance_rating(self, score: float) -> str:
        """Convert Lighthouse score to rating."""
        if score >= 0.9:
            return 'good'
        elif score >= 0.5:
            return 'needs_improvement'
        else:
            return 'poor'

    def _get_default_performance_metrics(self) -> Dict[str, Any]:
        """Return default performance metrics when API fails."""
        return {
            'core_web_vitals': {
                'largest_contentful_paint': {'value': None, 'display_value': 'No disponible', 'score': 0, 'rating': 'unknown'},
                'first_input_delay': {'value': None, 'display_value': 'No disponible', 'score': 0, 'rating': 'unknown'},
                'cumulative_layout_shift': {'value': None, 'display_value': 'No disponible', 'score': 0, 'rating': 'unknown'},
                'first_contentful_paint': {'value': None, 'display_value': 'No disponible', 'score': 0, 'rating': 'unknown'},
                'speed_index': {'value': None, 'display_value': 'No disponible', 'score': 0, 'rating': 'unknown'},
                'time_to_interactive': {'value': None, 'display_value': 'No disponible', 'score': 0, 'rating': 'unknown'}
            },
            'lighthouse_scores': {
                'performance': 0,
                'accessibility': 0,
                'best_practices': 0,
                'seo': 0
            },
            'overall_performance_score': 0
        }

    async def _analyze_schema_markup(self, soup) -> Dict[str, Any]:
        """Analyze structured data (schema markup) in the page."""
        try:
            schema_data = {
                'json_ld_scripts': [],
                'microdata_items': [],
                'rdfa_properties': [],
                'total_schemas': 0,
                'schema_types': []
            }

            # JSON-LD structured data
            json_ld_scripts = soup.find_all('script', type='application/ld+json')
            for script in json_ld_scripts:
                try:
                    schema_content = json.loads(script.string or '{}')
                    schema_type = schema_content.get('@type', 'Unknown')
                    schema_data['json_ld_scripts'].append({
                        'type': schema_type,
                        'content': schema_content
                    })
                    schema_data['schema_types'].append(schema_type)
                except json.JSONDecodeError:
                    continue

            # Microdata
            microdata_items = soup.find_all(attrs={'itemtype': True})
            for item in microdata_items:
                item_type = item.get('itemtype', '').split('/')[-1]
                schema_data['microdata_items'].append({
                    'type': item_type,
                    'element': item.name
                })
                schema_data['schema_types'].append(item_type)

            # RDFa
            rdfa_properties = soup.find_all(attrs={'property': True})
            for prop in rdfa_properties:
                property_name = prop.get('property', '')
                schema_data['rdfa_properties'].append({
                    'property': property_name,
                    'content': prop.get('content', prop.get_text()[:100])
                })

            schema_data['total_schemas'] = len(schema_data['json_ld_scripts']) + len(schema_data['microdata_items'])
            schema_data['schema_types'] = list(set(schema_data['schema_types']))

            return schema_data

        except Exception as e:
            logger.error(f"Error analyzing schema markup: {str(e)}")
            return {
                'json_ld_scripts': [],
                'microdata_items': [],
                'rdfa_properties': [],
                'total_schemas': 0,
                'schema_types': []
            }

    async def _analyze_mobile_usability(self, soup, url: str) -> Dict[str, Any]:
        """Analyze mobile usability factors."""
        try:
            mobile_data = {
                'has_viewport': False,
                'viewport_content': None,
                'has_responsive_images': False,
                'has_touch_friendly_elements': False,
                'font_size_issues': [],
                'mobile_score': 0
            }

            # Viewport analysis
            viewport = soup.find('meta', attrs={'name': 'viewport'})
            if viewport:
                mobile_data['has_viewport'] = True
                mobile_data['viewport_content'] = viewport.get('content', '')

            # Responsive images check
            images = soup.find_all('img')
            responsive_images = 0
            for img in images:
                if img.get('srcset') or img.get('sizes'):
                    responsive_images += 1

            if images:
                mobile_data['has_responsive_images'] = responsive_images / len(images) > 0.5

            # Touch-friendly elements (basic check)
            buttons = soup.find_all(['button', 'a', 'input'])
            touch_friendly = 0
            for element in buttons:
                # Check for common mobile-friendly classes or styles
                classes = element.get('class', [])
                if any('btn' in str(cls).lower() or 'button' in str(cls).lower() for cls in classes):
                    touch_friendly += 1

            if buttons:
                mobile_data['has_touch_friendly_elements'] = touch_friendly / len(buttons) > 0.3

            # Calculate mobile score
            score = 0
            if mobile_data['has_viewport']:
                score += 40
            if mobile_data['has_responsive_images']:
                score += 30
            if mobile_data['has_touch_friendly_elements']:
                score += 30

            mobile_data['mobile_score'] = score

            return mobile_data

        except Exception as e:
            logger.error(f"Error analyzing mobile usability: {str(e)}")
            return {
                'has_viewport': False,
                'viewport_content': None,
                'has_responsive_images': False,
                'has_touch_friendly_elements': False,
                'font_size_issues': [],
                'mobile_score': 0
            }

    def _classify_image_type(self, img) -> str:
        """Classify image type based on attributes and context."""
        src = img.get('src', '').lower()
        classes = ' '.join(img.get('class', [])).lower()

        # Facebook/tracking pixels
        if 'facebook.com' in src or 'tr?' in src:
            return 'tracking_pixel'

        # Avatars/profile images
        if 'avatar' in classes or 'gravatar' in src or 'profile' in classes:
            return 'avatar'

        # Logos
        if 'logo' in src or 'logo' in classes:
            return 'logo'

        # Icons/flags
        if any(term in src for term in ['icon', 'flag', 'emoji']):
            return 'icon'

        # Screenshots/captures
        if 'captura' in src or 'screenshot' in src or 'pantalla' in src:
            return 'screenshot'

        # Content images (photos, illustrations)
        if any(term in src for term in ['.jpg', '.jpeg', '.png', '.webp']):
            if any(term in classes for term in ['wp-image', 'content', 'post']):
                return 'content_image'

        # SVG/data images
        if src.startswith('data:image') or '.svg' in src:
            return 'inline_image'

        return 'unknown'

    def _evaluate_seo_element(self, element_type: str, **kwargs) -> dict:
        """
        Evaluate a single SEO element with world-class standards.
        Returns: {'status': 'excellent'|'good'|'poor'|'missing', 'score': 0-100, 'details': {...}}
        """
        if element_type == 'title':
            title = kwargs.get('title', '')
            title_length = kwargs.get('title_length', 0)

            if not title or title_length == 0:
                return {
                    'status': 'missing',
                    'score': 0,
                    'details': {
                        'issue': 'Página sin título - impacto crítico en SEO',
                        'severity': 'critical',
                        'recommendation': 'Agrega inmediatamente un título único y descriptivo de 50-60 caracteres'
                    }
                }
            elif 50 <= title_length <= 60:
                return {
                    'status': 'excellent',
                    'score': 100,
                    'details': {
                        'achievement': 'Título Perfectamente Optimizado',
                        'description': f'Tu título tiene {title_length} caracteres - longitud ideal para SEO',
                        'impact': 'Máxima visibilidad en resultados de búsqueda'
                    }
                }
            elif 30 <= title_length <= 65:
                return {
                    'status': 'good',
                    'score': 80,
                    'details': {
                        'achievement': 'Título Bien Optimizado',
                        'description': f'Tu título tiene {title_length} caracteres - dentro del rango aceptable',
                        'impact': 'Buena visibilidad en resultados de búsqueda'
                    }
                }
            elif title_length > 65:
                return {
                    'status': 'poor',
                    'score': 30,
                    'details': {
                        'issue': f'Título demasiado largo ({title_length} caracteres)',
                        'severity': 'high',
                        'recommendation': f'Reduce tu título a máximo 60 caracteres para evitar que se corte en Google'
                    }
                }
            else:  # title_length < 30
                return {
                    'status': 'poor',
                    'score': 40,
                    'details': {
                        'issue': f'Título demasiado corto ({title_length} caracteres)',
                        'severity': 'medium',
                        'recommendation': f'Expande tu título a 50-60 caracteres incluyendo palabras clave relevantes'
                    }
                }

        elif element_type == 'meta_description':
            meta_desc = kwargs.get('meta_description', '')
            meta_desc_length = kwargs.get('meta_description_length', 0)

            if not meta_desc or meta_desc_length == 0:
                return {
                    'status': 'missing',
                    'score': 0,
                    'details': {
                        'issue': 'Meta descripción faltante - oportunidad perdida',
                        'severity': 'high',
                        'recommendation': 'Crea una meta descripción atractiva de 140-160 caracteres'
                    }
                }
            elif 140 <= meta_desc_length <= 160:
                return {
                    'status': 'excellent',
                    'score': 100,
                    'details': {
                        'achievement': 'Meta Descripción Perfecta',
                        'description': f'Tu meta descripción tiene {meta_desc_length} caracteres - longitud ideal',
                        'impact': 'Máximo control sobre cómo apareces en Google'
                    }
                }
            elif 120 <= meta_desc_length <= 170:
                return {
                    'status': 'good',
                    'score': 85,
                    'details': {
                        'achievement': 'Meta Descripción Bien Optimizada',
                        'description': f'Tu meta descripción tiene {meta_desc_length} caracteres - longitud aceptable',
                        'impact': 'Buen control sobre el snippet en Google'
                    }
                }
            elif meta_desc_length > 170:
                return {
                    'status': 'poor',
                    'score': 40,
                    'details': {
                        'issue': f'Meta descripción demasiado larga ({meta_desc_length} caracteres)',
                        'severity': 'medium',
                        'recommendation': f'Reduce tu meta descripción a máximo 160 caracteres'
                    }
                }
            else:  # meta_desc_length < 120
                return {
                    'status': 'poor',
                    'score': 50,
                    'details': {
                        'issue': f'Meta descripción muy corta ({meta_desc_length} caracteres)',
                        'severity': 'medium',
                        'recommendation': f'Expande tu meta descripción a 140-160 caracteres'
                    }
                }

        elif element_type == 'h1_tags':
            h1_count = kwargs.get('h1_count', 0)

            if h1_count == 0:
                return {
                    'status': 'missing',
                    'score': 0,
                    'details': {
                        'issue': 'Sin etiquetas H1 - estructura deficiente',
                        'severity': 'high',
                        'recommendation': 'Agrega una etiqueta H1 única que describa el tema principal de la página'
                    }
                }
            elif h1_count == 1:
                return {
                    'status': 'excellent',
                    'score': 100,
                    'details': {
                        'achievement': 'H1 Único y Bien Estructurado',
                        'description': 'Tienes exactamente un H1 - estructura perfecta',
                        'impact': 'Google entiende claramente el tema principal'
                    }
                }
            else:  # h1_count > 1
                return {
                    'status': 'poor',
                    'score': 40,
                    'details': {
                        'issue': f'Múltiples etiquetas H1 ({h1_count} encontradas)',
                        'severity': 'medium',
                        'recommendation': 'Usa solo una etiqueta H1 por página para mejor estructura SEO'
                    }
                }

        elif element_type == 'images_alt':
            total_images = kwargs.get('total_images', 0)
            without_alt = kwargs.get('without_alt', 0)

            if total_images == 0:
                return {'status': 'not_applicable', 'score': 100, 'details': {}}

            alt_percentage = ((total_images - without_alt) / total_images) * 100

            if alt_percentage == 100:
                return {
                    'status': 'excellent',
                    'score': 100,
                    'details': {
                        'achievement': 'Excelente Uso de Alt Text',
                        'description': f'Todas las {total_images} imágenes tienen alt text (100%)',
                        'impact': 'Excelente accesibilidad y SEO de imágenes'
                    }
                }
            elif alt_percentage >= 90:
                return {
                    'status': 'good',
                    'score': 90,
                    'details': {
                        'achievement': 'Muy Buen Uso de Alt Text',
                        'description': f'{total_images - without_alt} de {total_images} imágenes tienen alt text ({alt_percentage:.0f}%)',
                        'impact': 'Muy buena accesibilidad y SEO de imágenes'
                    }
                }
            elif alt_percentage >= 70:
                return {
                    'status': 'good',
                    'score': 75,
                    'details': {
                        'achievement': 'Buen Uso de Alt Text',
                        'description': f'{total_images - without_alt} de {total_images} imágenes tienen alt text ({alt_percentage:.0f}%)',
                        'impact': 'Buena accesibilidad para usuarios con discapacidades'
                    }
                }
            else:
                return {
                    'status': 'poor',
                    'score': 30,
                    'details': {
                        'issue': f'{without_alt} de {total_images} imágenes sin alt text ({100-alt_percentage:.0f}% problemáticas)',
                        'severity': 'medium',
                        'recommendation': f'Agrega texto alternativo a las {without_alt} imágenes restantes para mejorar accesibilidad'
                    }
                }

        # Add more element types as needed
        return {'status': 'unknown', 'score': 0, 'details': {}}

    def _filter_contradictory_recommendations(self, recommendations: List[Dict[str, Any]], analysis_summary: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter out AI recommendations that contradict our strict evaluation logic."""
        filtered = []

        for rec in recommendations:
            issue = rec.get('issue', '').lower()
            should_include = True

            # Title contradiction check
            if 'título' in issue or 'title' in issue:
                title_eval = self._evaluate_seo_element(
                    'title',
                    title=analysis_summary.get("title", ""),
                    title_length=analysis_summary.get("title_length", 0)
                )
                # Only include if evaluation says it's a problem
                if title_eval['status'] not in ['missing', 'poor']:
                    should_include = False

            # Meta description contradiction check
            elif 'descripción' in issue or 'description' in issue:
                meta_eval = self._evaluate_seo_element(
                    'meta_description',
                    meta_description=analysis_summary.get("meta_description", ""),
                    meta_description_length=analysis_summary.get("meta_description_length", 0)
                )
                if meta_eval['status'] not in ['missing', 'poor']:
                    should_include = False

            # H1 contradiction check
            elif 'h1' in issue:
                h1_eval = self._evaluate_seo_element(
                    'h1_tags',
                    h1_count=analysis_summary.get("h1_count", 0)
                )
                if h1_eval['status'] not in ['missing', 'poor']:
                    should_include = False

            # Images contradiction check
            elif 'alt' in issue or 'imagen' in issue:
                images_eval = self._evaluate_seo_element(
                    'images_alt',
                    total_images=analysis_summary.get("images_total", 0),
                    without_alt=analysis_summary.get("images_without_alt", 0)
                )
                if images_eval['status'] not in ['poor']:
                    should_include = False

            if should_include:
                filtered.append(rec)

        return filtered

    def _generate_achievements(self, basic_info, content_analysis, technical_seo, og_data):
        """
        Generate positive achievements - things the website is doing RIGHT!
        Uses strict evaluation to avoid contradictions with recommendations.
        """
        achievements = []

        # HTTPS Security - Only if truly secure
        if basic_info.get('is_https', False):
            achievements.append({
                'category': 'Seguridad',
                'achievement': 'HTTPS Implementado Correctamente',
                'description': 'Tu sitio web es seguro y confiable para los usuarios',
                'icon': '🔒',
                'impact': 'Mejora la confianza del usuario y el ranking en Google'
            })

        # Title Tag - Using strict evaluation
        title_eval = self._evaluate_seo_element(
            'title',
            title=basic_info.get('title', ''),
            title_length=basic_info.get('title_length', 0)
        )

        if title_eval['status'] in ['excellent', 'good']:
            achievements.append({
                'category': 'Meta Tags',
                'achievement': title_eval['details']['achievement'],
                'description': title_eval['details']['description'],
                'icon': '📝',
                'impact': title_eval['details']['impact']
            })

        # Meta Description - Using strict evaluation
        meta_eval = self._evaluate_seo_element(
            'meta_description',
            meta_description=basic_info.get('meta_description', ''),
            meta_description_length=basic_info.get('meta_description_length', 0)
        )

        if meta_eval['status'] in ['excellent', 'good']:
            achievements.append({
                'category': 'Meta Tags',
                'achievement': meta_eval['details']['achievement'],
                'description': meta_eval['details']['description'],
                'icon': '📄',
                'impact': meta_eval['details']['impact']
            })

        # H1 Tags - Using strict evaluation
        h1_eval = self._evaluate_seo_element(
            'h1_tags',
            h1_count=basic_info.get('h1_count', 0)
        )

        if h1_eval['status'] in ['excellent', 'good']:
            achievements.append({
                'category': 'Estructura',
                'achievement': h1_eval['details']['achievement'],
                'description': h1_eval['details']['description'],
                'icon': '🏗️',
                'impact': h1_eval['details']['impact']
            })

        # Images with Alt Text - Using strict evaluation
        images_data = content_analysis.get('images', {})
        total_images = images_data.get('total', 0)
        without_alt = images_data.get('without_alt', 0)

        if total_images > 0:
            images_eval = self._evaluate_seo_element(
                'images_alt',
                total_images=total_images,
                without_alt=without_alt
            )

            if images_eval['status'] in ['excellent', 'good']:
                achievements.append({
                    'category': 'Accesibilidad',
                    'achievement': images_eval['details']['achievement'],
                    'description': images_eval['details']['description'],
                    'icon': '🖼️',
                    'impact': images_eval['details']['impact']
                })

        # Content Length
        word_count = content_analysis.get('word_count', 0)
        if word_count >= 1000:
            achievements.append({
                'category': 'Contenido',
                'achievement': 'Contenido Rico y Detallado',
                'description': f'Tu página tiene {word_count} palabras - excelente para SEO',
                'icon': '📚',
                'impact': 'Google valora el contenido extenso y de calidad'
            })
        elif word_count >= 300:
            achievements.append({
                'category': 'Contenido',
                'achievement': 'Contenido Sustancial',
                'description': f'Tu página tiene {word_count} palabras',
                'icon': '📚',
                'impact': 'Suficiente contenido para ser indexado correctamente'
            })

        # Internal Links
        links_data = content_analysis.get('links', {})
        internal_links = links_data.get('internal_count', 0)
        if internal_links >= 10:
            achievements.append({
                'category': 'Navegación',
                'achievement': 'Excelente Estructura de Enlaces',
                'description': f'{internal_links} enlaces internos encontrados',
                'icon': '🔗',
                'impact': 'Excelente distribución de autoridad de página'
            })
        elif internal_links >= 5:
            achievements.append({
                'category': 'Navegación',
                'achievement': 'Buena Estructura de Enlaces',
                'description': f'{internal_links} enlaces internos encontrados',
                'icon': '🔗',
                'impact': 'Buena navegación y distribución de PageRank'
            })

        # Technical SEO
        if technical_seo.get('has_canonical', False):
            achievements.append({
                'category': 'SEO Técnico',
                'achievement': 'Canonical URL Configurada',
                'description': 'Evitas problemas de contenido duplicado',
                'icon': '⚙️',
                'impact': 'Previene penalizaciones por contenido duplicado'
            })

        if technical_seo.get('has_viewport', False):
            achievements.append({
                'category': 'Mobile',
                'achievement': 'Optimizado para Móviles',
                'description': 'Meta viewport configurado correctamente',
                'icon': '📱',
                'impact': 'Experiencia optimizada en dispositivos móviles'
            })

        # Open Graph
        if og_data and len(og_data) >= 3:
            achievements.append({
                'category': 'Redes Sociales',
                'achievement': 'Open Graph Implementado',
                'description': 'Tu contenido se ve perfecto al compartir en redes sociales',
                'icon': '📱',
                'impact': 'Mayor engagement en redes sociales'
            })

        return achievements

# Create global instance
seo_analyzer = SEOAnalyzer()
