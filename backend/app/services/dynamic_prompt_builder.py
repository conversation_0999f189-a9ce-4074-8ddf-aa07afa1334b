"""
Dynamic Prompt Builder Service for Adaptive Ad Generation
Creates contextually relevant prompts based on product type, audience, and style
"""

import logging
from typing import Dict, Any, Optional, List
from enum import Enum

logger = logging.getLogger(__name__)

class AdStyle(Enum):
    """Advertisement style categories"""
    MEDICAL_PROFESSIONAL = "medical_professional"
    TECH_MODERN = "tech_modern" 
    FASHION_LIFESTYLE = "fashion_lifestyle"
    FOOD_APPETIZING = "food_appetizing"
    SERVICE_TRUSTWORTHY = "service_trustworthy"
    FITNESS_ENERGETIC = "fitness_energetic"
    BEAUTY_ELEGANT = "beauty_elegant"
    LUXURY_PREMIUM = "luxury_premium"
    PLAYFUL_FUN = "playful_fun"
    MINIMALIST_CLEAN = "minimalist_clean"

class DynamicPromptBuilder:
    """Service for building adaptive prompts based on context"""
    
    def __init__(self):
        """Initialize the prompt builder with style templates"""
        self.style_templates = self._initialize_style_templates()
        self.industry_mappings = self._initialize_industry_mappings()
        self.audience_adaptations = self._initialize_audience_adaptations()
    
    def build_contextual_prompt(self, 
                              base_description: str,
                              product_analysis: Optional[Dict[str, Any]] = None,
                              language_context: Optional[Dict[str, Any]] = None,
                              user_preferences: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """
        Build a contextually relevant prompt for ad generation
        
        Args:
            base_description: Core product/service description
            product_analysis: Analysis from image analysis service
            language_context: Language processing context
            user_preferences: User-specified preferences
            
        Returns:
            Dict with optimized prompt and metadata
        """
        
        # Determine the appropriate style
        ad_style = self._determine_ad_style(product_analysis, user_preferences)
        
        # Get style-specific template
        style_template = self.style_templates.get(ad_style, self.style_templates[AdStyle.MINIMALIST_CLEAN])
        
        # Build the core prompt
        core_prompt = self._build_core_prompt(base_description, style_template, product_analysis)
        
        # Add visual specifications
        visual_specs = self._build_visual_specifications(ad_style, product_analysis)
        
        # Add text and typography guidance
        text_guidance = self._build_text_guidance(language_context, ad_style)
        
        # Add composition and layout guidance
        composition = self._build_composition_guidance(ad_style, product_analysis)
        
        # Combine into final prompt
        final_prompt = f"{core_prompt}\n\n{visual_specs}\n\n{text_guidance}\n\n{composition}"
        
        # Clean up the prompt
        final_prompt = self._clean_prompt(final_prompt)
        
        return {
            "prompt": final_prompt,
            "style": ad_style.value,
            "style_type": self._get_ideogram_style_type(ad_style),
            "negative_prompt": self._build_negative_prompt(ad_style, language_context),
            "composition_focus": style_template["composition_focus"]
        }
    
    def _determine_ad_style(self, product_analysis: Optional[Dict[str, Any]], 
                           user_preferences: Optional[Dict[str, Any]]) -> AdStyle:
        """Determine the most appropriate ad style"""
        
        # User preference override
        if user_preferences and "style" in user_preferences:
            try:
                return AdStyle(user_preferences["style"])
            except ValueError:
                pass
        
        # Determine from product analysis
        if product_analysis:
            product_type = product_analysis.get("product_type", "").lower()
            brand_aesthetic = product_analysis.get("brand_aesthetic", "").lower()
            industry_style = product_analysis.get("industry_style", "").lower()
            
            # Map product type to style
            if product_type in ["supplement", "medicine", "health"]:
                return AdStyle.MEDICAL_PROFESSIONAL
            elif product_type in ["app", "software", "tech", "saas"]:
                return AdStyle.TECH_MODERN
            elif product_type in ["clothing", "fashion", "accessories"]:
                return AdStyle.FASHION_LIFESTYLE
            elif product_type in ["food", "restaurant", "recipe"]:
                return AdStyle.FOOD_APPETIZING
            elif product_type in ["service", "consulting", "business"]:
                return AdStyle.SERVICE_TRUSTWORTHY
            elif product_type in ["fitness", "gym", "workout", "sports"]:
                return AdStyle.FITNESS_ENERGETIC
            elif product_type in ["beauty", "cosmetic", "skincare"]:
                return AdStyle.BEAUTY_ELEGANT
            
            # Check for luxury indicators
            if any(word in brand_aesthetic for word in ["luxury", "premium", "high-end", "exclusive"]):
                return AdStyle.LUXURY_PREMIUM
            
            # Check for playful indicators
            if any(word in brand_aesthetic for word in ["playful", "fun", "colorful", "young"]):
                return AdStyle.PLAYFUL_FUN
        
        # Default to clean minimalist
        return AdStyle.MINIMALIST_CLEAN
    
    def _initialize_style_templates(self) -> Dict[AdStyle, Dict[str, str]]:
        """Initialize style-specific templates"""
        return {
            AdStyle.MEDICAL_PROFESSIONAL: {
                "visual_approach": "clean, clinical, trustworthy medical design",
                "color_palette": "medical blues, clean whites, trust-building colors",
                "typography": "professional, readable, authoritative fonts",
                "composition_focus": "evidence-based, organized, credible",
                "mood": "professional, trustworthy, reliable, scientific"
            },
            AdStyle.TECH_MODERN: {
                "visual_approach": "sleek, innovative, cutting-edge technology design",
                "color_palette": "modern tech colors, gradients, electric blues, vibrant accents",
                "typography": "futuristic, clean sans-serif, tech-forward fonts",
                "composition_focus": "interface-inspired, minimal, forward-thinking",
                "mood": "innovative, smart, efficient, modern"
            },
            AdStyle.FASHION_LIFESTYLE: {
                "visual_approach": "stylish, aspirational, lifestyle-focused design",
                "color_palette": "fashion-forward, seasonal, trendy color schemes",
                "typography": "stylish, editorial, brand-specific fonts",
                "composition_focus": "lifestyle-oriented, aspirational, trendy",
                "mood": "stylish, confident, aspirational, trendy"
            },
            AdStyle.FOOD_APPETIZING: {
                "visual_approach": "mouth-watering, appetizing, inviting food design",
                "color_palette": "warm, appetizing, natural food colors",
                "typography": "friendly, approachable, readable fonts",
                "composition_focus": "appetite-appealing, comfort-focused, inviting",
                "mood": "delicious, comforting, satisfying, appetizing"
            },
            AdStyle.SERVICE_TRUSTWORTHY: {
                "visual_approach": "professional, reliable, trustworthy service design",
                "color_palette": "professional blues, trustworthy colors, corporate tones",
                "typography": "professional, authoritative, clear fonts",
                "composition_focus": "credible, organized, professional",
                "mood": "trustworthy, professional, reliable, competent"
            },
            AdStyle.FITNESS_ENERGETIC: {
                "visual_approach": "dynamic, energetic, motivational fitness design",
                "color_palette": "energetic colors, bold contrasts, motivational tones",
                "typography": "bold, strong, motivational fonts",
                "composition_focus": "action-oriented, motivational, dynamic",
                "mood": "energetic, motivational, strong, active"
            },
            AdStyle.BEAUTY_ELEGANT: {
                "visual_approach": "elegant, luxurious, sophisticated beauty design",
                "color_palette": "soft, elegant, sophisticated beauty colors",
                "typography": "elegant, refined, sophisticated fonts",
                "composition_focus": "beauty-focused, elegant, refined",
                "mood": "elegant, beautiful, sophisticated, luxurious"
            },
            AdStyle.LUXURY_PREMIUM: {
                "visual_approach": "premium, exclusive, high-end luxury design",
                "color_palette": "luxury colors, gold accents, premium tones",
                "typography": "premium, exclusive, high-end fonts",
                "composition_focus": "exclusive, premium, sophisticated",
                "mood": "luxurious, exclusive, premium, sophisticated"
            },
            AdStyle.PLAYFUL_FUN: {
                "visual_approach": "playful, fun, colorful, engaging design",
                "color_palette": "bright, playful, fun colors, vibrant palette",
                "typography": "playful, fun, engaging fonts",
                "composition_focus": "entertaining, engaging, joyful",
                "mood": "fun, playful, joyful, entertaining"
            },
            AdStyle.MINIMALIST_CLEAN: {
                "visual_approach": "clean, minimal, sophisticated design",
                "color_palette": "clean, minimal color palette, sophisticated tones",
                "typography": "clean, minimal, readable fonts",
                "composition_focus": "organized, clear, uncluttered",
                "mood": "clean, professional, sophisticated, clear"
            }
        }
    
    def _build_core_prompt(self, base_description: str, style_template: Dict[str, str], 
                          product_analysis: Optional[Dict[str, Any]]) -> str:
        """Build the core prompt with product context"""
        
        # Start with base description
        core = f"Create a professional marketing advertisement for: {base_description}."
        
        # Add product-specific context if available
        if product_analysis:
            visual_desc = product_analysis.get("visual_description", "")
            key_features = product_analysis.get("key_features", [])
            
            if visual_desc:
                core += f" Product visual context: {visual_desc}."
            
            if key_features:
                features_text = ", ".join(key_features[:3])  # Top 3 features
                core += f" Key features to highlight: {features_text}."
        
        # Add style approach
        core += f" Visual approach: {style_template['visual_approach']}."
        
        return core
    
    def _build_visual_specifications(self, ad_style: AdStyle, 
                                   product_analysis: Optional[Dict[str, Any]]) -> str:
        """Build visual specifications section"""
        
        template = self.style_templates[ad_style]
        
        specs = f"VISUAL SPECIFICATIONS:\n"
        specs += f"- Color palette: {template['color_palette']}\n"
        specs += f"- Typography: {template['typography']}\n"
        specs += f"- Overall mood: {template['mood']}\n"
        
        # Add product-specific visual elements
        if product_analysis:
            brand_aesthetic = product_analysis.get("brand_aesthetic", "")
            if brand_aesthetic:
                specs += f"- Brand aesthetic: {brand_aesthetic}\n"
        
        return specs
    
    def _build_text_guidance(self, language_context: Optional[Dict[str, Any]], 
                           ad_style: AdStyle) -> str:
        """Build text and typography guidance"""
        
        guidance = "TEXT AND TYPOGRAPHY:\n"
        guidance += "- Include clear, readable headlines and call-to-action\n"
        guidance += "- Ensure text is legible and professionally designed\n"
        guidance += "- Use appropriate font hierarchy and spacing\n"
        
        # Add language-specific guidance
        if language_context:
            text_instruction = language_context.get("text_instruction", "")
            if text_instruction:
                guidance += f"- Language: {text_instruction}\n"
        
        # Add style-specific text guidance
        if ad_style == AdStyle.MEDICAL_PROFESSIONAL:
            guidance += "- Use authoritative, credible language\n"
        elif ad_style == AdStyle.TECH_MODERN:
            guidance += "- Use modern, innovative language\n"
        elif ad_style == AdStyle.PLAYFUL_FUN:
            guidance += "- Use engaging, fun language\n"
        
        return guidance
    
    def _build_composition_guidance(self, ad_style: AdStyle, 
                                  product_analysis: Optional[Dict[str, Any]]) -> str:
        """Build composition and layout guidance"""
        
        template = self.style_templates[ad_style]
        
        guidance = "COMPOSITION AND LAYOUT:\n"
        guidance += f"- Focus: {template['composition_focus']}\n"
        guidance += "- Professional layout with clear visual hierarchy\n"
        guidance += "- Balanced composition with effective use of white space\n"
        guidance += "- Strong call-to-action placement\n"
        
        # Add product-specific composition notes
        if product_analysis:
            target_audience = product_analysis.get("target_audience", "")
            if "young" in target_audience.lower():
                guidance += "- Dynamic, youth-oriented composition\n"
            elif "professional" in target_audience.lower():
                guidance += "- Professional, business-oriented composition\n"
        
        return guidance
    
    def _get_ideogram_style_type(self, ad_style: AdStyle) -> str:
        """Get appropriate Ideogram style_type for the ad style"""
        
        style_mapping = {
            AdStyle.MEDICAL_PROFESSIONAL: "DESIGN",
            AdStyle.TECH_MODERN: "DESIGN", 
            AdStyle.FASHION_LIFESTYLE: "DESIGN",
            AdStyle.FOOD_APPETIZING: "DESIGN",
            AdStyle.SERVICE_TRUSTWORTHY: "DESIGN",
            AdStyle.FITNESS_ENERGETIC: "DESIGN",
            AdStyle.BEAUTY_ELEGANT: "DESIGN",
            AdStyle.LUXURY_PREMIUM: "DESIGN",
            AdStyle.PLAYFUL_FUN: "DESIGN",
            AdStyle.MINIMALIST_CLEAN: "DESIGN"
        }
        
        return style_mapping.get(ad_style, "DESIGN")
    
    def _build_negative_prompt(self, ad_style: AdStyle, 
                             language_context: Optional[Dict[str, Any]]) -> str:
        """Build contextual negative prompt"""
        
        base_negative = "low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"
        
        # Add style-specific negatives
        style_negatives = {
            AdStyle.MEDICAL_PROFESSIONAL: "unprofessional, unreliable, sketchy, amateur medical claims",
            AdStyle.TECH_MODERN: "outdated, old-fashioned, non-tech, analog",
            AdStyle.FASHION_LIFESTYLE: "unfashionable, outdated style, poor fashion sense",
            AdStyle.FOOD_APPETIZING: "unappetizing, spoiled food, unnatural colors",
            AdStyle.LUXURY_PREMIUM: "cheap, low-end, budget, discount appearance",
            AdStyle.PLAYFUL_FUN: "serious, boring, corporate, stuffy"
        }
        
        style_specific = style_negatives.get(ad_style, "")
        if style_specific:
            base_negative += f", {style_specific}"
        
        # Add language-specific negatives
        if language_context:
            lang_negative = language_context.get("negative_additions", "")
            if lang_negative:
                base_negative += f", {lang_negative}"
        
        return base_negative
    
    def _clean_prompt(self, prompt: str) -> str:
        """Clean and optimize the final prompt"""
        
        # Remove excessive whitespace
        import re
        prompt = re.sub(r'\n\s*\n', '\n\n', prompt)
        prompt = re.sub(r' +', ' ', prompt)
        
        # Ensure proper punctuation
        prompt = prompt.strip()
        if not prompt.endswith('.'):
            prompt += '.'
        
        return prompt
    
    def _initialize_industry_mappings(self) -> Dict[str, AdStyle]:
        """Initialize industry to style mappings"""
        return {
            "healthcare": AdStyle.MEDICAL_PROFESSIONAL,
            "technology": AdStyle.TECH_MODERN,
            "fashion": AdStyle.FASHION_LIFESTYLE,
            "food": AdStyle.FOOD_APPETIZING,
            "consulting": AdStyle.SERVICE_TRUSTWORTHY,
            "fitness": AdStyle.FITNESS_ENERGETIC,
            "beauty": AdStyle.BEAUTY_ELEGANT,
            "luxury": AdStyle.LUXURY_PREMIUM
        }
    
    def _initialize_audience_adaptations(self) -> Dict[str, Dict[str, str]]:
        """Initialize audience-specific adaptations"""
        return {
            "young_adults": {
                "tone": "energetic, trendy, social",
                "visual": "dynamic, colorful, modern"
            },
            "professionals": {
                "tone": "authoritative, trustworthy, efficient", 
                "visual": "clean, professional, sophisticated"
            },
            "seniors": {
                "tone": "clear, trustworthy, respectful",
                "visual": "clear, readable, traditional"
            }
        }
