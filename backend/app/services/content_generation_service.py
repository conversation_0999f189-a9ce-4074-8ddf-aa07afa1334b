"""
Content generation service - Simplified and intelligent.
Uses Creative Genius for all content generation.
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class ContentGenerationService:
    """Simplified service that uses Creative Genius for all content generation."""
    
    def __init__(self):
        self.creative_genius = None
        self._initialize_creative_genius()
    
    def _initialize_creative_genius(self):
        """Initialize Creative Genius service."""
        try:
            from app.services.creative_genius_service import CreativeGeniusService
            self.creative_genius = CreativeGeniusService()
            logger.info("✅ Content Generation Service initialized with Creative Genius")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Creative Genius: {e}")
    
    async def generate_posts(self, user_context: Dict[str, Any], content_type: str = "educational", num_posts: int = 3) -> List[Dict[str, Any]]:
        """
        Generate multiple posts using Creative Genius.
        
        Args:
            user_context: User context (businessName, industry, etc.)
            content_type: Type of content (educational, motivational, balanced)
            num_posts: Number of posts to generate
            
        Returns:
            List of generated posts with visual hooks and content
        """
        if not self.creative_genius:
            logger.error("Creative Genius not available")
            return []
        
        posts = []
        
        for i in range(num_posts):
            try:
                # Generate creative breakthrough concept
                breakthrough = await self.creative_genius.create_breakthrough_content(user_context, content_type)
                
                # Generate post content using the breakthrough
                post_content = await self.generate_post_content(breakthrough, user_context)
                
                post = {
                    "visual_hook": breakthrough.hook,
                    "content": post_content,
                    "ideogram_prompt": breakthrough.ideogram_prompt,
                    "visual_concept": breakthrough.visual_concept,
                    "viral_score": breakthrough.viral_score
                }
                
                posts.append(post)
                logger.info(f"✅ Generated post {i+1}/{num_posts}")
                
            except Exception as e:
                logger.error(f"Error generating post {i+1}: {e}")
                continue
        
        return posts
    
    async def generate_post_content(self, creative_breakthrough, user_context: Dict[str, Any], platform: str = "Instagram") -> str:
        """
        Generate post content from Creative Genius breakthrough.
        
        Args:
            creative_breakthrough: CreativeBreakthrough object from Creative Genius
            user_context: User context information
            platform: Target platform
            
        Returns:
            Generated post content
        """
        if not self.creative_genius:
            return self._generate_simple_fallback(user_context)
        
        try:
            from app.utils.platform_utils import get_platform_character_limits
            from app.utils.content_utils import optimize_content_for_platform
            
            # Get platform limits
            char_limits = get_platform_character_limits(platform)
            
            # Generate intelligent content based on breakthrough
            content = await self._generate_intelligent_content(creative_breakthrough, user_context, char_limits)
            
            # Optimize for platform
            optimized_content = optimize_content_for_platform(content, platform, char_limits)
            
            return optimized_content
            
        except Exception as e:
            logger.error(f"Error generating post content: {e}")
            return self._generate_simple_fallback(user_context)
    
    async def _generate_intelligent_content(self, breakthrough, user_context: Dict[str, Any], char_limits: Dict[str, int]) -> str:
        """Generate intelligent content based on Creative Genius breakthrough."""
        try:
            import google.generativeai as genai
            from app.core.config import settings

            if not settings.GEMINI_API_KEY:
                return self._generate_simple_fallback(user_context)

            genai.configure(api_key=settings.GEMINI_API_KEY)
            model = genai.GenerativeModel('gemini-1.5-flash')

            # Get actual brand information - don't use fallbacks
            business_name = user_context.get("businessName", "").strip()
            if not business_name or business_name in ["Business", "Mi Marca", "Unknown"]:
                business_name = None  # Will be handled in prompt

            # Use specific industry from AI analysis if available, otherwise fallback to general
            industry = user_context.get("specific_industry") or user_context.get("industry", "general")

            # Get additional context from AI analysis
            context_analysis = user_context.get("context_analysis", {})
            detected_niche = context_analysis.get("nicho", industry)
            target_audience = context_analysis.get("audiencia", user_context.get("target_audience", "audiencia general"))

            logger.info(f"🎯 Using specific context - Industry: {industry}, Niche: {detected_niche}, Audience: {target_audience}")
            content_angle = breakthrough.content_angle
            hook = breakthrough.hook

            # 🎯 PRIORITY: Get user's specific topic/description
            user_topics = user_context.get("topics", [])
            extracted_topic = user_context.get("extracted_topic", "")
            specific_topic = context_analysis.get("tema_especifico", "")

            # Use the most specific topic available
            user_topic = specific_topic or extracted_topic or (user_topics[0].strip() if user_topics and user_topics[0].strip() else "")

            logger.info(f"🎯 Content generation using topic: '{user_topic}'")

            # Build context-aware prompt
            if business_name and user_topic:
                # Both brand name and topic available
                prompt = f"""
Crea un post de redes sociales ESPECÍFICO y PERSONALIZADO para la marca "{business_name}".

TEMA/DESCRIPCIÓN DEL NEGOCIO: "{user_topic}"
NOMBRE DE LA MARCA: {business_name}
NICHO ESPECÍFICO: {detected_niche}
AUDIENCIA OBJETIVO: {target_audience}
ESTILO: {content_angle}

CONTEXTO ESPECÍFICO EXTRAÍDO:
- Industria real: {detected_niche} (NO {industry} genérico)
- Audiencia específica: {target_audience}
- Tema extraído: {user_context.get('extracted_topic', user_topic)}

INSTRUCCIONES CRÍTICAS:
1. El post DEBE ser específico para "{business_name}" en el nicho de "{detected_niche}"
2. El contenido DEBE estar relacionado con: "{user_topic}"
3. Dirígete específicamente a: {target_audience}
4. Escribe como si fueras el dueño de "{business_name}" especializado en {detected_niche}
5. Incluye detalles específicos del negocio basados en la descripción
6. Máximo {char_limits.get('optimal', 800)} caracteres
7. Escribe en español natural y auténtico

Genera SOLO el contenido del post, sin explicaciones adicionales:
"""
            elif user_topic:
                # Only topic available, no specific brand name
                prompt = f"""
Crea un post de redes sociales específico sobre este tema/negocio:

TEMA/DESCRIPCIÓN: "{user_topic}"
NICHO ESPECÍFICO: {detected_niche}
AUDIENCIA OBJETIVO: {target_audience}
ESTILO: {content_angle}

CONTEXTO ESPECÍFICO EXTRAÍDO:
- Nicho real: {detected_niche} (NO genérico)
- Audiencia específica: {target_audience}
- Tema extraído: {user_context.get('extracted_topic', user_topic)}

INSTRUCCIONES CRÍTICAS:
1. El post DEBE ser específico sobre: "{user_topic}" en el nicho de "{detected_niche}"
2. Dirígete específicamente a: {target_audience}
3. NO uses placeholders genéricos como "tu marca", "mi marca", "[marca]"
4. Extrae información específica de la descripción y úsala
5. Si hay un nombre de marca en la descripción, úsalo
6. Máximo {char_limits.get('optimal', 800)} caracteres
7. Escribe en español natural y auténtico

Genera SOLO el contenido del post, sin explicaciones adicionales:
"""
            else:
                # Fallback when no specific information is available
                prompt = f"""
Crea un post de redes sociales sobre {detected_niche}.

NICHO: {detected_niche}
AUDIENCIA: {target_audience}
ESTILO: {content_angle}
Máximo {char_limits.get('optimal', 800)} caracteres en español.

INSTRUCCIONES:
1. Sé específico sobre {detected_niche}
2. Dirígete a {target_audience}
3. Evita placeholders genéricos
4. Proporciona valor real

Genera contenido específico y útil:
"""

            response = model.generate_content(prompt)

            if response and response.text:
                content = response.text.strip()

                # Clean up the content
                content = content.replace('"', '').strip()

                # Remove hook if it appears in content
                if hook and hook.lower() in content.lower():
                    content = content.replace(hook, "").strip()
                    content = " ".join(content.split())

                # Final validation - ensure no generic placeholders remain
                generic_placeholders = ["[marca]", "[tu marca]", "[nombre de la marca]", "tu marca", "mi marca", "nuestra marca"]
                for placeholder in generic_placeholders:
                    if placeholder.lower() in content.lower():
                        logger.warning(f"⚠️ Generic placeholder detected: {placeholder}")
                        # If we have a business name, replace it
                        if business_name:
                            content = content.replace(placeholder, business_name)
                        else:
                            # Try to regenerate without placeholders
                            return await self._regenerate_without_placeholders(user_context, char_limits, content_angle)

                return content
            else:
                return self._generate_simple_fallback(user_context)

        except Exception as e:
            logger.error(f"Error generating intelligent content: {e}")
            return self._generate_simple_fallback(user_context)

    async def _regenerate_without_placeholders(self, user_context: Dict[str, Any], char_limits: Dict[str, int], content_angle: str) -> str:
        """Regenerate content specifically avoiding placeholders."""
        try:
            import google.generativeai as genai
            from app.core.config import settings

            genai.configure(api_key=settings.GEMINI_API_KEY)
            model = genai.GenerativeModel('gemini-1.5-flash')

            user_topics = user_context.get("topics", [])
            user_topic = user_topics[0] if user_topics and user_topics[0].strip() else "marketing digital"

            # Get specific context
            context_analysis = user_context.get("context_analysis", {})
            detected_niche = context_analysis.get("nicho", "marketing digital")
            target_audience = context_analysis.get("audiencia", "audiencia general")

            prompt = f"""
Crea un post de redes sociales específico sobre: "{user_topic}"

CONTEXTO ESPECÍFICO:
- Nicho: {detected_niche}
- Audiencia: {target_audience}
- Tema extraído: {user_context.get('extracted_topic', user_topic)}

REGLAS ESTRICTAS:
- NO uses NUNCA palabras como "tu marca", "mi marca", "nuestra marca", "[marca]"
- Habla directamente sobre {detected_niche}
- Dirígete específicamente a {target_audience}
- Sé específico y útil
- Máximo {char_limits.get('optimal', 800)} caracteres
- Estilo: {content_angle}
- Idioma: español

Ejemplo de lo que NO quiero: "Descubre cómo tu marca puede..."
Ejemplo de lo que SÍ quiero: "Descubre cómo {detected_niche} puede..."

Genera SOLO el contenido del post:
"""

            response = model.generate_content(prompt)
            if response and response.text:
                return response.text.strip().replace('"', '')
            else:
                return self._generate_simple_fallback(user_context)

        except Exception as e:
            logger.error(f"Error regenerating content: {e}")
            return self._generate_simple_fallback(user_context)
    
    def _generate_simple_fallback(self, user_context: Dict[str, Any]) -> str:
        """Simple fallback content when everything fails."""
        business_name = user_context.get("businessName", "Nuestra marca")
        industry = user_context.get("industry", "general")
        
        return f"Descubre más sobre {industry} con {business_name}.\n\n¿Qué te gustaría saber? 💭\n\n#Contenido #{industry.title()}"

    # SIMILARITY FUNCTIONALITY MOVED TO DEDICATED SimilarPostsService
    # This keeps ContentGenerationService focused on its core responsibility


    # COMPATIBILITY METHODS - For backward compatibility with existing code

    async def generate_visual_hook_content(self, content_strategy: Dict[str, Any], brand_info: Dict[str, Any]) -> str:
        """
        COMPATIBILITY: Generate visual hook content.
        Redirects to Creative Genius for consistency.
        """
        logger.info("🔄 Using compatibility method - redirecting to Creative Genius")

        try:
            # Extract content type from strategy
            context_analysis = content_strategy.get("context_analysis", {})
            content_type = context_analysis.get("tipo_contenido", "educational")

            # Create user context from brand info
            user_context = {
                "businessName": brand_info.get("businessName", "Business"),
                "industry": brand_info.get("industry", "general"),
                "target_audience": brand_info.get("target_audience", "general audience")
            }

            # Generate breakthrough concept
            breakthrough = await self.creative_genius.create_breakthrough_content(user_context, content_type)

            return breakthrough.hook

        except Exception as e:
            logger.error(f"Error in compatibility visual hook generation: {e}")
            # Simple fallback
            topic = content_strategy.get("topic", "marketing digital")
            return f"Descubre {topic.title()}"

    async def generate_creative_genius_content(self, creative_concept, brand_info: Dict[str, Any], platform: str) -> str:
        """
        COMPATIBILITY: Generate content using Creative Genius concept.
        Redirects to the new generate_post_content method.
        """
        logger.info("🔄 Using compatibility method - redirecting to generate_post_content")

        user_context = {
            "businessName": brand_info.get("businessName", "Business"),
            "industry": brand_info.get("industry", "general")
        }

        return await self.generate_post_content(creative_concept, user_context, platform)

    # COMPATIBILITY METHODS REMOVED - Use SimilarPostsService instead
    # This keeps ContentGenerationService clean and focused
