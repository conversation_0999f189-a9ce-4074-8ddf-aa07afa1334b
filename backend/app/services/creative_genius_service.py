"""
Creative Genius Service - El Director Creativo Full Stack

Este módulo piensa como un director creativo de agencia top, sin límites.
Genera conceptos visuales y de contenido que la gente QUIERE ver.
No usa templates ni fórmulas quemadas.
"""

import asyncio
import json
import random
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class CreativeBreakthrough:
    """Resultado del Creative Genius - Un concepto creativo revolucionario"""
    visual_concept: str
    hook: str
    content_angle: str
    ideogram_prompt: str
    emotional_journey: Dict[str, str]
    viral_score: float
    why_its_brilliant: str
    psychology_target: str
    art_direction: str


class CreativeGeniusService:
    """
    El Creative Genius - Piensa como director creativo sin límites.
    
    No genera contenido genérico. Solo conceptos que rompen internet.
    Usa todo el potencial de Ideogram: 3D, cómics, arte, cinematográfico.
    """
    
    def __init__(self):
        self.model = None
        self.unlimited_thinking = True
        self.anti_corporate_filter = True
        self.viral_potential_threshold = 4.5  # Acepta conceptos chingones y relevantes
        self._initialize_ai()

        logger.info("🧠 Creative Genius initialized - Ready to break the internet")

    def _initialize_ai(self):
        """Initialize AI model for creative genius."""
        try:
            import google.generativeai as genai

            if settings.GEMINI_API_KEY:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Creative Genius AI initialized successfully")
            else:
                logger.warning("⚠️ GEMINI_API_KEY not configured - using fallback mode")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Creative Genius AI: {e}")
    
    async def create_breakthrough_content(self, user_context: Dict[str, Any], content_type: str = "educational", user_topic: str = None) -> CreativeBreakthrough:
        """
        Crea contenido ESPECÍFICO basado en el tema del usuario.
        NO más conceptos abstractos - solo contenido relevante al tema.

        Args:
            user_context: Contexto del usuario (negocio, industria, etc.)
            content_type: Tipo de contenido solicitado
            user_topic: TEMA ESPECÍFICO del usuario (PRIORIDAD MÁXIMA)

        Returns:
            CreativeBreakthrough enfocado en el tema específico del usuario
        """
        try:
            # 🎯 PRIORIDAD: Usar el tema específico del usuario
            actual_topic = user_topic or user_context.get('extracted_topic') or user_context.get('topics', ['marketing digital'])[0]
            business_name = user_context.get('businessName', 'Mi Marca')

            logger.info(f"🎯 Creative Genius focusing on SPECIFIC topic: '{actual_topic}' for {business_name}")

            # 🎨 ENFOQUE DIRECTO: Crear contenido específico para el tema
            visual_concept = await self._generate_topic_specific_visual(actual_topic, user_context)
            logger.info(f"🎨 Topic-specific visual: {visual_concept[:100]}...")

            # 🎯 HOOK ESPECÍFICO: Basado en el tema real
            hook = await self._generate_topic_specific_hook(actual_topic, content_type, user_context)
            logger.info(f"🎯 Topic-specific hook: {hook}")

            # 🖼️ PROMPT DIRECTO: Para generar imágenes del tema específico
            ideogram_prompt = await self._create_topic_specific_prompt(actual_topic, visual_concept)
            logger.info(f"🖼️ Topic-specific Ideogram prompt created")

            # ⚡ SIMPLIFICADO: Menos análisis abstracto, más enfoque directo
            content_angle = f"contenido_sobre_{actual_topic.lower().replace(' ', '_')}"
            viral_score = 7.5  # Good score for topic-specific content

            # 8. Crear el breakthrough enfocado en el tema
            breakthrough = CreativeBreakthrough(
                visual_concept=visual_concept,
                hook=hook,
                content_angle=content_angle,
                ideogram_prompt=ideogram_prompt,
                emotional_journey={"hook_emotion": "interest", "middle_emotion": "engagement", "end_emotion": "action"},
                viral_score=viral_score,
                why_its_brilliant=f"Contenido específico y relevante sobre {actual_topic} que la audiencia realmente busca",
                psychology_target="interest_in_topic",
                art_direction="topic_relevant"
            )

            logger.info(f"🎉 Topic-specific breakthrough completed for: {actual_topic}")
            return breakthrough

        except Exception as e:
            logger.error(f"❌ Error in topic-specific generation: {e}")
            # Fallback que mantiene el enfoque en el tema
            return await self._generate_topic_fallback(user_topic or "marketing digital", user_context)
    
    async def _analyze_deep_psychology(self, user_context: Dict[str, Any], content_type: str) -> Dict[str, Any]:
        """
        Análisis psicológico profundo de la audiencia.
        No solo demográficos, sino emociones, miedos, deseos profundos.
        """
        if not self.model:
            return self._get_fallback_psychology(user_context, content_type)
        
        try:
            business_name = user_context.get("businessName", "Business")
            industry = user_context.get("industry", "general")
            
            # Define different approaches based on content type
            if content_type == "educational":
                psychology_prompt = f"""
Analiza la psicología para crear contenido EDUCATIVO que enseñe algo útil.

CONTEXTO:
- Industria: {industry}
- Tipo: EDUCATIVO (enseñar, informar, educar)

ENFOQUE EDUCATIVO:
1. ¿Qué puede APRENDER la audiencia sobre {industry}?
2. ¿Qué conocimiento útil pueden obtener?
3. ¿Qué errores comunes cometen en {industry}?
4. ¿Qué datos/hechos les serían útiles?
5. ¿Cómo habla la gente cuando aprende algo nuevo?

EJEMPLOS EDUCATIVOS:
- Mascotas: "5 ingredientes tóxicos en comida para perros", "Cómo leer etiquetas de alimento"
- Fitness: "Por qué el cardio no quema grasa", "Cómo calcular tus macros"
- Tech: "3 métricas que importan más que las ventas", "Por qué el SEO no funciona solo"

Responde en JSON:
{{
    "core_emotion": "curiosidad_por_aprender",
    "learning_focus": ["dato_útil1", "dato_útil2", "dato_útil3"],
    "audience_language": "no sabía esto, aprendí algo nuevo, esto explica mucho",
    "visual_triggers": "infografías, datos, comparaciones, explicaciones visuales",
    "scroll_stopper": "información_valiosa_que_no_conocían",
    "content_angle": "enseñanza_práctica_y_útil",
    "emotional_journey": {{
        "hook_emotion": "curiosidad_por_saber",
        "middle_emotion": "comprensión_del_tema",
        "end_emotion": "sensación_de_haber_aprendido"
    }}
}}
"""
            elif content_type == "motivational":
                psychology_prompt = f"""
Analiza la psicología para crear contenido MOTIVACIONAL que inspire.

CONTEXTO:
- Industria: {industry}
- Tipo: MOTIVACIONAL (inspirar, motivar, emocionar)

ENFOQUE MOTIVACIONAL:
1. ¿Qué historias de superación existen en {industry}?
2. ¿Qué transformaciones inspiran a la gente?
3. ¿Qué miedos/obstáculos enfrentan?
4. ¿Qué logros celebran en {industry}?
5. ¿Cómo habla la gente cuando está inspirada?

EJEMPLOS MOTIVACIONALES:
- Mascotas: "Mi perro estaba enfermo, así lo salvé", "De perro abandonado a campeón"
- Fitness: "Perdí 30 kilos después de depresión", "De sedentario a maratonista"
- Tech: "De empleado a CEO en 3 años", "Cómo salvé mi negocio de la quiebra"

Responde en JSON:
{{
    "core_emotion": "inspiración_y_motivación",
    "inspiration_focus": ["historia_real1", "transformación2", "superación3"],
    "audience_language": "me inspira, yo también puedo, qué motivador",
    "visual_triggers": "antes/después, transformaciones, historias reales, logros",
    "scroll_stopper": "historia_inspiradora_real",
    "content_angle": "historia_de_superación_auténtica",
    "emotional_journey": {{
        "hook_emotion": "conexión_emocional",
        "middle_emotion": "inspiración_creciente",
        "end_emotion": "motivación_para_actuar"
    }}
}}
"""
            else:  # balanced
                psychology_prompt = f"""
Analiza la psicología para crear contenido BALANCEADO que eduque e inspire.

CONTEXTO:
- Industria: {industry}
- Tipo: BALANCEADO (combinar educación + motivación)

ENFOQUE BALANCEADO:
1. ¿Qué lecciones prácticas se pueden enseñar con historias?
2. ¿Qué experiencias reales contienen aprendizajes?
3. ¿Cómo combinar datos útiles con inspiración?
4. ¿Qué errores enseñan lecciones valiosas?
5. ¿Cómo habla la gente cuando aprende de experiencias?

EJEMPLOS BALANCEADOS:
- Mascotas: "3 errores que cometí con mi perro (y qué aprendí)"
- Fitness: "Mi fracaso en el gym me enseñó estas 4 lecciones"
- Tech: "Perdí $10K en mi startup, pero aprendí esto"

Responde en JSON:
{{
    "core_emotion": "aprendizaje_a_través_de_experiencia",
    "balanced_focus": ["lección_práctica1", "experiencia_real2", "aprendizaje3"],
    "audience_language": "esto me pasó también, buena lección, experiencia valiosa",
    "visual_triggers": "experiencias reales, lecciones visuales, historias con datos",
    "scroll_stopper": "experiencia_real_con_aprendizaje",
    "content_angle": "lecciones_de_experiencias_reales",
    "emotional_journey": {{
        "hook_emotion": "identificación_con_experiencia",
        "middle_emotion": "comprensión_de_lecciones",
        "end_emotion": "aplicación_de_aprendizajes"
    }}
}}
"""
            
            response = self.model.generate_content(psychology_prompt)
            
            if response and response.text:
                try:
                    psychology_data = json.loads(response.text.strip())
                    logger.info("✅ Deep psychology analysis successful")
                    return psychology_data
                except json.JSONDecodeError:
                    logger.warning("Failed to parse psychology JSON, using fallback")
                    return self._get_fallback_psychology(user_context, content_type)
            else:
                return self._get_fallback_psychology(user_context, content_type)
                
        except Exception as e:
            logger.error(f"Error in deep psychology analysis: {e}")
            return self._get_fallback_psychology(user_context, content_type)
    
    def _get_fallback_psychology(self, user_context: Dict[str, Any], content_type: str) -> Dict[str, Any]:
        """Psicología fallback diferenciada por tipo de contenido"""
        industry = user_context.get("industry", "general").lower()

        # Different psychology based on content type
        if content_type == "educational":
            return {
                "core_emotion": "curiosidad_por_aprender",
                "learning_focus": ["datos_útiles", "información_práctica", "conocimiento_valioso"],
                "audience_language": "no sabía esto, aprendí algo nuevo, esto explica mucho",
                "visual_triggers": "infografías, datos, comparaciones, explicaciones visuales",
                "scroll_stopper": "información_valiosa_que_no_conocían",
                "content_angle": "enseñanza_práctica_y_útil"
            }
        elif content_type == "motivational":
            return {
                "core_emotion": "inspiración_y_motivación",
                "inspiration_focus": ["historias_reales", "transformaciones", "superación"],
                "audience_language": "me inspira, yo también puedo, qué motivador",
                "visual_triggers": "antes/después, transformaciones, historias reales, logros",
                "scroll_stopper": "historia_inspiradora_real",
                "content_angle": "historia_de_superación_auténtica"
            }
        else:  # balanced
            return {
                "core_emotion": "aprendizaje_a_través_de_experiencia",
                "balanced_focus": ["lecciones_prácticas", "experiencias_reales", "aprendizajes"],
                "audience_language": "esto me pasó también, buena lección, experiencia valiosa",
                "visual_triggers": "experiencias reales, lecciones visuales, historias con datos",
                "scroll_stopper": "experiencia_real_con_aprendizaje",
                "content_angle": "lecciones_de_experiencias_reales"
            }

    async def _generate_unlimited_visual_concept(self, psychology: Dict[str, Any], user_context: Dict[str, Any]) -> str:
        """
        Genera conceptos visuales SIN LÍMITES usando todo el potencial de Ideogram.
        Piensa en: 3D, cómics, arte conceptual, cinematográfico, etc.
        """
        if not self.model:
            return self._get_fallback_visual_concept(psychology, user_context)

        try:
            core_emotion = psychology.get("core_emotion", "curiosity")
            visual_triggers = psychology.get("visual_triggers", [])
            art_direction = psychology.get("art_direction", "cinematic")
            business_name = user_context.get("businessName", "Brand")
            industry = user_context.get("industry", "general")

            unlimited_prompt = f"""
Crea un concepto visual ESTÁTICO chingón para {business_name}.

CONTEXTO DEL PRODUCTO:
- Negocio: {business_name}
- Industria: {industry}
- Emoción objetivo: {core_emotion}
- Triggers visuales: {', '.join(visual_triggers)}

IMPORTANTE:
- IMAGEN ESTÁTICA sobre {business_name}
- Debe mostrar el producto/servicio o sus beneficios
- Que la gente quiera verlo y guardarlo

EJEMPLOS CORRECTOS POR INDUSTRIA:

SUPLEMENTOS MASCOTAS:
- "Split screen: perro cansado vs perro súper energético después de suplementos, mismo dueño, lighting dramático"
- "Fotografía conceptual: Perro saltando con energía, suplementos flotando artísticamente alrededor"
- "Antes/después: Perro con pelaje opaco vs brillante, composición cinematográfica"

FITNESS/GYM:
- "Split screen dramático: persona antes vs después del entrenamiento, mismo fondo de gym"
- "Fotografía conceptual: Silueta transformándose en versión fit, efectos de luz épicos"
- "Composición artística: Pesas y resultados de transformación corporal"

TECH/NEGOCIOS:
- "Split screen: negocio pequeño vs próspero, misma persona, contraste visual potente"
- "Fotografía conceptual: Gráficas de crecimiento flotando sobre escritorio moderno"
- "Antes/después: Oficina vacía vs llena de éxito, lighting cinematográfico"

REGLAS:
❌ NO filosofía abstracta sin relación al producto
❌ NO "cápsulas futuristas en el espacio"
❌ NO conceptos que no tengan que ver con {business_name}
✅ SÍ sobre el producto/servicio real
✅ SÍ beneficios visibles del producto
✅ SÍ que la gente entienda de qué se trata

Crea UN concepto visual SOBRE {business_name} (máximo 2 líneas):
"""

            response = self.model.generate_content(unlimited_prompt)

            if response and response.text:
                concept = response.text.strip()
                logger.info(f"✅ Unlimited visual concept generated")
                return concept
            else:
                return self._get_fallback_visual_concept(psychology, user_context)

        except Exception as e:
            logger.error(f"Error generating unlimited visual concept: {e}")
            return self._get_fallback_visual_concept(psychology, user_context)

    def _get_fallback_visual_concept(self, psychology: Dict[str, Any], user_context: Dict[str, Any]) -> str:
        """Conceptos visuales fallback creativos para IMÁGENES ESTÁTICAS"""
        core_emotion = psychology.get("core_emotion", "curiosity")

        static_creative_concepts = [
            "Split screen dramático con contraste emocional potente y lighting cinematográfico",
            "Render 3D estático: producto flotando en ambiente surrealista con partículas doradas",
            "Arte conceptual minimalista con composición impactante y colores vibrantes",
            "Fotografía conceptual estilo poster de película con elementos 3D integrados",
            "Ilustración digital épica con efectos visuales estáticos pero poderosos",
            "Composición surrealista con elementos flotantes y profundidad cinematográfica",
            "Diseño gráfico estilo revista de alta gama con tipografía integrada artísticamente"
        ]

        return random.choice(static_creative_concepts)

    async def _revolutionize_content_angle(self, psychology: Dict[str, Any], content_type: str) -> str:
        """
        Genera ángulos de contenido RELEVANTES y específicos para el tipo solicitado.
        """
        # Simply return the content type - let the LLM be creative with the actual content
        return content_type

    def _get_fallback_revolutionary_angle(self, psychology: Dict[str, Any], content_type: str) -> str:
        """Fallback simple que devuelve el tipo de contenido"""
        return content_type

    async def _generate_unburned_hook(self, psychology: Dict[str, Any], content_angle: str, user_context: Dict[str, Any]) -> str:
        """
        Genera hooks diferenciados por tipo de contenido.
        NO sobre vender productos, sino sobre VALOR REAL.
        """
        if not self.model:
            return self._get_fallback_unburned_hook(psychology, user_context)

        try:
            core_emotion = psychology.get("core_emotion", "curiosity")
            content_angle = psychology.get("content_angle", "authentic")
            emotional_language = psychology.get("audience_language", "natural")
            industry = user_context.get("industry", "general")

            # Different hook approaches based on content type
            if "educativo" in content_angle or "enseñanza" in content_angle:
                hook_prompt = f"""
Crea un HOOK EDUCATIVO que prometa aprendizaje útil.

CONTEXTO:
- Industria: {industry}
- Emoción: {core_emotion}
- Lenguaje: {emotional_language}

HOOKS EDUCATIVOS (EJEMPLOS):
✅ "Esto no sabía sobre perros"
✅ "Error que cometes en el gym"
✅ "Dato que cambió mi perspectiva"
✅ "Lo que nadie te dice"

REGLAS:
❌ NO sobre vender productos
✅ SÍ sobre enseñar algo útil
✅ Máximo 6 palabras
✅ Promete conocimiento valioso

Crea UN hook educativo (máximo 6 palabras):
"""
            elif "motivacional" in content_angle or "inspiración" in content_angle:
                hook_prompt = f"""
Crea un HOOK MOTIVACIONAL que inspire con historia real.

CONTEXTO:
- Industria: {industry}
- Emoción: {core_emotion}
- Lenguaje: {emotional_language}

HOOKS MOTIVACIONALES (EJEMPLOS):
✅ "Mi transformación en 6 meses"
✅ "Cómo superé mi mayor miedo"
✅ "De fracaso a éxito"
✅ "Mi historia te inspirará"

REGLAS:
❌ NO sobre vender productos
✅ SÍ sobre historias reales
✅ Máximo 6 palabras
✅ Promete inspiración auténtica

Crea UN hook motivacional (máximo 6 palabras):
"""
            else:  # balanced
                hook_prompt = f"""
Crea un HOOK BALANCEADO que combine experiencia + aprendizaje.

CONTEXTO:
- Industria: {industry}
- Emoción: {core_emotion}
- Lenguaje: {emotional_language}

HOOKS BALANCEADOS (EJEMPLOS):
✅ "Mi error me enseñó esto"
✅ "Lección que aprendí tarde"
✅ "Experiencia que cambió todo"
✅ "Lo que descubrí haciendo esto"

REGLAS:
❌ NO sobre vender productos
✅ SÍ sobre experiencias + lecciones
✅ Máximo 6 palabras
✅ Promete aprendizaje de experiencia

Crea UN hook balanceado (máximo 6 palabras):
"""

            response = self.model.generate_content(hook_prompt)

            if response and response.text:
                hook = response.text.strip().replace('"', '').replace("'", "")

                # Ensure it's not too long
                words = hook.split()
                if len(words) > 6:
                    hook = ' '.join(words[:6])

                logger.info(f"✅ Content-type specific hook generated")
                return hook
            else:
                return self._get_fallback_unburned_hook(psychology, user_context)

        except Exception as e:
            logger.error(f"Error generating unburned hook: {e}")
            return self._get_fallback_unburned_hook(psychology, user_context)

    def _get_fallback_unburned_hook(self, psychology: Dict[str, Any], user_context: Dict[str, Any]) -> str:
        """Hooks fallback diferenciados por tipo de contenido"""
        content_angle = psychology.get("content_angle", "balanced")

        # Different hooks based on content type
        if "educativo" in content_angle or "enseñanza" in content_angle:
            educational_hooks = [
                "Esto no sabía antes",
                "Error que todos cometen",
                "Dato que cambió todo",
                "Lo que nadie te dice",
                "Descubrí esto tarde"
            ]
            return random.choice(educational_hooks)
        elif "motivacional" in content_angle or "inspiración" in content_angle:
            motivational_hooks = [
                "Mi transformación real",
                "Cómo superé esto",
                "De fracaso a éxito",
                "Mi historia te inspirará",
                "Cambié mi vida así"
            ]
            return random.choice(motivational_hooks)
        else:  # balanced
            balanced_hooks = [
                "Mi error me enseñó esto",
                "Lección que aprendí tarde",
                "Experiencia que cambió todo",
                "Lo que descubrí haciendo esto",
                "Mi fracaso fue mi maestro"
            ]
            return random.choice(balanced_hooks)

    async def _create_ideogram_masterpiece_prompt(self, visual_concept: str, hook: str) -> str:
        """
        🎨 TWO-LAYER SYSTEM: Crea un prompt LIMPIO para Ideogram (sin texto).
        El texto se agregará después con el TextOverlayService.
        """
        try:
            # 🎨 TWO-LAYER SYSTEM: Generate CLEAN background prompt (NO TEXT)
            # Remove any text-related instructions from visual concept
            clean_visual_concept = visual_concept.replace('with text', '').replace('text overlay', '').replace('typography', '')
            clean_visual_concept = clean_visual_concept.replace('that reads', '').replace('lettering', '').replace('words', '')

            # Create clean background prompt for two-layer system
            masterpiece_prompt = f'{clean_visual_concept}, clean design, no text, no typography, no words, professional background for social media'

            logger.info(f"✅ Clean background prompt created for two-layer system")
            return masterpiece_prompt

        except Exception as e:
            logger.error(f"Error creating clean background prompt: {e}")
            return f'A stunning creative design, clean background, no text, professional social media background'

    def _optimize_text_for_ideogram(self, text: str) -> str:
        """Optimiza texto para mejor renderizado en Ideogram"""
        import re

        # Remove emojis
        emoji_pattern = re.compile("["
                                   u"\U0001F600-\U0001F64F"  # emoticons
                                   u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                   u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                   u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
                                   u"\U00002702-\U000027B0"
                                   u"\U000024C2-\U0001F251"
                                   "]+", flags=re.UNICODE)
        text = emoji_pattern.sub('', text)

        # Remove hashtags
        text = re.sub(r'#\w+', '', text)

        # Limit length for better rendering
        if len(text) > 50:
            words = text.split()
            if len(words) > 6:
                text = ' '.join(words[:6])

        return text.strip()

    async def _calculate_viral_potential(self, hook: str, visual_concept: str, content_angle: str) -> float:
        """
        Calcula el potencial viral del concepto creativo.
        Solo aprueba conceptos con alto potencial.
        """
        try:
            score = 0.0

            # Hook analysis
            if any(burned in hook.lower() for burned in ["3 señales", "5 tips", "secretos de", "cómo conseguir"]):
                score -= 2.0  # Penalizar hooks quemados

            if any(fresh in hook.lower() for fresh in ["cambió", "descubrí", "plot twist", "me enseñó"]):
                score += 2.0  # Premiar hooks frescos

            # Visual concept analysis
            if any(creative in visual_concept.lower() for creative in ["3d", "cinematográfico", "épico", "render", "arte"]):
                score += 2.0  # Premiar conceptos visuales creativos

            if any(boring in visual_concept.lower() for boring in ["persona sonriendo", "laptop", "oficina"]):
                score -= 2.0  # Penalizar conceptos aburridos

            # Content angle analysis
            if any(revolutionary in content_angle.lower() for revolutionary in ["pov", "plot twist", "experimento", "confesión"]):
                score += 2.0  # Premiar ángulos revolucionarios

            # Base score
            score += 5.0

            # Ensure score is between 0-10
            score = max(0.0, min(10.0, score))

            return score

        except Exception as e:
            logger.error(f"Error calculating viral potential: {e}")
            return 5.0  # Default neutral score

    async def _explain_brilliance(self, hook: str, visual_concept: str, psychology: Dict[str, Any]) -> str:
        """Explica por qué el concepto es brillante"""
        try:
            core_emotion = psychology.get("core_emotion", "curiosity")

            brilliance = f"Combina {core_emotion} + concepto visual impactante + hook no quemado. "
            brilliance += f"El visual '{visual_concept[:50]}...' genera impacto inmediato, "
            brilliance += f"mientras que '{hook}' crea curiosidad genuina sin usar fórmulas gastadas."

            return brilliance

        except Exception as e:
            logger.error(f"Error explaining brilliance: {e}")
            return "Concepto creativo que combina impacto visual con hook auténtico"

    async def _generate_fallback_creative_concept(self, user_context: Dict[str, Any], content_type: str) -> CreativeBreakthrough:
        """Concepto creativo fallback cuando todo falla"""
        business_name = user_context.get("businessName", "Brand")

        return CreativeBreakthrough(
            visual_concept="Arte conceptual moderno con contraste emocional impactante",
            hook="Esto cambió mi perspectiva completamente",
            content_angle="revelación_personal_auténtica",
            ideogram_prompt=f'Modern conceptual art with emotional contrast, text that reads: "Esto cambió mi perspectiva completamente"',
            emotional_journey={"hook_emotion": "curiosity", "middle_emotion": "revelation", "end_emotion": "inspiration"},
            viral_score=7.0,
            why_its_brilliant="Concepto visual impactante + hook auténtico sin fórmulas quemadas",
            psychology_target="curiosidad_genuina",
            art_direction="conceptual_moderno"
        )

    async def _generate_topic_specific_visual(self, topic: str, user_context: Dict[str, Any]) -> str:
        """
        Genera un concepto visual específico para el tema del usuario.
        NO conceptos abstractos - solo visuals relevantes al tema.
        """
        if not self.model:
            return self._get_fallback_topic_visual(topic)

        try:
            business_name = user_context.get("businessName", "Mi Marca")

            topic_visual_prompt = f"""
Eres un DIRECTOR CREATIVO de agencia top. Crea un concepto visual ESPECÍFICO y CREATIVO para "{topic}".

TEMA: {topic}
MARCA: {business_name}

PIENSA COMO DIRECTOR CREATIVO:
1. ¿Qué industria/nicho representa este tema?
2. ¿Qué estilo visual sería más impactante y apropiado?
3. ¿Qué elementos específicos del tema debo destacar?
4. ¿Cómo puedo hacer que sea visualmente atractivo y profesional?

EJEMPLOS DE PENSAMIENTO CREATIVO:
- Espiritualidad → Ambiente místico con elementos naturales, luz suave, colores tierra
- Abogados → Ambiente corporativo serio, oficina ejecutiva, elementos legales elegantes
- Mascotas → Escena divertida y colorida, ambiente amigable, elementos juguetones
- Belleza → Estética elegante y glamorosa, iluminación suave, elementos premium
- Tecnología → Diseño futurista y limpio, elementos tech, colores modernos

GENERA un concepto visual específico que capture la esencia del tema con el estilo apropiado.

Describe el concepto en 1-2 líneas (máximo):
"""

            response = self.model.generate_content(topic_visual_prompt)

            if response and response.text:
                concept = response.text.strip()
                logger.info(f"✅ Topic-specific visual concept generated for: {topic}")
                return concept
            else:
                return self._get_fallback_topic_visual(topic)

        except Exception as e:
            logger.error(f"Error generating topic-specific visual: {e}")
            return self._get_fallback_topic_visual(topic)

    def _get_fallback_topic_visual(self, topic: str) -> str:
        """Fallback visual concept based on topic"""
        topic_lower = topic.lower()

        if any(word in topic_lower for word in ["perro", "dog", "mascota", "pet"]):
            return f"Imagen atractiva relacionada con perros y mascotas, mostrando {topic}"
        elif any(word in topic_lower for word in ["dormir", "sueño", "sleep", "descanso"]):
            return f"Imagen relajante sobre sueño y descanso, relacionada con {topic}"
        elif any(word in topic_lower for word in ["fitness", "ejercicio", "gym", "deporte"]):
            return f"Imagen energética sobre fitness y ejercicio, mostrando {topic}"
        elif any(word in topic_lower for word in ["comida", "food", "cocina", "receta"]):
            return f"Imagen apetitosa de comida relacionada con {topic}"
        else:
            return f"Imagen profesional y atractiva relacionada directamente con {topic}"

    async def _generate_topic_specific_hook(self, topic: str, content_type: str, user_context: Dict[str, Any]) -> str:
        """
        Genera un hook específico para el tema del usuario.
        """
        if not self.model:
            return self._get_fallback_topic_hook(topic, content_type)

        try:
            hook_prompt = f"""
Crea un HOOK específico para contenido sobre "{topic}".

TEMA: {topic}
TIPO: {content_type}

INSTRUCCIONES:
- El hook DEBE mencionar o relacionarse con "{topic}"
- Máximo 6 palabras
- Debe generar curiosidad sobre el tema específico
- NO uses frases genéricas

Ejemplos:
- Para "perros": "Lo que tu perro necesita"
- Para "dormir bien": "El secreto para dormir mejor"
- Para "fitness": "Ejercicio que realmente funciona"

Crea UN hook específico para "{topic}":
"""

            response = self.model.generate_content(hook_prompt)

            if response and response.text:
                hook = response.text.strip().replace('"', '').replace("'", "")
                if len(hook.split()) <= 6:
                    logger.info(f"✅ Topic-specific hook generated: {hook}")
                    return hook
                else:
                    return self._get_fallback_topic_hook(topic, content_type)
            else:
                return self._get_fallback_topic_hook(topic, content_type)

        except Exception as e:
            logger.error(f"Error generating topic-specific hook: {e}")
            return self._get_fallback_topic_hook(topic, content_type)

    def _get_fallback_topic_hook(self, topic: str, content_type: str) -> str:
        """Fallback hook based on topic"""
        topic_lower = topic.lower()

        if any(word in topic_lower for word in ["perro", "dog", "mascota", "pet"]):
            return "Lo que tu perro necesita"
        elif any(word in topic_lower for word in ["dormir", "sueño", "sleep", "descanso"]):
            return "El secreto para dormir mejor"
        elif any(word in topic_lower for word in ["fitness", "ejercicio", "gym", "deporte"]):
            return "Ejercicio que realmente funciona"
        elif any(word in topic_lower for word in ["comida", "food", "cocina", "receta"]):
            return "Receta que debes probar"
        else:
            return f"Todo sobre {topic}"

    async def _create_topic_specific_prompt(self, topic: str, visual_concept: str) -> str:
        """
        Crea un prompt específico para Ideogram basado en el tema del usuario.
        PIENSA COMO DIRECTOR CREATIVO para decidir el estilo visual apropiado.
        CRÍTICO: Genera imágenes LIMPIAS sin texto para Polotno editor.
        """
        try:
            if not self.model:
                return self._get_fallback_creative_prompt(topic)

            # 🎨 PROMPT PARA QUE LA IA PIENSE COMO DIRECTOR CREATIVO
            creative_director_prompt = f"""
Eres un DIRECTOR CREATIVO de una agencia top. Analiza este tema y decide el estilo visual más apropiado:

TEMA: "{topic}"
CONCEPTO VISUAL: {visual_concept}

PIENSA COMO DIRECTOR CREATIVO:
1. ¿Qué industria/nicho es este tema?
2. ¿Qué estilo visual sería más efectivo? (místico, corporativo, divertido, elegante, artístico, minimalista, bold, etc.)
3. ¿Qué colores y ambiente funcionarían mejor?
4. ¿Qué elementos visuales específicos necesita?

EJEMPLOS DE PENSAMIENTO CREATIVO:
- Espiritualidad → Estilo místico, colores suaves, elementos orgánicos, luz dorada
- Abogados → Estilo corporativo serio, colores sobrios, ambiente profesional
- Mascotas → Estilo divertido, colorido, amigable, juguetón
- Belleza → Estilo elegante, glamoroso, iluminación suave
- Arte → Estilo artístico, creativo, composición única
- Tecnología → Estilo futurista, limpio, colores tech

GENERA UN PROMPT PARA IDEOGRAM que capture:
- El estilo visual apropiado para este tema específico
- Calidad de agencia profesional
- Sin texto quemado en la imagen
- Optimizado para overlay de texto

RESPONDE SOLO CON EL PROMPT (máximo 200 palabras):
"""

            response = self.model.generate_content(creative_director_prompt)

            if response and response.text:
                ai_prompt = response.text.strip()

                # 🚫 CRÍTICO: Asegurar especificaciones para POLOTNO
                final_prompt = f"{ai_prompt}, clean background, no text, no typography, no words, no letters, no captions, text-free, clean design optimized for text overlay, social media background, professional agency quality, award-winning composition, 8K quality"

                logger.info(f"🎨 AI Creative Director prompt generated for: {topic}")
                return final_prompt
            else:
                return self._get_fallback_creative_prompt(topic)

        except Exception as e:
            logger.error(f"Error creating AI creative prompt: {e}")
            return self._get_fallback_creative_prompt(topic)

    def _get_fallback_creative_prompt(self, topic: str) -> str:
        """Fallback creativo que mantiene calidad de agencia"""
        return f"Professional agency-quality image about {topic}, sophisticated visual style appropriate for the subject matter, clean background, no text, no typography, optimized for text overlay, award-winning composition, 8K quality"

    async def _generate_topic_fallback(self, topic: str, user_context: Dict[str, Any]) -> CreativeBreakthrough:
        """
        Fallback que mantiene el enfoque en el tema específico.
        """
        visual_concept = self._get_fallback_topic_visual(topic)
        hook = self._get_fallback_topic_hook(topic, "educational")
        ideogram_prompt = await self._create_topic_specific_prompt(topic, visual_concept)

        return CreativeBreakthrough(
            visual_concept=visual_concept,
            hook=hook,
            content_angle=f"contenido_sobre_{topic.lower().replace(' ', '_')}",
            ideogram_prompt=ideogram_prompt,
            emotional_journey={"hook_emotion": "interest", "middle_emotion": "engagement", "end_emotion": "action"},
            viral_score=7.0,
            why_its_brilliant=f"Contenido específico y relevante sobre {topic}",
            psychology_target="interest_in_topic",
            art_direction="topic_relevant"
        )
