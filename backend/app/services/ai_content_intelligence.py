"""
AI-powered content intelligence service.
Handles natural language processing, context analysis, and strategic content planning.
"""

import logging
import json
from typing import Dict, Any

from app.models.post_models import ExtractedInfo, ContextAnalysis, ContentStrategy

logger = logging.getLogger(__name__)


class AIContentIntelligence:
    """Service for AI-powered content analysis and strategy generation."""
    
    def __init__(self):
        self.model = None
        self._initialize_ai()
    
    def _initialize_ai(self):
        """Initialize AI model for content intelligence."""
        try:
            import google.generativeai as genai
            from app.core.config import settings
            
            if settings.GEMINI_API_KEY:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ AI Content Intelligence initialized successfully")
            else:
                logger.warning("⚠️ GEMINI_API_KEY not configured - using fallback mode")
        except Exception as e:
            logger.error(f"❌ Failed to initialize AI Content Intelligence: {e}")
    
    async def process_natural_description(self, description: str) -> Dict[str, Any]:
        """
        Process natural user descriptions and extract key information.
        Handles inputs like: "mi marca es de suplementos para perro, se llama wouf y vendo muchos suplementos"
        
        Args:
            description: Natural description from user
            
        Returns:
            Extracted information: topic, business_name, industry, etc.
        """
        if not self.model:
            return self._get_fallback_extraction(description)
        
        try:
            extraction_prompt = f"""
Analiza esta descripción natural y extrae la información clave:

DESCRIPCIÓN: "{description}"

Extrae:
1. TEMA/NICHO principal (qué tipo de contenido necesita)
2. NOMBRE DE LA MARCA (si se menciona)
3. INDUSTRIA/SECTOR
4. PRODUCTOS/SERVICIOS principales
5. AUDIENCIA objetivo

EJEMPLOS:
- "mi marca es de suplementos para perro, se llama wouf" → Tema: "suplementos para perros", Marca: "Wouf", Industria: "Pet Care"
- "soy chef y hago cursos de cocina italiana" → Tema: "cursos de cocina italiana", Industria: "Food/Education"
- "vendo ropa fitness para mujeres" → Tema: "ropa fitness femenina", Industria: "Fashion/Fitness"

Responde SOLO con JSON válido:
{{
  "tema_principal": "tema específico extraído",
  "nombre_marca": "nombre de la marca si se menciona",
  "industria": "sector/industria identificada",
  "productos_servicios": ["producto1", "producto2"],
  "audiencia_objetivo": "audiencia identificada",
  "palabras_clave": ["keyword1", "keyword2", "keyword3"]
}}
"""
            
            response = self.model.generate_content(extraction_prompt)
            
            if response and response.text:
                try:
                    # Clean the response
                    clean_response = response.text.strip()
                    if '```json' in clean_response:
                        json_match = clean_response.split('```json')[1].split('```')[0]
                    elif '```' in clean_response:
                        json_match = clean_response.split('```')[1]
                    else:
                        json_match = clean_response
                    
                    extracted_info = json.loads(json_match.strip())
                    logger.info(f"✅ Successfully extracted info from natural description")
                    return extracted_info
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse extraction JSON: {e}")
                    return self._get_fallback_extraction(description)
            else:
                return self._get_fallback_extraction(description)
                
        except Exception as e:
            logger.error(f"Error processing natural description: {e}")
            return self._get_fallback_extraction(description)
    
    def _get_fallback_extraction(self, description: str) -> Dict[str, Any]:
        """Fallback extraction when AI fails"""
        # Simple keyword-based extraction
        desc_lower = description.lower()
        
        # Try to extract business name
        business_name = "Mi Marca"
        if "se llama" in desc_lower:
            try:
                name_part = desc_lower.split("se llama")[1].split()[0]
                business_name = name_part.title()
            except:
                pass
        
        # Try to extract main topic
        topic = description
        if "es de" in desc_lower:
            try:
                topic_part = desc_lower.split("es de")[1].split(",")[0].strip()
                topic = topic_part
            except:
                pass
        
        return {
            "tema_principal": topic,
            "nombre_marca": business_name,
            "industria": "general",
            "productos_servicios": [topic],
            "audiencia_objetivo": "audiencia general",
            "palabras_clave": [topic]
        }
    
    async def analyze_topic_context(self, topic: str, brand_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the topic context using AI to understand what type of content is appropriate.
        This replaces hardcoded templates with intelligent context analysis.
        
        Args:
            topic: User's topic (e.g., "recetas veganas", "fitness", "gaming")
            brand_info: Brand information
            
        Returns:
            Context analysis with content recommendations
        """
        if not self.model:
            return self._get_fallback_context(topic)
        
        try:
            analysis_prompt = f"""
Analiza este tema para crear contenido de redes sociales APROPIADO y ÚNICO:

TEMA: "{topic}"
MARCA: {brand_info.get('businessName', 'Mi Marca')}

Determina:
1. NICHO: ¿Qué tipo de contenido necesita este tema?
2. AUDIENCIA: ¿Quién consume este contenido?
3. ESTILO VISUAL: ¿Qué tipo de imágenes funcionan mejor?
4. TONO: ¿Cómo debe sonar el contenido?
5. FORMATOS: ¿Qué tipos de posts funcionan en este nicho?

EJEMPLOS DE ANÁLISIS:
- "Recetas veganas" → Nicho: Food/Lifestyle, Visual: Fotos de comida, Tono: Inspirador/Saludable
- "Gaming" → Nicho: Entertainment, Visual: Screenshots/Memes, Tono: Casual/Divertido  
- "Fitness" → Nicho: Health/Lifestyle, Visual: Transformaciones/Rutinas, Tono: Motivacional
- "Marketing digital" → Nicho: Business, Visual: Infografías/Stats, Tono: Profesional

Responde SOLO con JSON válido:
{{
  "nicho": "categoría del contenido",
  "audiencia": "descripción de la audiencia objetivo",
  "estilo_visual": "tipo de imágenes que funcionan mejor",
  "tono": "tono de comunicación apropiado",
  "formatos_populares": ["formato1", "formato2", "formato3"],
  "palabras_clave": ["keyword1", "keyword2", "keyword3"],
  "tipo_contenido": "educational/entertainment/lifestyle/business",
  "nivel_formalidad": "casual/semi-formal/formal"
}}
"""
            
            response = self.model.generate_content(analysis_prompt)
            
            if response and response.text:
                try:
                    # Clean the response
                    clean_response = response.text.strip()
                    json_match = clean_response
                    if '```json' in clean_response:
                        json_match = clean_response.split('```json')[1].split('```')[0]
                    elif '```' in clean_response:
                        json_match = clean_response.split('```')[1]
                    
                    context = json.loads(json_match.strip())
                    logger.info(f"✅ Context analysis successful for topic: {topic}")
                    return context
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse context analysis JSON: {e}")
                    return self._get_fallback_context(topic)
            else:
                return self._get_fallback_context(topic)
                
        except Exception as e:
            logger.error(f"Error in context analysis: {e}")
            return self._get_fallback_context(topic)
    
    def _get_fallback_context(self, topic: str) -> Dict[str, Any]:
        """Fallback context analysis when AI fails"""
        return {
            "nicho": topic.lower(),  # Use actual topic as niche
            "audiencia": f"personas interesadas en {topic.lower()}",
            "estilo_visual": "relacionado con el tema",
            "tono": "profesional y amigable",
            "formatos_populares": ["consejos", "insights", "inspiración"],
            "palabras_clave": [topic.lower()],
            "tipo_contenido": "educational",
            "nivel_formalidad": "semi-formal",
            "tema_especifico": topic  # Preserve exact user topic
        }

    async def analyze_topic_context_simple(self, topic: str, brand_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Simplified topic analysis that focuses on the user's actual topic.
        No complex psychology or abstract analysis - just practical context.
        """
        if not self.model:
            return self._get_fallback_context(topic)

        try:
            # 🎯 SIMPLIFIED PROMPT: Focus on practical content creation for the specific topic
            simple_prompt = f"""
Analiza este tema específico para crear contenido de redes sociales RELEVANTE:

TEMA ESPECÍFICO: "{topic}"
MARCA: {brand_info.get('businessName', 'Mi Marca')}

Determina SOLO lo esencial:
1. ¿Qué tipo de contenido visual necesita este tema específico?
2. ¿Qué audiencia busca contenido sobre "{topic}"?
3. ¿Qué palabras clave son relevantes para "{topic}"?

IMPORTANTE:
- Mantén el foco en "{topic}" exactamente
- No generalices ni abstraigas
- Si el tema es "perros", habla de perros
- Si el tema es "dormir bien", habla de sueño
- Si el tema es "fitness", habla de ejercicio

Responde SOLO con JSON válido:
{{
  "nicho": "nicho específico del tema",
  "audiencia": "audiencia específica para este tema",
  "estilo_visual": "estilo visual apropiado para el tema",
  "tono": "tono apropiado",
  "palabras_clave": ["palabra1", "palabra2", "palabra3"],
  "tipo_contenido": "educational",
  "tema_especifico": "{topic}"
}}
"""

            response = self.model.generate_content(simple_prompt)

            if response and response.text:
                try:
                    # Clean the response
                    clean_response = response.text.strip()
                    if '```json' in clean_response:
                        json_match = clean_response.split('```json')[1].split('```')[0]
                    elif '```' in clean_response:
                        json_match = clean_response.split('```')[1]
                    else:
                        json_match = clean_response

                    context = json.loads(json_match.strip())
                    # Ensure we preserve the exact topic
                    context["tema_especifico"] = topic
                    logger.info(f"✅ Simple context analysis successful for topic: {topic}")
                    return context

                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse simple context JSON: {e}")
                    return self._get_fallback_context(topic)
            else:
                return self._get_fallback_context(topic)

        except Exception as e:
            logger.error(f"Error in simple context analysis: {e}")
            return self._get_fallback_context(topic)
    
    async def generate_strategic_content_plan(self, template: str, brand_info: Dict[str, Any], platform: str, user_topic: str = None) -> Dict[str, Any]:
        """
        Generate intelligent content plan that PRESERVES user's actual topic.
        Focus on what the user actually wants instead of generic analysis.

        Args:
            template: Template VIBE (Balance, Motivational, etc.)
            brand_info: Brand information
            platform: Target platform
            user_topic: User's actual topic (from express mode or topics) - THIS IS PRIORITY

        Returns:
            Content strategy that respects user's actual intent
        """
        business_name = brand_info.get("businessName", "Mi Marca")

        # 🎯 PRIORITY: Use user's actual topic as-is
        raw_topic = user_topic or brand_info.get("topics", ["marketing digital"])[0]

        # 🔍 SIMPLIFIED PROCESSING: Only extract business name if needed, preserve topic
        if len(raw_topic.split()) > 5 or any(phrase in raw_topic.lower() for phrase in ["mi marca", "se llama", "vendo", "soy", "hago", "somos", "ofrecemos", "vendemos"]):
            # This looks like a natural description, extract business name but preserve topic intent
            logger.info(f"🔍 Extracting business info while preserving topic: {raw_topic[:50]}...")
            extracted_info = await self.process_natural_description(raw_topic)

            # Extract business name if available
            extracted_business_name = extracted_info.get("nombre_marca")
            if extracted_business_name and extracted_business_name not in ["Mi Marca", "Business", "Unknown", ""]:
                business_name = extracted_business_name
                brand_info["businessName"] = business_name
                logger.info(f"✅ Extracted business name: '{business_name}'")

            # 🎯 CRITICAL: Use the extracted topic but preserve user intent
            extracted_topic = extracted_info.get("tema_principal", raw_topic)

            # If the extracted topic is too generic, prefer the original user input
            if extracted_topic in ["marketing digital", "general", "business"] and raw_topic not in ["marketing digital", "general", "business"]:
                topic = raw_topic  # Keep user's original intent
                logger.info(f"🎯 Preserving original user topic over generic extraction: '{topic}'")
            else:
                topic = extracted_topic
                logger.info(f"✅ Using extracted topic: '{topic}'")
        else:
            # Clean topic, use exactly as user provided
            topic = raw_topic
            logger.info(f"✅ Using user topic exactly as provided: '{topic}'")

        # 🎯 SIMPLIFIED CONTEXT: Focus on user's actual topic, not abstract analysis
        context = await self.analyze_topic_context_simple(topic, brand_info)
        
        # Template vibes (not rigid structures)
        template_vibes = {
            "Balance": "equilibrado, profesional pero accesible, confiable",
            "Motivational": "inspirador, energético, que motive a la acción",
            "Educational": "educativo, claro, que enseñe algo valioso",
            "Creativo": "único, llamativo, que destaque del resto",
            "Minimalista": "simple, elegante, directo al punto",
            "Profesional": "serio, corporativo, autoridad en el tema",
            "Viral": "impactante, sorprendente, que genere conversación"
        }
        
        selected_vibe = template_vibes.get(template, template_vibes["Balance"])
        
        # Platform adaptations
        platform_specs = {
            "Instagram": {"visual_priority": "alta", "text_style": "casual con hashtags", "format": "cuadrado"},
            "LinkedIn": {"visual_priority": "media", "text_style": "profesional", "format": "horizontal"},
            "Facebook": {"visual_priority": "media", "text_style": "conversacional", "format": "horizontal"},
            "X": {"visual_priority": "baja", "text_style": "conciso", "format": "horizontal"}
        }
        
        platform_spec = platform_specs.get(platform, platform_specs["Instagram"])
        
        return {
            "topic": topic,
            "context_analysis": context,
            "template_vibe": selected_vibe,
            "platform_specs": platform_spec,
            "brand_context": {
                "business_name": business_name,
                "voice": brand_info.get("voice", "profesional y amigable")
            }
        }

    async def analyze_reference_post_for_similarity(self, reference_content: str, reference_template: str, brand_info: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """
        Analyze a reference post to understand its style and create a strategy for generating similar posts.

        Args:
            reference_content: Content of the reference post
            reference_template: Template used in reference post
            brand_info: Brand information
            platform: Target platform

        Returns:
            Enhanced strategy for generating similar posts
        """
        if not self.model:
            return self._get_fallback_similarity_strategy(reference_content, reference_template, brand_info, platform)

        try:
            business_name = brand_info.get("businessName", "Business")
            industry = brand_info.get("industry", "general")

            analysis_prompt = f"""
Analiza este post de referencia para generar posts similares pero únicos.

POST DE REFERENCIA:
"{reference_content}"

INFORMACIÓN DE MARCA:
- Negocio: {business_name}
- Industria: {industry}
- Template: {reference_template}
- Plataforma: {platform}

ANÁLISIS REQUERIDO:
1. ESTILO Y TONO: ¿Qué tono y estilo tiene el post?
2. ESTRUCTURA: ¿Cómo está estructurado el contenido?
3. ELEMENTOS CLAVE: ¿Qué elementos hacen que este post sea efectivo?
4. ENFOQUE: ¿Cuál es el enfoque principal (educativo, promocional, inspiracional)?
5. AUDIENCIA: ¿A qué tipo de audiencia se dirige?

ESTRATEGIA PARA POSTS SIMILARES:
- Mantener el mismo tono y estilo
- Usar estructura similar pero con contenido diferente
- Conservar los elementos que funcionan
- Variar los ejemplos y detalles específicos
- Mantener el mismo nivel de engagement

Responde en formato JSON:
{{
    "similarity_approach": "descripción del enfoque",
    "tono": "tono identificado",
    "estructura": "estructura del contenido",
    "elementos_clave": ["elemento1", "elemento2", "elemento3"],
    "tipo_contenido": "tipo de contenido",
    "audiencia": "audiencia objetivo",
    "variaciones_sugeridas": ["variación1", "variación2", "variación3"],
    "mantener": ["qué mantener igual"],
    "variar": ["qué cambiar en cada post"]
}}
"""

            response = self.model.generate_content(analysis_prompt)

            if response and response.text:
                try:
                    # Try to parse JSON response
                    import json
                    analysis_result = json.loads(response.text.strip())

                    # Enhance with additional metadata
                    analysis_result.update({
                        "reference_content": reference_content,
                        "reference_template": reference_template,
                        "platform": platform,
                        "generation_type": "similarity_based"
                    })

                    logger.info(f"✅ Reference post analysis successful for similarity generation")
                    return analysis_result

                except json.JSONDecodeError:
                    logger.warning("Failed to parse JSON from similarity analysis, using fallback")
                    return self._get_fallback_similarity_strategy(reference_content, reference_template, brand_info, platform)
            else:
                return self._get_fallback_similarity_strategy(reference_content, reference_template, brand_info, platform)

        except Exception as e:
            logger.error(f"Error in reference post analysis for similarity: {e}")
            return self._get_fallback_similarity_strategy(reference_content, reference_template, brand_info, platform)

    def _get_fallback_similarity_strategy(self, reference_content: str, reference_template: str, brand_info: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """Generate fallback similarity strategy when AI analysis fails"""

        # Analyze reference content for basic patterns
        content_lower = reference_content.lower()

        # Determine content type based on reference
        if any(word in content_lower for word in ["tip", "consejo", "cómo", "aprende"]):
            content_type = "educational"
            tono = "educativo"
        elif any(word in content_lower for word in ["compra", "oferta", "descuento", "promoción"]):
            content_type = "promotional"
            tono = "promocional"
        elif any(word in content_lower for word in ["inspira", "motiva", "logra", "éxito"]):
            content_type = "inspirational"
            tono = "inspiracional"
        else:
            content_type = "informational"
            tono = "informativo"

        return {
            "similarity_approach": f"Generar posts con el mismo {tono} que el post de referencia",
            "tono": tono,
            "estructura": "Mantener estructura similar al post de referencia",
            "elementos_clave": ["mismo tono", "estructura similar", "enfoque consistente"],
            "tipo_contenido": content_type,
            "audiencia": brand_info.get("target_audience", "audiencia general"),
            "variaciones_sugeridas": ["cambiar ejemplos", "variar detalles", "diferentes ángulos"],
            "mantener": ["tono", "estructura", "estilo"],
            "variar": ["ejemplos específicos", "detalles", "perspectiva"],
            "reference_content": reference_content,
            "reference_template": reference_template,
            "platform": platform,
            "generation_type": "similarity_based"
        }
