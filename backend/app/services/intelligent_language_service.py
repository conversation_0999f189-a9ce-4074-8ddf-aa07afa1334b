"""
Intelligent Language Service for Smart Spanish/English Handling
Preserves Spanish context while optimizing for Ideogram generation
"""

import logging
import re
from typing import Dict, Any, Optional, Tuple
from enum import Enum

logger = logging.getLogger(__name__)

class LanguageStrategy(Enum):
    """Language handling strategies"""
    PRESERVE_SPANISH = "preserve_spanish"  # Keep Spanish text in final ad
    STRATEGIC_TRANSLATE = "strategic_translate"  # Translate for Ideogram but preserve intent
    FULL_ENGLISH = "full_english"  # Full English for international appeal

class IntelligentLanguageService:
    """Service for intelligent language handling in ad generation"""
    
    def __init__(self):
        """Initialize the language service"""
        self.spanish_indicators = [
            "suplemento", "aplicación", "ropa", "comida", "servicio", "consultoría",
            "para", "con", "sin", "más", "mejor", "nuevo", "gratis", "ahora",
            "dolor", "cabeza", "natural", "saludable", "profesional", "moderno"
        ]
        
        self.context_preserving_words = {
            # Keep these in Spanish for cultural context
            "suplemento natural": "natural supplement",
            "dolor de cabeza": "headache relief", 
            "comida saludable": "healthy food",
            "ropa deportiva": "sportswear",
            "consultoría empresarial": "business consulting",
            "app móvil": "mobile app",
            "marketing digital": "digital marketing"
        }
        
        self.strategic_translations = {
            # Strategic translations that preserve meaning
            "para": "for",
            "con": "with", 
            "sin": "without",
            "más": "more",
            "mejor": "better",
            "nuevo": "new",
            "gratis": "free",
            "ahora": "now",
            "profesional": "professional",
            "moderno": "modern",
            "natural": "natural",
            "saludable": "healthy",
            "efectivo": "effective",
            "rápido": "fast",
            "fácil": "easy"
        }
    
    def detect_language_intent(self, prompt: str) -> Dict[str, Any]:
        """
        Detect language and user intent for smart handling
        
        Args:
            prompt: User's input prompt
            
        Returns:
            Dict with language analysis and strategy
        """
        prompt_lower = prompt.lower()
        
        # Count Spanish indicators
        spanish_count = sum(1 for word in self.spanish_indicators if word in prompt_lower)
        total_words = len(prompt.split())
        spanish_ratio = spanish_count / max(total_words, 1)
        
        # Detect if user wants Spanish text in final ad
        spanish_text_intent = any(phrase in prompt_lower for phrase in [
            "en español", "texto en español", "spanish text", "keep spanish",
            "mantener español", "conservar español"
        ])
        
        # Detect if user wants international appeal
        international_intent = any(phrase in prompt_lower for phrase in [
            "international", "global", "english", "worldwide", "export"
        ])
        
        # Determine strategy
        if spanish_text_intent:
            strategy = LanguageStrategy.PRESERVE_SPANISH
        elif international_intent or spanish_ratio < 0.3:
            strategy = LanguageStrategy.FULL_ENGLISH
        else:
            strategy = LanguageStrategy.STRATEGIC_TRANSLATE
        
        return {
            "original_language": "spanish" if spanish_ratio > 0.5 else "mixed",
            "spanish_ratio": spanish_ratio,
            "spanish_text_intent": spanish_text_intent,
            "international_intent": international_intent,
            "strategy": strategy,
            "preserve_cultural_context": spanish_ratio > 0.7
        }
    
    def process_prompt_for_ideogram(self, prompt: str, language_analysis: Dict[str, Any], 
                                  product_analysis: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """
        Process prompt for Ideogram while preserving user intent
        
        Args:
            prompt: Original user prompt
            language_analysis: Result from detect_language_intent
            product_analysis: Optional product analysis for context
            
        Returns:
            Dict with processed prompts for different purposes
        """
        strategy = language_analysis["strategy"]
        
        if strategy == LanguageStrategy.PRESERVE_SPANISH:
            return self._preserve_spanish_strategy(prompt, product_analysis)
        elif strategy == LanguageStrategy.FULL_ENGLISH:
            return self._full_english_strategy(prompt, product_analysis)
        else:  # STRATEGIC_TRANSLATE
            return self._strategic_translate_strategy(prompt, language_analysis, product_analysis)
    
    def _preserve_spanish_strategy(self, prompt: str, product_analysis: Optional[Dict[str, Any]]) -> Dict[str, str]:
        """Strategy to preserve Spanish text in the final ad"""
        
        # Translate for Ideogram understanding but specify Spanish text
        english_description = self._smart_translate(prompt)
        
        # Add instruction for Spanish text in the ad
        ideogram_prompt = f"""Create a marketing advertisement with Spanish text for: {english_description}.
        IMPORTANT: Include Spanish text and headlines in the advertisement.
        Use Spanish language for all text elements, headlines, and call-to-action.
        Design should appeal to Spanish-speaking audience with cultural relevance."""
        
        return {
            "ideogram_prompt": ideogram_prompt,
            "display_language": "spanish",
            "text_instruction": "Include Spanish text in the advertisement",
            "cultural_context": "spanish_speaking_market"
        }
    
    def _full_english_strategy(self, prompt: str, product_analysis: Optional[Dict[str, Any]]) -> Dict[str, str]:
        """Strategy for full English international appeal"""
        
        english_prompt = self._smart_translate(prompt)
        
        ideogram_prompt = f"""Create a professional international marketing advertisement for: {english_prompt}.
        Use English text and headlines optimized for global audience.
        Design should have international appeal with modern, professional aesthetic."""
        
        return {
            "ideogram_prompt": ideogram_prompt,
            "display_language": "english", 
            "text_instruction": "Use English text for international appeal",
            "cultural_context": "international_market"
        }
    
    def _strategic_translate_strategy(self, prompt: str, language_analysis: Dict[str, Any], 
                                    product_analysis: Optional[Dict[str, Any]]) -> Dict[str, str]:
        """Strategic translation that preserves cultural context"""
        
        # Smart translation preserving key concepts
        translated_prompt = self._contextual_translate(prompt, language_analysis)
        
        # Determine if we should suggest Spanish or English text based on product
        product_type = product_analysis.get("product_type", "") if product_analysis else ""
        target_audience = product_analysis.get("target_audience", "") if product_analysis else ""
        
        # Some products work better with Spanish text
        spanish_friendly_products = ["supplement", "food", "service", "local"]
        use_spanish_text = any(ptype in product_type.lower() for ptype in spanish_friendly_products)
        
        if use_spanish_text and language_analysis.get("preserve_cultural_context"):
            text_language = "Spanish"
            cultural_note = "with cultural relevance for Spanish-speaking audience"
        else:
            text_language = "English" 
            cultural_note = "with broad market appeal"
        
        ideogram_prompt = f"""Create a professional marketing advertisement for: {translated_prompt}.
        Use {text_language} text and headlines {cultural_note}.
        Design should be culturally appropriate and professionally appealing."""
        
        return {
            "ideogram_prompt": ideogram_prompt,
            "display_language": text_language.lower(),
            "text_instruction": f"Use {text_language} text {cultural_note}",
            "cultural_context": "culturally_adapted"
        }
    
    def _smart_translate(self, text: str) -> str:
        """Smart translation preserving meaning and context"""
        
        # First handle complete phrases
        translated = text.lower()
        for spanish_phrase, english_phrase in self.context_preserving_words.items():
            if spanish_phrase in translated:
                translated = translated.replace(spanish_phrase, english_phrase)
        
        # Then handle individual words strategically
        words = translated.split()
        translated_words = []
        
        for word in words:
            # Clean word for matching
            clean_word = re.sub(r'[^\w]', '', word.lower())
            
            if clean_word in self.strategic_translations:
                # Preserve original punctuation
                translated_word = word.replace(clean_word, self.strategic_translations[clean_word])
                translated_words.append(translated_word)
            else:
                translated_words.append(word)
        
        result = ' '.join(translated_words)
        
        # Capitalize first letter
        if result:
            result = result[0].upper() + result[1:]
        
        return result
    
    def _contextual_translate(self, text: str, language_analysis: Dict[str, Any]) -> str:
        """Contextual translation based on language analysis"""
        
        # If high Spanish ratio, preserve more cultural context
        if language_analysis.get("spanish_ratio", 0) > 0.7:
            # Minimal translation - keep cultural concepts
            return self._minimal_translate(text)
        else:
            # Standard smart translation
            return self._smart_translate(text)
    
    def _minimal_translate(self, text: str) -> str:
        """Minimal translation preserving maximum cultural context"""
        
        # Only translate essential words for Ideogram understanding
        essential_translations = {
            "suplemento": "supplement",
            "aplicación": "app", 
            "ropa": "clothing",
            "comida": "food",
            "servicio": "service"
        }
        
        translated = text.lower()
        for spanish, english in essential_translations.items():
            translated = translated.replace(spanish, english)
        
        # Capitalize
        if translated:
            translated = translated[0].upper() + translated[1:]
            
        return translated
    
    def get_negative_prompt_for_language(self, language_strategy: LanguageStrategy, 
                                       product_type: str = "") -> str:
        """Get appropriate negative prompt based on language strategy"""
        
        base_negative = "low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution, blurry text, illegible text, distorted text, unreadable text, poor typography"
        
        if language_strategy == LanguageStrategy.PRESERVE_SPANISH:
            # Don't restrict text language for Spanish preservation
            return f"{base_negative}, english text when spanish is requested"
        elif language_strategy == LanguageStrategy.FULL_ENGLISH:
            # Restrict non-English text
            return f"{base_negative}, foreign language text, non-english text"
        else:
            # Balanced approach
            return base_negative
    
    def get_text_guidance(self, language_analysis: Dict[str, Any], 
                         product_analysis: Optional[Dict[str, Any]] = None) -> str:
        """Get text guidance for the advertisement"""
        
        strategy = language_analysis["strategy"]
        
        if strategy == LanguageStrategy.PRESERVE_SPANISH:
            return "Include clear, readable Spanish text and headlines"
        elif strategy == LanguageStrategy.FULL_ENGLISH:
            return "Include clear, readable English text and headlines"
        else:
            # Determine best language based on product and audience
            if product_analysis:
                target = product_analysis.get("target_audience", "").lower()
                if "spanish" in target or "latino" in target or "hispanic" in target:
                    return "Include clear, readable Spanish text and headlines"
            
            return "Include clear, readable text and headlines in appropriate language"
