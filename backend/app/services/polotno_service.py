"""
Polotno Service - Generate JSON structures compatible with Polotno editor.

This service creates the proper JSON format for Polotno with:
- Clean background images as image layers
- Editable text as separate text layers
- Proper positioning and styling
"""

import logging
from typing import Dict, Any, List
import uuid

logger = logging.getLogger(__name__)


class PolotnoService:
    """Service for generating Polotno-compatible JSON structures."""
    
    def __init__(self):
        logger.info("✅ Polotno Service initialized")
    
    def create_polotno_json(
        self, 
        background_image_url: str, 
        text_content: str, 
        hook_text: str = None,
        dimensions: Dict[str, int] = None,
        brand_color: str = "#FFFFFF"
    ) -> Dict[str, Any]:
        """
        Create Polotno-compatible JSON structure with separate image and text layers.
        
        Args:
            background_image_url: Clean background image URL
            text_content: Main post content text
            hook_text: Hook/title text (optional)
            dimensions: Image dimensions
            brand_color: Brand color for text
            
        Returns:
            Polotno JSON structure with editable layers
        """
        try:
            # Default dimensions
            if not dimensions:
                dimensions = {"width": 1080, "height": 1080}
            
            width = dimensions.get("width", 1080)
            height = dimensions.get("height", 1080)
            
            # Create base Polotno structure
            polotno_json = {
                "width": width,
                "height": height,
                "children": []
            }
            
            # 1. Background Image Layer
            if background_image_url:
                image_layer = {
                    "type": "image",
                    "id": str(uuid.uuid4()),
                    "src": background_image_url,
                    "x": 0,
                    "y": 0,
                    "width": width,
                    "height": height,
                    "scaleX": 1,
                    "scaleY": 1,
                    "rotation": 0,
                    "opacity": 1,
                    "locked": False
                }
                polotno_json["children"].append(image_layer)
            
            # 2. Hook Text Layer (if provided)
            if hook_text and hook_text.strip():
                hook_layer = self._create_text_layer(
                    text=hook_text.strip(),
                    x=width * 0.1,  # 10% from left
                    y=height * 0.15,  # 15% from top
                    width=width * 0.8,  # 80% width
                    font_size=self._calculate_font_size(hook_text, width, "hook"),
                    font_weight="bold",
                    fill=brand_color,
                    text_align="center"
                )
                polotno_json["children"].append(hook_layer)
            
            # 3. Main Content Text Layer
            if text_content and text_content.strip():
                # Position main content below hook (or center if no hook)
                content_y = height * 0.4 if hook_text else height * 0.3
                
                content_layer = self._create_text_layer(
                    text=text_content.strip(),
                    x=width * 0.1,  # 10% from left
                    y=content_y,
                    width=width * 0.8,  # 80% width
                    font_size=self._calculate_font_size(text_content, width, "content"),
                    font_weight="normal",
                    fill="#FFFFFF",
                    text_align="left"
                )
                polotno_json["children"].append(content_layer)
            
            logger.info(f"✅ Polotno JSON created with {len(polotno_json['children'])} layers")
            return polotno_json
            
        except Exception as e:
            logger.error(f"❌ Error creating Polotno JSON: {e}")
            return self._create_fallback_json(background_image_url, text_content, dimensions)
    
    def _create_text_layer(
        self,
        text: str,
        x: float,
        y: float,
        width: float,
        font_size: int = 24,
        font_weight: str = "normal",
        fill: str = "#FFFFFF",
        text_align: str = "left",
        font_family: str = "Arial"
    ) -> Dict[str, Any]:
        """Create a single text layer for Polotno."""
        
        return {
            "type": "text",
            "id": str(uuid.uuid4()),
            "text": text,
            "x": x,
            "y": y,
            "width": width,
            "height": "auto",  # Auto-height based on content
            "fontSize": font_size,
            "fontFamily": font_family,
            "fontWeight": font_weight,
            "fill": fill,
            "align": text_align,
            "verticalAlign": "top",
            "rotation": 0,
            "opacity": 1,
            "locked": False,
            "draggable": True,
            "resizable": True,
            "editable": True
        }
    
    def _calculate_font_size(self, text: str, canvas_width: int, text_type: str = "content") -> int:
        """Calculate appropriate font size based on text length and canvas width."""
        
        base_sizes = {
            "hook": 48,      # Large for hooks/titles
            "content": 24,   # Medium for main content
            "caption": 18    # Small for captions
        }
        
        base_size = base_sizes.get(text_type, 24)
        
        # Adjust based on text length
        text_length = len(text)
        
        if text_length < 20:
            # Short text can be larger
            multiplier = 1.2
        elif text_length < 50:
            # Medium text
            multiplier = 1.0
        elif text_length < 100:
            # Long text should be smaller
            multiplier = 0.8
        else:
            # Very long text
            multiplier = 0.6
        
        # Adjust based on canvas width
        width_factor = canvas_width / 1080  # Normalize to 1080px
        
        final_size = int(base_size * multiplier * width_factor)
        
        # Ensure minimum and maximum sizes
        return max(12, min(final_size, 72))
    
    def _create_fallback_json(
        self, 
        background_image_url: str, 
        text_content: str, 
        dimensions: Dict[str, int]
    ) -> Dict[str, Any]:
        """Create a simple fallback JSON structure."""
        
        width = dimensions.get("width", 1080) if dimensions else 1080
        height = dimensions.get("height", 1080) if dimensions else 1080
        
        fallback = {
            "width": width,
            "height": height,
            "children": []
        }
        
        # Add background image if available
        if background_image_url:
            fallback["children"].append({
                "type": "image",
                "id": str(uuid.uuid4()),
                "src": background_image_url,
                "x": 0,
                "y": 0,
                "width": width,
                "height": height
            })
        
        # Add simple text layer
        if text_content:
            fallback["children"].append({
                "type": "text",
                "id": str(uuid.uuid4()),
                "text": text_content,
                "x": width * 0.1,
                "y": height * 0.3,
                "width": width * 0.8,
                "fontSize": 24,
                "fontFamily": "Arial",
                "fill": "#FFFFFF"
            })
        
        return fallback


# Global instance
polotno_service = PolotnoService()
