"""
Semantic Search Service for Emma Studio
Provides semantic search capabilities using Sentence-BERT embeddings and FAISS vector storage
"""

import os
import json
import hashlib
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor

import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.core.config import settings
from app.db.models import Base
from app.db.seo_gpt_models import SEOGPTProject, ContentAnalysis
from app.models.seo_intelligence_models import GeneratedContent

logger = logging.getLogger(__name__)

class SemanticSearchService:
    """Service for semantic search using embeddings and vector storage."""

    def __init__(self):
        self.model_name = "all-MiniLM-L6-v2"  # Lightweight, fast model
        self.model = None
        self.index = None
        self.content_mapping = {}  # Maps vector indices to content metadata
        self.embedding_dim = 384  # Dimension for all-MiniLM-L6-v2
        self.index_path = "storage/semantic_search"
        self.executor = ThreadPoolExecutor(max_workers=2)
        self._is_shutdown = False

        # Ensure storage directory exists
        os.makedirs(self.index_path, exist_ok=True)

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with proper cleanup."""
        self.shutdown()

    def shutdown(self):
        """Properly shutdown the thread pool executor."""
        if not self._is_shutdown and self.executor:
            try:
                logger.info("Shutting down SemanticSearchService thread pool...")
                self.executor.shutdown(wait=True)
                self._is_shutdown = True
                logger.info("✅ SemanticSearchService thread pool shutdown complete")
            except Exception as e:
                logger.error(f"❌ Error shutting down SemanticSearchService thread pool: {e}")

    def __del__(self):
        """Destructor to ensure cleanup."""
        self.shutdown()
        
    async def initialize(self):
        """Initialize the semantic search service."""
        try:
            if self._is_shutdown:
                logger.error("Cannot initialize: SemanticSearchService is shutdown")
                return False

            # Load model in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            try:
                self.model = await loop.run_in_executor(
                    self.executor,
                    self._load_model
                )
            except Exception as e:
                logger.error(f"Failed to load model in thread pool: {e}")
                raise

            # Load or create FAISS index
            await self._load_or_create_index()

            logger.info("Semantic search service initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize semantic search service: {e}")
            return False
    
    def _load_model(self) -> SentenceTransformer:
        """Load the sentence transformer model."""
        return SentenceTransformer(self.model_name)
    
    async def _load_or_create_index(self):
        """Load existing FAISS index or create a new one."""
        index_file = os.path.join(self.index_path, "faiss_index.bin")
        mapping_file = os.path.join(self.index_path, "content_mapping.json")
        
        if os.path.exists(index_file) and os.path.exists(mapping_file):
            try:
                # Load existing index
                loop = asyncio.get_event_loop()
                self.index = await loop.run_in_executor(
                    self.executor,
                    faiss.read_index,
                    index_file
                )
                
                # Load content mapping
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    self.content_mapping = json.load(f)
                
                logger.info(f"Loaded existing FAISS index with {self.index.ntotal} vectors")
                
            except Exception as e:
                logger.warning(f"Failed to load existing index: {e}. Creating new index.")
                self._create_new_index()
        else:
            self._create_new_index()
    
    def _create_new_index(self):
        """Create a new FAISS index."""
        # Use IndexFlatIP for cosine similarity (after L2 normalization)
        self.index = faiss.IndexFlatIP(self.embedding_dim)
        self.content_mapping = {}
        logger.info("Created new FAISS index")
    
    async def add_content_to_index(self, content_id: str, title: str, content: str, 
                                 content_type: str = "article", metadata: Dict[str, Any] = None):
        """Add content to the semantic search index."""
        try:
            if not self.model:
                await self.initialize()
            
            # Create text for embedding (title + content)
            text_for_embedding = f"{title}\n\n{content}"
            
            # Generate embedding
            loop = asyncio.get_event_loop()
            embedding = await loop.run_in_executor(
                self.executor,
                self._generate_embedding,
                text_for_embedding
            )
            
            # Normalize for cosine similarity
            embedding = embedding / np.linalg.norm(embedding)
            embedding = embedding.reshape(1, -1).astype('float32')
            
            # Add to index
            vector_id = self.index.ntotal
            self.index.add(embedding)
            
            # Store content mapping
            self.content_mapping[str(vector_id)] = {
                "content_id": content_id,
                "title": title,
                "content_type": content_type,
                "content_preview": content[:200] + "..." if len(content) > 200 else content,
                "metadata": metadata or {},
                "added_at": datetime.utcnow().isoformat()
            }
            
            # Save index and mapping
            await self._save_index()
            
            logger.info(f"Added content '{title}' to semantic search index")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add content to index: {e}")
            return False
    
    def _generate_embedding(self, text: str) -> np.ndarray:
        """Generate embedding for text."""
        return self.model.encode(text)
    
    async def search(self, query: str, top_k: int = 10, content_type: str = None) -> List[Dict[str, Any]]:
        """Perform semantic search."""
        try:
            if not self.model or not self.index:
                await self.initialize()
            
            if self.index.ntotal == 0:
                return []
            
            # Generate query embedding
            loop = asyncio.get_event_loop()
            query_embedding = await loop.run_in_executor(
                self.executor,
                self._generate_embedding,
                query
            )
            
            # Normalize for cosine similarity
            query_embedding = query_embedding / np.linalg.norm(query_embedding)
            query_embedding = query_embedding.reshape(1, -1).astype('float32')
            
            # Search
            scores, indices = self.index.search(query_embedding, min(top_k, self.index.ntotal))
            
            # Format results
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx == -1:  # FAISS returns -1 for invalid indices
                    continue
                    
                content_info = self.content_mapping.get(str(idx))
                if content_info:
                    # Filter by content type if specified
                    if content_type and content_info.get("content_type") != content_type:
                        continue
                    
                    result = {
                        "content_id": content_info["content_id"],
                        "title": content_info["title"],
                        "content_type": content_info["content_type"],
                        "content_preview": content_info["content_preview"],
                        "similarity_score": float(score),
                        "metadata": content_info.get("metadata", {}),
                        "added_at": content_info.get("added_at")
                    }
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return []
    
    async def _save_index(self):
        """Save FAISS index and content mapping to disk."""
        try:
            index_file = os.path.join(self.index_path, "faiss_index.bin")
            mapping_file = os.path.join(self.index_path, "content_mapping.json")
            
            # Save index
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor,
                faiss.write_index,
                self.index,
                index_file
            )
            
            # Save mapping
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.content_mapping, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save index: {e}")
    
    async def rebuild_index_from_database(self, db: Session):
        """Rebuild the semantic search index from existing database content."""
        try:
            logger.info("Starting semantic search index rebuild...")
            
            # Clear existing index
            self._create_new_index()
            
            # Get all SEO GPT projects
            seo_projects = db.query(SEOGPTProject).filter(
                SEOGPTProject.content_text.isnot(None),
                SEOGPTProject.content_text != ""
            ).all()
            
            for project in seo_projects:
                await self.add_content_to_index(
                    content_id=project.project_id,
                    title=project.title,
                    content=project.content_text,
                    content_type="seo_article",
                    metadata={
                        "topic": project.topic,
                        "language": project.target_language,
                        "gpt_rank_score": project.current_gpt_rank_score,
                        "status": project.status.value if project.status else "unknown"
                    }
                )
            
            # Get generated content
            generated_content = db.query(GeneratedContent).all()
            
            for content in generated_content:
                await self.add_content_to_index(
                    content_id=content.id,
                    title=content.title,
                    content=content.content,
                    content_type=content.content_type,
                    metadata={
                        "topic": content.topic,
                        "keywords": content.keywords,
                        "tone": content.tone,
                        "target_audience": content.target_audience
                    }
                )
            
            logger.info(f"Semantic search index rebuilt with {self.index.ntotal} documents")
            return True
            
        except Exception as e:
            logger.error(f"Failed to rebuild index: {e}")
            return False
    
    async def get_similar_content(self, content_id: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Find similar content to a given content ID."""
        try:
            # Find the content in our mapping
            target_vector_id = None
            for vector_id, content_info in self.content_mapping.items():
                if content_info["content_id"] == content_id:
                    target_vector_id = int(vector_id)
                    break
            
            if target_vector_id is None:
                return []
            
            # Get the embedding for this content
            target_embedding = self.index.reconstruct(target_vector_id)
            target_embedding = target_embedding.reshape(1, -1)
            
            # Search for similar content
            scores, indices = self.index.search(target_embedding, top_k + 1)  # +1 to exclude self
            
            # Format results (excluding the original content)
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx == target_vector_id or idx == -1:
                    continue
                    
                content_info = self.content_mapping.get(str(idx))
                if content_info:
                    result = {
                        "content_id": content_info["content_id"],
                        "title": content_info["title"],
                        "content_type": content_info["content_type"],
                        "content_preview": content_info["content_preview"],
                        "similarity_score": float(score),
                        "metadata": content_info.get("metadata", {})
                    }
                    results.append(result)
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"Failed to find similar content: {e}")
            return []

# Global instance
semantic_search_service = SemanticSearchService()
