"""
Image generation service for creating contextual image prompts and managing image providers.
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class ImageGenerationService:
    """Service for generating contextual image prompts and managing image providers."""
    
    def __init__(self):
        self.model = None
        self._initialize_ai()
    
    def _initialize_ai(self):
        """Initialize AI model for image prompt generation."""
        try:
            import google.generativeai as genai
            from app.core.config import settings
            
            if settings.GEMINI_API_KEY:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Image Generation Service initialized successfully")
            else:
                logger.warning("⚠️ GEMINI_API_KEY not configured - using fallback mode")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Image Generation Service: {e}")
    
    def select_image_provider(self, template: str, content_description: str) -> str:
        """
        Intelligently select image provider based on template and content.
        
        PRIORITY ORDER:
        1. LOCAL MEME: Template-based memes (cost-effective)
        2. IDEOGRAM 3.0 QUALITY: All other content types (PRIMARY)
        
        Args:
            template: The selected template name
            content_description: Description of the content to generate
            
        Returns:
            Provider name: 'meme' or 'ideogram'
        """
        content_lower = content_description.lower()
        
        # LOCAL MEME service for template-based memes (cost optimization)
        if template == "Meme" or any(keyword in content_lower for keyword in [
            "meme", "humor", "gracioso", "divertido", "vs", "antes", "después", "template"
        ]):
            return "meme"
        
        # IDEOGRAM 3.0 QUALITY for all other content (text-heavy, visual, professional)
        # This includes: quotes, comics, landscapes, products, scenes, graphics, professional photography
        return "ideogram"
    
    async def generate_image_prompt_strategic(self, visual_hook: str, content_strategy: Dict[str, Any], platform: str, brand_color: str = None) -> str:
        """
        Generate AGENCY-LEVEL creative prompts for Ideogram AI.
        Creates visually stunning, professional designs that look like they were made by top graphic designers.

        Args:
            visual_hook: The text that will appear in the image
            content_strategy: Strategic content plan with context analysis
            platform: Target platform
            brand_color: Brand color (hex code like #3018ef)

        Returns:
            Professional, creative prompt optimized for stunning visual results
        """
        try:
            topic = content_strategy.get("topic", "marketing digital")
            context_analysis = content_strategy.get("context_analysis", {})

            # Get context-specific information
            nicho = context_analysis.get("nicho", "general")
            tipo_contenido = context_analysis.get("tipo_contenido", "educational")

            # Optimize text for Ideogram rendering
            optimized_hook = self._optimize_text_for_ideogram(visual_hook)

            # 🎨 AGENCY-LEVEL CREATIVE PROMPT CONSTRUCTION
            # Build sophisticated, visually stunning prompts that create scroll-stopping content

            # Get the creative foundation
            creative_foundation = self._get_creative_foundation(topic, nicho, tipo_contenido)

            # Get platform-specific visual style
            platform_style = self._get_platform_visual_style(platform)

            # Get advanced composition techniques
            composition = self._get_advanced_composition(tipo_contenido, platform)

            # Get color and lighting strategy
            color_lighting = self._get_color_lighting_strategy(brand_color, topic, platform)

            # Get typography and text integration
            typography = self._get_typography_strategy(platform, tipo_contenido)

            # 🚀 CONSTRUCT AGENCY-LEVEL PROMPT
            agency_prompt = f"""A {creative_foundation}, {platform_style}. {composition}. {color_lighting}. {typography}. Text overlay reads: "{optimized_hook}". Professional graphic design, award-winning composition, trending on Behance, studio lighting, ultra-detailed, 8K quality"""

            logger.info(f"🎨 Generated AGENCY-LEVEL prompt for Ideogram: {agency_prompt[:100]}...")
            return agency_prompt

        except Exception as e:
            logger.error(f"Error generating image prompt: {e}")
            return self._get_fallback_image_prompt(visual_hook, content_strategy, platform, brand_color)

    def _get_creative_foundation(self, topic: str, nicho: str, tipo_contenido: str) -> str:
        """Get the creative foundation for agency-level designs."""

        # Topic-specific creative foundations with high-end design elements
        topic_foundations = {
            "fitness": "dynamic fitness lifestyle scene with athletic energy, modern gym aesthetic, motivational atmosphere",
            "wellness": "serene wellness sanctuary with natural elements, spa-like ambiance, calming organic textures",
            "business": "sophisticated corporate environment with premium materials, executive boardroom aesthetic, luxury office design",
            "technology": "futuristic tech workspace with neon accents, holographic elements, cyberpunk aesthetic",
            "food": "artisanal culinary scene with gourmet presentation, restaurant-quality lighting, chef's table aesthetic",
            "travel": "wanderlust-inspiring destination with cinematic landscape, adventure photography style",
            "education": "modern learning environment with innovative design, academic excellence aesthetic",
            "lifestyle": "aspirational lifestyle scene with premium products, magazine-quality composition"
        }

        # Content type refinements
        content_refinements = {
            "educational": "infographic-style layout with data visualization elements",
            "promotional": "advertising campaign aesthetic with commercial photography quality",
            "inspirational": "motivational poster design with emotional impact",
            "informational": "editorial design with magazine-quality layout"
        }

        # Find best match for topic
        foundation = "premium social media design with professional photography aesthetic"
        for key, value in topic_foundations.items():
            if key in topic.lower() or key in nicho.lower():
                foundation = value
                break

        # Add content type refinement
        if tipo_contenido in content_refinements:
            foundation += f", {content_refinements[tipo_contenido]}"

        return foundation

    def _get_platform_visual_style(self, platform: str) -> str:
        """Get platform-specific visual style for agency-level designs."""

        platform_styles = {
            "Instagram": "Instagram-optimized square format, influencer-quality aesthetic, social media trending style, vibrant and engaging",
            "LinkedIn": "professional business aesthetic, corporate design standards, executive presentation quality, sophisticated color palette",
            "Facebook": "community-focused design, approachable yet professional, social engagement optimized, warm and inviting",
            "Twitter": "bold and impactful design, attention-grabbing composition, news-worthy aesthetic, dynamic visual hierarchy"
        }

        return platform_styles.get(platform, platform_styles["Instagram"])

    def _get_advanced_composition(self, tipo_contenido: str, platform: str) -> str:
        """Get advanced composition techniques for professional results."""

        base_compositions = {
            "educational": "asymmetrical grid layout with visual hierarchy, golden ratio composition, strategic white space usage",
            "promotional": "center-focused composition with radiating elements, Z-pattern visual flow, call-to-action prominence",
            "inspirational": "rule of thirds composition with emotional focal point, dramatic perspective, inspiring visual metaphors",
            "informational": "structured layout with clear information architecture, modular design system, scannable content flow"
        }

        # Platform-specific composition adjustments
        platform_adjustments = {
            "Instagram": "mobile-first composition, thumb-stopping visual impact",
            "LinkedIn": "professional presentation layout, business-appropriate composition",
            "Facebook": "community-engaging layout, conversation-starting composition"
        }

        base = base_compositions.get(tipo_contenido, "balanced composition with professional visual hierarchy")
        adjustment = platform_adjustments.get(platform, "")

        return f"{base}, {adjustment}" if adjustment else base

    def _get_color_lighting_strategy(self, brand_color: str, topic: str, platform: str) -> str:
        """Get sophisticated color and lighting strategy for agency-level designs."""

        # Base lighting setups
        lighting_setups = {
            "business": "professional studio lighting with soft shadows, executive photography lighting",
            "wellness": "natural daylight with warm golden hour tones, organic lighting",
            "technology": "dramatic LED lighting with neon accents, futuristic ambient lighting",
            "fitness": "high-energy lighting with dynamic shadows, motivational atmosphere lighting",
            "food": "appetizing food photography lighting, restaurant-quality illumination",
            "lifestyle": "aspirational lifestyle lighting, magazine-quality illumination"
        }

        # Color strategy based on brand color
        color_strategy = "sophisticated color palette with premium brand integration"
        if brand_color:
            color_strategy = f"premium color palette featuring {brand_color} as accent color, sophisticated brand color integration"

        # Topic-specific lighting
        lighting = "professional studio lighting with cinematic quality"
        for key, value in lighting_setups.items():
            if key in topic.lower():
                lighting = value
                break

        # Platform-specific color adjustments
        platform_color_adjustments = {
            "Instagram": "vibrant and saturated colors optimized for mobile viewing",
            "LinkedIn": "professional color scheme with corporate sophistication",
            "Facebook": "warm and approachable color palette for community engagement"
        }

        platform_adjustment = platform_color_adjustments.get(platform, "")

        result = f"{color_strategy}, {lighting}"
        if platform_adjustment:
            result += f", {platform_adjustment}"

        return result

    def _get_typography_strategy(self, platform: str, tipo_contenido: str) -> str:
        """Get advanced typography strategy for professional text integration."""

        typography_strategies = {
            "educational": "clean sans-serif typography with excellent readability, information hierarchy through font weights",
            "promotional": "bold display typography with marketing impact, attention-grabbing font choices",
            "inspirational": "elegant typography with emotional resonance, motivational font styling",
            "informational": "structured typography system with clear content organization, editorial font choices"
        }

        platform_typography = {
            "Instagram": "mobile-optimized typography, social media font sizing, thumb-friendly readability",
            "LinkedIn": "professional business typography, corporate font standards, executive presentation quality",
            "Facebook": "community-friendly typography, approachable font choices, conversation-encouraging text style"
        }

        base_typography = typography_strategies.get(tipo_contenido, "professional typography with excellent readability")
        platform_specific = platform_typography.get(platform, "")

        result = f"{base_typography}, perfect text-to-background contrast, professional text overlay integration"
        if platform_specific:
            result += f", {platform_specific}"

        return result

    def _optimize_text_for_ideogram(self, text: str) -> str:
        """
        Optimize text for better Ideogram rendering.
        Removes emojis, shortens long words, limits length.
        """
        import re

        # Remove emojis
        emoji_pattern = re.compile("["
                                   u"\U0001F600-\U0001F64F"  # emoticons
                                   u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                   u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                   u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
                                   u"\U00002702-\U000027B0"
                                   u"\U000024C2-\U0001F251"
                                   "]+", flags=re.UNICODE)
        text = emoji_pattern.sub('', text)

        # Remove hashtags
        text = re.sub(r'#\w+', '', text)

        # Shorten common long words for better rendering
        word_replacements = {
            "transformación": "cambio",
            "estrategias": "tips",
            "resultados": "éxito",
            "marketing": "marketing",
            "digital": "digital",
            "profesional": "pro",
            "increíble": "genial",
            "extraordinario": "único",
            "innovación": "innovar",
            "tradicional": "clásico"
        }

        for long_word, short_word in word_replacements.items():
            text = text.replace(long_word, short_word)

        # Limit length for better rendering (Ideogram works best with shorter text)
        if len(text) > 60:
            words = text.split()
            if len(words) > 8:
                text = ' '.join(words[:8])

        return text.strip()
    
    def _get_fallback_image_prompt(self, visual_hook: str, content_strategy: Dict[str, Any], platform: str, brand_color: str = None) -> str:
        """Generate agency-level fallback image prompt when AI fails"""
        topic = content_strategy.get("topic", "marketing digital")

        # Optimize text for Ideogram rendering
        optimized_hook = self._optimize_text_for_ideogram(visual_hook)

        # Agency-level fallback prompts based on topic
        agency_fallbacks = {
            "food": f'A gourmet culinary scene with restaurant-quality lighting, artisanal food presentation, chef\'s table aesthetic, professional food photography style. Text overlay reads: "{optimized_hook}". Award-winning composition, trending on Behance',

            "fitness": f'A dynamic fitness lifestyle scene with athletic energy, modern gym aesthetic, motivational atmosphere, high-energy lighting with dramatic shadows. Text overlay reads: "{optimized_hook}". Professional sports photography, ultra-detailed',

            "wellness": f'A serene wellness sanctuary with natural elements, spa-like ambiance, calming organic textures, natural daylight with warm golden hour tones. Text overlay reads: "{optimized_hook}". Magazine-quality composition, peaceful aesthetic',

            "business": f'A sophisticated corporate environment with premium materials, executive boardroom aesthetic, luxury office design, professional studio lighting. Text overlay reads: "{optimized_hook}". Corporate photography excellence, 8K quality',

            "technology": f'A futuristic tech workspace with neon accents, holographic elements, cyberpunk aesthetic, dramatic LED lighting. Text overlay reads: "{optimized_hook}". Sci-fi design trending, ultra-modern',

            "beauty": f'A luxury beauty scene with premium cosmetics, high-end salon aesthetic, glamorous lighting setup, fashion photography style. Text overlay reads: "{optimized_hook}". Beauty editorial quality, sophisticated styling'
        }

        # Find best match for topic
        for key, prompt in agency_fallbacks.items():
            if any(word in topic.lower() for word in [key, key.replace("ness", ""), key + "s"]):
                return prompt

        # Default agency-level fallback
        platform_style = self._get_platform_visual_style(platform)
        color_lighting = "professional studio lighting with cinematic quality, sophisticated color palette"
        if brand_color:
            color_lighting += f" featuring {brand_color} as accent color"

        return f'A premium social media design with professional photography aesthetic, {platform_style.lower()}, {color_lighting}. Text overlay reads: "{optimized_hook}". Award-winning composition, trending on Behance, studio lighting, ultra-detailed'
    
    def generate_image_prompt_legacy(self, template: str, brand_info: Dict[str, Any], post_text: str, platform: str) -> str:
        """
        Legacy function for backward compatibility with the /generate endpoint.
        Creates basic image prompts - the new strategic system uses generate_image_prompt_strategic.
        """
        business_name = brand_info.get("businessName", "Business")
        industry = brand_info.get("industry", "business")
        
        # Basic image prompts for backward compatibility
        basic_prompts = {
            "Balance": f"Professional business graphic for {business_name} in {industry}, clean modern design, corporate aesthetic",
            "Educational": f"Educational infographic style for {business_name}, clean layout, professional {industry} context",
            "Motivational": f"Inspiring motivational design for {business_name}, uplifting colors, success-oriented imagery",
            "Informativo": f"Informational graphic for {business_name}, data visualization, {industry} professional design"
        }
        
        base_prompt = basic_prompts.get(template, basic_prompts["Balance"])
        
        # Add platform optimization
        platform_additions = {
            "Instagram": "square format, Instagram-optimized, social media aesthetic",
            "LinkedIn": "professional business setting, corporate aesthetic, LinkedIn-appropriate",
            "Facebook": "engaging social media style, Facebook-optimized, community feel",
            "X": "dynamic composition, Twitter-style visual, concise visual impact"
        }
        
        platform_addition = platform_additions.get(platform, platform_additions["Instagram"])
        
        return f"{base_prompt}, {platform_addition}"

    async def generate_similar_image_prompt(self, visual_hook: str, enhanced_strategy: Dict[str, Any], platform: str, brand_color: str = None, reference_metadata: Dict[str, Any] = None) -> str:
        """
        Generate image prompt similar to reference post style.

        Args:
            visual_hook: The text that will appear in the image
            enhanced_strategy: Strategy from similarity analysis
            platform: Target platform
            brand_color: Brand color (hex code like #3018ef)
            reference_metadata: Metadata from reference post

        Returns:
            Simple, direct prompt optimized for similar style
        """
        try:
            # Get similarity strategy details
            tono = enhanced_strategy.get("tono", "profesional")
            tipo_contenido = enhanced_strategy.get("tipo_contenido", "educational")
            elementos_clave = enhanced_strategy.get("elementos_clave", [])

            # Optimize text for Ideogram rendering
            optimized_hook = self._optimize_text_for_ideogram(visual_hook)

            # Context mapping based on similarity strategy
            if tono == "educativo":
                context = "educational social media graphic"
            elif tono == "promocional":
                context = "promotional marketing poster"
            elif tono == "inspiracional":
                context = "motivational design"
            else:
                context = "professional social media graphic"

            # Platform-specific adjustments
            if platform == "LinkedIn":
                context = f"professional {context}"
            elif platform == "Instagram":
                context = f"modern {context}"

            # Create simple, direct prompt following Ideogram best practices
            # Keep it similar to reference style but unique
            similar_prompt = f'A {context} with text that reads: "{optimized_hook}"'

            logger.info(f"✅ Generated SIMILAR prompt for Ideogram: {similar_prompt[:100]}...")
            return similar_prompt

        except Exception as e:
            logger.error(f"Error generating similar image prompt: {e}")
            return self._get_fallback_image_prompt(visual_hook, enhanced_strategy, platform, brand_color)
