"""
Schemas for Focus Group Simulator API endpoints.
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class FocusGroupRequest(BaseModel):
    """Request schema for focus group simulation."""
    content: str = Field(..., description="Content to analyze in the focus group")
    product_category: Optional[str] = Field(None, description="Product category for context")
    context: Optional[str] = Field(None, description="Additional context for the simulation")
    questions: Optional[List[str]] = Field(None, description="Custom questions for the focus group")
    num_participants: Optional[int] = Field(6, description="Number of participants (default: 6)")
    discussion_rounds: Optional[int] = Field(3, description="Number of discussion rounds (default: 3)")


class TextAssistRequest(BaseModel):
    """Request schema for text assistance/improvement."""
    content: str = Field(..., description="Text content to improve")
    context: Optional[str] = Field(None, description="Context for the improvement")


class Participant(BaseModel):
    """Focus group participant profile."""
    id: int
    name: str
    age_range: str
    gender: str
    education: str
    income_level: str
    digital_usage: str
    interests: List[str]
    personality_traits: List[str]
    buying_behavior: Dict[str, str]


class Comment(BaseModel):
    """Individual comment in a discussion."""
    participant_id: int
    participant_name: str
    comment: str
    sentiment: str  # "positivo", "neutral", "negativo"


class Discussion(BaseModel):
    """Discussion thread for a specific question."""
    question: str
    conversation: List[Comment]


class DemographicPattern(BaseModel):
    """Demographic pattern identified in the focus group."""
    pattern: str
    affected_demographics: List[str]


class SentimentBreakdown(BaseModel):
    """Detailed sentiment analysis breakdown."""
    positive_aspects: List[str]
    negative_aspects: List[str]
    neutral_observations: List[str]


class SentimentAnalysis(BaseModel):
    """Overall sentiment analysis."""
    overall: str
    breakdown: SentimentBreakdown


class FocusGroupSummary(BaseModel):
    """Summary of focus group results."""
    key_insights: List[str]
    sentiment_analysis: SentimentAnalysis
    demographic_patterns: List[DemographicPattern]
    recommendations: List[str]


class FocusGroupSimulation(BaseModel):
    """Complete focus group simulation results."""
    discussions: List[Discussion]
    summary: FocusGroupSummary


class FocusGroupResponse(BaseModel):
    """Response schema for focus group simulation."""
    status: str
    error_message: Optional[str] = None
    focus_group_simulation: Optional[FocusGroupSimulation] = None
    timestamp: str
    simulation_id: Optional[str] = None  # ID of the saved simulation in database


class TextAssistResponse(BaseModel):
    """Response schema for text assistance."""
    status: str
    suggestions: Optional[List[str]] = None
    error_message: Optional[str] = None
