#!/usr/bin/env python3
"""
Test script for buyer persona endpoint
Run this to diagnose the 500 error issue
"""

import asyncio
import json
import logging
import sys
import traceback
from datetime import datetime

# Add the app directory to the path
sys.path.append('/Users/<USER>/Desktop/emma-studio--main/backend')

from app.api.endpoints.buyer_persona import generate_buyer_personas
from app.schemas.buyer_persona import BuyerPersonaRequest
from fastapi import Request
from unittest.mock import Mock

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_buyer_persona_endpoint():
    """Test the buyer persona endpoint directly"""
    
    print("🧪 Testing Buyer Persona Endpoint Directly")
    print("=" * 50)
    
    # Create a mock request
    mock_http_request = Mock(spec=Request)
    mock_http_request.headers = {"X-Request-ID": "test_direct_call"}
    
    # Create test data that matches what the frontend sends
    test_data = {
        "product_description": "Una plataforma de marketing digital con IA para empresas pequeñas y medianas",
        "num_personas": 2,
        "industry": "Tecnología",
        "target_market": "Pequeñas empresas",
        "business_goals": "Aumentar ventas y mejorar engagement",
        "competitors": "HubSpot, Mailchimp",
        "target_countries": ["España", "México"],
        "request_timestamp": int(datetime.now().timestamp() * 1000),
        "request_id": "test_direct_call",
        "unique_seed": "test_seed_123",
        "generation_context": "Direct test call"
    }
    
    print(f"📝 Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        # Create the request object
        print("\n🔧 Creating BuyerPersonaRequest object...")
        request = BuyerPersonaRequest(**test_data)
        print(f"✅ Request object created successfully: {request}")
        
        # Call the endpoint function directly
        print("\n🚀 Calling generate_buyer_personas function...")
        response = await generate_buyer_personas(request, mock_http_request)
        
        print(f"✅ SUCCESS! Response received:")
        print(f"Status: {response.status}")
        print(f"Number of personas: {len(response.buyer_personas)}")
        print(f"AI Model: {response.ai_model_used}")
        print(f"Generation time: {response.generation_time_seconds}s")
        
        if response.buyer_personas:
            print(f"First persona: {response.buyer_personas[0].name}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {type(e).__name__}: {str(e)}")
        print(f"📍 Traceback:")
        traceback.print_exc()
        return False

async def test_ai_service_directly():
    """Test the AI service directly"""
    
    print("\n🤖 Testing AI Service Directly")
    print("=" * 50)
    
    try:
        from app.services.buyer_persona_service import buyer_persona_service
        
        # Check service status
        print("🔍 Checking service status...")
        status = buyer_persona_service.get_service_status()
        print(f"Service status: {json.dumps(status, indent=2)}")
        
        # Test AI availability
        print(f"AI Available: {status['ai_available']}")
        print(f"Model Name: {status['model_name']}")
        
        return status['ai_available']
        
    except Exception as e:
        print(f"❌ AI Service Error: {type(e).__name__}: {str(e)}")
        traceback.print_exc()
        return False

async def test_gemini_api_directly():
    """Test Gemini API directly"""
    
    print("\n🔑 Testing Gemini API Directly")
    print("=" * 50)
    
    try:
        from app.services.ai_provider_service import GeminiProvider
        
        provider = GeminiProvider()
        print(f"Gemini model initialized: {provider.model is not None}")
        
        if provider.model:
            print("🧪 Testing content generation...")
            test_prompt = "Generate a simple test response in JSON format with a 'test' field containing 'success'."
            
            response = await provider.generate_content(test_prompt)
            print(f"✅ Gemini response: {response[:200]}...")
            
            return True
        else:
            print("❌ Gemini model not initialized")
            return False
            
    except Exception as e:
        print(f"❌ Gemini API Error: {type(e).__name__}: {str(e)}")
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    
    print("🚀 BUYER PERSONA ENDPOINT DIAGNOSTIC")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    # Test 1: AI Service Status
    ai_available = await test_ai_service_directly()
    
    # Test 2: Gemini API Direct
    gemini_working = await test_gemini_api_directly()
    
    # Test 3: Full Endpoint Test
    endpoint_working = await test_buyer_persona_endpoint()
    
    # Summary
    print("\n📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    print(f"AI Service Available: {'✅' if ai_available else '❌'}")
    print(f"Gemini API Working: {'✅' if gemini_working else '❌'}")
    print(f"Endpoint Working: {'✅' if endpoint_working else '❌'}")
    
    if endpoint_working:
        print("\n🎉 ALL TESTS PASSED - Endpoint should work!")
    else:
        print("\n❌ TESTS FAILED - Check the errors above")
        
        if not ai_available:
            print("🔧 RECOMMENDATION: Check AI service configuration and API keys")
        if not gemini_working:
            print("🔧 RECOMMENDATION: Check Gemini API key and network connectivity")

if __name__ == "__main__":
    asyncio.run(main())
