#!/bin/bash

# Emma Studio - Smart File Watcher with Clean Restart
# Provides hot-reload functionality without uvic<PERSON>'s problematic auto-reload

echo "🔍 Emma Studio - SMART FILE WATCHER"
echo "Watching for changes and restarting cleanly..."
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

BACKEND_PID=""
FRONTEND_PID=""

# Function to start services
start_services() {
    echo -e "${BLUE}🚀 Starting Emma Studio services...${NC}"
    
    # Kill any existing processes
    lsof -ti :3002 | xargs kill -9 2>/dev/null || true
    lsof -ti :8001 | xargs kill -9 2>/dev/null || true
    
    # Start backend (NO --reload)
    cd backend
    python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 > ../backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    
    # Start frontend
    cd client
    npm run dev-clean > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    cd ..
    
    echo -e "${GREEN}✅ Services started (Backend PID: $BACKEND_PID, Frontend PID: $FRONTEND_PID)${NC}"
    echo -e "${GREEN}   Frontend: http://localhost:3002${NC}"
    echo -e "${GREEN}   Backend:  http://localhost:8001${NC}"
}

# Function to stop services
stop_services() {
    echo -e "${YELLOW}🛑 Stopping services...${NC}"
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Force kill any remaining processes
    lsof -ti :3002 | xargs kill -9 2>/dev/null || true
    lsof -ti :8001 | xargs kill -9 2>/dev/null || true
    
    sleep 2
    echo -e "${GREEN}✅ Services stopped${NC}"
}

# Function to restart backend only
restart_backend() {
    echo -e "${YELLOW}🔄 Restarting backend...${NC}"
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    lsof -ti :8001 | xargs kill -9 2>/dev/null || true
    
    sleep 1
    
    cd backend
    python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 > ../backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    
    echo -e "${GREEN}✅ Backend restarted (PID: $BACKEND_PID)${NC}"
}

# Cleanup function
cleanup() {
    echo ""
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    stop_services
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start initial services
start_services

# Check if fswatch is available
if ! command -v fswatch &> /dev/null; then
    echo -e "${RED}❌ fswatch not found. Installing...${NC}"
    echo "Please install fswatch: brew install fswatch"
    echo "For now, services are running without auto-restart."
    echo "Press Ctrl+C to stop."
    wait
    exit 1
fi

echo -e "${BLUE}👀 Watching for file changes...${NC}"
echo -e "${YELLOW}💡 Backend will restart on Python file changes${NC}"
echo -e "${YELLOW}💡 Press Ctrl+C to stop${NC}"
echo ""

# Watch for changes in backend Python files
fswatch -o backend/app --exclude=".*\.pyc$" --exclude=".*__pycache__.*" | while read num; do
    echo -e "${BLUE}📝 Backend files changed, restarting...${NC}"
    restart_backend
    
    # Wait a moment for restart
    sleep 3
    
    # Test if backend is responding
    if curl -s http://localhost:8001/api/v1/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend restart successful${NC}"
    else
        echo -e "${RED}❌ Backend restart failed, check logs${NC}"
        echo "Last 10 lines of backend.log:"
        tail -10 backend.log
    fi
    echo ""
done
