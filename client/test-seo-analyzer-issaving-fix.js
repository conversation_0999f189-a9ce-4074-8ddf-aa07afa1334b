// Test script to verify the isSaving fix in SEOAnalyzerMain component
// Run this in the browser console on the SEO Analyzer page

console.log('🧪 Testing isSaving variable fix in SEOAnalyzerMain...');

async function testIsSavingFix() {
  try {
    console.log('\n=== 🔍 CHECKING COMPONENT RENDERING ===');
    
    // Check if the SEO Analyzer component is rendered without errors
    const seoAnalyzerElement = document.querySelector('[data-testid="seo-analyzer"]') || 
                              document.querySelector('.seo-analyzer') ||
                              document.querySelector('div[class*="seo"]');
    
    if (seoAnalyzerElement) {
      console.log('✅ SEO Analyzer component found in DOM');
    } else {
      console.log('ℹ️ SEO Analyzer component not found (may not be on this page)');
    }
    
    // Check for any JavaScript errors in the console
    const errorCount = window.console.error.length || 0;
    console.log(`📊 Console error count: ${errorCount}`);
    
    // Look for the "Guardar en Favoritos" button that uses isSaving
    const saveButton = Array.from(document.querySelectorAll('button')).find(btn => 
      btn.textContent && (btn.textContent.includes('Guardar en Favoritos') || btn.textContent.includes('Guardando'))
    );
    
    if (saveButton) {
      console.log('✅ Save to Favorites button found:', {
        text: saveButton.textContent,
        disabled: saveButton.disabled,
        className: saveButton.className
      });
      
      // Check if the button shows the correct state
      if (saveButton.textContent.includes('Guardando')) {
        console.log('🔄 Button is in saving state (isSaving = true)');
      } else if (saveButton.textContent.includes('Guardar en Favoritos')) {
        console.log('✅ Button is in normal state (isSaving = false)');
      }
    } else {
      console.log('ℹ️ Save to Favorites button not found (may not be visible yet)');
    }
    
    console.log('\n=== 🔍 CHECKING FOR REACT ERRORS ===');
    
    // Check for React error boundaries or error messages
    const errorBoundary = document.querySelector('[data-react-error]') ||
                         document.querySelector('.react-error') ||
                         document.querySelector('[class*="error"]');
    
    if (errorBoundary) {
      console.warn('⚠️ React error boundary detected:', errorBoundary.textContent);
    } else {
      console.log('✅ No React error boundaries detected');
    }
    
    // Check for any "ReferenceError: isSaving is not defined" in the page
    const pageText = document.body.textContent || '';
    if (pageText.includes('isSaving is not defined')) {
      console.error('❌ isSaving error still present in page content');
    } else {
      console.log('✅ No isSaving reference errors found in page content');
    }
    
    console.log('\n=== 🔍 TESTING COMPONENT INTERACTION ===');
    
    // Try to navigate to the SEO Analyzer tab if tabs are present
    const seoTab = Array.from(document.querySelectorAll('button, [role="tab"]')).find(tab => 
      tab.textContent && tab.textContent.toLowerCase().includes('analizador')
    );
    
    if (seoTab) {
      console.log('🔄 Attempting to click SEO Analyzer tab...');
      try {
        seoTab.click();
        setTimeout(() => {
          console.log('✅ SEO Analyzer tab clicked successfully');
          
          // Check if the component rendered without errors after tab switch
          const urlInput = document.querySelector('input[type="url"]') ||
                          document.querySelector('input[placeholder*="URL"]') ||
                          document.querySelector('input[placeholder*="url"]');
          
          if (urlInput) {
            console.log('✅ URL input field found - component rendered successfully');
          } else {
            console.log('ℹ️ URL input not found - may still be loading');
          }
        }, 1000);
      } catch (error) {
        console.warn('⚠️ Could not click SEO tab:', error.message);
      }
    } else {
      console.log('ℹ️ SEO Analyzer tab not found');
    }
    
    console.log('\n=== 🏁 TEST SUMMARY ===');
    console.log('✅ isSaving variable fix test completed');
    console.log('💡 If no errors were reported above, the fix is working correctly');
    console.log('🔧 The isSaving variable should now be properly destructured from useSEOAnalysisHistory hook');
    
    return true;
    
  } catch (error) {
    console.error('💥 Test error:', error);
    return false;
  }
}

// Run the test
testIsSavingFix();
