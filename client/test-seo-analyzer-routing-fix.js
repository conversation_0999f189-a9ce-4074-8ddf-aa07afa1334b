// Test script to verify SEO Analyzer routing fix
// Run this in the browser console

console.log('🧪 Testing SEO Analyzer routing fix...');

async function testSEOAnalyzerRouting() {
  try {
    console.log('\n=== 🔍 TESTING ROUTE ACCESSIBILITY ===');
    
    // Test the main routes that should work for SEO Analyzer
    const routesToTest = [
      '/tools/seo-analyzer',
      '/dashboard/herramientas/seo-analyzer'
    ];
    
    console.log('📋 Routes to test:', routesToTest);
    
    // Check current URL
    const currentUrl = window.location.href;
    console.log('🌐 Current URL:', currentUrl);
    
    // Check if we're on the SEO Analyzer page
    const isSEOAnalyzerPage = currentUrl.includes('seo-analyzer');
    console.log('🎯 On SEO Analyzer page:', isSEOAnalyzerPage);
    
    if (isSEOAnalyzerPage) {
      console.log('✅ Successfully accessed SEO Analyzer page!');
      
      // Check if the component is rendered
      console.log('\n=== 🔍 CHECKING COMPONENT RENDERING ===');
      
      // Look for SEO Analyzer specific elements
      const urlInput = document.querySelector('input[type="url"]') ||
                      document.querySelector('input[placeholder*="URL"]') ||
                      document.querySelector('input[placeholder*="url"]');
      
      if (urlInput) {
        console.log('✅ URL input field found - SEO Analyzer component rendered');
        console.log('📊 Input details:', {
          placeholder: urlInput.placeholder,
          type: urlInput.type,
          value: urlInput.value
        });
      } else {
        console.log('⚠️ URL input not found - component may still be loading');
      }
      
      // Look for tabs (Analizador, Dashboard)
      const tabs = Array.from(document.querySelectorAll('[role="tab"], button')).filter(tab => 
        tab.textContent && (
          tab.textContent.toLowerCase().includes('analizador') ||
          tab.textContent.toLowerCase().includes('dashboard')
        )
      );
      
      if (tabs.length > 0) {
        console.log('✅ SEO Analyzer tabs found:', tabs.map(tab => tab.textContent));
      } else {
        console.log('ℹ️ SEO Analyzer tabs not found - may still be loading');
      }
      
      // Check for any error messages
      const errorElements = document.querySelectorAll('[class*="error"], .error, [data-error]');
      if (errorElements.length > 0) {
        console.warn('⚠️ Error elements found:', Array.from(errorElements).map(el => el.textContent));
      } else {
        console.log('✅ No error elements detected');
      }
      
      // Check for 404 content
      const pageText = document.body.textContent || '';
      if (pageText.includes('404') || pageText.includes('Page Not Found') || pageText.includes('not found')) {
        console.error('❌ 404 error content detected in page');
      } else {
        console.log('✅ No 404 error content detected');
      }
      
    } else {
      console.log('ℹ️ Not currently on SEO Analyzer page');
    }
    
    console.log('\n=== 🔍 TESTING NAVIGATION ===');
    
    // Test programmatic navigation to SEO Analyzer
    const testNavigation = (url) => {
      return new Promise((resolve) => {
        console.log(`🔄 Testing navigation to: ${url}`);
        
        // Save current URL
        const originalUrl = window.location.href;
        
        try {
          // Navigate to the URL
          window.history.pushState({}, '', url);
          
          // Trigger a popstate event to simulate navigation
          window.dispatchEvent(new PopStateEvent('popstate'));
          
          setTimeout(() => {
            const newUrl = window.location.href;
            console.log(`📍 Navigated to: ${newUrl}`);
            
            // Check if navigation was successful
            if (newUrl.includes(url.replace(window.location.origin, ''))) {
              console.log('✅ Navigation successful');
              resolve(true);
            } else {
              console.log('⚠️ Navigation may not have completed');
              resolve(false);
            }
            
            // Restore original URL
            window.history.pushState({}, '', originalUrl);
          }, 1000);
          
        } catch (error) {
          console.error('❌ Navigation error:', error);
          resolve(false);
        }
      });
    };
    
    // Test the short route
    if (!currentUrl.includes('/tools/seo-analyzer')) {
      await testNavigation('/tools/seo-analyzer');
    }
    
    console.log('\n=== 🔍 CHECKING ROUTE CONFIGURATION ===');
    
    // Check if ToolPage component is available
    if (window.React && window.React.version) {
      console.log('✅ React is available:', window.React.version);
    }
    
    // Check for router configuration
    const routerElements = document.querySelectorAll('[data-router], [class*="router"]');
    if (routerElements.length > 0) {
      console.log('✅ Router elements found in DOM');
    }
    
    console.log('\n=== 🏁 ROUTING TEST SUMMARY ===');
    console.log('✅ SEO Analyzer routing fix test completed');
    console.log('🎯 Key findings:');
    console.log('  - /tools/seo-analyzer route should now be accessible');
    console.log('  - /dashboard/herramientas/seo-analyzer route should continue working');
    console.log('  - Component should render without 404 errors');
    console.log('  - Additional tool routes have been added for consistency');
    
    return true;
    
  } catch (error) {
    console.error('💥 Routing test error:', error);
    return false;
  }
}

// Run the test
testSEOAnalyzerRouting();
