/**
 * Auto-Save Diagnostic Script
 * Run this in the browser console to diagnose auto-save issues
 */

console.log('🔍 Auto-Save Diagnostic Starting...');

async function diagnoseAutoSave() {
  console.log('='.repeat(50));
  console.log('🧪 AUTO-SAVE DIAGNOSTIC REPORT');
  console.log('='.repeat(50));
  
  // 1. Check authentication
  console.log('\n1️⃣ AUTHENTICATION CHECK');
  try {
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error) {
      console.log('❌ Auth error:', error.message);
      return false;
    }
    if (user) {
      console.log('✅ User authenticated:', user.email);
      console.log('📝 User ID:', user.id);
    } else {
      console.log('❌ No authenticated user');
      return false;
    }
  } catch (error) {
    console.log('❌ Auth check failed:', error.message);
    return false;
  }
  
  // 2. Check service availability
  console.log('\n2️⃣ SERVICE AVAILABILITY CHECK');
  if (window.designAnalysisService) {
    console.log('✅ designAnalysisService available');
    
    // Check service methods
    const methods = ['saveAnalysis', 'getUserAnalyses', 'deleteAnalysis'];
    methods.forEach(method => {
      if (typeof window.designAnalysisService[method] === 'function') {
        console.log(`✅ ${method} method available`);
      } else {
        console.log(`❌ ${method} method missing`);
      }
    });
  } else {
    console.log('❌ designAnalysisService not available');
    return false;
  }
  
  // 3. Check database connection
  console.log('\n3️⃣ DATABASE CONNECTION CHECK');
  try {
    const { data: { user } } = await window.supabase.auth.getUser();
    const testQuery = await window.supabase
      .from('design_analyses')
      .select('id')
      .eq('user_id', user.id)
      .limit(1);
    
    if (testQuery.error) {
      console.log('❌ Database query error:', testQuery.error.message);
    } else {
      console.log('✅ Database connection working');
      console.log('📊 Query returned:', testQuery.data?.length || 0, 'records');
    }
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
  }
  
  // 4. Test auto-save functionality
  console.log('\n4️⃣ AUTO-SAVE FUNCTIONALITY TEST');
  try {
    const { data: { user } } = await window.supabase.auth.getUser();
    
    const testData = {
      user_id: user.id,
      original_filename: 'diagnostic-test.png',
      file_size: 1024,
      file_type: 'image/png',
      file_url: null,
      overall_score: 75,
      complexity_scores: { visual: 70, cognitive: 75, structural: 80 },
      analysis_areas: [{
        name: 'Test Area',
        score: 75,
        description: 'Diagnostic test area',
        recommendations: ['Test recommendation']
      }],
      recommendations: ['Test overall recommendation'],
      ai_analysis_summary: 'Diagnostic test summary',
      gemini_analysis: 'Diagnostic test analysis',
      agent_message: 'Diagnostic test message',
      visuai_insights: 'Diagnostic test insights',
      tags: ['diagnostic', 'auto-save-test']
    };
    
    console.log('💾 Testing saveAnalysis...');
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testData, null);
    
    if (savedAnalysis && savedAnalysis.id) {
      console.log('✅ Auto-save test PASSED');
      console.log('📝 Saved analysis ID:', savedAnalysis.id);
      console.log('📅 Created at:', savedAnalysis.created_at);
      
      // Verify it appears in user analyses
      console.log('🔍 Verifying in user history...');
      const userAnalyses = await window.designAnalysisService.getUserAnalyses(user.id, 5);
      const foundAnalysis = userAnalyses.find(a => a.id === savedAnalysis.id);
      
      if (foundAnalysis) {
        console.log('✅ Analysis found in user history');
      } else {
        console.log('❌ Analysis NOT found in user history');
      }
      
      // Clean up
      console.log('🧹 Cleaning up test data...');
      await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('✅ Test data cleaned up');
      
      return true;
    } else {
      console.log('❌ Auto-save test FAILED - no analysis returned');
      return false;
    }
    
  } catch (error) {
    console.log('❌ Auto-save test FAILED:', error.message);
    console.log('🔍 Error details:', error);
    return false;
  }
}

// 5. Check for common issues
async function checkCommonIssues() {
  console.log('\n5️⃣ COMMON ISSUES CHECK');
  
  // Check for React Query client
  if (window.queryClient || document.querySelector('[data-reactroot]')) {
    console.log('✅ React Query context likely available');
  } else {
    console.log('⚠️ React Query context may not be available');
  }
  
  // Check for network issues
  try {
    const response = await fetch('/api/health', { method: 'HEAD' });
    if (response.ok) {
      console.log('✅ Network connectivity good');
    } else {
      console.log('⚠️ Network issues detected:', response.status);
    }
  } catch (error) {
    console.log('⚠️ Network connectivity issues:', error.message);
  }
  
  // Check browser console for errors
  const errors = [];
  const originalError = console.error;
  console.error = (...args) => {
    errors.push(args.join(' '));
    originalError.apply(console, args);
  };
  
  setTimeout(() => {
    console.error = originalError;
    if (errors.length > 0) {
      console.log('⚠️ Recent console errors detected:');
      errors.forEach(error => console.log('  -', error));
    } else {
      console.log('✅ No recent console errors');
    }
  }, 1000);
}

// Run diagnostics
diagnoseAutoSave().then(success => {
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('🎉 AUTO-SAVE DIAGNOSTIC: PASSED');
    console.log('✅ Auto-save functionality is working correctly');
  } else {
    console.log('💥 AUTO-SAVE DIAGNOSTIC: FAILED');
    console.log('❌ Auto-save functionality has issues');
  }
  console.log('='.repeat(50));
  
  // Run common issues check
  checkCommonIssues();
}).catch(error => {
  console.error('💥 Diagnostic failed:', error);
});

// Export for manual use
window.diagnoseAutoSave = diagnoseAutoSave;
console.log('🔧 Diagnostic function available as window.diagnoseAutoSave()');
