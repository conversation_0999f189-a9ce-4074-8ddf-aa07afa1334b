// Real-time debugging script for SEO History tab
// This will monitor React Query execution and data flow in real-time

console.log('🔍 Real-time SEO History Debug');
console.log('==============================');

let debugInterval;
let queryClient;

async function initializeDebugger() {
  console.log('\n🚀 Initializing Real-time Debugger...');
  
  try {
    // Get React Query client
    const { useQueryClient } = await import('@tanstack/react-query');
    
    // We need to access the query client from a component context
    // For now, let's try to access it from the window if available
    if (window.__REACT_QUERY_CLIENT__) {
      queryClient = window.__REACT_QUERY_CLIENT__;
      console.log('✅ Found React Query client on window');
    } else {
      console.log('⚠️ React Query client not found on window');
      console.log('💡 We\'ll monitor service calls directly');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize debugger:', error);
    return false;
  }
}

async function checkCurrentAuthState() {
  console.log('\n🔐 Checking Current Auth State...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Not authenticated');
      return null;
    }
    
    const enabledCondition = !!user?.id && user.id !== 'anonymous';
    
    console.log('✅ Auth State:', {
      userId: user.id,
      email: user.email,
      isAnonymous: user.id === 'anonymous',
      enabledCondition: enabledCondition
    });
    
    return user;
  } catch (error) {
    console.error('❌ Auth check failed:', error);
    return null;
  }
}

async function monitorServiceCalls(user) {
  console.log('\n📡 Monitoring Service Calls...');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Wrap service methods to monitor calls
    const originalGetRecent = seoAnalysisService.getRecentAnalyses;
    const originalGetFavorites = seoAnalysisService.getFavoriteAnalyses;
    
    seoAnalysisService.getRecentAnalyses = async function(...args) {
      console.log('🔄 SERVICE CALL: getRecentAnalyses() called');
      const startTime = Date.now();
      
      try {
        const result = await originalGetRecent.apply(this, args);
        const duration = Date.now() - startTime;
        console.log('✅ SERVICE RESULT: getRecentAnalyses()', {
          duration: `${duration}ms`,
          count: result.length,
          analyses: result.map(a => ({
            id: a.id,
            url: a.url,
            score: a.overall_score,
            created_at: a.created_at
          }))
        });
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        console.error('❌ SERVICE ERROR: getRecentAnalyses()', {
          duration: `${duration}ms`,
          error: error.message
        });
        throw error;
      }
    };
    
    seoAnalysisService.getFavoriteAnalyses = async function(...args) {
      console.log('🔄 SERVICE CALL: getFavoriteAnalyses() called');
      const startTime = Date.now();
      
      try {
        const result = await originalGetFavorites.apply(this, args);
        const duration = Date.now() - startTime;
        console.log('✅ SERVICE RESULT: getFavoriteAnalyses()', {
          duration: `${duration}ms`,
          count: result.length,
          analyses: result.map(a => ({
            id: a.id,
            url: a.url,
            score: a.overall_score,
            is_favorite: a.is_favorite
          }))
        });
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        console.error('❌ SERVICE ERROR: getFavoriteAnalyses()', {
          duration: `${duration}ms`,
          error: error.message
        });
        throw error;
      }
    };
    
    console.log('✅ Service call monitoring active');
    
    // Test the service calls directly
    console.log('\n🧪 Testing Service Calls Directly...');
    
    console.log('🔄 Direct call to getRecentAnalyses...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    
    console.log('🔄 Direct call to getFavoriteAnalyses...');
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    
    return { recentAnalyses, favoriteAnalyses };
  } catch (error) {
    console.error('❌ Service monitoring setup failed:', error);
    return null;
  }
}

async function checkReactQueryCache() {
  console.log('\n🗄️ Checking React Query Cache...');
  
  try {
    // Try to access React Query DevTools data
    if (window.__REACT_QUERY_DEVTOOLS_GLOBAL_HOOK__) {
      console.log('✅ React Query DevTools detected');
    }
    
    // Check if we can access query cache
    if (queryClient) {
      const cache = queryClient.getQueryCache();
      const queries = cache.getAll();
      
      console.log('📊 Query Cache Status:', {
        totalQueries: queries.length,
        seoQueries: queries.filter(q => 
          q.queryKey.some(key => 
            typeof key === 'string' && key.includes('seo-analyses')
          )
        ).length
      });
      
      // Look for SEO analysis queries specifically
      const seoQueries = queries.filter(q => 
        q.queryKey.some(key => 
          typeof key === 'string' && key.includes('seo-analyses')
        )
      );
      
      seoQueries.forEach(query => {
        console.log('🔍 SEO Query Found:', {
          queryKey: query.queryKey,
          state: query.state.status,
          data: query.state.data ? `${query.state.data.length} items` : 'No data',
          error: query.state.error?.message || 'No error',
          lastUpdated: query.state.dataUpdatedAt ? new Date(query.state.dataUpdatedAt).toLocaleString() : 'Never'
        });
      });
    } else {
      console.log('⚠️ Cannot access query cache - client not available');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Cache check failed:', error);
    return false;
  }
}

async function monitorHistoryTabAccess() {
  console.log('\n👁️ Monitoring History Tab Access...');
  
  // Look for History tab button
  const historyTab = Array.from(document.querySelectorAll('button, [role="tab"]'))
    .find(el => el.textContent?.includes('Historial') || el.textContent?.includes('History'));
  
  if (historyTab) {
    console.log('✅ History tab found');
    
    // Add click listener to monitor when it's accessed
    const originalClick = historyTab.onclick;
    historyTab.addEventListener('click', function(event) {
      console.log('🖱️ HISTORY TAB CLICKED');
      console.log('⏰ Timestamp:', new Date().toLocaleString());
      
      // Check if queries should execute after tab click
      setTimeout(() => {
        console.log('🔄 Checking for query execution after tab click...');
      }, 100);
      
      if (originalClick) {
        originalClick.call(this, event);
      }
    });
    
    console.log('✅ History tab click monitoring active');
  } else {
    console.log('⚠️ History tab not found in current page');
  }
}

async function createTestAnalysisForDebugging(user) {
  console.log('\n💾 Creating Test Analysis for Debugging...');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    const testData = {
      user_id: user.id,
      url: `https://example.com/debug-realtime-${Date.now()}`,
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 85,
      basic_info: { 
        title: 'Real-time Debug Test', 
        title_length: 19,
        meta_description: 'Testing real-time debugging functionality',
        meta_description_length: 40,
        h1_tags: ['Real-time Debug Test'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 500,
        images: { total: 3, without_alt: 0 },
        links: { total: 8, internal: 5, external: 3 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Debug',
          issue: 'This is a debug test recommendation',
          importance: 'low',
          recommendation: 'Testing real-time debugging functionality'
        }
      ],
      achievements: [
        {
          category: 'Debug',
          achievement: 'Real-time Debug Test',
          description: 'Testing the real-time debugging system',
          icon: '🔍',
          impact: 'positive'
        }
      ],
      status: 'completed'
    };

    console.log('🔄 Saving test analysis...');
    const savedAnalysis = await seoAnalysisService.saveAnalysis(testData);
    console.log('✅ Test analysis saved:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      score: savedAnalysis.overall_score
    });
    
    // Wait and check if it appears in queries
    console.log('⏳ Waiting 2 seconds for cache updates...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('🔄 Checking if analysis appears in recent analyses...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    const foundAnalysis = recentAnalyses.find(a => a.id === savedAnalysis.id);
    
    if (foundAnalysis) {
      console.log('✅ Analysis found in recent analyses');
    } else {
      console.error('❌ Analysis NOT found in recent analyses');
      console.log('🔍 Current recent analyses:', recentAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        created_at: a.created_at
      })));
    }
    
    return savedAnalysis;
  } catch (error) {
    console.error('❌ Test analysis creation failed:', error);
    return null;
  }
}

async function startRealtimeMonitoring() {
  console.log('\n⏰ Starting Real-time Monitoring...');
  
  let monitorCount = 0;
  
  debugInterval = setInterval(async () => {
    monitorCount++;
    console.log(`\n📊 Monitor Check #${monitorCount} (${new Date().toLocaleString()})`);
    
    try {
      const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
      
      // Quick service check
      const recentCount = (await seoAnalysisService.getRecentAnalyses()).length;
      const favoriteCount = (await seoAnalysisService.getFavoriteAnalyses()).length;
      
      console.log(`📈 Current counts: ${recentCount} recent, ${favoriteCount} favorites`);
      
      // Check React Query cache if available
      if (queryClient) {
        await checkReactQueryCache();
      }
      
    } catch (error) {
      console.error('❌ Monitor check failed:', error);
    }
  }, 10000); // Every 10 seconds
  
  console.log('✅ Real-time monitoring started (every 10 seconds)');
  console.log('💡 Use stopRealtimeMonitoring() to stop');
}

function stopRealtimeMonitoring() {
  if (debugInterval) {
    clearInterval(debugInterval);
    debugInterval = null;
    console.log('⏹️ Real-time monitoring stopped');
  }
}

async function runRealtimeDebug() {
  console.log('🚀 Starting Real-time SEO History Debug...\n');
  
  // Initialize
  const initialized = await initializeDebugger();
  if (!initialized) {
    console.log('❌ Failed to initialize - continuing with limited debugging');
  }
  
  // Check auth
  const user = await checkCurrentAuthState();
  if (!user) {
    console.log('❌ Not authenticated - cannot proceed');
    return;
  }
  
  // Monitor service calls
  const serviceResult = await monitorServiceCalls(user);
  
  // Check cache
  await checkReactQueryCache();
  
  // Monitor tab access
  await monitorHistoryTabAccess();
  
  // Create test analysis
  const testAnalysis = await createTestAnalysisForDebugging(user);
  
  // Start real-time monitoring
  await startRealtimeMonitoring();
  
  console.log('\n' + '='.repeat(60));
  console.log('🎯 REAL-TIME DEBUG ACTIVE');
  console.log('='.repeat(60));
  console.log('✅ Service call monitoring: Active');
  console.log('✅ History tab monitoring: Active');
  console.log('✅ Real-time monitoring: Active (every 10 seconds)');
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Navigate to SEO Analyzer (if not already there)');
  console.log('2. Click on the "Historial" tab');
  console.log('3. Watch the console for real-time debugging info');
  console.log('4. Run an SEO analysis to test auto-save');
  console.log('5. Use stopRealtimeMonitoring() when done');
}

// Export functions
window.realtimeSEODebug = {
  runRealtimeDebug,
  stopRealtimeMonitoring,
  checkCurrentAuthState,
  monitorServiceCalls,
  checkReactQueryCache,
  createTestAnalysisForDebugging
};

// Auto-run
runRealtimeDebug();

console.log('\n💡 Available functions:');
console.log('- window.realtimeSEODebug.stopRealtimeMonitoring()');
console.log('- window.realtimeSEODebug.checkReactQueryCache()');
console.log('- window.realtimeSEODebug.createTestAnalysisForDebugging(user)');
