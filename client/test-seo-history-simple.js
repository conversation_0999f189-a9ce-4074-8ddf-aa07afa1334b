// Simple test to create a test analysis and verify History tab functionality
// Run this in the browser console after signing in

console.log('🧪 Simple SEO History Test');
console.log('==========================');

async function createTestAnalysis() {
  console.log('\n🔐 Checking authentication...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Please sign in first to test the History functionality');
      console.log('💡 Go to the sign-in page and authenticate, then run this test again');
      return false;
    }
    
    console.log('✅ User authenticated:', user.email);
    
    console.log('\n💾 Creating test analysis...');
    
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    const testData = {
      user_id: user.id,
      url: `https://example.com/test-${Date.now()}`,
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: Math.floor(Math.random() * 40) + 60, // Random score 60-100
      basic_info: { 
        title: 'Test Analysis for History', 
        title_length: 25,
        meta_description: 'This is a test analysis to verify History tab functionality',
        meta_description_length: 62,
        h1_tags: ['Test Analysis'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 500,
        images: { total: 3, without_alt: 1 },
        links: { total: 8, internal: 5, external: 3 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Images',
          issue: 'Some images missing alt text',
          importance: 'medium',
          recommendation: 'Add descriptive alt text to all images for better accessibility and SEO'
        }
      ],
      achievements: [
        {
          category: 'Technical',
          achievement: 'HTTPS Enabled',
          description: 'Your site uses secure HTTPS protocol',
          icon: '🔒',
          impact: 'positive'
        }
      ],
      status: 'completed'
    };

    const savedAnalysis = await seoAnalysisService.saveAnalysis(testData);
    console.log('✅ Test analysis created:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      score: savedAnalysis.overall_score,
      created_at: savedAnalysis.created_at
    });
    
    console.log('\n🔄 Checking if analysis appears in History...');
    
    // Wait a moment for cache updates
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    const foundAnalysis = recentAnalyses.find(a => a.id === savedAnalysis.id);
    
    if (foundAnalysis) {
      console.log('✅ Analysis found in History!');
      console.log('📊 Total analyses in History:', recentAnalyses.length);
    } else {
      console.error('❌ Analysis NOT found in History');
      console.log('🔍 Available analyses:', recentAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        created_at: a.created_at
      })));
    }
    
    console.log('\n🎯 INSTRUCTIONS:');
    console.log('1. Navigate to the SEO Analyzer tool');
    console.log('2. Click on the "Historial" tab');
    console.log('3. You should see the test analysis listed');
    console.log('4. The analysis should show:');
    console.log(`   - URL: ${savedAnalysis.url}`);
    console.log(`   - Score: ${savedAnalysis.overall_score}`);
    console.log(`   - Date: ${new Date(savedAnalysis.created_at).toLocaleString()}`);
    
    return savedAnalysis;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('Error details:', {
      message: error.message,
      stack: error.stack
    });
    return false;
  }
}

async function checkHistoryTab() {
  console.log('\n🎨 Checking History Tab UI...');
  
  try {
    // Check if we're on the SEO Analyzer page
    const currentUrl = window.location.href;
    if (!currentUrl.includes('seo') && !currentUrl.includes('SEO')) {
      console.log('⚠️ You may not be on the SEO Analyzer page');
      console.log('💡 Navigate to the SEO Analyzer tool first');
    }
    
    // Look for the History tab
    const historyTab = Array.from(document.querySelectorAll('button, [role="tab"]'))
      .find(el => el.textContent?.includes('Historial') || el.textContent?.includes('History'));
    
    if (historyTab) {
      console.log('✅ History tab found in UI');
      console.log('🖱️ You can click on it to view your analyses');
      
      // Check if it's clickable
      if (!historyTab.disabled) {
        console.log('✅ History tab is clickable');
      } else {
        console.log('⚠️ History tab appears to be disabled');
      }
    } else {
      console.log('⚠️ History tab not found in current page');
      console.log('💡 Make sure you\'re on the SEO Analyzer page');
    }
    
    return true;
  } catch (error) {
    console.error('❌ UI check failed:', error);
    return false;
  }
}

async function runSimpleTest() {
  console.log('🚀 Starting Simple SEO History Test...\n');
  
  const analysisResult = await createTestAnalysis();
  const uiResult = await checkHistoryTab();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 TEST RESULTS');
  console.log('='.repeat(50));
  
  if (analysisResult && uiResult) {
    console.log('🎉 SUCCESS: Test analysis created and History tab available!');
    console.log('✅ Database integration working');
    console.log('✅ Service layer functional');
    console.log('✅ UI components available');
    console.log('\n💡 Now check the History tab in the SEO Analyzer to see your test analysis!');
  } else if (analysisResult) {
    console.log('✅ PARTIAL SUCCESS: Analysis created but UI issues detected');
    console.log('🔧 Check that you\'re on the correct page');
  } else {
    console.log('❌ FAILED: Could not create test analysis');
    console.log('🔧 Check authentication and database connection');
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Navigate to SEO Analyzer if not already there');
  console.log('2. Click on the "Historial" tab');
  console.log('3. Look for your test analysis');
  console.log('4. If empty, check browser console for errors');
}

// Auto-run the test
runSimpleTest();

// Export for manual use
window.testSEOHistorySimple = {
  runSimpleTest,
  createTestAnalysis,
  checkHistoryTab
};

console.log('\n💡 Manual testing available:');
console.log('- window.testSEOHistorySimple.runSimpleTest()');
console.log('- window.testSEOHistorySimple.createTestAnalysis()');
console.log('- window.testSEOHistorySimple.checkHistoryTab()');
