<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Data Flow Debug</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
        .info-box {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .warning {
            background: #fef3c7;
            color: #92400e;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #f59e0b;
            margin: 15px 0;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #10b981;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 User Data Flow Debug</h1>
        
        <div class="info-box">
            <h3>🎯 Purpose</h3>
            <p>This tool analyzes why the username is showing "Usuario Demo" instead of the actual user's name after login.</p>
            <p><strong>What it checks:</strong></p>
            <ul>
                <li>Supabase user object structure and metadata</li>
                <li>Username extraction logic effectiveness</li>
                <li>Available user data sources (user_metadata, identities, etc.)</li>
                <li>Recommended fixes for username display issues</li>
            </ul>
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> You must be logged in for this debug to work properly. 
            If you're not logged in, please <a href="/login">log in first</a>, then return to this page.
        </div>

        <div style="text-align: center;">
            <button class="button" onclick="runDebug()">🔍 Debug User Data Flow</button>
            <button class="button secondary" onclick="clearConsole()">🧹 Clear Console</button>
            <button class="button secondary" onclick="goToLogin()">🔐 Go to Login</button>
        </div>

        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        async function runDebug() {
            clearConsole();
            
            try {
                // Load and run the debug script
                const script = document.createElement('script');
                script.src = '/debug-user-data.js';
                script.onload = () => {
                    console.log('✅ Debug script loaded and executed');
                };
                script.onerror = () => {
                    console.error('❌ Failed to load debug script');
                };
                document.head.appendChild(script);
                
            } catch (error) {
                console.error('Debug execution failed:', error);
            }
        }
        
        // Auto-run debug on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔍 User Data Flow Debug Tool Loaded');
                console.log('💡 Click "Debug User Data Flow" to analyze username display issues');
                console.log('🔐 Make sure you are logged in for accurate results');
            }, 500);
        });
    </script>
</body>
</html>
