/**
 * Test Image Loading Optimization
 * Verifies that the optimized image loading system works efficiently
 */

console.log('🧪 TESTING IMAGE LOADING OPTIMIZATION');

async function testImageLoadingOptimization() {
  try {
    console.log('🔍 Testing optimized image loading system...');
    
    // Check authentication
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ Not authenticated');
      return false;
    }
    
    console.log('✅ User:', user.email);
    
    // Test 1: Check if we have existing analyses with images
    console.log('\n1️⃣ Getting existing analyses to test image loading...');
    
    const analyses = await window.designAnalysisService.getUserAnalyses(user.id, { limit: 3 });
    console.log('📊 Found analyses:', analyses.length);
    
    if (analyses.length === 0) {
      console.log('⚠️ No analyses found to test image loading');
      return false;
    }
    
    // Find an analysis with a file_url
    const analysisWithImage = analyses.find(a => a.file_url);
    
    if (!analysisWithImage) {
      console.log('⚠️ No analyses with images found');
      return false;
    }
    
    console.log('✅ Found analysis with image:', {
      id: analysisWithImage.id,
      filename: analysisWithImage.original_filename,
      file_url: analysisWithImage.file_url
    });
    
    // Test 2: Test the optimized getImageUrl method
    console.log('\n2️⃣ Testing optimized getImageUrl method...');
    
    const startTime = performance.now();
    
    try {
      const imageUrl = await window.designAnalysisService.getImageUrl(analysisWithImage.file_url);
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      if (imageUrl) {
        console.log('✅ Image URL retrieved successfully!');
        console.log('⚡ Load time:', Math.round(loadTime), 'ms');
        console.log('🔗 URL type:', typeof imageUrl);
        console.log('🔗 URL starts with:', imageUrl.substring(0, 20) + '...');
        
        // Test if the URL is a blob URL (indicates direct Supabase access)
        if (imageUrl.startsWith('blob:')) {
          console.log('✅ OPTIMIZATION VERIFIED: Using direct Supabase access (blob URL)');
        } else {
          console.log('⚠️ Using different method (not blob URL)');
        }
        
        // Test 3: Verify image can be loaded
        console.log('\n3️⃣ Testing image loading in browser...');
        
        const img = new Image();
        const imageLoadPromise = new Promise((resolve, reject) => {
          img.onload = () => {
            console.log('✅ Image loaded successfully in browser');
            console.log('📊 Image dimensions:', img.width, 'x', img.height);
            resolve(true);
          };
          img.onerror = (error) => {
            console.log('❌ Image failed to load in browser:', error);
            reject(error);
          };
        });
        
        img.src = imageUrl;
        await imageLoadPromise;
        
        return true;
        
      } else {
        console.log('❌ getImageUrl returned null');
        return false;
      }
      
    } catch (imageError) {
      console.log('❌ getImageUrl failed:', imageError.message);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Test performance comparison
async function testPerformanceComparison() {
  console.log('\n🏃 PERFORMANCE COMPARISON TEST');
  
  try {
    const { data: { user } } = await window.supabase.auth.getUser();
    if (!user) return;
    
    const analyses = await window.designAnalysisService.getUserAnalyses(user.id, { limit: 1 });
    const analysisWithImage = analyses.find(a => a.file_url);
    
    if (!analysisWithImage) {
      console.log('⚠️ No analysis with image found for performance test');
      return;
    }
    
    console.log('🔄 Testing image loading performance...');
    
    // Test multiple loads to get average performance
    const loadTimes = [];
    const testRuns = 3;
    
    for (let i = 1; i <= testRuns; i++) {
      console.log(`📊 Performance test run ${i}/${testRuns}`);
      
      const startTime = performance.now();
      const imageUrl = await window.designAnalysisService.getImageUrl(analysisWithImage.file_url);
      const endTime = performance.now();
      
      if (imageUrl) {
        const loadTime = endTime - startTime;
        loadTimes.push(loadTime);
        console.log(`  ⚡ Run ${i}: ${Math.round(loadTime)}ms`);
        
        // Clean up blob URL to prevent memory leaks
        if (imageUrl.startsWith('blob:')) {
          URL.revokeObjectURL(imageUrl);
        }
      }
    }
    
    if (loadTimes.length > 0) {
      const avgTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length;
      const minTime = Math.min(...loadTimes);
      const maxTime = Math.max(...loadTimes);
      
      console.log('\n📊 PERFORMANCE RESULTS:');
      console.log(`  ⚡ Average: ${Math.round(avgTime)}ms`);
      console.log(`  ⚡ Fastest: ${Math.round(minTime)}ms`);
      console.log(`  ⚡ Slowest: ${Math.round(maxTime)}ms`);
      
      if (avgTime < 1000) {
        console.log('✅ EXCELLENT: Average load time under 1 second');
      } else if (avgTime < 2000) {
        console.log('✅ GOOD: Average load time under 2 seconds');
      } else {
        console.log('⚠️ SLOW: Average load time over 2 seconds');
      }
    }
    
  } catch (error) {
    console.error('❌ Performance test failed:', error);
  }
}

// Test console noise reduction
function testConsoleNoiseReduction() {
  console.log('\n🔇 CONSOLE NOISE REDUCTION TEST');
  console.log('✅ Check console output during image loading:');
  console.log('  - Should see "📥 Loading image via Supabase" (concise)');
  console.log('  - Should NOT see "💥 Backend image proxy error" (reduced noise)');
  console.log('  - Should see "✅ Image loaded successfully" (success confirmation)');
  console.log('  - Overall logging should be cleaner and more focused');
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting image loading optimization tests...');
  
  const success = await testImageLoadingOptimization();
  
  if (success) {
    await testPerformanceComparison();
    testConsoleNoiseReduction();
    
    console.log('\n🎉 IMAGE LOADING OPTIMIZATION: SUCCESS!');
    console.log('✅ Direct Supabase access is now primary method');
    console.log('✅ Backend proxy failures no longer cause delays');
    console.log('✅ Console noise reduced significantly');
    console.log('✅ Image loading performance optimized');
    
  } else {
    console.log('\n💥 IMAGE LOADING OPTIMIZATION: NEEDS INVESTIGATION');
    console.log('❌ Image loading system may have issues');
  }
}

// Auto-run the tests
runAllTests().catch(error => {
  console.error('💥 Test execution failed:', error);
});

// Make available globally
window.testImageLoadingOptimization = testImageLoadingOptimization;
window.testPerformanceComparison = testPerformanceComparison;
console.log('🔧 Test functions available: window.testImageLoadingOptimization(), window.testPerformanceComparison()');
