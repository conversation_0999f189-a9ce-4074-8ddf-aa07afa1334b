/**
 * Run Diagnostics Script
 * This script can be loaded in the browser to run all our diagnostic tests
 */

console.log('🔍 LOADING AUTO-SAVE DIAGNOSTICS');

// Function to load and execute a script
function loadScript(src) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

// Main diagnostic runner
async function runAllDiagnostics() {
  console.log('🚀 Starting comprehensive auto-save diagnostics...');
  
  try {
    // Step 1: Basic environment check
    console.log('\n1️⃣ BASIC ENVIRONMENT CHECK');
    
    // Check if we're on the right page
    const currentUrl = window.location.href;
    console.log('📍 Current URL:', currentUrl);
    
    if (!currentUrl.includes('design-complexity-analyzer')) {
      console.log('⚠️ Not on the Design Complexity Analyzer page');
      console.log('🔄 Please navigate to: http://localhost:3002/dashboard/herramientas/design-complexity-analyzer');
      return false;
    }
    
    // Check authentication
    console.log('🔐 Checking authentication...');
    if (!window.supabase) {
      console.log('❌ Supabase not available');
      return false;
    }
    
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ User not authenticated');
      console.log('🔄 Please log in first');
      return false;
    }
    
    console.log('✅ User authenticated:', user.email);
    
    // Step 2: Check service availability
    console.log('\n2️⃣ SERVICE AVAILABILITY CHECK');
    
    if (!window.designAnalysisService) {
      console.log('❌ designAnalysisService not available');
      return false;
    }
    console.log('✅ designAnalysisService available');
    
    // Step 3: Check QueryClient
    console.log('\n3️⃣ QUERY CLIENT CHECK');
    
    if (!window.queryClient) {
      console.log('❌ QueryClient not available on window');
      console.log('🔍 This is likely the main issue!');
      return false;
    }
    console.log('✅ QueryClient available');
    
    // Step 4: Test basic query functionality
    console.log('\n4️⃣ BASIC QUERY FUNCTIONALITY TEST');
    
    const queryClient = window.queryClient;
    const cache = queryClient.getQueryCache();
    const allQueries = cache.getAll();
    
    console.log('📊 Total queries in cache:', allQueries.length);
    
    // Look for design analysis queries
    const designQueries = allQueries.filter(q => 
      Array.isArray(q.queryKey) && q.queryKey[0] === 'design-analyses'
    );
    
    console.log('📊 Design analysis queries:', designQueries.length);
    
    if (designQueries.length === 0) {
      console.log('⚠️ No design analysis queries found');
      console.log('🔄 This might be normal if no data has been loaded yet');
    }
    
    // Check for user-specific query
    const userQuery = cache.find(['design-analyses', user.id]);
    if (userQuery) {
      console.log('✅ User-specific query found');
      console.log('📊 Query status:', userQuery.state.status);
      console.log('📊 Data length:', userQuery.state.data?.length || 0);
    } else {
      console.log('⚠️ User-specific query not found');
    }
    
    // Step 5: Test service call
    console.log('\n5️⃣ SERVICE CALL TEST');
    
    try {
      console.log('🔄 Testing getUserAnalyses...');
      const analyses = await window.designAnalysisService.getUserAnalyses(user.id, 5);
      console.log('✅ Service call successful');
      console.log('📊 Returned analyses:', analyses.length);
      
      // Check if this created a query
      const afterServiceQuery = cache.find(['design-analyses', user.id]);
      if (afterServiceQuery) {
        console.log('✅ Query created/updated after service call');
        console.log('📊 Query data length:', afterServiceQuery.state.data?.length || 0);
      } else {
        console.log('❌ No query created after service call');
        console.log('🔍 This indicates a React Query setup issue');
      }
      
    } catch (serviceError) {
      console.log('❌ Service call failed:', serviceError.message);
      console.log('🔍 This might be due to backend being down');
    }
    
    // Step 6: Test invalidation
    console.log('\n6️⃣ INVALIDATION TEST');
    
    let invalidationCalled = false;
    let matchedQueries = 0;
    
    const originalInvalidate = queryClient.invalidateQueries.bind(queryClient);
    queryClient.invalidateQueries = function(filters) {
      invalidationCalled = true;
      matchedQueries = cache.findAll(filters).length;
      console.log('🔄 Invalidation called:', { filters, matchedQueries });
      return originalInvalidate(filters);
    };
    
    // Test invalidation
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    console.log('📊 Invalidation called:', invalidationCalled);
    console.log('📊 Queries matched:', matchedQueries);
    
    if (!invalidationCalled) {
      console.log('❌ Invalidation was not called');
    } else if (matchedQueries === 0) {
      console.log('⚠️ Invalidation called but matched 0 queries');
      console.log('🔍 This suggests a query key mismatch');
    } else {
      console.log('✅ Invalidation working correctly');
    }
    
    // Restore original function
    queryClient.invalidateQueries = originalInvalidate;
    
    // Step 7: UI State Check
    console.log('\n7️⃣ UI STATE CHECK');
    
    // Check if we're on the history tab
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      console.log('✅ History tab found');
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    } else {
      console.log('⚠️ History tab not found');
    }
    
    // Check for analysis cards
    const analysisCards = document.querySelectorAll('[data-testid*="analysis"], [class*="analysis"]');
    console.log('📊 Analysis cards in UI:', analysisCards.length);
    
    // Check for loading states
    const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"], .animate-spin');
    console.log('📊 Loading elements:', loadingElements.length);
    
    // Check for "no analyses" message
    const bodyText = document.body.textContent || '';
    const hasNoAnalysesMsg = bodyText.includes('No hay análisis') || 
                            bodyText.includes('Sin análisis') ||
                            bodyText.includes('historial');
    console.log('📊 Shows "no analyses" message:', hasNoAnalysesMsg);
    
    // Step 8: Component State Check
    console.log('\n8️⃣ COMPONENT STATE CHECK');
    
    // Try to find React components
    const reactElements = document.querySelectorAll('[data-reactroot] *');
    let reactComponentsFound = 0;
    
    for (const element of reactElements) {
      if (element._reactInternalFiber || element._reactInternalInstance) {
        reactComponentsFound++;
      }
    }
    
    console.log('📊 React components found:', reactComponentsFound);
    
    // Check for React DevTools
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      console.log('✅ React DevTools available');
    } else {
      console.log('⚠️ React DevTools not available');
    }
    
    // Step 9: Summary and Recommendations
    console.log('\n9️⃣ SUMMARY AND RECOMMENDATIONS');
    
    const issues = [];
    const recommendations = [];
    
    if (!window.queryClient) {
      issues.push('QueryClient not available globally');
      recommendations.push('Fix QueryClient setup in App.tsx');
    }
    
    if (designQueries.length === 0) {
      issues.push('No design analysis queries in cache');
      recommendations.push('Check useDesignAnalysis hook setup');
    }
    
    if (!userQuery) {
      issues.push('User-specific query not found');
      recommendations.push('Check query key format and user authentication');
    }
    
    if (matchedQueries === 0 && invalidationCalled) {
      issues.push('Query invalidation matches no queries');
      recommendations.push('Fix query key mismatch in invalidation calls');
    }
    
    if (analysisCards.length === 0 && !hasNoAnalysesMsg) {
      issues.push('No analysis cards and no "no analyses" message');
      recommendations.push('Check React component rendering');
    }
    
    console.log('\n⚠️ ISSUES FOUND:');
    if (issues.length === 0) {
      console.log('  🎉 No major issues detected!');
    } else {
      issues.forEach((issue, i) => {
        console.log(`  ${i + 1}. ${issue}`);
      });
    }
    
    console.log('\n🔧 RECOMMENDATIONS:');
    if (recommendations.length === 0) {
      console.log('  ✅ System appears to be working correctly');
    } else {
      recommendations.forEach((rec, i) => {
        console.log(`  ${i + 1}. ${rec}`);
      });
    }
    
    console.log('\n✅ Diagnostic completed');
    return issues.length === 0;
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
    return false;
  }
}

// Auto-run when loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', runAllDiagnostics);
} else {
  runAllDiagnostics();
}

// Make available globally
window.runAllDiagnostics = runAllDiagnostics;
console.log('🔧 Diagnostic function available as: window.runAllDiagnostics()');
