/**
 * Username Extraction Test
 * 
 * This script tests the username extraction logic with various user data scenarios
 * to identify why usernames are not being displayed correctly.
 */

console.log('🧪 Testing Username Extraction Logic...');

// Mock Supabase user data scenarios
const testUsers = [
  {
    name: "Email-only user",
    data: {
      id: "test-1",
      email: "<EMAIL>",
      user_metadata: {},
      identities: []
    }
  },
  {
    name: "User with full_name in metadata",
    data: {
      id: "test-2", 
      email: "<EMAIL>",
      user_metadata: {
        full_name: "<PERSON>"
      },
      identities: []
    }
  },
  {
    name: "User with name in metadata",
    data: {
      id: "test-3",
      email: "<EMAIL>", 
      user_metadata: {
        name: "<PERSON>"
      },
      identities: []
    }
  },
  {
    name: "Google OAuth user",
    data: {
      id: "test-4",
      email: "<EMAIL>",
      user_metadata: {
        avatar_url: "https://example.com/avatar.jpg",
        email: "<EMAIL>",
        email_verified: true,
        full_name: "<PERSON>",
        iss: "https://accounts.google.com",
        name: "<PERSON>",
        picture: "https://example.com/avatar.jpg",
        provider_id: "*********",
        sub: "*********"
      },
      identities: [
        {
          provider: "google",
          identity_data: {
            email: "<EMAIL>",
            email_verified: true,
            full_name: "Alice Johnson",
            name: "Alice Johnson",
            picture: "https://example.com/avatar.jpg",
            provider_id: "*********",
            sub: "*********"
          }
        }
      ]
    }
  },
  {
    name: "User with first_name and last_name",
    data: {
      id: "test-5",
      email: "<EMAIL>",
      user_metadata: {
        first_name: "Charlie",
        last_name: "Brown"
      },
      identities: []
    }
  },
  {
    name: "Minimal user (edge case)",
    data: {
      id: "test-6",
      email: null,
      user_metadata: null,
      identities: []
    }
  }
];

// Test the current extraction logic
function testCurrentLogic(supabaseUser) {
  return supabaseUser.user_metadata?.full_name ||
         supabaseUser.user_metadata?.name ||
         supabaseUser.email?.split("@")[0] ||
         "Usuario";
}

// Test the enhanced extraction logic
function testEnhancedLogic(supabaseUser) {
  let username = "Usuario";
  let source = "fallback";

  if (supabaseUser.user_metadata?.full_name) {
    username = supabaseUser.user_metadata.full_name;
    source = "user_metadata.full_name";
  } else if (supabaseUser.user_metadata?.name) {
    username = supabaseUser.user_metadata.name;
    source = "user_metadata.name";
  } else if (supabaseUser.identities?.[0]?.identity_data?.full_name) {
    username = supabaseUser.identities[0].identity_data.full_name;
    source = "identities[0].identity_data.full_name";
  } else if (supabaseUser.identities?.[0]?.identity_data?.name) {
    username = supabaseUser.identities[0].identity_data.name;
    source = "identities[0].identity_data.name";
  } else if (supabaseUser.user_metadata?.first_name && supabaseUser.user_metadata?.last_name) {
    username = `${supabaseUser.user_metadata.first_name} ${supabaseUser.user_metadata.last_name}`;
    source = "user_metadata.first_name + last_name";
  } else if (supabaseUser.email) {
    username = supabaseUser.email.split("@")[0];
    source = "email prefix";
  }

  return { username, source };
}

console.log('\n📊 Username Extraction Test Results:');
console.log('=====================================');

testUsers.forEach((testCase, index) => {
  console.log(`\n🧪 Test ${index + 1}: ${testCase.name}`);
  console.log('-----------------------------------');
  
  const currentResult = testCurrentLogic(testCase.data);
  const enhancedResult = testEnhancedLogic(testCase.data);
  
  console.log(`📧 Email: ${testCase.data.email || 'null'}`);
  console.log(`📋 User Metadata:`, testCase.data.user_metadata);
  console.log(`🆔 Identities:`, testCase.data.identities?.length || 0, 'identity(ies)');
  
  console.log(`\n🔍 Results:`);
  console.log(`- Current Logic: "${currentResult}"`);
  console.log(`- Enhanced Logic: "${enhancedResult.username}" (from ${enhancedResult.source})`);
  
  if (currentResult !== enhancedResult.username) {
    console.log(`✨ Enhancement improves result!`);
  } else {
    console.log(`✅ Both methods produce same result`);
  }
});

console.log('\n🎯 Summary:');
console.log('===========');
console.log('The enhanced extraction logic provides better fallback options and more detailed source tracking.');
console.log('This should help identify why usernames are showing as "Usuario Demo" in the actual application.');

console.log('\n💡 Next Steps:');
console.log('1. Check what actual user data is being received from Supabase after login');
console.log('2. Verify the enhanced logic is being used in the auth hook');
console.log('3. Ensure the dashboard is receiving the correct user object');

// Make test available globally
window.testUsernameExtraction = () => {
  testUsers.forEach((testCase, index) => {
    const result = testEnhancedLogic(testCase.data);
    console.log(`Test ${index + 1} (${testCase.name}): "${result.username}" from ${result.source}`);
  });
};

console.log('\n💡 Run testUsernameExtraction() to test again');
