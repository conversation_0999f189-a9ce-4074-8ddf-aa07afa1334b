/**
 * Login Flow Debug Script
 * 
 * This script traces the complete login data flow from Supabase authentication
 * to the frontend application state to identify where null/undefined values occur.
 */

console.log('🔍 Starting Login Flow Debug Analysis...');

async function debugLoginFlow() {
  try {
    console.log('\n📦 Step 1: Importing Supabase client...');
    const { supabase } = await import('/src/lib/supabase.ts');
    console.log('✅ Supabase client imported successfully');

    console.log('\n🔐 Step 2: Checking current authentication state...');
    
    // Check current session
    console.log('🔍 Getting current session...');
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    console.log('📊 Session check results:');
    console.log('- Session error:', sessionError);
    console.log('- Session data exists:', !!sessionData);
    console.log('- Session object:', sessionData);
    
    if (sessionError) {
      console.error('❌ Session error details:', sessionError);
    }
    
    if (sessionData?.session) {
      console.log('✅ Active session found');
      console.log('📋 Session details:');
      console.log('- Access token exists:', !!sessionData.session.access_token);
      console.log('- Refresh token exists:', !!sessionData.session.refresh_token);
      console.log('- User exists:', !!sessionData.session.user);
      
      if (sessionData.session.user) {
        console.log('\n👤 User object analysis:');
        const user = sessionData.session.user;
        
        console.log('🔍 User object structure:');
        console.log('- ID:', user.id);
        console.log('- Email:', user.email);
        console.log('- Created at:', user.created_at);
        console.log('- Last sign in:', user.last_sign_in_at);
        console.log('- Email confirmed:', user.email_confirmed_at);
        
        console.log('\n🏷️ User metadata analysis:');
        if (user.user_metadata) {
          console.log('- user_metadata exists:', true);
          console.log('- user_metadata content:', JSON.stringify(user.user_metadata, null, 2));
          
          // Check specific fields
          console.log('- full_name:', user.user_metadata.full_name);
          console.log('- name:', user.user_metadata.name);
          console.log('- username:', user.user_metadata.username);
          console.log('- first_name:', user.user_metadata.first_name);
          console.log('- last_name:', user.user_metadata.last_name);
        } else {
          console.log('⚠️ user_metadata is null/undefined');
        }
        
        console.log('\n🆔 Identities analysis:');
        if (user.identities && user.identities.length > 0) {
          console.log('- identities exist:', true);
          console.log('- identities count:', user.identities.length);
          
          user.identities.forEach((identity, index) => {
            console.log(`\n🔍 Identity ${index + 1}:`);
            console.log('- Provider:', identity.provider);
            console.log('- Identity data:', JSON.stringify(identity.identity_data, null, 2));
          });
        } else {
          console.log('⚠️ No identities found');
        }
        
        console.log('\n🧪 Testing username extraction logic...');
        
        // Test the current extraction logic
        let extractedUsername = "Usuario";
        let source = "fallback";
        
        if (user.user_metadata?.full_name) {
          extractedUsername = user.user_metadata.full_name;
          source = "user_metadata.full_name";
        } else if (user.user_metadata?.name) {
          extractedUsername = user.user_metadata.name;
          source = "user_metadata.name";
        } else if (user.identities?.[0]?.identity_data?.full_name) {
          extractedUsername = user.identities[0].identity_data.full_name;
          source = "identities[0].identity_data.full_name";
        } else if (user.identities?.[0]?.identity_data?.name) {
          extractedUsername = user.identities[0].identity_data.name;
          source = "identities[0].identity_data.name";
        } else if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
          extractedUsername = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
          source = "user_metadata.first_name + last_name";
        } else if (user.email) {
          extractedUsername = user.email.split("@")[0];
          source = "email prefix";
        }
        
        console.log(`🎯 Extracted username: "${extractedUsername}"`);
        console.log(`📍 Source: ${source}`);
        
        // Test app user creation
        console.log('\n🔄 Testing app user creation...');
        try {
          const appUser = {
            id: user.id,
            username: extractedUsername,
            email: user.email || "",
            role: "user" as const,
            isActive: true,
            createdAt: new Date(),
          };
          
          console.log('✅ App user creation successful:');
          console.log(JSON.stringify(appUser, null, 2));
          
          // Validate app user
          const validation = {
            hasId: !!appUser.id,
            hasUsername: !!appUser.username && appUser.username !== "Usuario",
            hasEmail: !!appUser.email,
            hasRole: !!appUser.role,
            isComplete: !!appUser.id && !!appUser.username && !!appUser.email
          };
          
          console.log('\n📊 App user validation:');
          Object.entries(validation).forEach(([key, value]) => {
            console.log(`- ${key}: ${value ? '✅' : '❌'}`);
          });
          
          if (validation.isComplete) {
            console.log('🎉 App user is complete and valid!');
          } else {
            console.log('⚠️ App user is missing required data');
          }
          
        } catch (appUserError) {
          console.error('❌ App user creation failed:', appUserError);
        }
        
      } else {
        console.log('⚠️ Session exists but no user object found');
      }
    } else {
      console.log('ℹ️ No active session found');
    }
    
    // Check getUser as well
    console.log('\n🔍 Step 3: Checking getUser() method...');
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    console.log('📊 getUser() results:');
    console.log('- User error:', userError);
    console.log('- User data exists:', !!userData);
    console.log('- User object exists:', !!userData?.user);
    
    if (userError) {
      console.error('❌ getUser() error details:', userError);
    }
    
    if (userData?.user) {
      console.log('✅ getUser() returned user data');
      console.log('- Same as session user:', userData.user.id === sessionData?.session?.user?.id);
    } else {
      console.log('⚠️ getUser() returned no user');
    }
    
    console.log('\n📊 Step 4: Summary and Diagnosis...');
    console.log('=====================================');
    
    if (!sessionData?.session) {
      console.log('❌ Issue: No active session found');
      console.log('💡 Solution: User needs to log in');
    } else if (!sessionData.session.user) {
      console.log('❌ Issue: Session exists but no user object');
      console.log('💡 Solution: Check Supabase authentication configuration');
    } else if (!sessionData.session.user.id || !sessionData.session.user.email) {
      console.log('❌ Issue: User object missing essential data (id/email)');
      console.log('💡 Solution: Check Supabase user creation process');
    } else if (!sessionData.session.user.user_metadata?.full_name && 
               !sessionData.session.user.user_metadata?.name &&
               !sessionData.session.user.identities?.[0]?.identity_data?.full_name) {
      console.log('❌ Issue: User object missing name metadata');
      console.log('💡 Solution: Update user metadata or fix registration process');
    } else {
      console.log('✅ User data appears complete');
      console.log('💡 Issue might be in the auth context or state management');
    }
    
  } catch (error) {
    console.error('❌ Login flow debug failed:', error);
  }
}

// Auto-run the debug
debugLoginFlow();

// Make it available globally
window.debugLoginFlow = debugLoginFlow;

console.log('\n💡 You can run this debug again with: debugLoginFlow()');
