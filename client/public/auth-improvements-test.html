<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Improvements Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        #console { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            max-height: 400px; 
            overflow-y: auto; 
        }
        .score {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .score.excellent { background: #d4edda; color: #155724; }
        .score.good { background: #fff3cd; color: #856404; }
        .score.poor { background: #f8d7da; color: #721c24; }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-card {
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        .test-card.pass { background: #d4edda; border-color: #c3e6cb; }
        .test-card.fail { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>🧪 Authentication Improvements Test Suite</h1>
    
    <div class="info">
        <strong>Purpose:</strong> This test suite verifies that all authentication improvements have been 
        successfully implemented and are working correctly.
    </div>
    
    <h2>Test Controls</h2>
    <button onclick="runFullTest()" id="testBtn">🧪 Run Full Test Suite</button>
    <button onclick="clearResults()">🗑️ Clear Results</button>
    <button onclick="goToLogin()">🔑 Go to Login</button>
    <button onclick="goToDashboard()">📊 Go to Dashboard</button>
    
    <h2>Test Results</h2>
    <div id="scoreDisplay"></div>
    <div id="testGrid" class="test-grid"></div>
    
    <h2>Console Output</h2>
    <div id="console"></div>
    
    <script type="module">
        let testResults = {};
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            
            console.log(logMessage);
            
            const consoleDiv = document.getElementById('console');
            consoleDiv.textContent += logMessage + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        function updateScoreDisplay(score) {
            const scoreDiv = document.getElementById('scoreDisplay');
            let scoreClass = 'poor';
            let scoreText = 'Needs Work';
            
            if (score >= 75) {
                scoreClass = 'excellent';
                scoreText = 'Excellent';
            } else if (score >= 50) {
                scoreClass = 'good';
                scoreText = 'Good';
            }
            
            scoreDiv.innerHTML = `
                <div class="score ${scoreClass}">
                    ${score}% - ${scoreText}
                </div>
            `;
        }
        
        function updateTestGrid(results) {
            const gridDiv = document.getElementById('testGrid');
            gridDiv.innerHTML = '';
            
            const tests = [
                { key: 'multipleClientsFixed', name: 'Multiple Clients Fixed', icon: '🔧' },
                { key: 'sessionTimeoutFixed', name: 'Session Timeout Fixed', icon: '⏱️' },
                { key: 'configurationImproved', name: 'Configuration Improved', icon: '⚙️' },
                { key: 'authFlowWorking', name: 'Auth Flow Working', icon: '🔐' }
            ];
            
            tests.forEach(test => {
                const passed = results[test.key];
                const cardDiv = document.createElement('div');
                cardDiv.className = `test-card ${passed ? 'pass' : 'fail'}`;
                cardDiv.innerHTML = `
                    <div style="font-size: 2em; margin-bottom: 10px;">${test.icon}</div>
                    <div style="font-weight: bold; margin-bottom: 5px;">${test.name}</div>
                    <div style="font-size: 1.2em;">${passed ? '✅ PASS' : '❌ FAIL'}</div>
                `;
                gridDiv.appendChild(cardDiv);
            });
        }
        
        async function runFullTest() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '🔄 Running Tests...';
            
            clearResults();
            log('🧪 Starting comprehensive authentication improvements test...', 'info');
            
            try {
                // Import and run the test
                const { testAuthImprovements } = await import('/test-auth-improvements.js');
                const results = await testAuthImprovements();
                
                testResults = results;
                
                // Update displays
                updateScoreDisplay(results.overallScore || 0);
                updateTestGrid(results);
                
                log(`🎉 Test completed with score: ${results.overallScore}%`, 'success');
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                updateScoreDisplay(0);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🧪 Run Full Test Suite';
            }
        }
        
        function clearResults() {
            document.getElementById('console').textContent = '';
            document.getElementById('scoreDisplay').innerHTML = '';
            document.getElementById('testGrid').innerHTML = '';
            log('🗑️ Results cleared');
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        function goToDashboard() {
            window.location.href = '/dashboard';
        }
        
        // Make functions globally available
        window.runFullTest = runFullTest;
        window.clearResults = clearResults;
        window.goToLogin = goToLogin;
        window.goToDashboard = goToDashboard;
        
        // Auto-run test on load
        log('🚀 Authentication improvements test page loaded');
        log('Click "Run Full Test Suite" to start testing');
        
        // Auto-run after a short delay
        setTimeout(() => {
            log('🔄 Auto-running test in 3 seconds...');
            setTimeout(runFullTest, 3000);
        }, 1000);
    </script>
</body>
</html>
