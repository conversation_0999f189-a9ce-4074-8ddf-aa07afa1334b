<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Username Display Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-flow {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.2s;
            font-weight: 600;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
        }
        .button.large {
            font-size: 18px;
            padding: 20px 40px;
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .button.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 25px;
            border: 1px solid #374151;
        }
        .step-box {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .step-box h3 {
            margin: 0 0 10px 0;
            color: #1e293b;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pending { background: #6b7280; }
        .status-running { background: #f59e0b; }
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Final Username Display Test</h1>
        
        <div class="test-flow">
            <h3>🔍 Complete Username Display Diagnosis</h3>
            <p>This test will comprehensively analyze why "Usuario Demo" appears instead of the real username and provide specific fixes.</p>
            
            <div style="margin-top: 20px;">
                <strong>Test Flow:</strong>
                <ol style="margin: 10px 0; padding-left: 20px;">
                    <li>Check current authentication state</li>
                    <li>Analyze user metadata and extraction logic</li>
                    <li>Test dashboard user data reception</li>
                    <li>Identify specific issue and provide solution</li>
                </ol>
            </div>
        </div>

        <div class="step-box">
            <h3><span class="status-indicator status-pending"></span>Step 1: Authentication State Check</h3>
            <p>Verify user is logged in and check Supabase user data structure</p>
            <button class="button" onclick="runStep1()">🔐 Check Auth State</button>
        </div>

        <div class="step-box">
            <h3><span class="status-indicator status-pending"></span>Step 2: Username Extraction Analysis</h3>
            <p>Test the username extraction logic and identify available data sources</p>
            <button class="button" onclick="runStep2()">🧪 Test Extraction</button>
        </div>

        <div class="step-box">
            <h3><span class="status-indicator status-pending"></span>Step 3: Dashboard Data Flow</h3>
            <p>Check if the dashboard component receives correct user data</p>
            <button class="button" onclick="runStep3()">🏠 Test Dashboard</button>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button large success" onclick="runCompleteTest()">
                🚀 Run Complete Diagnosis
            </button>
        </div>

        <div style="text-align: center;">
            <button class="button secondary" onclick="clearConsole()">🧹 Clear Console</button>
            <button class="button warning" onclick="goToLogin()">🔐 Login Page</button>
            <button class="button warning" onclick="goToDashboard()">🏠 Dashboard</button>
        </div>

        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        let currentStep = 0;
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function updateStepStatus(stepNumber, status) {
            const indicators = document.querySelectorAll('.status-indicator');
            if (indicators[stepNumber - 1]) {
                indicators[stepNumber - 1].className = `status-indicator status-${status}`;
            }
        }
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
            // Reset step indicators
            document.querySelectorAll('.status-indicator').forEach(indicator => {
                indicator.className = 'status-indicator status-pending';
            });
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        function goToDashboard() {
            window.location.href = '/dashboard';
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function runStep1() {
            console.log('🔐 Step 1: Checking Authentication State...');
            updateStepStatus(1, 'running');
            
            try {
                await loadScript('/debug-user-data.js');
                updateStepStatus(1, 'success');
                console.log('✅ Step 1 completed');
            } catch (error) {
                updateStepStatus(1, 'error');
                console.error('❌ Step 1 failed:', error);
            }
        }
        
        async function runStep2() {
            console.log('🧪 Step 2: Testing Username Extraction...');
            updateStepStatus(2, 'running');
            
            try {
                await loadScript('/test-username-extraction.js');
                updateStepStatus(2, 'success');
                console.log('✅ Step 2 completed');
            } catch (error) {
                updateStepStatus(2, 'error');
                console.error('❌ Step 2 failed:', error);
            }
        }
        
        async function runStep3() {
            console.log('🏠 Step 3: Testing Dashboard Data Flow...');
            updateStepStatus(3, 'running');
            
            try {
                await loadScript('/test-dashboard-user-data.js');
                updateStepStatus(3, 'success');
                console.log('✅ Step 3 completed');
            } catch (error) {
                updateStepStatus(3, 'error');
                console.error('❌ Step 3 failed:', error);
            }
        }
        
        async function runCompleteTest() {
            clearConsole();
            console.log('🚀 Starting Complete Username Display Diagnosis...');
            console.log('==================================================');
            
            try {
                await runStep1();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await runStep2();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await runStep3();
                
                console.log('\n🎯 Complete Diagnosis Finished!');
                console.log('===============================');
                console.log('📊 Review the results above to identify the username display issue.');
                console.log('💡 Look for specific recommendations and fixes in the output.');
                
            } catch (error) {
                console.error('❌ Complete diagnosis failed:', error);
            }
        }
        
        // Auto-run message on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🎯 Final Username Display Test Ready');
                console.log('===================================');
                console.log('🔍 This tool will identify why "Usuario Demo" appears instead of real usernames');
                console.log('📋 Make sure you are logged in for accurate diagnosis');
                console.log('🚀 Click "Run Complete Diagnosis" to start the full analysis');
                console.log('');
                console.log('💡 You can also run individual steps to focus on specific areas');
            }, 500);
        });
    </script>
</body>
</html>
