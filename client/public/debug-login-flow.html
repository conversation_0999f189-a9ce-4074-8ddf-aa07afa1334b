<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Flow Debug - Null/Undefined Values</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
        .problem-alert {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
        }
        .problem-alert h3 {
            margin: 0 0 15px 0;
            font-size: 1.3rem;
        }
        .solution-box {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.2s;
            font-weight: 600;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
        }
        .button.large {
            font-size: 18px;
            padding: 20px 40px;
        }
        .button.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 25px;
            border: 1px solid #374151;
        }
        .diagnostic-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .step-card {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            padding: 20px;
            border-radius: 8px;
        }
        .step-card h4 {
            margin: 0 0 10px 0;
            color: #1e293b;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pending { background: #6b7280; }
        .status-running { background: #f59e0b; }
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Login Flow Debug - Null/Undefined Values</h1>
        
        <div class="problem-alert">
            <h3>🚨 Critical Issue Detected</h3>
            <p><strong>Problem:</strong> Authentication system is receiving null/undefined values during login process.</p>
            <p><strong>Symptoms:</strong></p>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Console shows: <code>appUser: null</code></li>
                <li>Console shows: <code>email: undefined, id: undefined, username: undefined</code></li>
                <li>User data is not being properly processed from Supabase to frontend</li>
            </ul>
        </div>

        <div class="solution-box">
            <h3>🎯 Diagnostic Approach</h3>
            <p>This tool will trace the complete data flow from Supabase authentication to frontend state to identify exactly where the null/undefined values are introduced.</p>
        </div>

        <div class="diagnostic-steps">
            <div class="step-card">
                <h4><span class="status-indicator status-pending"></span>Step 1: Supabase Data Check</h4>
                <p>Verify what data Supabase is actually providing during authentication</p>
                <button class="button" onclick="runStep1()">🔍 Check Supabase Data</button>
            </div>

            <div class="step-card">
                <h4><span class="status-indicator status-pending"></span>Step 2: Transformation Test</h4>
                <p>Test the createAppUserFromSupabase function with actual data</p>
                <button class="button" onclick="runStep2()">🔄 Test Transformation</button>
            </div>

            <div class="step-card">
                <h4><span class="status-indicator status-pending"></span>Step 3: State Management</h4>
                <p>Check if processed data is correctly set in authentication state</p>
                <button class="button" onclick="runStep3()">📊 Check State</button>
            </div>

            <div class="step-card">
                <h4><span class="status-indicator status-pending"></span>Step 4: Complete Flow</h4>
                <p>Trace the complete login flow from start to finish</p>
                <button class="button" onclick="runStep4()">🔄 Trace Flow</button>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button large danger" onclick="runCompleteDebug()">
                🚨 Run Complete Login Flow Debug
            </button>
        </div>

        <div style="text-align: center;">
            <button class="button secondary" onclick="clearConsole()">🧹 Clear Console</button>
            <button class="button success" onclick="goToLogin()">🔐 Go to Login</button>
            <button class="button success" onclick="goToDashboard()">🏠 Go to Dashboard</button>
        </div>

        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function updateStepStatus(stepNumber, status) {
            const indicators = document.querySelectorAll('.status-indicator');
            if (indicators[stepNumber - 1]) {
                indicators[stepNumber - 1].className = `status-indicator status-${status}`;
            }
        }
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
            // Reset step indicators
            document.querySelectorAll('.status-indicator').forEach(indicator => {
                indicator.className = 'status-indicator status-pending';
            });
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        function goToDashboard() {
            window.location.href = '/dashboard';
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function runStep1() {
            console.log('🔍 Step 1: Checking Supabase Data...');
            updateStepStatus(1, 'running');
            
            try {
                await loadScript('/debug-login-flow.js');
                updateStepStatus(1, 'success');
                console.log('✅ Step 1 completed');
            } catch (error) {
                updateStepStatus(1, 'error');
                console.error('❌ Step 1 failed:', error);
            }
        }
        
        async function runStep2() {
            console.log('🔄 Step 2: Testing Transformation...');
            updateStepStatus(2, 'running');
            
            // This will be implemented based on findings from step 1
            console.log('ℹ️ Step 2: Run Step 1 first to get actual user data for transformation testing');
            updateStepStatus(2, 'success');
        }
        
        async function runStep3() {
            console.log('📊 Step 3: Checking State Management...');
            updateStepStatus(3, 'running');
            
            // Check current auth state
            console.log('ℹ️ Step 3: Checking current authentication state in browser...');
            
            // Look for auth state in DOM or global variables
            const userDisplayElements = document.querySelectorAll('[data-testid="user-display"]');
            if (userDisplayElements.length > 0) {
                console.log('✅ Found user display elements:', userDisplayElements.length);
                userDisplayElements.forEach((el, index) => {
                    console.log(`Element ${index + 1} text:`, el.textContent);
                });
            } else {
                console.log('⚠️ No user display elements found');
            }
            
            updateStepStatus(3, 'success');
        }
        
        async function runStep4() {
            console.log('🔄 Step 4: Tracing Complete Flow...');
            updateStepStatus(4, 'running');
            
            console.log('ℹ️ Step 4: Complete flow tracing requires running all previous steps');
            console.log('💡 Use "Run Complete Login Flow Debug" for full analysis');
            
            updateStepStatus(4, 'success');
        }
        
        async function runCompleteDebug() {
            clearConsole();
            console.log('🚨 Starting Complete Login Flow Debug...');
            console.log('==========================================');
            
            try {
                console.log('\n🔍 Phase 1: Supabase Data Analysis...');
                await runStep1();
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.log('\n📊 Phase 2: State Management Check...');
                await runStep3();
                
                console.log('\n🎯 Complete Debug Finished!');
                console.log('============================');
                console.log('📊 Review the results above to identify where null/undefined values are introduced.');
                console.log('💡 Look for specific error messages and data validation results.');
                
            } catch (error) {
                console.error('❌ Complete debug failed:', error);
            }
        }
        
        // Auto-run message on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔍 Login Flow Debug Tool Loaded');
                console.log('===============================');
                console.log('🚨 This tool diagnoses null/undefined values in authentication data');
                console.log('📋 Make sure you are logged in for accurate analysis');
                console.log('🚀 Click "Run Complete Login Flow Debug" to start comprehensive analysis');
                console.log('');
                console.log('💡 Expected to find the exact point where user data becomes null/undefined');
            }, 500);
        });
    </script>
</body>
</html>
