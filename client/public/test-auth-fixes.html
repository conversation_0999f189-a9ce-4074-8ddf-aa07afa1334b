<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication System Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.2s;
        }
        .button:hover {
            background: #1976d2;
        }
        .button.secondary {
            background: #757575;
        }
        .button.secondary:hover {
            background: #616161;
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .status.warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Authentication System Test</h1>
        
        <div class="test-info">
            <h3>What this test checks:</h3>
            <ul>
                <li>✅ Multiple GoTrueClient instances warning elimination</li>
                <li>✅ Proper session establishment without timeouts</li>
                <li>✅ User detection functionality restoration</li>
                <li>✅ Authentication state maintenance across components</li>
            </ul>
        </div>

        <div style="text-align: center;">
            <button class="button" onclick="runTest()">🚀 Run Authentication Test</button>
            <button class="button secondary" onclick="clearConsole()">🧹 Clear Console</button>
            <button class="button secondary" onclick="goToLogin()">🔐 Go to Login</button>
        </div>

        <div id="status"></div>
        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
            document.getElementById('status').innerHTML = '';
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        async function runTest() {
            clearConsole();
            document.getElementById('status').innerHTML = '<div class="status warning">🔄 Running authentication test...</div>';
            
            try {
                // Load and run the test script
                const script = document.createElement('script');
                script.src = '/test-auth-fixes.js';
                script.onload = () => {
                    console.log('✅ Test script loaded successfully');
                };
                script.onerror = () => {
                    console.error('❌ Failed to load test script');
                    document.getElementById('status').innerHTML = '<div class="status error">❌ Failed to load test script</div>';
                };
                document.head.appendChild(script);
                
                // Update status after a delay
                setTimeout(() => {
                    document.getElementById('status').innerHTML = '<div class="status success">✅ Test completed - check console output above</div>';
                }, 5000);
                
            } catch (error) {
                console.error('Test execution failed:', error);
                document.getElementById('status').innerHTML = '<div class="status error">❌ Test execution failed</div>';
            }
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔧 Authentication Test Page Loaded');
                console.log('💡 Click "Run Authentication Test" to start testing');
            }, 500);
        });
    </script>
</body>
</html>
