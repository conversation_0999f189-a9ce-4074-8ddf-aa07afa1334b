<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Timeout Diagnostic</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        #console { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            max-height: 500px; 
            overflow-y: auto; 
        }
        .metric {
            display: inline-block;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 3px;
            margin: 2px;
            font-family: monospace;
        }
        .metric.good { background: #d4edda; }
        .metric.warning { background: #fff3cd; }
        .metric.bad { background: #f8d7da; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🔍 Session Timeout Diagnostic Tool</h1>
    
    <div class="info">
        <strong>Purpose:</strong> This tool diagnoses session timeout issues by testing network connectivity, 
        Supabase configuration, and authentication performance.
    </div>
    
    <h2>Quick Actions</h2>
    <button onclick="runDiagnostic()" id="runBtn">🔍 Run Full Diagnostic</button>
    <button onclick="testNetworkOnly()">🌐 Test Network Only</button>
    <button onclick="testSessionOnly()">📋 Test Session Only</button>
    <button onclick="clearResults()">🗑️ Clear Results</button>
    
    <h2>Diagnostic Results</h2>
    <div id="results"></div>
    
    <h2>Performance Metrics</h2>
    <div id="metrics"></div>
    
    <h2>Console Output</h2>
    <div id="console"></div>
    
    <script type="module">
        let diagnosticResults = {};
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            
            console.log(logMessage);
            
            const consoleDiv = document.getElementById('console');
            consoleDiv.textContent += logMessage + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            log(message, type);
        }
        
        function updateMetrics(results) {
            const metricsDiv = document.getElementById('metrics');
            metricsDiv.innerHTML = '';
            
            if (results.networkConnectivity?.latency) {
                const latency = results.networkConnectivity.latency;
                const latencyClass = latency < 1000 ? 'good' : latency < 3000 ? 'warning' : 'bad';
                metricsDiv.innerHTML += `<span class="metric ${latencyClass}">Network: ${latency}ms</span>`;
            }
            
            if (results.authEndpoint?.latency) {
                const authLatency = results.authEndpoint.latency;
                const authClass = authLatency < 2000 ? 'good' : authLatency < 5000 ? 'warning' : 'bad';
                metricsDiv.innerHTML += `<span class="metric ${authClass}">Auth: ${authLatency}ms</span>`;
            }
            
            if (results.sessionCheck?.latency) {
                const sessionLatency = results.sessionCheck.latency;
                const sessionClass = sessionLatency < 3000 ? 'good' : sessionLatency < 8000 ? 'warning' : 'bad';
                metricsDiv.innerHTML += `<span class="metric ${sessionClass}">Session: ${sessionLatency}ms</span>`;
            }
        }
        
        async function runDiagnostic() {
            const runBtn = document.getElementById('runBtn');
            runBtn.disabled = true;
            runBtn.textContent = '🔄 Running Diagnostic...';
            
            clearResults();
            addResult('🔍 Starting comprehensive session timeout diagnostic...', 'info');
            
            try {
                // Import the diagnostic script
                const { diagnoseSessionTimeout } = await import('/diagnose-session-timeout.js');
                
                // Run the diagnostic
                const results = await diagnoseSessionTimeout();
                diagnosticResults = results;
                
                // Update metrics display
                updateMetrics(results);
                
                // Generate summary
                generateSummary(results);
                
                addResult('🎉 Diagnostic completed successfully!', 'success');
                
            } catch (error) {
                addResult(`❌ Diagnostic failed: ${error.message}`, 'error');
            } finally {
                runBtn.disabled = false;
                runBtn.textContent = '🔍 Run Full Diagnostic';
            }
        }
        
        async function testNetworkOnly() {
            addResult('🌐 Testing network connectivity only...', 'info');
            
            try {
                const supabaseUrl = 'https://pthewpjbegkgomvyhkin.supabase.co';
                const startTime = Date.now();
                
                const response = await fetch(`${supabaseUrl}/rest/v1/`, {
                    method: 'HEAD',
                    signal: AbortSignal.timeout(5000)
                });
                
                const latency = Date.now() - startTime;
                const status = latency < 1000 ? 'success' : latency < 3000 ? 'warning' : 'error';
                
                addResult(`Network test: ${latency}ms latency, status: ${response.status}`, status);
                
            } catch (error) {
                addResult(`❌ Network test failed: ${error.message}`, 'error');
            }
        }
        
        async function testSessionOnly() {
            addResult('📋 Testing session check only...', 'info');
            
            try {
                const { supabase } = await import('/src/lib/supabase.ts');
                
                const startTime = Date.now();
                const { data, error } = await supabase.auth.getSession();
                const latency = Date.now() - startTime;
                
                if (error) {
                    addResult(`❌ Session check error: ${error.message}`, 'error');
                } else {
                    const status = latency < 3000 ? 'success' : latency < 8000 ? 'warning' : 'error';
                    addResult(`Session check: ${latency}ms latency, has session: ${!!data?.session}`, status);
                }
                
            } catch (error) {
                addResult(`❌ Session test failed: ${error.message}`, 'error');
            }
        }
        
        function generateSummary(results) {
            addResult('📊 Generating diagnostic summary...', 'info');
            
            // Check for critical issues
            if (!results.networkConnectivity?.success) {
                addResult('🚨 CRITICAL: Network connectivity failed - check internet connection', 'error');
            }
            
            if (results.sessionCheck?.latency > 8000) {
                addResult('🚨 CRITICAL: Session check exceeds 8s timeout - increase timeout to 12-15s', 'error');
            }
            
            // Check for warnings
            if (results.networkConnectivity?.latency > 3000) {
                addResult('⚠️ WARNING: High network latency detected - consider increasing timeouts', 'warning');
            }
            
            if (results.authEndpoint?.latency > 5000) {
                addResult('⚠️ WARNING: Auth endpoint is slow - may cause authentication delays', 'warning');
            }
            
            // Recommendations
            const recommendations = [];
            
            if (results.sessionCheck?.latency > 5000) {
                recommendations.push('Increase session timeout to 12-15 seconds');
            }
            
            if (results.networkConnectivity?.latency > 2000) {
                recommendations.push('Add retry logic for failed requests');
            }
            
            if (!results.sessionCheck?.hasSession) {
                recommendations.push('User needs to log in to establish a session');
            }
            
            if (recommendations.length > 0) {
                addResult('💡 Recommendations: ' + recommendations.join(', '), 'info');
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('console').textContent = '';
            document.getElementById('metrics').innerHTML = '';
            log('🗑️ Results cleared');
        }
        
        // Make functions globally available
        window.runDiagnostic = runDiagnostic;
        window.testNetworkOnly = testNetworkOnly;
        window.testSessionOnly = testSessionOnly;
        window.clearResults = clearResults;
        
        // Auto-run basic check on load
        log('🚀 Session timeout diagnostic tool loaded');
        testNetworkOnly();
    </script>
</body>
</html>
