<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Authentication Debug - Null/Undefined Values</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2rem;
        }
        .critical-alert {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border-left: 5px solid #991b1b;
        }
        .critical-alert h3 {
            margin: 0 0 15px 0;
            font-size: 1.4rem;
        }
        .debug-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .debug-card {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            padding: 20px;
            border-radius: 12px;
            transition: all 0.2s;
        }
        .debug-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .debug-card h4 {
            margin: 0 0 15px 0;
            color: #1e293b;
            font-size: 1.1rem;
        }
        .debug-card p {
            margin: 0 0 15px 0;
            color: #64748b;
            font-size: 0.9rem;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s;
            width: 100%;
        }
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        .button.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .button.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        .button.large {
            font-size: 18px;
            padding: 20px 40px;
            margin: 20px 10px;
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.6;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 25px;
            border: 1px solid #374151;
        }
        .flow-steps {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
        }
        .flow-steps h3 {
            margin: 0 0 15px 0;
        }
        .flow-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .flow-steps li {
            margin: 8px 0;
        }
        .status-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            color: #0c4a6e;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Complete Authentication Debug</h1>
        
        <div class="critical-alert">
            <h3>🚨 Critical Issue: Null/Undefined Values in Authentication</h3>
            <p><strong>Problem:</strong> Authentication system receiving null/undefined values during login process</p>
            <p><strong>Symptoms:</strong></p>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li><code>appUser: null</code></li>
                <li><code>email: undefined, id: undefined, username: undefined</code></li>
                <li>User data not flowing from Supabase to frontend components</li>
            </ul>
            <p><strong>Impact:</strong> Users cannot see their profile information after successful login</p>
        </div>

        <div class="flow-steps">
            <h3>🎯 Complete Debug Flow</h3>
            <ol>
                <li><strong>Supabase Data Analysis:</strong> Check what data Supabase returns during authentication</li>
                <li><strong>Transformation Testing:</strong> Verify user data transformation from Supabase to app format</li>
                <li><strong>State Management Check:</strong> Ensure processed data is correctly set in auth context</li>
                <li><strong>UI Data Flow:</strong> Confirm user data reaches dashboard and other components</li>
                <li><strong>Complete Login Test:</strong> Test the entire login flow with real credentials</li>
            </ol>
        </div>

        <div class="debug-grid">
            <div class="debug-card">
                <h4>🔍 Supabase Data Analysis</h4>
                <p>Analyze raw authentication response from Supabase to identify data availability</p>
                <button class="button" onclick="runSupabaseAnalysis()">Analyze Supabase Data</button>
            </div>

            <div class="debug-card">
                <h4>🔄 User Transformation Test</h4>
                <p>Test the createAppUserFromSupabase function with various user data scenarios</p>
                <button class="button" onclick="runTransformationTest()">Test Transformation</button>
            </div>

            <div class="debug-card">
                <h4>📊 Auth State Check</h4>
                <p>Check current authentication state and context values</p>
                <button class="button" onclick="runAuthStateCheck()">Check Auth State</button>
            </div>

            <div class="debug-card">
                <h4>🏠 Dashboard Data Flow</h4>
                <p>Verify user data reaches dashboard components correctly</p>
                <button class="button" onclick="runDashboardCheck()">Check Dashboard</button>
            </div>

            <div class="debug-card">
                <h4>🧪 Login Simulation</h4>
                <p>Simulate login process to identify where null values are introduced</p>
                <button class="button warning" onclick="runLoginSimulation()">Simulate Login</button>
            </div>

            <div class="debug-card">
                <h4>🔧 Metadata Analysis</h4>
                <p>Check user metadata and suggest fixes for missing information</p>
                <button class="button" onclick="runMetadataAnalysis()">Analyze Metadata</button>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button large danger" onclick="runCompleteDebug()">
                🚨 Run Complete Authentication Debug
            </button>
        </div>

        <div class="status-box">
            <strong>💡 Instructions:</strong>
            <ol style="margin: 10px 0; padding-left: 20px;">
                <li>Click "Run Complete Authentication Debug" for comprehensive analysis</li>
                <li>If not logged in, use individual tools first, then log in and re-run</li>
                <li>Watch console output for specific error patterns and recommendations</li>
                <li>Use the debug information to identify the exact point where null/undefined values are introduced</li>
            </ol>
        </div>

        <div style="text-align: center;">
            <button class="button success large" onclick="goToLogin()">🔐 Go to Login Page</button>
            <button class="button success large" onclick="goToDashboard()">🏠 Go to Dashboard</button>
            <button class="button large" onclick="clearConsole()">🧹 Clear Console</button>
        </div>

        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        function goToDashboard() {
            window.location.href = '/dashboard';
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function runSupabaseAnalysis() {
            console.log('🔍 Running Supabase Data Analysis...');
            try {
                await loadScript('/debug-login-flow.js');
            } catch (error) {
                console.error('Failed to run Supabase analysis:', error);
            }
        }
        
        async function runTransformationTest() {
            console.log('🔄 Running User Transformation Test...');
            try {
                await loadScript('/test-login-simulation.js');
            } catch (error) {
                console.error('Failed to run transformation test:', error);
            }
        }
        
        async function runAuthStateCheck() {
            console.log('📊 Running Auth State Check...');
            try {
                await loadScript('/test-dashboard-user-data.js');
            } catch (error) {
                console.error('Failed to run auth state check:', error);
            }
        }
        
        async function runDashboardCheck() {
            console.log('🏠 Running Dashboard Data Flow Check...');
            console.log('ℹ️ Checking dashboard user display elements...');
            
            const userDisplayElements = document.querySelectorAll('[data-testid="user-display"]');
            if (userDisplayElements.length > 0) {
                console.log('✅ Found user display elements:', userDisplayElements.length);
                userDisplayElements.forEach((el, index) => {
                    console.log(`Element ${index + 1} text: "${el.textContent}"`);
                });
            } else {
                console.log('⚠️ No user display elements found - user may not be on dashboard');
            }
        }
        
        async function runLoginSimulation() {
            console.log('🧪 Running Login Simulation...');
            try {
                await loadScript('/test-login-simulation.js');
            } catch (error) {
                console.error('Failed to run login simulation:', error);
            }
        }
        
        async function runMetadataAnalysis() {
            console.log('🔧 Running Metadata Analysis...');
            try {
                await loadScript('/fix-user-metadata.js');
            } catch (error) {
                console.error('Failed to run metadata analysis:', error);
            }
        }
        
        async function runCompleteDebug() {
            clearConsole();
            console.log('🚨 Starting Complete Authentication Debug...');
            console.log('===========================================');
            console.log('🎯 This will identify where null/undefined values are introduced');
            console.log('');
            
            try {
                console.log('📊 Phase 1: Supabase Data Analysis...');
                await runSupabaseAnalysis();
                
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                console.log('\n🔄 Phase 2: User Transformation Testing...');
                await runTransformationTest();
                
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                console.log('\n📊 Phase 3: Auth State Verification...');
                await runAuthStateCheck();
                
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                console.log('\n🏠 Phase 4: Dashboard Data Flow Check...');
                await runDashboardCheck();
                
                console.log('\n🎯 Complete Authentication Debug Finished!');
                console.log('==========================================');
                console.log('📊 Review the detailed output above to identify the null/undefined value source');
                console.log('💡 Look for specific error messages and validation failures');
                console.log('🔧 Follow the recommendations provided for each identified issue');
                
            } catch (error) {
                console.error('❌ Complete debug failed:', error);
            }
        }
        
        // Auto-run message on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔍 Complete Authentication Debug Tool Ready');
                console.log('==========================================');
                console.log('🚨 This tool diagnoses null/undefined values in authentication data');
                console.log('📋 Enhanced debugging has been added to the authentication system');
                console.log('🚀 Click "Run Complete Authentication Debug" for comprehensive analysis');
                console.log('');
                console.log('💡 Expected to identify the exact point where user data becomes null/undefined');
                console.log('🔧 Will provide specific recommendations for fixing the identified issues');
            }, 500);
        });
    </script>
</body>
</html>
