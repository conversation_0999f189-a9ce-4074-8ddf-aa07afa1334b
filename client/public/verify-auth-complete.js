/**
 * Complete Authentication Verification
 * 
 * This script performs comprehensive verification that the authentication
 * system is working correctly and user information is properly displayed.
 */

console.log('✅ Starting Complete Authentication Verification...');

async function verifyCompleteAuthFlow() {
  console.log('\n✅ COMPLETE AUTHENTICATION VERIFICATION');
  console.log('======================================');
  console.log('This verification will test the entire authentication flow');
  console.log('to ensure null/undefined values have been resolved.\n');

  const verificationResults = {
    supabaseConnection: false,
    userAuthenticated: false,
    userMetadataComplete: false,
    usernameExtraction: false,
    authStateManagement: false,
    uiDisplayCorrect: false,
    overallScore: 0
  };

  try {
    // Test 1: Supabase Connection and Authentication
    console.log('🔍 TEST 1: Supabase Connection and Authentication');
    console.log('===============================================');
    
    const { supabase } = await import('/src/lib/supabase.ts');
    
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('❌ Authentication error:', error.message);
      console.log('💡 Please log in and run verification again');
      return;
    }
    
    if (!user) {
      console.log('❌ No user authenticated');
      console.log('💡 Please log in first, then run this verification');
      return;
    }
    
    console.log('✅ User authenticated successfully');
    console.log('📊 User details:');
    console.log('- ID:', user.id);
    console.log('- Email:', user.email);
    console.log('- Last sign in:', user.last_sign_in_at);
    
    verificationResults.supabaseConnection = true;
    verificationResults.userAuthenticated = true;
    
    // Test 2: User Metadata Verification
    console.log('\n🔍 TEST 2: User Metadata Verification');
    console.log('====================================');
    
    console.log('📋 User metadata analysis:');
    console.log('- user_metadata exists:', !!user.user_metadata);
    console.log('- user_metadata content:', JSON.stringify(user.user_metadata, null, 2));
    
    const hasFullName = !!user.user_metadata?.full_name;
    const hasName = !!user.user_metadata?.name;
    const hasIdentityName = !!user.identities?.[0]?.identity_data?.full_name;
    
    console.log('📊 Name availability:');
    console.log('- full_name:', hasFullName ? `"${user.user_metadata.full_name}"` : 'Missing');
    console.log('- name:', hasName ? `"${user.user_metadata.name}"` : 'Missing');
    console.log('- identity name:', hasIdentityName ? `"${user.identities[0].identity_data.full_name}"` : 'Missing');
    
    if (hasFullName || hasName || hasIdentityName) {
      console.log('✅ User metadata contains name information');
      verificationResults.userMetadataComplete = true;
    } else {
      console.log('❌ User metadata still missing name information');
      console.log('💡 The metadata fix may not have been applied or needs to be re-run');
    }
    
    // Test 3: Username Extraction Verification
    console.log('\n🔍 TEST 3: Username Extraction Verification');
    console.log('==========================================');
    
    // Test the exact same logic used in createAppUserFromSupabase
    let extractedUsername = 'Usuario';
    let extractionSource = 'fallback';
    
    if (user.user_metadata?.full_name) {
      extractedUsername = user.user_metadata.full_name;
      extractionSource = 'user_metadata.full_name';
    } else if (user.user_metadata?.name) {
      extractedUsername = user.user_metadata.name;
      extractionSource = 'user_metadata.name';
    } else if (user.identities?.[0]?.identity_data?.full_name) {
      extractedUsername = user.identities[0].identity_data.full_name;
      extractionSource = 'identities[0].identity_data.full_name';
    } else if (user.identities?.[0]?.identity_data?.name) {
      extractedUsername = user.identities[0].identity_data.name;
      extractionSource = 'identities[0].identity_data.name';
    } else if (user.email) {
      extractedUsername = user.email.split('@')[0];
      extractionSource = 'email prefix';
    }
    
    console.log(`🎯 Extracted username: "${extractedUsername}"`);
    console.log(`📍 Extraction source: ${extractionSource}`);
    
    if (extractedUsername !== 'Usuario') {
      console.log('✅ Username extraction working correctly');
      verificationResults.usernameExtraction = true;
    } else {
      console.log('❌ Username extraction still using fallback');
      console.log('💡 This indicates the metadata fix was not successful');
    }
    
    // Test 4: Auth State Management Verification
    console.log('\n🔍 TEST 4: Auth State Management Verification');
    console.log('============================================');
    
    // Create a test app user to verify the transformation process
    try {
      const testAppUser = {
        id: user.id,
        username: extractedUsername,
        email: user.email || '',
        role: 'user',
        isActive: true,
        createdAt: new Date()
      };
      
      console.log('✅ App user transformation successful');
      console.log('📊 App user details:');
      console.log('- ID:', testAppUser.id);
      console.log('- Username:', testAppUser.username);
      console.log('- Email:', testAppUser.email);
      console.log('- Valid:', !!(testAppUser.id && testAppUser.username && testAppUser.email));
      
      if (testAppUser.id && testAppUser.username && testAppUser.email) {
        console.log('✅ Auth state management working correctly');
        verificationResults.authStateManagement = true;
      } else {
        console.log('❌ App user missing required fields');
      }
      
    } catch (transformError) {
      console.log('❌ App user transformation failed:', transformError.message);
    }
    
    // Test 5: UI Display Verification
    console.log('\n🔍 TEST 5: UI Display Verification');
    console.log('=================================');
    
    const userDisplayElements = document.querySelectorAll('[data-testid="user-display"]');
    console.log('📊 User display elements found:', userDisplayElements.length);
    
    if (userDisplayElements.length > 0) {
      let correctDisplayCount = 0;
      
      userDisplayElements.forEach((el, index) => {
        const displayText = el.textContent?.trim();
        console.log(`Element ${index + 1}: "${displayText}"`);
        
        if (displayText && displayText !== 'Usuario Demo' && displayText !== 'Usuario') {
          console.log(`  ✅ Showing actual user data`);
          correctDisplayCount++;
        } else if (displayText === 'Usuario Demo' || displayText === 'Usuario') {
          console.log(`  ❌ Still showing fallback text`);
        } else {
          console.log(`  ⚠️ Empty or unexpected content`);
        }
      });
      
      if (correctDisplayCount > 0) {
        console.log('✅ UI displaying correct user information');
        verificationResults.uiDisplayCorrect = true;
      } else {
        console.log('❌ UI still showing fallback or incorrect information');
      }
      
    } else {
      console.log('ℹ️ No user display elements found');
      console.log('💡 Navigate to dashboard to verify UI display');
    }
    
    // Test 6: Overall System Health Check
    console.log('\n🔍 TEST 6: Overall System Health Check');
    console.log('====================================');
    
    // Check for any remaining auth-related errors
    console.log('📊 Checking for common authentication issues...');
    
    // Test session persistence
    const { data: sessionData } = await supabase.auth.getSession();
    const sessionValid = !!(sessionData?.session?.user);
    
    console.log('- Session persistence:', sessionValid ? '✅ Working' : '❌ Failed');
    console.log('- User ID consistency:', sessionData?.session?.user?.id === user.id ? '✅ Consistent' : '❌ Inconsistent');
    
    // Calculate overall score
    const passedTests = Object.values(verificationResults).filter(result => result === true).length;
    verificationResults.overallScore = Math.round((passedTests / 6) * 100);
    
    // Final Results
    console.log('\n📊 VERIFICATION RESULTS SUMMARY');
    console.log('==============================');
    
    console.log('Test Results:');
    console.log('- Supabase Connection:', verificationResults.supabaseConnection ? '✅ PASS' : '❌ FAIL');
    console.log('- User Authenticated:', verificationResults.userAuthenticated ? '✅ PASS' : '❌ FAIL');
    console.log('- User Metadata Complete:', verificationResults.userMetadataComplete ? '✅ PASS' : '❌ FAIL');
    console.log('- Username Extraction:', verificationResults.usernameExtraction ? '✅ PASS' : '❌ FAIL');
    console.log('- Auth State Management:', verificationResults.authStateManagement ? '✅ PASS' : '❌ FAIL');
    console.log('- UI Display Correct:', verificationResults.uiDisplayCorrect ? '✅ PASS' : 'ℹ️ N/A (not on dashboard)');
    
    console.log(`\n🎯 Overall Score: ${verificationResults.overallScore}%`);
    
    // Final Assessment
    if (verificationResults.overallScore >= 90) {
      console.log('🎉 EXCELLENT! Authentication system is working perfectly');
      console.log('✅ All critical issues have been resolved');
      console.log('💡 Users should now see their actual names instead of "Usuario Demo"');
    } else if (verificationResults.overallScore >= 75) {
      console.log('✅ GOOD! Authentication system is mostly working');
      console.log('💡 Minor issues may remain - check specific failed tests above');
    } else if (verificationResults.overallScore >= 50) {
      console.log('⚠️ PARTIAL! Some authentication issues remain');
      console.log('🔧 Review failed tests and apply additional fixes');
    } else {
      console.log('❌ POOR! Significant authentication issues remain');
      console.log('🔧 Major fixes still needed - review all failed tests');
    }
    
    console.log('\n💡 NEXT STEPS:');
    
    if (!verificationResults.userMetadataComplete) {
      console.log('1. 🔧 Apply metadata fix using the fix tool');
      console.log('2. 🔄 Refresh page and re-run verification');
    }
    
    if (!verificationResults.uiDisplayCorrect && userDisplayElements.length === 0) {
      console.log('1. 🏠 Navigate to dashboard to test UI display');
      console.log('2. 🔄 Re-run verification from dashboard page');
    }
    
    if (verificationResults.overallScore >= 90) {
      console.log('1. 🧪 Test login/logout cycle to ensure persistence');
      console.log('2. ✅ Authentication system is ready for production use');
    }
    
    console.log('\n✅ VERIFICATION COMPLETE');
    console.log('=======================');
    
  } catch (error) {
    console.error('❌ Complete authentication verification failed:', error);
  }
}

// Auto-run the verification
verifyCompleteAuthFlow();

// Make it available globally
window.verifyCompleteAuthFlow = verifyCompleteAuthFlow;

console.log('\n💡 You can run this verification again with: verifyCompleteAuthFlow()');
