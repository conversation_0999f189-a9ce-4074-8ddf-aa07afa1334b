/**
 * Complete User Metadata Fix
 * 
 * This script provides a comprehensive solution to fix user metadata
 * and ensure proper username display throughout the application.
 */

console.log('🔧 Complete User Metadata Fix Tool');

async function fixUserMetadataComplete() {
  console.log('\n🔧 COMPLETE USER METADATA FIX');
  console.log('=============================');
  console.log('This tool will fix user metadata issues and restore proper username display.\n');

  try {
    // Step 1: Check current user and metadata
    console.log('📊 Step 1: Analyzing Current User Metadata');
    console.log('==========================================');
    
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Error getting user:', error.message);
      console.log('💡 Please log in first, then run this fix again');
      return;
    }
    
    if (!user) {
      console.log('ℹ️ No user logged in. Please log in first.');
      return;
    }
    
    console.log('✅ User found:', user.email);
    console.log('📋 Current metadata:', JSON.stringify(user.user_metadata, null, 2));
    
    // Analyze current username extraction
    let currentUsername = 'Usuario';
    let currentSource = 'fallback';
    
    if (user.user_metadata?.full_name) {
      currentUsername = user.user_metadata.full_name;
      currentSource = 'user_metadata.full_name';
    } else if (user.user_metadata?.name) {
      currentUsername = user.user_metadata.name;
      currentSource = 'user_metadata.name';
    } else if (user.identities?.[0]?.identity_data?.full_name) {
      currentUsername = user.identities[0].identity_data.full_name;
      currentSource = 'identities[0].identity_data.full_name';
    } else if (user.email) {
      currentUsername = user.email.split('@')[0];
      currentSource = 'email prefix';
    }
    
    console.log(`🎯 Current username: "${currentUsername}" (from ${currentSource})`);
    
    // Step 2: Determine if fix is needed
    console.log('\n🔍 Step 2: Determining Fix Requirements');
    console.log('=====================================');
    
    const needsMetadataFix = !user.user_metadata?.full_name && !user.user_metadata?.name;
    const hasIdentityName = user.identities?.[0]?.identity_data?.full_name || user.identities?.[0]?.identity_data?.name;
    
    if (!needsMetadataFix) {
      console.log('✅ User metadata already contains name information');
      console.log('💡 The issue might be elsewhere in the authentication flow');
      console.log('🔍 Check auth state management and component integration');
      return;
    }
    
    console.log('❌ User metadata missing name information');
    console.log('🔧 Fix required: Update user metadata with proper name');
    
    // Step 3: Determine best name source
    console.log('\n📝 Step 3: Determining Best Name Source');
    console.log('======================================');
    
    let suggestedName = null;
    let nameSource = null;
    
    if (hasIdentityName) {
      suggestedName = user.identities[0].identity_data.full_name || user.identities[0].identity_data.name;
      nameSource = 'OAuth identity data';
      console.log(`✅ Found name in identity data: "${suggestedName}"`);
    } else if (user.email) {
      suggestedName = user.email.split('@')[0];
      nameSource = 'email prefix';
      console.log(`💡 Using email prefix as fallback: "${suggestedName}"`);
    } else {
      console.log('⚠️ No suitable name source found');
      console.log('💡 Manual name input will be required');
    }
    
    // Step 4: Apply the fix
    console.log('\n🔧 Step 4: Applying Metadata Fix');
    console.log('================================');
    
    if (suggestedName) {
      console.log(`🔄 Updating user metadata with name: "${suggestedName}"`);
      console.log(`📍 Source: ${nameSource}`);
      
      try {
        const { error: updateError } = await supabase.auth.updateUser({
          data: {
            full_name: suggestedName,
            username: suggestedName,
            display_name: suggestedName
          }
        });
        
        if (updateError) {
          console.error('❌ Failed to update user metadata:', updateError.message);
          return;
        }
        
        console.log('✅ User metadata updated successfully!');
        
        // Step 5: Verify the fix
        console.log('\n✅ Step 5: Verifying Fix');
        console.log('========================');
        
        // Wait a moment for the update to propagate
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Get updated user data
        const { data: { user: updatedUser }, error: verifyError } = await supabase.auth.getUser();
        
        if (verifyError) {
          console.error('❌ Error verifying update:', verifyError.message);
          return;
        }
        
        console.log('📋 Updated metadata:', JSON.stringify(updatedUser.user_metadata, null, 2));
        
        // Test username extraction with updated data
        let newUsername = 'Usuario';
        let newSource = 'fallback';
        
        if (updatedUser.user_metadata?.full_name) {
          newUsername = updatedUser.user_metadata.full_name;
          newSource = 'user_metadata.full_name';
        } else if (updatedUser.user_metadata?.name) {
          newUsername = updatedUser.user_metadata.name;
          newSource = 'user_metadata.name';
        }
        
        console.log(`🎯 New username: "${newUsername}" (from ${newSource})`);
        
        if (newUsername !== 'Usuario') {
          console.log('🎉 SUCCESS! Username extraction now working correctly');
          console.log('💡 The dashboard should now display the correct username');
          
          // Step 6: Trigger auth state refresh
          console.log('\n🔄 Step 6: Refreshing Authentication State');
          console.log('==========================================');
          
          console.log('🔄 Triggering auth state refresh...');
          
          // Force a session refresh to trigger auth state change
          try {
            await supabase.auth.refreshSession();
            console.log('✅ Auth state refresh triggered');
            console.log('💡 The UI should update automatically');
          } catch (refreshError) {
            console.warn('⚠️ Auth refresh failed, but metadata update was successful');
            console.log('💡 Try refreshing the page to see the updated username');
          }
          
        } else {
          console.log('⚠️ Username still showing fallback after update');
          console.log('🔍 There might be additional issues in the auth system');
        }
        
      } catch (updateError) {
        console.error('❌ Failed to update user metadata:', updateError);
      }
      
    } else {
      console.log('💡 Manual metadata update required');
      console.log('🔧 Use this template to update manually:');
      console.log(`
// Manual metadata update:
const { error } = await supabase.auth.updateUser({
  data: {
    full_name: "User's Actual Name",
    username: "User's Actual Name",
    display_name: "User's Actual Name"
  }
});
      `);
    }
    
    // Step 7: Additional recommendations
    console.log('\n💡 Step 7: Additional Recommendations');
    console.log('====================================');
    
    console.log('🔧 For future users, ensure registration captures names:');
    console.log('1. Update registration form to include full name field');
    console.log('2. Save name to user_metadata during signup');
    console.log('3. For OAuth users, ensure name is extracted from provider data');
    
    console.log('\n🧪 Testing recommendations:');
    console.log('1. Refresh the page and check if username displays correctly');
    console.log('2. Navigate to dashboard and verify user information');
    console.log('3. Test login/logout cycle to ensure persistence');
    
    console.log('\n✅ METADATA FIX COMPLETE');
    console.log('========================');
    console.log('The user metadata has been updated with proper name information.');
    console.log('The authentication system should now display the correct username.');
    
  } catch (error) {
    console.error('❌ Complete metadata fix failed:', error);
  }
}

// Auto-run the complete fix
fixUserMetadataComplete();

// Make it available globally
window.fixUserMetadataComplete = fixUserMetadataComplete;

console.log('\n💡 You can run this fix again with: fixUserMetadataComplete()');
