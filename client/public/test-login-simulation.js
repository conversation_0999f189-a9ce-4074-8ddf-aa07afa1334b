/**
 * Login Simulation Test
 * 
 * This script simulates the login process to identify where null/undefined values
 * are introduced in the authentication data flow.
 */

console.log('🧪 Starting Login Simulation Test...');

async function testLoginSimulation() {
  try {
    console.log('\n📦 Step 1: Importing Supabase client...');
    const { supabase } = await import('/src/lib/supabase.ts');
    console.log('✅ Supabase client imported');

    console.log('\n🔍 Step 2: Testing createAppUserFromSupabase function...');
    
    // Create mock Supabase user data to test the transformation function
    const mockSupabaseUsers = [
      {
        name: "Complete user with full metadata",
        data: {
          id: "test-user-1",
          email: "<EMAIL>",
          created_at: "2024-01-01T00:00:00Z",
          user_metadata: {
            full_name: "Test User",
            email: "<EMAIL>"
          },
          identities: []
        }
      },
      {
        name: "User with minimal data",
        data: {
          id: "test-user-2", 
          email: "<EMAIL>",
          created_at: "2024-01-01T00:00:00Z",
          user_metadata: {},
          identities: []
        }
      },
      {
        name: "OAuth user with identity data",
        data: {
          id: "test-user-3",
          email: "<EMAIL>", 
          created_at: "2024-01-01T00:00:00Z",
          user_metadata: {
            avatar_url: "https://example.com/avatar.jpg",
            email: "<EMAIL>",
            full_name: "OAuth User",
            name: "OAuth User"
          },
          identities: [
            {
              provider: "google",
              identity_data: {
                email: "<EMAIL>",
                full_name: "OAuth User",
                name: "OAuth User"
              }
            }
          ]
        }
      }
    ];

    // Test the transformation function with mock data
    console.log('\n🔄 Testing user transformation with mock data...');
    
    mockSupabaseUsers.forEach((testCase, index) => {
      console.log(`\n🧪 Test Case ${index + 1}: ${testCase.name}`);
      console.log('Input data:', JSON.stringify(testCase.data, null, 2));
      
      try {
        // Simulate the createAppUserFromSupabase function logic
        const mockUser = testCase.data;
        
        // Test username extraction
        let username = "Usuario";
        let source = "fallback";
        
        if (mockUser.user_metadata?.full_name) {
          username = mockUser.user_metadata.full_name;
          source = "user_metadata.full_name";
        } else if (mockUser.user_metadata?.name) {
          username = mockUser.user_metadata.name;
          source = "user_metadata.name";
        } else if (mockUser.identities?.[0]?.identity_data?.full_name) {
          username = mockUser.identities[0].identity_data.full_name;
          source = "identities[0].identity_data.full_name";
        } else if (mockUser.identities?.[0]?.identity_data?.name) {
          username = mockUser.identities[0].identity_data.name;
          source = "identities[0].identity_data.name";
        } else if (mockUser.user_metadata?.first_name && mockUser.user_metadata?.last_name) {
          username = `${mockUser.user_metadata.first_name} ${mockUser.user_metadata.last_name}`;
          source = "user_metadata.first_name + last_name";
        } else if (mockUser.email) {
          username = mockUser.email.split("@")[0];
          source = "email prefix";
        }
        
        // Create app user
        const appUser = {
          id: mockUser.id,
          username: username,
          email: mockUser.email || "",
          role: "user",
          isActive: true,
          createdAt: new Date(),
        };
        
        console.log(`✅ Transformation successful:`);
        console.log(`- Username: "${username}" (from ${source})`);
        console.log(`- App user:`, appUser);
        
        // Validate the result
        const isValid = appUser.id && appUser.username && appUser.email;
        console.log(`- Validation: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
        
        if (!isValid) {
          console.log('❌ Issues found:');
          if (!appUser.id) console.log('  - Missing ID');
          if (!appUser.username) console.log('  - Missing username');
          if (!appUser.email) console.log('  - Missing email');
        }
        
      } catch (error) {
        console.error(`❌ Transformation failed:`, error);
      }
    });

    console.log('\n🔍 Step 3: Testing with actual current user data...');
    
    // Get actual current user if logged in
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('ℹ️ No current user (not logged in):', error.message);
    } else if (user) {
      console.log('✅ Current user found, testing transformation...');
      console.log('Raw user data:', JSON.stringify(user, null, 2));
      
      try {
        // Test the actual transformation
        let username = "Usuario";
        let source = "fallback";
        
        if (user.user_metadata?.full_name) {
          username = user.user_metadata.full_name;
          source = "user_metadata.full_name";
        } else if (user.user_metadata?.name) {
          username = user.user_metadata.name;
          source = "user_metadata.name";
        } else if (user.identities?.[0]?.identity_data?.full_name) {
          username = user.identities[0].identity_data.full_name;
          source = "identities[0].identity_data.full_name";
        } else if (user.identities?.[0]?.identity_data?.name) {
          username = user.identities[0].identity_data.name;
          source = "identities[0].identity_data.name";
        } else if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
          username = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
          source = "user_metadata.first_name + last_name";
        } else if (user.email) {
          username = user.email.split("@")[0];
          source = "email prefix";
        }
        
        const appUser = {
          id: user.id,
          username: username,
          email: user.email || "",
          role: "user",
          isActive: true,
          createdAt: new Date(),
        };
        
        console.log('✅ Real user transformation result:');
        console.log(`- Username: "${username}" (from ${source})`);
        console.log('- App user:', appUser);
        
        // Check if this matches what should be in the auth context
        const isComplete = appUser.id && appUser.username && appUser.email;
        console.log(`- Complete: ${isComplete ? '✅ Yes' : '❌ No'}`);
        
        if (!isComplete) {
          console.log('❌ Issues with real user data:');
          if (!appUser.id) console.log('  - Missing ID');
          if (!appUser.username || appUser.username === "Usuario") console.log('  - Missing or default username');
          if (!appUser.email) console.log('  - Missing email');
        }
        
      } catch (transformError) {
        console.error('❌ Real user transformation failed:', transformError);
      }
    } else {
      console.log('ℹ️ No current user found (not logged in)');
    }

    console.log('\n📊 Step 4: Diagnosis and Recommendations...');
    console.log('============================================');
    
    if (!user) {
      console.log('❌ Issue: No user is currently logged in');
      console.log('💡 Solution: Log in first, then run this test again');
    } else {
      console.log('✅ User is logged in');
      
      if (!user.user_metadata?.full_name && !user.user_metadata?.name && !user.identities?.[0]?.identity_data?.full_name) {
        console.log('❌ Issue: User metadata is missing name information');
        console.log('💡 Solution: Update user metadata or fix registration process');
        console.log('🔧 Suggested fix:');
        console.log(`
// Update user metadata (example):
const { error } = await supabase.auth.updateUser({
  data: {
    full_name: "User's Actual Name",
    username: "User's Actual Name"
  }
});
        `);
      } else {
        console.log('✅ User metadata contains name information');
        console.log('💡 Issue might be in the auth context state management');
        console.log('🔧 Check the auth context debugging logs in the console');
      }
    }
    
  } catch (error) {
    console.error('❌ Login simulation test failed:', error);
  }
}

// Auto-run the test
testLoginSimulation();

// Make it available globally
window.testLoginSimulation = testLoginSimulation;

console.log('\n💡 You can run this test again with: testLoginSimulation()');
