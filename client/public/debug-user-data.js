/**
 * User Data Debug Script
 * 
 * This script analyzes the user data flow from Supabase authentication
 * to identify why the username is not being displayed correctly.
 */

console.log('🔍 Starting User Data Flow Analysis...');

async function debugUserDataFlow() {
  try {
    console.log('\n📦 Step 1: Importing Supabase client...');
    const { supabase } = await import('/src/lib/supabase.ts');
    console.log('✅ Supabase client imported');

    console.log('\n🔐 Step 2: Checking current authentication state...');
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return;
    }

    if (!session) {
      console.log('ℹ️ No active session found. Please log in first.');
      console.log('💡 Go to /login, log in, then run this script again.');
      return;
    }

    console.log('✅ Active session found');

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('❌ User error:', userError);
      return;
    }

    if (!user) {
      console.log('ℹ️ No user found in session');
      return;
    }

    console.log('\n👤 Step 3: Analyzing Supabase User Data...');
    console.log('=====================================');
    
    // Log complete user object structure
    console.log('📋 Complete Supabase User Object:');
    console.log(JSON.stringify(user, null, 2));
    
    console.log('\n🔍 Step 4: Analyzing User Metadata Fields...');
    console.log('============================================');
    
    console.log('📧 Email:', user.email);
    console.log('🆔 ID:', user.id);
    console.log('📅 Created At:', user.created_at);
    console.log('📅 Last Sign In:', user.last_sign_in_at);
    
    console.log('\n🏷️ User Metadata Analysis:');
    if (user.user_metadata) {
      console.log('📋 Complete user_metadata:');
      console.log(JSON.stringify(user.user_metadata, null, 2));
      
      console.log('\n🔍 Individual metadata fields:');
      console.log('- full_name:', user.user_metadata.full_name);
      console.log('- name:', user.user_metadata.name);
      console.log('- display_name:', user.user_metadata.display_name);
      console.log('- username:', user.user_metadata.username);
      console.log('- first_name:', user.user_metadata.first_name);
      console.log('- last_name:', user.user_metadata.last_name);
    } else {
      console.log('⚠️ No user_metadata found');
    }

    console.log('\n🏷️ App Metadata Analysis:');
    if (user.app_metadata) {
      console.log('📋 Complete app_metadata:');
      console.log(JSON.stringify(user.app_metadata, null, 2));
    } else {
      console.log('ℹ️ No app_metadata found');
    }

    console.log('\n🔄 Step 5: Testing Username Extraction Logic...');
    console.log('===============================================');
    
    // Test the current extraction logic
    const extractedUsername = 
      user.user_metadata?.full_name ||
      user.user_metadata?.name ||
      user.email?.split("@")[0] ||
      "Usuario";
    
    console.log('🎯 Current extraction result:', extractedUsername);
    
    // Test alternative extraction methods
    console.log('\n🧪 Testing alternative extraction methods:');
    
    const alternatives = {
      'full_name': user.user_metadata?.full_name,
      'name': user.user_metadata?.name,
      'display_name': user.user_metadata?.display_name,
      'username': user.user_metadata?.username,
      'email_prefix': user.email?.split("@")[0],
      'first_last': user.user_metadata?.first_name && user.user_metadata?.last_name 
        ? `${user.user_metadata.first_name} ${user.user_metadata.last_name}` 
        : null,
      'identities_name': user.identities?.[0]?.identity_data?.full_name,
      'identities_email': user.identities?.[0]?.identity_data?.email,
    };
    
    Object.entries(alternatives).forEach(([method, value]) => {
      console.log(`- ${method}:`, value || 'null');
    });

    console.log('\n🔍 Step 6: Checking Identity Data...');
    console.log('===================================');
    
    if (user.identities && user.identities.length > 0) {
      user.identities.forEach((identity, index) => {
        console.log(`🆔 Identity ${index + 1}:`);
        console.log(`- Provider: ${identity.provider}`);
        console.log(`- Identity Data:`, JSON.stringify(identity.identity_data, null, 2));
      });
    } else {
      console.log('ℹ️ No identities found');
    }

    console.log('\n🎯 Step 7: Recommended Username Extraction...');
    console.log('=============================================');
    
    // Find the best available username
    let recommendedUsername = 'Usuario';
    let source = 'fallback';
    
    if (user.user_metadata?.full_name) {
      recommendedUsername = user.user_metadata.full_name;
      source = 'user_metadata.full_name';
    } else if (user.user_metadata?.name) {
      recommendedUsername = user.user_metadata.name;
      source = 'user_metadata.name';
    } else if (user.identities?.[0]?.identity_data?.full_name) {
      recommendedUsername = user.identities[0].identity_data.full_name;
      source = 'identities[0].identity_data.full_name';
    } else if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
      recommendedUsername = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
      source = 'first_name + last_name';
    } else if (user.email) {
      recommendedUsername = user.email.split("@")[0];
      source = 'email prefix';
    }
    
    console.log(`🎯 Recommended username: "${recommendedUsername}"`);
    console.log(`📍 Source: ${source}`);
    
    console.log('\n✅ User Data Analysis Complete!');
    console.log('===============================');
    
    if (recommendedUsername === 'Usuario') {
      console.log('⚠️ Issue Found: No proper username data available');
      console.log('💡 This explains why "Usuario Demo" is being displayed');
      console.log('🔧 Recommendation: Check how user registration/login is setting user metadata');
    } else {
      console.log('✅ Username data is available');
      console.log('🔧 Check if the auth hook is properly using this data');
    }

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  }
}

// Auto-run the debug
debugUserDataFlow();

// Make it available globally
window.debugUserDataFlow = debugUserDataFlow;

console.log('\n💡 You can run this debug again with: debugUserDataFlow()');
