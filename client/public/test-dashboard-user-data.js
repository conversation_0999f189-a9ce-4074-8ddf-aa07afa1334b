/**
 * Dashboard User Data Test
 * 
 * This script tests if the dashboard is receiving the correct user data
 * and helps identify where the username display issue occurs.
 */

console.log('🏠 Testing Dashboard User Data Reception...');

async function testDashboardUserData() {
  try {
    console.log('\n📦 Step 1: Importing auth hook...');
    
    // We need to test this from within the React context
    // This script will help identify the issue by checking the auth state
    
    console.log('\n🔍 Step 2: Checking if we can access the auth context...');
    
    // Check if we're on a page with React context
    if (typeof window.React === 'undefined') {
      console.log('ℹ️ React not available in this context');
      console.log('💡 This test needs to be run from within the application');
      console.log('🔧 Try running this from the browser console on the dashboard page');
      return;
    }
    
    console.log('\n🔍 Step 3: Checking auth state from browser context...');
    
    // Try to access the auth state through the DOM or global variables
    const authElements = document.querySelectorAll('[data-testid="user-display"]');
    if (authElements.length > 0) {
      console.log('✅ Found user display elements:', authElements.length);
      authElements.forEach((el, index) => {
        console.log(`Element ${index + 1}:`, el.textContent);
      });
    } else {
      console.log('ℹ️ No user display elements found with data-testid="user-display"');
    }
    
    // Check for username display in the header
    const headerElements = document.querySelectorAll('header span, .header span, [class*="header"] span');
    console.log('\n🔍 Checking header elements for username display...');
    
    let foundUserDisplay = false;
    headerElements.forEach((el, index) => {
      const text = el.textContent?.trim();
      if (text && (text.includes('Usuario') || text.includes('@') || text.length > 2)) {
        console.log(`Header element ${index + 1}: "${text}"`);
        if (text === 'Usuario Demo') {
          console.log('❌ Found "Usuario Demo" - this is the issue!');
          foundUserDisplay = true;
        } else if (text !== 'Usuario Demo' && text.includes('@')) {
          console.log('✅ Found what looks like a real username/email');
          foundUserDisplay = true;
        }
      }
    });
    
    if (!foundUserDisplay) {
      console.log('ℹ️ No obvious username display found in header elements');
    }
    
    console.log('\n🔍 Step 4: Checking localStorage for auth data...');
    
    // Check localStorage for Supabase auth data
    const authKeys = Object.keys(localStorage).filter(key => 
      key.includes('supabase') || key.includes('auth') || key.startsWith('sb-')
    );
    
    if (authKeys.length > 0) {
      console.log('✅ Found auth-related localStorage keys:');
      authKeys.forEach(key => {
        try {
          const value = localStorage.getItem(key);
          if (value) {
            const parsed = JSON.parse(value);
            if (parsed.user) {
              console.log(`📋 ${key} contains user data:`);
              console.log('- Email:', parsed.user.email);
              console.log('- ID:', parsed.user.id);
              console.log('- User metadata:', parsed.user.user_metadata);
            }
          }
        } catch (e) {
          console.log(`- ${key}: (not JSON or error parsing)`);
        }
      });
    } else {
      console.log('⚠️ No auth-related localStorage keys found');
    }
    
    console.log('\n🔍 Step 5: Testing direct Supabase access...');
    
    try {
      const { supabase } = await import('/src/lib/supabase.ts');
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('❌ Error getting user from Supabase:', error);
      } else if (user) {
        console.log('✅ Direct Supabase user access successful:');
        console.log('- Email:', user.email);
        console.log('- User metadata:', user.user_metadata);
        
        // Test the username extraction that should be happening
        const extractedUsername = 
          user.user_metadata?.full_name ||
          user.user_metadata?.name ||
          user.email?.split("@")[0] ||
          "Usuario";
        
        console.log(`🎯 Username that should be displayed: "${extractedUsername}"`);
        
        if (extractedUsername === "Usuario") {
          console.log('❌ Issue found: User metadata is missing name information');
          console.log('💡 This explains why "Usuario Demo" is being displayed');
        } else {
          console.log('✅ Username data is available');
          console.log('🔧 The issue might be in the auth context or dashboard component');
        }
      } else {
        console.log('ℹ️ No user found - not logged in');
      }
    } catch (importError) {
      console.error('❌ Failed to import Supabase:', importError);
    }
    
    console.log('\n📊 Step 6: Summary and Recommendations...');
    console.log('=========================================');
    
    console.log('💡 To fully debug this issue:');
    console.log('1. Open the dashboard page (/dashboard)');
    console.log('2. Open browser developer tools');
    console.log('3. Look for console messages starting with "🔄 Auth:" and "🏠 Dashboard:"');
    console.log('4. Check if the user object contains the correct username');
    console.log('5. Verify the dashboard component is receiving the user data');
    
    console.log('\n🔧 If "Usuario Demo" is still showing:');
    console.log('- Check if user metadata contains name information');
    console.log('- Verify the auth context is properly providing user data');
    console.log('- Ensure the dashboard component is using the correct user object');
    
  } catch (error) {
    console.error('❌ Dashboard user data test failed:', error);
  }
}

// Auto-run the test
testDashboardUserData();

// Make it available globally
window.testDashboardUserData = testDashboardUserData;

console.log('\n💡 You can run this test again with: testDashboardUserData()');
