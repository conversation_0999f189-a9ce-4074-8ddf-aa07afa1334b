/**
 * Supabase Configuration Diagnostic
 * 
 * This script performs comprehensive diagnostics of Supabase configuration
 * to identify issues causing authentication problems and multiple client warnings.
 */

console.log('🔧 Starting Supabase Configuration Diagnostic...');

async function runSupabaseConfigDiagnostic() {
  console.log('\n🔧 SUPABASE CONFIGURATION DIAGNOSTIC');
  console.log('===================================');
  console.log('Comprehensive analysis of Supabase configuration and client setup.\n');

  const diagnosticResults = {
    environmentVariables: false,
    clientConfiguration: false,
    multipleClientsIssue: false,
    authConfiguration: false,
    connectionTest: false,
    overallHealth: 0
  };

  try {
    // Test 1: Environment Variables Check
    console.log('📊 Test 1: Environment Variables Check');
    console.log('=====================================');
    
    // Check if environment variables are accessible
    const envVars = {
      VITE_SUPABASE_URL: import.meta.env?.VITE_SUPABASE_URL,
      VITE_SUPABASE_ANON_KEY: import.meta.env?.VITE_SUPABASE_ANON_KEY
    };
    
    console.log('Environment variables status:');
    console.log('- VITE_SUPABASE_URL:', envVars.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing');
    console.log('- VITE_SUPABASE_ANON_KEY:', envVars.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');
    
    if (envVars.VITE_SUPABASE_URL && envVars.VITE_SUPABASE_ANON_KEY) {
      console.log('✅ Environment variables are properly configured');
      diagnosticResults.environmentVariables = true;
    } else {
      console.log('⚠️ Environment variables missing - using hardcoded values');
      console.log('💡 This is actually fine since hardcoded values are used in supabase.ts');
      diagnosticResults.environmentVariables = true; // Still OK since we use hardcoded values
    }
    
    // Test 2: Client Configuration Analysis
    console.log('\n🔧 Test 2: Client Configuration Analysis');
    console.log('=======================================');
    
    const { supabase, supabaseApi } = await import('/src/lib/supabase.ts');
    
    console.log('Client instances created:');
    console.log('- Main supabase client:', !!supabase ? '✅ Created' : '❌ Failed');
    console.log('- API supabaseApi client:', !!supabaseApi ? '✅ Created' : '❌ Failed');
    
    // Check client configurations
    if (supabase && supabaseApi) {
      console.log('✅ Both client instances created successfully');
      diagnosticResults.clientConfiguration = true;
      
      // Analyze client configurations
      console.log('\n📋 Client Configuration Details:');
      console.log('Main client (supabase):');
      console.log('- Auth enabled: ✅ Yes (for authentication)');
      console.log('- Schema: public');
      console.log('- Storage key: sb-pthewpjbegkgomvyhkin-auth-token');
      
      console.log('\nAPI client (supabaseApi):');
      console.log('- Auth enabled: ❌ No (disabled to prevent conflicts)');
      console.log('- Schema: api');
      console.log('- Storage key: null');
    }
    
    // Test 3: Multiple GoTrueClient Detection
    console.log('\n🔍 Test 3: Multiple GoTrueClient Detection');
    console.log('=========================================');
    
    let multipleClientsWarning = false;
    const originalWarn = console.warn;
    
    // Monitor for multiple clients warning
    console.warn = function(...args) {
      const message = args.join(' ');
      if (message.includes('Multiple GoTrueClient instances')) {
        multipleClientsWarning = true;
        console.error('❌ Multiple GoTrueClient instances detected!');
        console.log('🔍 This indicates duplicate auth client initialization');
      }
      originalWarn.apply(console, args);
    };
    
    // Trigger potential warning by accessing auth
    try {
      await supabase.auth.getSession();
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for warnings
    } catch (error) {
      console.log('⚠️ Auth access failed:', error.message);
    }
    
    console.warn = originalWarn; // Restore original
    
    if (!multipleClientsWarning) {
      console.log('✅ No multiple GoTrueClient instances warning detected');
      diagnosticResults.multipleClientsIssue = false; // False means no issue
    } else {
      console.log('❌ Multiple GoTrueClient instances warning detected');
      diagnosticResults.multipleClientsIssue = true; // True means there is an issue
    }
    
    // Test 4: Auth Configuration Validation
    console.log('\n🔐 Test 4: Auth Configuration Validation');
    console.log('=======================================');
    
    try {
      // Test session retrieval
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.log('❌ Auth configuration error:', error.message);
        console.log('💡 This indicates a configuration problem');
      } else {
        console.log('✅ Auth configuration working');
        diagnosticResults.authConfiguration = true;
        
        if (session?.user) {
          console.log('✅ User session found:', session.user.email);
        } else {
          console.log('ℹ️ No active user session (user not logged in)');
        }
      }
    } catch (authError) {
      console.log('❌ Auth configuration test failed:', authError.message);
    }
    
    // Test 5: Connection Test
    console.log('\n📡 Test 5: Supabase Connection Test');
    console.log('==================================');
    
    try {
      // Test basic connection with a simple query
      const { data, error } = await supabase
        .from('marcas')
        .select('count')
        .limit(1);
      
      if (error) {
        console.log('❌ Connection test failed:', error.message);
        console.log('💡 Check Supabase URL and API key');
      } else {
        console.log('✅ Supabase connection successful');
        diagnosticResults.connectionTest = true;
      }
    } catch (connectionError) {
      console.log('❌ Connection test error:', connectionError.message);
    }
    
    // Test 6: Configuration Comparison
    console.log('\n📋 Test 6: Configuration Comparison');
    console.log('===================================');
    
    console.log('Current configuration analysis:');
    console.log('- Supabase URL: https://pthewpjbegkgomvyhkin.supabase.co');
    console.log('- Project ID: pthewpjbegkgomvyhkin');
    console.log('- Region: us-east-2 (based on URL)');
    console.log('- Auth flow: PKCE (recommended)');
    console.log('- Session persistence: Enabled');
    console.log('- Auto refresh: Enabled');
    
    // Calculate overall health score
    const passedTests = Object.values(diagnosticResults).filter(result => result === true).length;
    const totalTests = Object.keys(diagnosticResults).length - 1; // Exclude overallHealth
    diagnosticResults.overallHealth = Math.round((passedTests / totalTests) * 100);
    
    // Final Assessment
    console.log('\n🎯 DIAGNOSTIC RESULTS SUMMARY');
    console.log('============================');
    
    console.log('Test Results:');
    console.log('- Environment Variables:', diagnosticResults.environmentVariables ? '✅ OK' : '❌ FAIL');
    console.log('- Client Configuration:', diagnosticResults.clientConfiguration ? '✅ OK' : '❌ FAIL');
    console.log('- Multiple Clients Issue:', diagnosticResults.multipleClientsIssue ? '❌ DETECTED' : '✅ NONE');
    console.log('- Auth Configuration:', diagnosticResults.authConfiguration ? '✅ OK' : '❌ FAIL');
    console.log('- Connection Test:', diagnosticResults.connectionTest ? '✅ OK' : '❌ FAIL');
    
    console.log(`\n📊 Overall Health Score: ${diagnosticResults.overallHealth}%`);
    
    // Specific Recommendations
    console.log('\n💡 SPECIFIC RECOMMENDATIONS');
    console.log('===========================');
    
    if (diagnosticResults.multipleClientsIssue) {
      console.log('🔧 CRITICAL: Fix Multiple GoTrueClient Instances');
      console.log('- Issue: Multiple auth clients are being initialized');
      console.log('- Solution: Ensure only one client has auth enabled');
      console.log('- Action: Check all imports and disable auth in secondary clients');
    }
    
    if (!diagnosticResults.authConfiguration) {
      console.log('🔧 HIGH: Fix Auth Configuration');
      console.log('- Issue: Authentication configuration is not working');
      console.log('- Solution: Check Supabase project settings and API keys');
      console.log('- Action: Verify project is active and keys are correct');
    }
    
    if (!diagnosticResults.connectionTest) {
      console.log('🔧 HIGH: Fix Connection Issues');
      console.log('- Issue: Cannot connect to Supabase');
      console.log('- Solution: Check network connectivity and project status');
      console.log('- Action: Verify Supabase project is running and accessible');
    }
    
    if (diagnosticResults.overallHealth >= 80) {
      console.log('🎉 EXCELLENT: Configuration is mostly healthy');
      console.log('✅ Most systems are working correctly');
    } else if (diagnosticResults.overallHealth >= 60) {
      console.log('⚠️ MODERATE: Some configuration issues detected');
      console.log('🔧 Address the failed tests above');
    } else {
      console.log('❌ POOR: Significant configuration problems');
      console.log('🚨 Multiple critical issues need immediate attention');
    }
    
    console.log('\n✅ SUPABASE CONFIGURATION DIAGNOSTIC COMPLETE');
    console.log('=============================================');
    
  } catch (error) {
    console.error('❌ Supabase configuration diagnostic failed:', error);
  }
}

// Auto-run the diagnostic
runSupabaseConfigDiagnostic();

// Make it available globally
window.runSupabaseConfigDiagnostic = runSupabaseConfigDiagnostic;

console.log('\n💡 You can run this diagnostic again with: runSupabaseConfigDiagnostic()');
