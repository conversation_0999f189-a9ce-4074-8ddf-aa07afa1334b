/**
 * Final Auto-Save Verification Script
 * This script verifies that the auto-save functionality is now working correctly
 */

console.log('🎯 FINAL AUTO-SAVE VERIFICATION');
console.log('Testing the complete auto-save workflow after schema fixes');

async function finalAutoSaveVerification() {
  console.log('🚀 Starting final auto-save verification...');
  
  try {
    // Step 1: Check authentication
    console.log('\n1️⃣ AUTHENTICATION CHECK');
    if (!window.supabase) {
      console.log('❌ Supabase not available');
      return false;
    }
    
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ User not authenticated. Please log in first.');
      return false;
    }
    
    console.log('✅ User authenticated:', user.email);
    console.log('📝 User ID:', user.id);
    
    // Step 2: Check service availability
    console.log('\n2️⃣ SERVICE AVAILABILITY CHECK');
    if (!window.designAnalysisService) {
      console.log('❌ designAnalysisService not available');
      return false;
    }
    console.log('✅ designAnalysisService available');
    
    // Step 3: Check QueryClient
    console.log('\n3️⃣ QUERY CLIENT CHECK');
    if (!window.queryClient) {
      console.log('❌ QueryClient not available');
      return false;
    }
    console.log('✅ QueryClient available');
    
    const queryClient = window.queryClient;
    
    // Step 4: Test getUserAnalyses (this was failing before)
    console.log('\n4️⃣ TESTING getUserAnalyses (Previously Failing)');
    
    try {
      console.log('🔄 Calling getUserAnalyses...');
      const analyses = await window.designAnalysisService.getUserAnalyses(user.id, { limit: 5 });
      console.log('✅ getUserAnalyses SUCCESS!');
      console.log('📊 Returned analyses:', analyses.length);
      
      if (analyses.length > 0) {
        console.log('📝 Sample analysis:', {
          id: analyses[0].id,
          filename: analyses[0].original_filename,
          score: analyses[0].overall_score,
          created: analyses[0].created_at
        });
      }
      
    } catch (serviceError) {
      console.log('❌ getUserAnalyses FAILED:', serviceError.message);
      console.log('🔍 This indicates the schema fix didn\'t work');
      return false;
    }
    
    // Step 5: Check query cache
    console.log('\n5️⃣ QUERY CACHE CHECK');
    const cache = queryClient.getQueryCache();
    const userQuery = cache.find(['design-analyses', user.id]);
    
    if (userQuery) {
      console.log('✅ User query found in cache');
      console.log('📊 Query status:', userQuery.state.status);
      console.log('📊 Data length:', userQuery.state.data?.length || 0);
    } else {
      console.log('⚠️ User query not found in cache yet');
    }
    
    // Step 6: Test auto-save functionality
    console.log('\n6️⃣ TESTING AUTO-SAVE FUNCTIONALITY');
    
    const testAnalysisData = {
      user_id: user.id,
      original_filename: 'final-verification-test.png',
      file_size: 2048,
      file_type: 'image/png',
      file_url: null,
      overall_score: 94,
      complexity_scores: { visual: 92, cognitive: 94, structural: 96 },
      analysis_areas: [{
        name: 'Layout',
        score: 94,
        description: 'Final verification test - excellent layout',
        recommendations: ['Perfect as is']
      }],
      recommendations: ['Excellent design overall'],
      ai_analysis_summary: 'Final verification test - auto-save working',
      gemini_analysis: 'Schema fix successful',
      agent_message: 'Auto-save functionality restored',
      visuai_insights: 'All systems operational',
      tags: ['final-verification', 'schema-fix-test']
    };
    
    // Get initial count
    const initialAnalyses = await window.designAnalysisService.getUserAnalyses(user.id, { limit: 100 });
    const initialCount = initialAnalyses.length;
    console.log('📊 Initial analysis count:', initialCount);
    
    // Test save
    console.log('💾 Testing saveAnalysis...');
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testAnalysisData, null);
    
    if (!savedAnalysis) {
      console.log('❌ saveAnalysis FAILED');
      return false;
    }
    
    console.log('✅ saveAnalysis SUCCESS!');
    console.log('📝 Saved analysis ID:', savedAnalysis.id);
    
    // Step 7: Test query invalidation
    console.log('\n7️⃣ TESTING QUERY INVALIDATION');
    
    console.log('🔄 Invalidating queries...');
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    // Wait for refetch
    console.log('⏳ Waiting for refetch...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if the new analysis appears
    const updatedAnalyses = await window.designAnalysisService.getUserAnalyses(user.id, { limit: 100 });
    const finalCount = updatedAnalyses.length;
    const foundInService = updatedAnalyses.find(a => a.id === savedAnalysis.id);
    
    console.log('📊 Final analysis count:', finalCount);
    console.log('📊 Count difference:', finalCount - initialCount);
    
    if (foundInService) {
      console.log('✅ New analysis found in service data');
    } else {
      console.log('❌ New analysis NOT found in service data');
    }
    
    // Check query cache
    const updatedQuery = cache.find(['design-analyses', user.id]);
    const foundInQuery = updatedQuery?.state.data?.find(a => a.id === savedAnalysis.id);
    
    if (foundInQuery) {
      console.log('✅ New analysis found in query cache');
    } else {
      console.log('❌ New analysis NOT found in query cache');
    }
    
    // Step 8: Test UI update
    console.log('\n8️⃣ TESTING UI UPDATE');
    
    // Switch to history tab
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('🔄 Switched to history tab');
    }
    
    // Look for the analysis in the UI
    const analysisInUI = Array.from(document.querySelectorAll('*')).find(el => 
      el.textContent && el.textContent.includes('final-verification-test.png')
    );
    
    if (analysisInUI) {
      console.log('✅ New analysis found in UI');
    } else {
      console.log('❌ New analysis NOT found in UI');
      
      // Debug UI state
      const analysisCards = document.querySelectorAll('[data-testid*="analysis"], [class*="analysis"]');
      console.log('🔍 Analysis cards in UI:', analysisCards.length);
      
      const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"]');
      console.log('🔍 Loading elements:', loadingElements.length);
    }
    
    // Step 9: Clean up
    console.log('\n9️⃣ CLEANUP');
    
    console.log('🧹 Deleting test analysis...');
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    console.log('✅ Test analysis deleted');
    
    // Final invalidation
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    // Step 10: Final verification
    console.log('\n🔟 FINAL VERIFICATION');
    
    const success = foundInService && foundInQuery;
    
    if (success) {
      console.log('🎉 AUTO-SAVE VERIFICATION: SUCCESS!');
      console.log('✅ Schema fix worked');
      console.log('✅ getUserAnalyses working');
      console.log('✅ saveAnalysis working');
      console.log('✅ Query invalidation working');
      console.log('✅ Data persistence working');
      
      if (analysisInUI) {
        console.log('✅ UI updates working');
      } else {
        console.log('⚠️ UI updates may need additional investigation');
      }
      
    } else {
      console.log('💥 AUTO-SAVE VERIFICATION: PARTIAL SUCCESS');
      console.log('✅ Schema fix worked');
      console.log('✅ Database operations working');
      
      if (!foundInQuery) {
        console.log('❌ Query cache not updating properly');
      }
      
      if (!analysisInUI) {
        console.log('❌ UI not updating properly');
      }
    }
    
    return success;
    
  } catch (error) {
    console.error('❌ Final verification failed:', error);
    return false;
  }
}

// Auto-run when loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', finalAutoSaveVerification);
} else {
  setTimeout(finalAutoSaveVerification, 1000); // Give the page time to load
}

// Make available globally
window.finalAutoSaveVerification = finalAutoSaveVerification;
console.log('🔧 Verification function available as: window.finalAutoSaveVerification()');
