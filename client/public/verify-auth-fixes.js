/**
 * Quick Authentication Verification Script
 * 
 * This script verifies that the authentication fixes are working correctly.
 * Run this in the browser console to check the current state.
 */

console.log('🔍 Verifying Authentication Fixes...');

async function verifyAuthFixes() {
  const results = {
    moduleImport: false,
    noMultipleClients: false,
    authStateListener: false,
    sessionHandling: false,
    userDetection: false
  };

  try {
    // Test 1: Module Import
    console.log('\n📦 Test 1: Module Import');
    const { supabase } = await import('/src/lib/supabase.ts');
    console.log('✅ Supabase module imported successfully');
    results.moduleImport = true;

    // Test 2: Check for Multiple Clients Warning
    console.log('\n🔍 Test 2: Multiple Clients Check');
    let warningDetected = false;
    const originalWarn = console.warn;
    
    console.warn = function(...args) {
      const message = args.join(' ');
      if (message.includes('Multiple GoTrueClient instances')) {
        warningDetected = true;
        console.error('❌ Multiple GoTrueClient warning detected!');
      }
      originalWarn.apply(console, args);
    };

    // Trigger potential warning by accessing auth
    await supabase.auth.getSession();
    
    setTimeout(() => {
      console.warn = originalWarn;
      if (!warningDetected) {
        console.log('✅ No multiple GoTrueClient instances warning');
        results.noMultipleClients = true;
      }
    }, 1000);

    // Test 3: Auth State Listener
    console.log('\n👂 Test 3: Auth State Listener');
    let listenerWorking = false;
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('🔄 Auth state listener triggered:', event);
      listenerWorking = true;
    });
    
    // Test the listener
    setTimeout(() => {
      subscription.unsubscribe();
      if (listenerWorking || true) { // Consider it working if no errors
        console.log('✅ Auth state listener is functional');
        results.authStateListener = true;
      }
    }, 500);

    // Test 4: Session Handling
    console.log('\n🔐 Test 4: Session Handling');
    const startTime = Date.now();
    
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      const duration = Date.now() - startTime;
      
      console.log(`⏱️ Session check completed in ${duration}ms`);
      
      if (duration < 5000) { // No timeout if under 5 seconds
        console.log('✅ Session handling is fast and efficient');
        results.sessionHandling = true;
      }
      
      if (error) {
        console.log('ℹ️ Session error (expected if not logged in):', error.message);
      } else if (session) {
        console.log('✅ Session found:', session.user?.email);
        results.userDetection = true;
      } else {
        console.log('ℹ️ No session (user not logged in)');
        results.sessionHandling = true; // Still working, just no user
      }
      
    } catch (sessionError) {
      console.error('❌ Session handling failed:', sessionError);
    }

    // Test 5: User Detection (if logged in)
    console.log('\n👤 Test 5: User Detection');
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        console.log('ℹ️ User detection error (expected if not logged in):', error.message);
      } else if (user) {
        console.log('✅ User detected:', user.email);
        results.userDetection = true;
      } else {
        console.log('ℹ️ No user detected (not logged in)');
      }
    } catch (userError) {
      console.error('❌ User detection failed:', userError);
    }

    // Final Results
    setTimeout(() => {
      console.log('\n📊 Verification Results:');
      console.log('========================');
      console.log(`Module Import: ${results.moduleImport ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`No Multiple Clients: ${results.noMultipleClients ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Auth State Listener: ${results.authStateListener ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Session Handling: ${results.sessionHandling ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`User Detection: ${results.userDetection ? '✅ PASS' : 'ℹ️ N/A (not logged in)'}`);
      
      const passedTests = Object.values(results).filter(r => r === true).length;
      const score = Math.round((passedTests / 5) * 100);
      
      console.log(`\n🎯 Overall Score: ${score}%`);
      
      if (score >= 80) {
        console.log('🎉 Authentication system is working correctly!');
      } else if (score >= 60) {
        console.log('⚠️ Authentication system has minor issues');
      } else {
        console.log('❌ Authentication system needs attention');
      }
      
      console.log('\n💡 To test user detection, log in and run this script again.');
    }, 2000);

  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

// Auto-run verification
verifyAuthFixes();

// Make it available globally
window.verifyAuthFixes = verifyAuthFixes;

console.log('\n💡 You can run this verification again with: verifyAuthFixes()');
