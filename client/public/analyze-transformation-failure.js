/**
 * User Transformation Failure Analysis
 * 
 * This script analyzes why createAppUserFromSupabase is not being called
 * or is returning null, causing the authentication system to fail.
 */

console.log('🔍 Analyzing User Transformation Failure...');

async function analyzeTransformationFailure() {
  console.log('\n🔍 USER TRANSFORMATION FAILURE ANALYSIS');
  console.log('======================================');
  console.log('Investigating why createAppUserFromSupabase is failing or not being called.\n');

  try {
    // Step 1: Check if user exists in Supabase
    console.log('📊 Step 1: Checking Supabase User Data');
    console.log('=====================================');
    
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('❌ Supabase getUser error:', error.message);
      console.log('💡 This indicates a fundamental Supabase connection issue');
      return;
    }
    
    if (!user) {
      console.log('❌ No user found in Supabase session');
      console.log('💡 User needs to log in first');
      return;
    }
    
    console.log('✅ Supabase user found:', user.email);
    console.log('📋 Raw user data structure:');
    console.log('- ID:', user.id);
    console.log('- Email:', user.email);
    console.log('- Created:', user.created_at);
    console.log('- user_metadata:', JSON.stringify(user.user_metadata, null, 2));
    console.log('- identities:', user.identities?.length || 0, 'identities');
    
    // Step 2: Test the transformation function directly
    console.log('\n🧪 Step 2: Testing createAppUserFromSupabase Function');
    console.log('==================================================');
    
    try {
      // Test the exact transformation logic from the current implementation
      console.log('🔄 Testing transformation with current user data...');
      
      // Simulate the function with enhanced debugging
      console.log("🔄 Creating app user from Supabase user - Input validation:");
      console.log("- supabaseUser exists:", !!user);
      console.log("- supabaseUser type:", typeof user);

      if (!user) {
        console.error("❌ createAppUserFromSupabase: Received null/undefined supabaseUser");
        throw new Error("Cannot create app user from null/undefined Supabase user");
      }

      console.log("🔍 Raw Supabase user input:", {
        id: user.id,
        email: user.email,
        user_metadata: user.user_metadata,
        identities: user.identities?.map(i => ({
          provider: i.provider,
          identity_data: i.identity_data
        }))
      });

      // Enhanced username extraction with better fallback logic
      let username = "Usuario";
      let usernameSource = "fallback";

      if (user.user_metadata?.full_name) {
        username = user.user_metadata.full_name;
        usernameSource = "user_metadata.full_name";
      } else if (user.user_metadata?.name) {
        username = user.user_metadata.name;
        usernameSource = "user_metadata.name";
      } else if (user.identities?.[0]?.identity_data?.full_name) {
        username = user.identities[0].identity_data.full_name;
        usernameSource = "identities[0].identity_data.full_name";
      } else if (user.identities?.[0]?.identity_data?.name) {
        username = user.identities[0].identity_data.name;
        usernameSource = "identities[0].identity_data.name";
      } else if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
        username = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
        usernameSource = "user_metadata.first_name + last_name";
      } else if (user.email) {
        username = user.email.split("@")[0];
        usernameSource = "email prefix";
      }

      console.log(`✅ Extracted username: "${username}" from ${usernameSource}`);

      const appUser = {
        id: user.id,
        username: username,
        email: user.email || "",
        role: "user",
        isActive: true,
        createdAt: new Date(),
      };

      console.log("✅ Created app user:", appUser);
      
      // Validate the app user
      const isValid = !!(appUser.id && appUser.username && appUser.email);
      console.log('📊 App user validation:', isValid ? '✅ Valid' : '❌ Invalid');
      
      if (!isValid) {
        console.log('❌ App user validation failed:');
        if (!appUser.id) console.log('  - Missing ID');
        if (!appUser.username) console.log('  - Missing username');
        if (!appUser.email) console.log('  - Missing email');
      }
      
      console.log('✅ Transformation function works correctly');
      
    } catch (transformError) {
      console.error('❌ Transformation function failed:', transformError);
      console.log('💡 This indicates an issue in the createAppUserFromSupabase function');
    }
    
    // Step 3: Check auth state management flow
    console.log('\n🔄 Step 3: Checking Auth State Management Flow');
    console.log('============================================');
    
    console.log('🔍 Analyzing potential issues in auth initialization...');
    
    // Check if the complex async initialization is causing issues
    console.log('📊 Potential issues identified:');
    console.log('1. Complex async initialization with mounted flag');
    console.log('2. Multiple validation checks that might prevent state setting');
    console.log('3. Enhanced error handling that might be too strict');
    
    // Step 4: Compare with original working version
    console.log('\n📋 Step 4: Comparison with Original Working Version');
    console.log('=================================================');
    
    console.log('🔍 Original working version characteristics:');
    console.log('- Simple, direct auth state management');
    console.log('- Immediate state setting without complex validation');
    console.log('- Straightforward auth state change handling');
    
    console.log('🔍 Current version characteristics:');
    console.log('- Complex async initialization with mounted flag');
    console.log('- Multiple validation steps before setting state');
    console.log('- Enhanced debugging that might interfere with flow');
    
    // Step 5: Identify the specific issue
    console.log('\n🎯 Step 5: Root Cause Identification');
    console.log('===================================');
    
    console.log('❌ IDENTIFIED ISSUE: Over-engineered auth initialization');
    console.log('');
    console.log('📊 Specific problems:');
    console.log('1. The async initAuth function with mounted flag creates race conditions');
    console.log('2. Validation checks prevent state setting even when transformation works');
    console.log('3. Complex flow makes debugging harder and introduces failure points');
    
    console.log('\n💡 RECOMMENDED SOLUTION:');
    console.log('========================');
    console.log('1. Revert to the original simple auth initialization pattern');
    console.log('2. Keep the enhanced createAppUserFromSupabase function');
    console.log('3. Remove complex async initialization and validation');
    console.log('4. Preserve debugging but simplify the core flow');
    
    console.log('\n🔧 SPECIFIC CHANGES NEEDED:');
    console.log('===========================');
    console.log('1. Remove the complex initAuth async function');
    console.log('2. Use the original simple useEffect pattern');
    console.log('3. Remove the mounted flag and race condition handling');
    console.log('4. Simplify the auth state change handler');
    console.log('5. Keep enhanced debugging and error messages');
    
    console.log('\n✅ ANALYSIS COMPLETE');
    console.log('===================');
    console.log('The issue is in the over-engineered auth initialization flow.');
    console.log('The transformation function works correctly, but the complex');
    console.log('async initialization prevents it from being called properly.');
    
  } catch (error) {
    console.error('❌ Transformation failure analysis failed:', error);
  }
}

// Auto-run the analysis
analyzeTransformationFailure();

// Make it available globally
window.analyzeTransformationFailure = analyzeTransformationFailure;

console.log('\n💡 You can run this analysis again with: analyzeTransformationFailure()');
