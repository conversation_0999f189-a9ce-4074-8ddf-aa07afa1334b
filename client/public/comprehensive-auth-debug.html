<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Authentication Debug</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2rem;
        }
        .critical-header {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
        }
        .critical-header h3 {
            margin: 0 0 15px 0;
            font-size: 1.4rem;
        }
        .debug-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .debug-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            transition: all 0.2s;
        }
        .debug-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-color: #ef4444;
        }
        .debug-card h4 {
            margin: 0 0 15px 0;
            color: #1e293b;
            font-size: 1.1rem;
        }
        .debug-card p {
            margin: 0 0 15px 0;
            color: #64748b;
            font-size: 0.9rem;
        }
        .button {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s;
            width: 100%;
        }
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }
        .button.large {
            font-size: 18px;
            padding: 20px 40px;
            margin: 20px 10px;
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.6;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 25px;
            border: 1px solid #374151;
        }
        .issue-summary {
            background: #fef2f2;
            border: 1px solid #ef4444;
            color: #991b1b;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .issue-summary h4 {
            margin: 0 0 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Comprehensive Authentication Debug</h1>
        
        <div class="critical-header">
            <h3>🚨 Critical Authentication Issues Detected</h3>
            <p>The authentication system is receiving null/undefined user data despite configuration fixes.</p>
            <p><strong>Symptoms:</strong></p>
            <ul>
                <li>Dashboard receiving: email: undefined, id: undefined, user: null, username: undefined</li>
                <li>Auth system shows: "App user cleared" message</li>
                <li>"Setting up Supabase auth state" but no successful completion</li>
            </ul>
        </div>

        <div class="issue-summary">
            <h4>🎯 Investigation Focus Areas</h4>
            <ul>
                <li><strong>Session Retrieval:</strong> Is Supabase returning valid session data?</li>
                <li><strong>User Transformation:</strong> Is createAppUserFromSupabase being called?</li>
                <li><strong>Auth State Management:</strong> Is the useAuth hook setting state correctly?</li>
                <li><strong>Timing Issues:</strong> Are there race conditions in initialization?</li>
                <li><strong>Component Integration:</strong> Is AuthProvider passing data to components?</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button large" onclick="runCompleteAuthDebug()">
                🚨 Run Complete Authentication Debug
            </button>
        </div>

        <div class="debug-grid">
            <div class="debug-card">
                <h4>🔍 Deep Auth Flow Analysis</h4>
                <p>Comprehensive analysis of the entire authentication flow to identify failure points</p>
                <button class="button" onclick="runDeepAuthAnalysis()">🔍 Analyze Auth Flow</button>
            </div>

            <div class="debug-card">
                <h4>📊 Session Retrieval Debug</h4>
                <p>Detailed investigation of Supabase session retrieval and user data presence</p>
                <button class="button" onclick="runSessionDebug()">📊 Debug Session</button>
            </div>

            <div class="debug-card">
                <h4>🔄 User Transformation Test</h4>
                <p>Test if createAppUserFromSupabase function is working correctly with current data</p>
                <button class="button" onclick="runTransformationTest()">🔄 Test Transformation</button>
            </div>

            <div class="debug-card">
                <h4>⏱️ Timing Analysis</h4>
                <p>Check for race conditions and timing issues in auth initialization sequence</p>
                <button class="button" onclick="runTimingAnalysis()">⏱️ Analyze Timing</button>
            </div>

            <div class="debug-card">
                <h4>🏠 Component Integration</h4>
                <p>Verify that auth data is flowing correctly from provider to dashboard components</p>
                <button class="button" onclick="runComponentTest()">🏠 Test Components</button>
            </div>

            <div class="debug-card">
                <h4>🔧 Live Auth State Monitor</h4>
                <p>Real-time monitoring of authentication state changes and data flow</p>
                <button class="button" onclick="startAuthMonitor()">🔧 Start Monitor</button>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button success large" onclick="goToLogin()">🔐 Go to Login</button>
            <button class="button success large" onclick="goToDashboard()">🏠 Go to Dashboard</button>
            <button class="button secondary large" onclick="clearConsole()">🧹 Clear Console</button>
        </div>

        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        let authMonitorActive = false;
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        function goToDashboard() {
            window.location.href = '/dashboard';
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function runDeepAuthAnalysis() {
            console.log('🔍 Running Deep Authentication Analysis...');
            try {
                await loadScript('/deep-auth-analysis.js');
            } catch (error) {
                console.error('❌ Failed to run deep auth analysis:', error);
            }
        }
        
        async function runSessionDebug() {
            console.log('📊 Running Session Retrieval Debug...');
            try {
                await loadScript('/debug-session-retrieval.js');
            } catch (error) {
                console.error('❌ Failed to run session debug:', error);
            }
        }
        
        async function runTransformationTest() {
            console.log('🔄 Running User Transformation Test...');
            try {
                const { supabase } = await import('/src/lib/supabase.ts');
                const { data: { user }, error } = await supabase.auth.getUser();
                
                if (error || !user) {
                    console.log('⚠️ No user found - cannot test transformation');
                    console.log('💡 Please log in first to test user transformation');
                    return;
                }
                
                console.log('✅ User found, testing transformation...');
                
                // Test transformation logic
                let username = "Usuario";
                if (user.user_metadata?.full_name) {
                    username = user.user_metadata.full_name;
                } else if (user.email) {
                    username = user.email.split("@")[0];
                }
                
                console.log('📊 Transformation result:');
                console.log('- Username:', username);
                console.log('- Source:', username === 'Usuario' ? 'fallback' : 'user data');
                
            } catch (error) {
                console.error('❌ Transformation test failed:', error);
            }
        }
        
        async function runTimingAnalysis() {
            console.log('⏱️ Running Timing Analysis...');
            
            const timingTests = [];
            
            try {
                const { supabase } = await import('/src/lib/supabase.ts');
                
                for (let i = 0; i < 5; i++) {
                    const start = performance.now();
                    await supabase.auth.getSession();
                    const end = performance.now();
                    timingTests.push(end - start);
                    console.log(`Test ${i + 1}: ${(end - start).toFixed(2)}ms`);
                }
                
                const avgTime = timingTests.reduce((a, b) => a + b, 0) / timingTests.length;
                console.log(`Average session retrieval time: ${avgTime.toFixed(2)}ms`);
                
                if (avgTime > 100) {
                    console.log('⚠️ Session retrieval is slow - potential performance issue');
                } else {
                    console.log('✅ Session retrieval timing is acceptable');
                }
                
            } catch (error) {
                console.error('❌ Timing analysis failed:', error);
            }
        }
        
        async function runComponentTest() {
            console.log('🏠 Running Component Integration Test...');
            
            // Check for user display elements
            const userDisplayElements = document.querySelectorAll('[data-testid="user-display"]');
            console.log('User display elements found:', userDisplayElements.length);
            
            userDisplayElements.forEach((el, index) => {
                const displayText = el.textContent?.trim();
                console.log(`Element ${index + 1}: "${displayText}"`);
                
                if (displayText === 'Usuario Demo' || displayText === 'Usuario') {
                    console.log('  ❌ Showing fallback text - auth data not reaching component');
                } else if (displayText && displayText !== '') {
                    console.log('  ✅ Showing actual user data');
                } else {
                    console.log('  ⚠️ Empty content');
                }
            });
            
            if (userDisplayElements.length === 0) {
                console.log('ℹ️ No user display elements found - may not be on dashboard');
                console.log('💡 Navigate to dashboard to test component integration');
            }
        }
        
        async function startAuthMonitor() {
            if (authMonitorActive) {
                console.log('⚠️ Auth monitor already active');
                return;
            }
            
            console.log('🔧 Starting Live Auth State Monitor...');
            authMonitorActive = true;
            
            try {
                const { supabase } = await import('/src/lib/supabase.ts');
                
                const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
                    console.log('🔔 Auth state change detected:');
                    console.log('- Event:', event);
                    console.log('- Has session:', !!session);
                    console.log('- Has user:', !!session?.user);
                    
                    if (session?.user) {
                        console.log('- User email:', session.user.email);
                        console.log('- User ID:', session.user.id);
                    }
                });
                
                console.log('✅ Auth monitor started - watching for state changes...');
                console.log('💡 Perform login/logout actions to see real-time monitoring');
                
                // Auto-stop after 2 minutes
                setTimeout(() => {
                    subscription.unsubscribe();
                    authMonitorActive = false;
                    console.log('⏹️ Auth monitor stopped (2 minute timeout)');
                }, 120000);
                
            } catch (error) {
                authMonitorActive = false;
                console.error('❌ Failed to start auth monitor:', error);
            }
        }
        
        async function runCompleteAuthDebug() {
            clearConsole();
            console.log('🚨 Starting Complete Authentication Debug...');
            console.log('===========================================');
            
            try {
                console.log('\n🔍 Phase 1: Deep Authentication Analysis');
                await runDeepAuthAnalysis();
                
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                console.log('\n📊 Phase 2: Session Retrieval Debug');
                await runSessionDebug();
                
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                console.log('\n🔄 Phase 3: User Transformation Test');
                await runTransformationTest();
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.log('\n⏱️ Phase 4: Timing Analysis');
                await runTimingAnalysis();
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.log('\n🏠 Phase 5: Component Integration Test');
                await runComponentTest();
                
                console.log('\n✅ COMPLETE AUTHENTICATION DEBUG FINISHED');
                console.log('=========================================');
                console.log('📊 Review the detailed analysis above to identify the root cause');
                console.log('🔧 Follow the specific recommendations provided for each identified issue');
                
            } catch (error) {
                console.error('❌ Complete authentication debug failed:', error);
            }
        }
        
        // Auto-run message on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔍 Comprehensive Authentication Debug Ready');
                console.log('==========================================');
                console.log('🚨 This tool will identify why authentication is receiving null/undefined values');
                console.log('📊 Despite configuration fixes, user data is not flowing correctly');
                console.log('🎯 Focus: Session retrieval, user transformation, auth state management');
                console.log('');
                console.log('🚀 Click "Run Complete Authentication Debug" for full analysis');
                console.log('💡 Or use individual debug tools to focus on specific areas');
            }, 500);
        });
    </script>
</body>
</html>
