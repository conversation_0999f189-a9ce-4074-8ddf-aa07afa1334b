<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Authentication System Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        .test-info {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
        }
        .test-info h3 {
            margin: 0 0 15px 0;
            font-size: 1.2rem;
        }
        .test-info ul {
            margin: 0;
            padding-left: 20px;
        }
        .test-info li {
            margin: 8px 0;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.2s;
            box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
        }
        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            box-shadow: 0 2px 10px rgba(107, 114, 128, 0.3);
        }
        .button.secondary:hover {
            box-shadow: 0 4px 20px rgba(107, 114, 128, 0.4);
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 2px 10px rgba(16, 185, 129, 0.3);
        }
        .button.success:hover {
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.4);
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 25px;
            border: 1px solid #374151;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        .status.success {
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.warning {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        .status.error {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .status.info {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .button-group {
            text-align: center;
            margin: 25px 0;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #10b981, #059669);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Complete Authentication System Test</h1>
        
        <div class="test-info">
            <h3>🎯 What this comprehensive test verifies:</h3>
            <ul>
                <li>✅ Multiple GoTrueClient instances warning elimination</li>
                <li>✅ Loading timeout issues resolution</li>
                <li>✅ User detection and profile display functionality</li>
                <li>✅ Authentication state maintenance across components</li>
                <li>✅ Session handling performance and reliability</li>
                <li>✅ Auth state listener functionality</li>
                <li>✅ Overall system stability and user experience</li>
            </ul>
        </div>

        <div class="button-group">
            <button class="button" onclick="runCompleteTest()">🚀 Run Complete Test</button>
            <button class="button secondary" onclick="clearConsole()">🧹 Clear Console</button>
            <button class="button success" onclick="goToLogin()">🔐 Test Login</button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div id="status"></div>
        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        let testProgress = 0;
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
            document.getElementById('status').innerHTML = '';
            updateProgress(0);
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        async function runCompleteTest() {
            clearConsole();
            updateProgress(10);
            document.getElementById('status').innerHTML = '<div class="status info">🔄 Running comprehensive authentication test...</div>';
            
            try {
                // Load and run the test script
                const script = document.createElement('script');
                script.src = '/test-auth-complete-fix.js';
                script.onload = () => {
                    console.log('✅ Complete test script loaded successfully');
                    updateProgress(100);
                };
                script.onerror = () => {
                    console.error('❌ Failed to load complete test script');
                    document.getElementById('status').innerHTML = '<div class="status error">❌ Failed to load test script</div>';
                };
                document.head.appendChild(script);
                
                // Update progress incrementally
                setTimeout(() => updateProgress(30), 1000);
                setTimeout(() => updateProgress(50), 3000);
                setTimeout(() => updateProgress(70), 5000);
                setTimeout(() => updateProgress(90), 7000);
                
                // Update status after completion
                setTimeout(() => {
                    document.getElementById('status').innerHTML = '<div class="status success">✅ Complete authentication test finished - check detailed results above</div>';
                }, 8000);
                
            } catch (error) {
                console.error('Complete test execution failed:', error);
                document.getElementById('status').innerHTML = '<div class="status error">❌ Test execution failed</div>';
                updateProgress(0);
            }
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔧 Complete Authentication Test Page Loaded');
                console.log('💡 Click "Run Complete Test" to start comprehensive testing');
                console.log('🔐 Use "Test Login" to verify user detection with actual login');
            }, 500);
        });
    </script>
</body>
</html>
