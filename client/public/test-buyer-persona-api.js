// Comprehensive Buyer Persona API Test Script
// Run this in the browser console to diagnose the 500 error

window.buyerPersonaApiTest = {
  async testBackendHealth() {
    console.log('🏥 Testing Backend Health...');
    
    try {
      const response = await fetch('/api/buyer-persona/health', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      console.log('Health check response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Backend health check passed:', data);
        return data;
      } else {
        console.error('❌ Backend health check failed:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error details:', errorText);
        return null;
      }
    } catch (error) {
      console.error('❌ Backend health check error:', error);
      return null;
    }
  },

  async testGeneratePersonasEndpoint() {
    console.log('🧪 Testing Generate Personas Endpoint...');
    
    const testPayload = {
      product_description: 'Una plataforma de marketing digital con IA para empresas pequeñas y medianas',
      num_personas: 2,
      industry: 'Tecnología',
      target_market: 'Pequeñas empresas',
      business_goals: 'Aumentar ventas y mejorar engagement',
      competitors: 'HubSpot, Mailchimp',
      target_countries: ['España', 'México'],
      request_timestamp: Date.now(),
      request_id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      unique_seed: `test_${Date.now()}`,
      generation_context: 'Test generation from browser console'
    };

    console.log('📝 Test payload:', testPayload);

    try {
      const response = await fetch('/api/generate-buyer-personas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': testPayload.request_id,
        },
        body: JSON.stringify(testPayload)
      });

      console.log('Generate personas response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Generate personas test passed:', data);
        return data;
      } else {
        console.error('❌ Generate personas test failed:', response.status, response.statusText);
        
        // Try to get error details
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          console.error('Error JSON:', errorData);
        } else {
          const errorText = await response.text();
          console.error('Error text:', errorText);
        }
        return null;
      }
    } catch (error) {
      console.error('❌ Generate personas test error:', error);
      return null;
    }
  },

  async testNetworkConnectivity() {
    console.log('🌐 Testing Network Connectivity...');
    
    // Test basic connectivity to backend
    try {
      const response = await fetch('/api/health', {
        method: 'GET'
      });
      
      console.log('Basic health endpoint status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Basic connectivity test passed:', data);
        return true;
      } else {
        console.error('❌ Basic connectivity test failed:', response.status);
        return false;
      }
    } catch (error) {
      console.error('❌ Network connectivity error:', error);
      return false;
    }
  },

  async runComprehensiveDiagnostic() {
    console.log('🚀 Running Comprehensive Buyer Persona API Diagnostic...');
    console.log('================================================');
    
    // Test 1: Network connectivity
    const networkTest = await this.testNetworkConnectivity();
    
    // Test 2: Backend health
    const healthTest = await this.testBackendHealth();
    
    // Test 3: Generate personas endpoint
    const generateTest = await this.testGeneratePersonasEndpoint();
    
    console.log('================================================');
    console.log('📊 DIAGNOSTIC RESULTS:');
    console.log('Network Connectivity:', networkTest ? '✅ PASS' : '❌ FAIL');
    console.log('Backend Health:', healthTest ? '✅ PASS' : '❌ FAIL');
    console.log('Generate Personas:', generateTest ? '✅ PASS' : '❌ FAIL');
    
    if (networkTest && healthTest && generateTest) {
      console.log('🎉 ALL TESTS PASSED - API is working correctly!');
      return true;
    } else {
      console.log('❌ SOME TESTS FAILED - API has issues');
      
      // Provide specific recommendations
      if (!networkTest) {
        console.log('🔧 RECOMMENDATION: Check if backend server is running on port 8001');
      }
      if (!healthTest) {
        console.log('🔧 RECOMMENDATION: Check buyer persona service configuration and API keys');
      }
      if (!generateTest) {
        console.log('🔧 RECOMMENDATION: Check request payload format and AI service availability');
      }
      
      return false;
    }
  },

  async testCurrentFormData() {
    console.log('📋 Testing with Current Form Data...');
    
    // Try to get form data from the current page
    try {
      const formData = {
        product_description: document.querySelector('textarea[name="product_description"]')?.value || 'Test product description',
        num_personas: parseInt(document.querySelector('input[name="num_personas"]')?.value) || 3,
        industry: document.querySelector('select[name="industry"]')?.value || 'Tecnología',
        target_market: document.querySelector('input[name="target_market"]')?.value || 'Test market',
        business_goals: document.querySelector('textarea[name="business_goals"]')?.value || 'Test goals',
        competitors: document.querySelector('input[name="competitors"]')?.value || 'Test competitors',
        target_countries: [],
        request_timestamp: Date.now(),
        request_id: `form_test_${Date.now()}`,
        unique_seed: `form_${Date.now()}`,
        generation_context: 'Test with actual form data'
      };

      console.log('📝 Using form data:', formData);

      const response = await fetch('/api/generate-buyer-personas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': formData.request_id,
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Form data test passed:', data);
        return data;
      } else {
        console.error('❌ Form data test failed:', response.status);
        const errorText = await response.text();
        console.error('Error:', errorText);
        return null;
      }
    } catch (error) {
      console.error('❌ Form data test error:', error);
      return null;
    }
  }
};

console.log('🧪 Buyer Persona API Test Suite loaded!');
console.log('Run: window.buyerPersonaApiTest.runComprehensiveDiagnostic()');
console.log('Or test individual components:');
console.log('- window.buyerPersonaApiTest.testBackendHealth()');
console.log('- window.buyerPersonaApiTest.testGeneratePersonasEndpoint()');
console.log('- window.buyerPersonaApiTest.testCurrentFormData()');
