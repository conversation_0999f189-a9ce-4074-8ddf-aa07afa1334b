/**
 * User Metadata Fix Script
 * 
 * This script checks the current user's metadata and can help fix missing username data.
 */

console.log('🔧 User Metadata Fix Tool...');

async function checkAndFixUserMetadata() {
  try {
    console.log('\n📦 Step 1: Importing Supabase client...');
    const { supabase } = await import('/src/lib/supabase.ts');
    
    console.log('\n🔐 Step 2: Getting current user...');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Error getting user:', error);
      return;
    }
    
    if (!user) {
      console.log('ℹ️ No user logged in. Please log in first.');
      return;
    }
    
    console.log('✅ User found:', user.email);
    
    console.log('\n🔍 Step 3: Analyzing current user metadata...');
    console.log('Current user_metadata:', JSON.stringify(user.user_metadata, null, 2));
    
    // Test current username extraction
    const currentUsername = 
      user.user_metadata?.full_name ||
      user.user_metadata?.name ||
      user.email?.split("@")[0] ||
      "Usuario";
    
    console.log(`🎯 Current extracted username: "${currentUsername}"`);
    
    // Check if we need to fix metadata
    const needsFix = !user.user_metadata?.full_name && !user.user_metadata?.name;
    
    if (needsFix) {
      console.log('\n⚠️ Issue detected: User metadata is missing name information');
      console.log('This explains why "Usuario Demo" is being displayed');
      
      // Suggest a fix
      const suggestedName = user.email?.split("@")[0] || "Usuario";
      console.log(`💡 Suggested fix: Set full_name to "${suggestedName}"`);
      
      console.log('\n🔧 Would you like to fix this? (This is just a simulation)');
      console.log('In a real scenario, you could update the user metadata like this:');
      console.log(`
// Example fix (DO NOT RUN - this is just for reference):
const { error } = await supabase.auth.updateUser({
  data: {
    full_name: "${suggestedName}",
    username: "${suggestedName}"
  }
});
      `);
      
    } else {
      console.log('✅ User metadata looks good!');
      console.log('The issue might be elsewhere in the data flow.');
    }
    
    console.log('\n🔍 Step 4: Checking identity data...');
    if (user.identities && user.identities.length > 0) {
      user.identities.forEach((identity, index) => {
        console.log(`Identity ${index + 1} (${identity.provider}):`);
        console.log('- full_name:', identity.identity_data?.full_name);
        console.log('- name:', identity.identity_data?.name);
        console.log('- email:', identity.identity_data?.email);
      });
    } else {
      console.log('No identity data found');
    }
    
    console.log('\n📊 Step 5: Testing username extraction methods...');
    
    const extractionMethods = {
      'user_metadata.full_name': user.user_metadata?.full_name,
      'user_metadata.name': user.user_metadata?.name,
      'identities[0].identity_data.full_name': user.identities?.[0]?.identity_data?.full_name,
      'identities[0].identity_data.name': user.identities?.[0]?.identity_data?.name,
      'email_prefix': user.email?.split("@")[0],
      'fallback': 'Usuario'
    };
    
    console.log('Available extraction methods:');
    Object.entries(extractionMethods).forEach(([method, value]) => {
      const status = value ? '✅' : '❌';
      console.log(`${status} ${method}: ${value || 'null'}`);
    });
    
    // Find the best available option
    const bestOption = Object.entries(extractionMethods).find(([_, value]) => value)?.[1] || 'Usuario';
    console.log(`\n🎯 Best available username: "${bestOption}"`);
    
    console.log('\n💡 Recommendations:');
    if (needsFix) {
      console.log('1. User needs metadata update to display proper name');
      console.log('2. Consider implementing a profile setup flow for existing users');
      console.log('3. For new users, ensure registration captures full name properly');
    } else {
      console.log('1. User metadata is properly set');
      console.log('2. Check if the auth hook is using the enhanced extraction logic');
      console.log('3. Verify the dashboard is receiving the correct user object');
    }
    
  } catch (error) {
    console.error('❌ Fix script failed:', error);
  }
}

// Auto-run the check
checkAndFixUserMetadata();

// Make it available globally
window.checkAndFixUserMetadata = checkAndFixUserMetadata;

console.log('\n💡 You can run this check again with: checkAndFixUserMetadata()');
