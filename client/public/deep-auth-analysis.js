/**
 * Deep Authentication Flow Analysis
 * 
 * This script performs a comprehensive analysis of the authentication flow
 * to identify exactly why user data is null/undefined despite configuration fixes.
 */

console.log('🔍 Starting Deep Authentication Flow Analysis...');

async function performDeepAuthAnalysis() {
  console.log('\n🔍 DEEP AUTHENTICATION FLOW ANALYSIS');
  console.log('===================================');
  console.log('Investigating why user data is null/undefined despite Supabase configuration being correct.\n');

  const analysisResults = {
    supabaseConnection: null,
    sessionRetrieval: null,
    userDataPresent: null,
    transformationCalled: null,
    authStateSet: null,
    timingIssues: null
  };

  try {
    // Phase 1: Supabase Connection and Configuration
    console.log('📊 PHASE 1: Supabase Connection and Configuration');
    console.log('===============================================');
    
    const { supabase } = await import('/src/lib/supabase.ts');
    
    if (!supabase) {
      console.error('❌ CRITICAL: Supabase client not imported');
      return;
    }
    
    console.log('✅ Supabase client imported successfully');
    console.log('🔍 Client configuration check:');
    console.log('- Client exists:', !!supabase);
    console.log('- Auth client exists:', !!supabase.auth);
    
    analysisResults.supabaseConnection = true;
    
    // Phase 2: Session Retrieval Deep Dive
    console.log('\n📊 PHASE 2: Session Retrieval Deep Dive');
    console.log('======================================');
    
    console.log('🔄 Attempting to retrieve current session...');
    
    try {
      const sessionStart = performance.now();
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      const sessionEnd = performance.now();
      
      console.log(`⏱️ Session retrieval took: ${(sessionEnd - sessionStart).toFixed(2)}ms`);
      
      if (sessionError) {
        console.error('❌ Session retrieval error:', sessionError);
        console.log('🔍 Error details:');
        console.log('- Message:', sessionError.message);
        console.log('- Status:', sessionError.status);
        analysisResults.sessionRetrieval = false;
        return;
      }
      
      console.log('✅ Session retrieval successful');
      console.log('🔍 Session data analysis:');
      console.log('- Session exists:', !!sessionData?.session);
      console.log('- User exists in session:', !!sessionData?.session?.user);
      
      if (sessionData?.session) {
        console.log('📋 Session details:');
        console.log('- Access token exists:', !!sessionData.session.access_token);
        console.log('- Refresh token exists:', !!sessionData.session.refresh_token);
        console.log('- Expires at:', sessionData.session.expires_at);
        console.log('- Token type:', sessionData.session.token_type);
        
        analysisResults.sessionRetrieval = true;
        
        if (sessionData.session.user) {
          console.log('\n👤 User data in session:');
          const user = sessionData.session.user;
          console.log('- ID:', user.id);
          console.log('- Email:', user.email);
          console.log('- Created at:', user.created_at);
          console.log('- Last sign in:', user.last_sign_in_at);
          console.log('- Email confirmed:', user.email_confirmed_at ? 'Yes' : 'No');
          
          console.log('\n📋 User metadata analysis:');
          console.log('- user_metadata exists:', !!user.user_metadata);
          console.log('- user_metadata keys:', user.user_metadata ? Object.keys(user.user_metadata) : 'None');
          console.log('- user_metadata content:', JSON.stringify(user.user_metadata, null, 2));
          
          console.log('\n🔗 Identities analysis:');
          console.log('- Identities count:', user.identities?.length || 0);
          if (user.identities && user.identities.length > 0) {
            user.identities.forEach((identity, index) => {
              console.log(`- Identity ${index + 1}:`, {
                provider: identity.provider,
                identity_data: identity.identity_data
              });
            });
          }
          
          analysisResults.userDataPresent = true;
          
          // Phase 3: Test User Transformation
          console.log('\n🔄 PHASE 3: User Transformation Testing');
          console.log('=====================================');
          
          console.log('🧪 Testing createAppUserFromSupabase function...');
          
          try {
            // Manually execute the transformation logic
            let username = "Usuario";
            let usernameSource = "fallback";

            if (user.user_metadata?.full_name) {
              username = user.user_metadata.full_name;
              usernameSource = "user_metadata.full_name";
            } else if (user.user_metadata?.name) {
              username = user.user_metadata.name;
              usernameSource = "user_metadata.name";
            } else if (user.identities?.[0]?.identity_data?.full_name) {
              username = user.identities[0].identity_data.full_name;
              usernameSource = "identities[0].identity_data.full_name";
            } else if (user.identities?.[0]?.identity_data?.name) {
              username = user.identities[0].identity_data.name;
              usernameSource = "identities[0].identity_data.name";
            } else if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
              username = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
              usernameSource = "user_metadata.first_name + last_name";
            } else if (user.email) {
              username = user.email.split("@")[0];
              usernameSource = "email prefix";
            }

            const transformedUser = {
              id: user.id,
              username: username,
              email: user.email || "",
              role: "user",
              isActive: true,
              createdAt: new Date(),
            };
            
            console.log('✅ User transformation successful');
            console.log('📊 Transformation results:');
            console.log(`- Username: "${username}" (from ${usernameSource})`);
            console.log('- Transformed user:', transformedUser);
            console.log('- Valid user object:', !!(transformedUser.id && transformedUser.username && transformedUser.email));
            
            analysisResults.transformationCalled = true;
            
          } catch (transformError) {
            console.error('❌ User transformation failed:', transformError);
            analysisResults.transformationCalled = false;
          }
          
        } else {
          console.log('❌ No user data in session');
          analysisResults.userDataPresent = false;
        }
        
      } else {
        console.log('❌ No session found');
        analysisResults.sessionRetrieval = false;
      }
      
    } catch (sessionRetrievalError) {
      console.error('❌ Session retrieval failed:', sessionRetrievalError);
      analysisResults.sessionRetrieval = false;
    }
    
    // Phase 4: Auth State Management Analysis
    console.log('\n🔄 PHASE 4: Auth State Management Analysis');
    console.log('========================================');
    
    console.log('🔍 Checking current auth state in the application...');
    
    // Check if we can access the auth context
    try {
      // Look for auth-related elements in the DOM
      const userDisplayElements = document.querySelectorAll('[data-testid="user-display"]');
      console.log('📊 User display elements found:', userDisplayElements.length);
      
      if (userDisplayElements.length > 0) {
        userDisplayElements.forEach((el, index) => {
          const displayText = el.textContent?.trim();
          console.log(`- Element ${index + 1}: "${displayText}"`);
          
          if (displayText === 'Usuario Demo' || displayText === 'Usuario') {
            console.log('  ❌ Showing fallback text - indicates auth state not set');
          } else if (displayText && displayText !== '') {
            console.log('  ✅ Showing actual user data');
          } else {
            console.log('  ⚠️ Empty content');
          }
        });
      } else {
        console.log('ℹ️ No user display elements found (may not be on dashboard)');
      }
      
    } catch (domError) {
      console.log('⚠️ DOM analysis failed:', domError.message);
    }
    
    // Phase 5: Timing and Race Condition Analysis
    console.log('\n⏱️ PHASE 5: Timing and Race Condition Analysis');
    console.log('==============================================');
    
    console.log('🔍 Analyzing potential timing issues...');
    
    // Test multiple rapid session calls to check for race conditions
    const timingTests = [];
    for (let i = 0; i < 3; i++) {
      const start = performance.now();
      try {
        await supabase.auth.getSession();
        const end = performance.now();
        timingTests.push(end - start);
      } catch (error) {
        timingTests.push(-1); // Error indicator
      }
    }
    
    console.log('📊 Session retrieval timing tests:');
    timingTests.forEach((time, index) => {
      if (time >= 0) {
        console.log(`- Test ${index + 1}: ${time.toFixed(2)}ms`);
      } else {
        console.log(`- Test ${index + 1}: Failed`);
      }
    });
    
    const avgTime = timingTests.filter(t => t >= 0).reduce((a, b) => a + b, 0) / timingTests.filter(t => t >= 0).length;
    console.log(`- Average time: ${avgTime.toFixed(2)}ms`);
    
    if (avgTime > 100) {
      console.log('⚠️ Session retrieval is slow - potential performance issue');
      analysisResults.timingIssues = true;
    } else {
      console.log('✅ Session retrieval timing is acceptable');
      analysisResults.timingIssues = false;
    }
    
    // Final Analysis and Recommendations
    console.log('\n🎯 FINAL ANALYSIS AND DIAGNOSIS');
    console.log('==============================');
    
    console.log('📊 Analysis Results Summary:');
    console.log('- Supabase Connection:', analysisResults.supabaseConnection ? '✅ OK' : '❌ FAIL');
    console.log('- Session Retrieval:', analysisResults.sessionRetrieval ? '✅ OK' : '❌ FAIL');
    console.log('- User Data Present:', analysisResults.userDataPresent ? '✅ OK' : '❌ FAIL');
    console.log('- Transformation Called:', analysisResults.transformationCalled ? '✅ OK' : '❌ FAIL');
    console.log('- Timing Issues:', analysisResults.timingIssues ? '❌ DETECTED' : '✅ NONE');
    
    // Determine the root cause
    console.log('\n🎯 ROOT CAUSE DIAGNOSIS:');
    
    if (!analysisResults.sessionRetrieval) {
      console.log('❌ PRIMARY ISSUE: Session Retrieval Failure');
      console.log('🔧 SOLUTION: Check Supabase connection and authentication configuration');
      console.log('📋 ACTIONS:');
      console.log('1. Verify Supabase project is active and accessible');
      console.log('2. Check API keys and project URL');
      console.log('3. Verify network connectivity');
      
    } else if (!analysisResults.userDataPresent) {
      console.log('❌ PRIMARY ISSUE: No User Data in Session');
      console.log('🔧 SOLUTION: User needs to log in or session has expired');
      console.log('📋 ACTIONS:');
      console.log('1. User should log in through the login page');
      console.log('2. Check if session has expired');
      console.log('3. Verify authentication flow is working');
      
    } else if (!analysisResults.transformationCalled) {
      console.log('❌ PRIMARY ISSUE: User Transformation Failure');
      console.log('🔧 SOLUTION: Fix the createAppUserFromSupabase function');
      console.log('📋 ACTIONS:');
      console.log('1. Check function implementation');
      console.log('2. Verify function is being called in auth flow');
      console.log('3. Add error handling and logging');
      
    } else {
      console.log('⚠️ COMPLEX ISSUE: Auth State Management Problem');
      console.log('🔧 SOLUTION: The transformation works but auth state is not being set');
      console.log('📋 ACTIONS:');
      console.log('1. Check if useAuth hook is properly setting state');
      console.log('2. Verify AuthProvider is passing data correctly');
      console.log('3. Check for timing issues in component mounting');
      console.log('4. Verify auth context is being consumed correctly');
      
      console.log('\n🔍 SPECIFIC DEBUGGING STEPS:');
      console.log('1. Add logging to useAuth hook state changes');
      console.log('2. Check AuthProvider render cycle');
      console.log('3. Verify dashboard component is receiving auth context');
      console.log('4. Test auth state persistence across page refreshes');
    }
    
    console.log('\n✅ DEEP AUTHENTICATION ANALYSIS COMPLETE');
    console.log('========================================');
    
  } catch (error) {
    console.error('❌ Deep authentication analysis failed:', error);
  }
}

// Auto-run the deep analysis
performDeepAuthAnalysis();

// Make it available globally
window.performDeepAuthAnalysis = performDeepAuthAnalysis;

console.log('\n💡 You can run this analysis again with: performDeepAuthAnalysis()');
