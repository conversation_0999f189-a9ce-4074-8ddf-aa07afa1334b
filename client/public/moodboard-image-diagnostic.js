/**
 * MoodBoard Image Diagnostic Tool
 * Run this in the browser console while logged into Emma Studio
 * to diagnose image display issues in MoodBoard
 */

console.log('🎨 MOODBOARD IMAGE DIAGNOSTIC TOOL');
console.log('==================================');
console.log('Run this in browser console while logged into Emma Studio');
console.log('');

async function diagnoseMoodBoardImages() {
  try {
    // Step 1: Check authentication
    console.log('🔐 Step 1: Checking authentication...');
    
    const token = localStorage.getItem('supabase.auth.token') || 
                  localStorage.getItem('auth_token') ||
                  sessionStorage.getItem('auth_token');
    
    if (!token) {
      console.log('❌ No authentication token found. Please log in first.');
      return;
    }
    console.log('✅ Authentication token found');

    // Step 2: Get moodboards
    console.log('\n📋 Step 2: Fetching moodboards...');
    
    const moodboardsResponse = await fetch('/api/moodboard/list?page=1&limit=10', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!moodboardsResponse.ok) {
      console.log('❌ Failed to fetch moodboards:', moodboardsResponse.status);
      return;
    }

    const moodboardsData = await moodboardsResponse.json();
    const moodboards = moodboardsData.data || [];
    
    console.log(`✅ Found ${moodboards.length} moodboards`);

    if (moodboards.length === 0) {
      console.log('ℹ️ No moodboards found. Create a moodboard with images to test.');
      return;
    }

    // Step 3: Analyze moodboard data structure
    console.log('\n🔍 Step 3: Analyzing moodboard data structure...');
    
    for (const moodboard of moodboards.slice(0, 3)) { // Test first 3 moodboards
      console.log(`\n📊 Analyzing moodboard: "${moodboard.title}"`);
      
      if (!moodboard.tldraw_data) {
        console.log('⚠️ No tldraw_data found');
        continue;
      }

      console.log('✅ tldraw_data exists');

      // Analyze the tldraw_data structure
      const tldrawData = moodboard.tldraw_data;
      const dataSize = JSON.stringify(tldrawData).length;
      console.log(`📏 Data size: ${(dataSize / 1024).toFixed(2)} KB`);

      // Look for image shapes
      let imageCount = 0;
      let totalImageSize = 0;
      const imageFormats = new Set();
      const imageIssues = [];

      if (tldrawData.store) {
        for (const [shapeId, shape] of Object.entries(tldrawData.store)) {
          if (shape.type === 'image' && shape.props?.src) {
            imageCount++;
            const src = shape.props.src;
            const imageSize = src.length;
            totalImageSize += imageSize;

            console.log(`🖼️ Image ${imageCount}:`, {
              shapeId: shapeId.substring(0, 8) + '...',
              srcType: src.startsWith('data:') ? 'DATA_URL' : 
                      src.startsWith('http') ? 'HTTP_URL' : 
                      src.startsWith('blob:') ? 'BLOB_URL' : 'UNKNOWN',
              srcLength: src.length,
              srcPreview: src.substring(0, 50) + '...'
            });

            // Detect image format and potential issues
            if (src.startsWith('data:image/')) {
              const format = src.split(';')[0].split('/')[1];
              imageFormats.add(format.toUpperCase());
              
              // Check if data URL is complete
              if (!src.includes('base64,')) {
                imageIssues.push(`Shape ${shapeId}: Invalid data URL format`);
              } else {
                const base64Data = src.split('base64,')[1];
                if (!base64Data || base64Data.length < 100) {
                  imageIssues.push(`Shape ${shapeId}: Data URL appears truncated or empty`);
                }
              }
            } else if (src.startsWith('http')) {
              imageFormats.add('HTTP_URL');
              // Test if HTTP URL is accessible
              try {
                const testResponse = await fetch(src, { method: 'HEAD' });
                if (!testResponse.ok) {
                  imageIssues.push(`Shape ${shapeId}: HTTP URL not accessible (${testResponse.status})`);
                }
              } catch (error) {
                imageIssues.push(`Shape ${shapeId}: HTTP URL failed to load (${error.message})`);
              }
            } else if (src.startsWith('blob:')) {
              imageFormats.add('BLOB_URL');
              imageIssues.push(`Shape ${shapeId}: Blob URL detected - may not persist across sessions`);
            } else {
              imageFormats.add('UNKNOWN');
              imageIssues.push(`Shape ${shapeId}: Unknown image source format: ${src.substring(0, 50)}...`);
            }
          }
        }
      }

      console.log(`📊 Summary:`);
      console.log(`  - Total images: ${imageCount}`);
      console.log(`  - Total image data: ${(totalImageSize / 1024).toFixed(2)} KB`);
      console.log(`  - Image formats: ${Array.from(imageFormats).join(', ') || 'None'}`);

      if (imageIssues.length > 0) {
        console.log(`⚠️ Potential issues found:`);
        imageIssues.forEach(issue => console.log(`    - ${issue}`));
      } else if (imageCount > 0) {
        console.log('✅ No obvious image issues detected');
      }

      if (imageCount === 0) {
        console.log('ℹ️ No images found in this moodboard');
      }
    }

    // Step 4: Test image loading in current page
    console.log('\n🧪 Step 4: Testing image loading in current page...');
    
    // Check if we're on a moodboard page
    const currentUrl = window.location.href;
    if (currentUrl.includes('/mood-board/')) {
      console.log('✅ Currently on a moodboard page');
      
      // Look for tldraw canvas
      const tldrawCanvas = document.querySelector('canvas');
      if (tldrawCanvas) {
        console.log('✅ Tldraw canvas found');
        
        // Check for any error messages in console
        const originalConsoleError = console.error;
        const errors = [];
        console.error = function(...args) {
          errors.push(args.join(' '));
          originalConsoleError.apply(console, args);
        };
        
        setTimeout(() => {
          console.error = originalConsoleError;
          if (errors.length > 0) {
            console.log('⚠️ Console errors detected:');
            errors.forEach(error => console.log(`    - ${error}`));
          } else {
            console.log('✅ No console errors detected');
          }
        }, 2000);
        
      } else {
        console.log('⚠️ Tldraw canvas not found');
      }
    } else {
      console.log('ℹ️ Not currently on a moodboard page');
      console.log('💡 Navigate to a moodboard to test image loading');
    }

    // Step 5: Recommendations
    console.log('\n🔧 Step 5: Recommendations...');
    
    console.log('✅ Common solutions for yellow squares in tldraw:');
    console.log('  1. Ensure images are properly converted to data URLs when added');
    console.log('  2. Check browser console for CORS or loading errors');
    console.log('  3. Verify that blob URLs haven\'t been revoked');
    console.log('  4. Consider implementing custom asset resolver');
    console.log('  5. Test with different image formats (PNG, JPEG, WebP)');

    console.log('\n🏁 DIAGNOSIS COMPLETE');
    console.log('====================');
    console.log('💡 To run this diagnostic again, call: diagnoseMoodBoardImages()');

  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

// Auto-run the diagnostic
diagnoseMoodBoardImages();

// Make function available globally for re-running
window.diagnoseMoodBoardImages = diagnoseMoodBoardImages;
