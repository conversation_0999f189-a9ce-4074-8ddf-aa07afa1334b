/**
 * Verify Authentication Execution
 * 
 * This script verifies if the authentication system is actually executing
 * and identifies exactly where the user data flow breaks.
 */

console.log('🔄 Starting Authentication Execution Verification...');

async function verifyAuthExecution() {
  console.log('\n🔄 AUTHENTICATION EXECUTION VERIFICATION');
  console.log('======================================');
  console.log('Verifying if auth system is executing and where user data flow breaks.\n');

  try {
    // Step 1: Check if user is actually logged in
    console.log('📊 Step 1: User Login Status Check');
    console.log('=================================');
    
    const { supabase } = await import('/src/lib/supabase.ts');
    
    console.log('🔄 Checking current user session...');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Error getting user:', error.message);
      console.log('💡 This indicates a Supabase connection or configuration issue');
      return;
    }
    
    if (!user) {
      console.log('❌ CRITICAL FINDING: No user is logged in!');
      console.log('==========================================');
      console.log('🎯 ROOT CAUSE IDENTIFIED: User is not authenticated');
      console.log('');
      console.log('💡 This explains why:');
      console.log('- Dashboard shows: user: null, username: undefined');
      console.log('- UI displays: "Usuario Demo" (fallback text)');
      console.log('- Auth system logs: "App user cleared"');
      console.log('');
      console.log('🔧 SOLUTION: User needs to log in');
      console.log('📋 ACTIONS:');
      console.log('1. Navigate to /login');
      console.log('2. Enter valid credentials');
      console.log('3. Complete authentication process');
      console.log('4. Verify user data appears in dashboard');
      
      // Check if there's any session data at all
      console.log('\n🔍 Checking session data...');
      const { data: sessionData } = await supabase.auth.getSession();
      
      if (sessionData?.session) {
        console.log('⚠️ Session exists but no user - unusual situation');
        console.log('📋 Session details:');
        console.log('- Access token exists:', !!sessionData.session.access_token);
        console.log('- Expires at:', sessionData.session.expires_at);
      } else {
        console.log('✅ No session data - confirms user is not logged in');
      }
      
      return;
    }
    
    console.log('✅ USER IS LOGGED IN!');
    console.log('====================');
    console.log('📊 User details:');
    console.log('- ID:', user.id);
    console.log('- Email:', user.email);
    console.log('- Created:', user.created_at);
    console.log('- Last sign in:', user.last_sign_in_at);
    
    // Step 2: Test user transformation execution
    console.log('\n🔄 Step 2: User Transformation Execution Test');
    console.log('============================================');
    
    console.log('🧪 Testing createAppUserFromSupabase function execution...');
    
    try {
      // Execute the exact transformation logic from the auth hook
      let username = "Usuario";
      let usernameSource = "fallback";

      if (user.user_metadata?.full_name) {
        username = user.user_metadata.full_name;
        usernameSource = "user_metadata.full_name";
      } else if (user.user_metadata?.name) {
        username = user.user_metadata.name;
        usernameSource = "user_metadata.name";
      } else if (user.identities?.[0]?.identity_data?.full_name) {
        username = user.identities[0].identity_data.full_name;
        usernameSource = "identities[0].identity_data.full_name";
      } else if (user.identities?.[0]?.identity_data?.name) {
        username = user.identities[0].identity_data.name;
        usernameSource = "identities[0].identity_data.name";
      } else if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
        username = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
        usernameSource = "user_metadata.first_name + last_name";
      } else if (user.email) {
        username = user.email.split("@")[0];
        usernameSource = "email prefix";
      }

      const appUser = {
        id: user.id,
        username: username,
        email: user.email || "",
        role: "user",
        isActive: true,
        createdAt: new Date(),
      };
      
      console.log('✅ User transformation executed successfully!');
      console.log('📊 Transformation results:');
      console.log(`- Username: "${username}" (from ${usernameSource})`);
      console.log('- App user object:', appUser);
      console.log('- Valid app user:', !!(appUser.id && appUser.username && appUser.email));
      
      // Step 3: Check why auth state is not being set
      console.log('\n🔍 Step 3: Auth State Management Investigation');
      console.log('===========================================');
      
      console.log('🎯 CRITICAL ANALYSIS: User is logged in and transformation works');
      console.log('❌ BUT: Dashboard still shows null user data');
      console.log('');
      console.log('💡 This indicates the issue is in the auth hook state management:');
      console.log('1. useAuth hook is not properly setting appUser state');
      console.log('2. Auth initialization might not be running');
      console.log('3. Auth state change listener might not be triggering');
      console.log('4. Component might not be re-rendering with updated auth state');
      
      // Step 4: Check auth hook execution
      console.log('\n🔄 Step 4: Auth Hook Execution Check');
      console.log('==================================');
      
      console.log('🔍 Checking if auth hook initialization is running...');
      
      // Look for auth initialization logs in console
      console.log('💡 Expected auth logs that should appear:');
      console.log('- "🔄 Auth: Setting up Supabase auth state"');
      console.log('- "✅ Auth: Initial session found: [email]"');
      console.log('- "🔄 Auth: Created app user for [email] with username: [name]"');
      console.log('- "🔄 Auth: App user set - [name] ([email])"');
      
      console.log('\n🔍 If these logs are missing, the auth hook is not executing properly');
      
      // Step 5: Provide specific debugging steps
      console.log('\n🔧 Step 5: Specific Debugging Actions');
      console.log('===================================');
      
      console.log('📋 IMMEDIATE DEBUGGING STEPS:');
      console.log('1. Check browser console for auth initialization logs');
      console.log('2. Verify AuthProvider is wrapping the application');
      console.log('3. Check if useAuth hook is being called in dashboard');
      console.log('4. Test auth state change by logging out and back in');
      
      console.log('\n🔧 POTENTIAL FIXES:');
      console.log('1. Force auth state refresh by calling supabase.auth.refreshSession()');
      console.log('2. Check if auth hook useEffect dependencies are correct');
      console.log('3. Verify no race conditions in auth initialization');
      console.log('4. Test if manual state setting works');
      
      // Step 6: Test manual state setting
      console.log('\n🧪 Step 6: Manual State Test');
      console.log('===========================');
      
      console.log('💡 To test if the issue is in state management:');
      console.log('1. The auth hook should automatically detect this logged-in user');
      console.log('2. If it doesn\'t, there\'s an issue with the useEffect in the auth hook');
      console.log('3. Try refreshing the page to trigger auth initialization');
      
    } catch (transformError) {
      console.error('❌ User transformation failed:', transformError);
      console.log('🔧 This indicates an issue in the createAppUserFromSupabase function');
    }
    
    console.log('\n✅ AUTHENTICATION EXECUTION VERIFICATION COMPLETE');
    console.log('================================================');
    
  } catch (error) {
    console.error('❌ Authentication execution verification failed:', error);
  }
}

// Auto-run the verification
verifyAuthExecution();

// Make it available globally
window.verifyAuthExecution = verifyAuthExecution;

console.log('\n💡 You can run this verification again with: verifyAuthExecution()');
