<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Username Display Debug - Complete Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
        .problem-box {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .solution-box {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .button.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Username Display Debug - Complete Tool</h1>
        
        <div class="problem-box">
            <h3>🎯 Problem Being Solved</h3>
            <p><strong>Issue:</strong> After successful login, the dashboard shows "Usuario Demo" instead of the actual user's name.</p>
            <p><strong>Goal:</strong> Display the real username/name from the authenticated user's profile data.</p>
        </div>

        <div class="solution-box">
            <h3>🔧 Debugging Approach</h3>
            <p>This tool provides comprehensive debugging to identify and fix username display issues:</p>
            <ul>
                <li>Analyze actual user data from Supabase authentication</li>
                <li>Test username extraction logic with various scenarios</li>
                <li>Check user metadata and identify missing information</li>
                <li>Provide specific recommendations for fixes</li>
            </ul>
        </div>

        <div class="status info">
            <strong>📋 Prerequisites:</strong> You must be logged in for the user data analysis to work properly. 
            If not logged in, please <a href="/login">log in first</a>, then return to this page.
        </div>

        <div class="tool-grid">
            <button class="button" onclick="runUserDataDebug()">🔍 Analyze User Data</button>
            <button class="button warning" onclick="runUsernameTest()">🧪 Test Extraction Logic</button>
            <button class="button success" onclick="runMetadataCheck()">🔧 Check Metadata</button>
            <button class="button secondary" onclick="clearConsole()">🧹 Clear Console</button>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="button" onclick="runCompleteAnalysis()" style="font-size: 18px; padding: 15px 30px;">
                🚀 Run Complete Analysis
            </button>
        </div>

        <div style="text-align: center;">
            <button class="button secondary" onclick="goToLogin()">🔐 Go to Login</button>
            <button class="button secondary" onclick="goToDashboard()">🏠 Go to Dashboard</button>
        </div>

        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        function goToDashboard() {
            window.location.href = '/dashboard';
        }
        
        function loadScript(src, description) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    console.log(`✅ ${description} loaded successfully`);
                    resolve();
                };
                script.onerror = () => {
                    console.error(`❌ Failed to load ${description}`);
                    reject();
                };
                document.head.appendChild(script);
            });
        }
        
        async function runUserDataDebug() {
            console.log('🔍 Running User Data Analysis...');
            try {
                await loadScript('/debug-user-data.js', 'User Data Debug Script');
            } catch (error) {
                console.error('Failed to run user data debug');
            }
        }
        
        async function runUsernameTest() {
            console.log('🧪 Running Username Extraction Test...');
            try {
                await loadScript('/test-username-extraction.js', 'Username Extraction Test');
            } catch (error) {
                console.error('Failed to run username test');
            }
        }
        
        async function runMetadataCheck() {
            console.log('🔧 Running Metadata Check...');
            try {
                await loadScript('/fix-user-metadata.js', 'User Metadata Fix Tool');
            } catch (error) {
                console.error('Failed to run metadata check');
            }
        }
        
        async function runCompleteAnalysis() {
            clearConsole();
            console.log('🚀 Starting Complete Username Display Analysis...');
            console.log('================================================');
            
            try {
                console.log('\n📊 Step 1: Testing Username Extraction Logic...');
                await loadScript('/test-username-extraction.js', 'Username Extraction Test');
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.log('\n🔍 Step 2: Analyzing Current User Data...');
                await loadScript('/debug-user-data.js', 'User Data Debug Script');
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.log('\n🔧 Step 3: Checking User Metadata...');
                await loadScript('/fix-user-metadata.js', 'User Metadata Fix Tool');
                
                console.log('\n✅ Complete Analysis Finished!');
                console.log('============================');
                console.log('Review the results above to identify the username display issue.');
                
            } catch (error) {
                console.error('❌ Complete analysis failed:', error);
            }
        }
        
        // Auto-run message on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔍 Username Display Debug Tool Loaded');
                console.log('=====================================');
                console.log('💡 This tool helps identify why "Usuario Demo" is shown instead of real usernames');
                console.log('🔐 Make sure you are logged in for accurate analysis');
                console.log('🚀 Click "Run Complete Analysis" for comprehensive debugging');
            }, 500);
        });
    </script>
</body>
</html>
