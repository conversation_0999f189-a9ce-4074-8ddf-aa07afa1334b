/**
 * Diagnostic script to investigate session timeout root causes
 * This script will test various aspects of Supabase connectivity and configuration
 */

async function diagnoseSessionTimeout() {
  console.log('🔍 Diagnosing Session Timeout Issues');
  console.log('====================================');
  
  const results = {
    networkConnectivity: null,
    supabaseConfig: null,
    authEndpoint: null,
    sessionCheck: null,
    browserEnvironment: null
  };
  
  try {
    // Step 1: Test basic network connectivity to Supabase
    console.log('\n🌐 Step 1: Testing network connectivity to Supabase...');
    
    const supabaseUrl = 'https://pthewpjbegkgomvyhkin.supabase.co';
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      
      const endTime = Date.now();
      const latency = endTime - startTime;
      
      results.networkConnectivity = {
        success: true,
        latency: latency,
        status: response.status,
        statusText: response.statusText
      };
      
      console.log(`✅ Network connectivity: ${latency}ms latency, status: ${response.status}`);
      
      if (latency > 3000) {
        console.warn(`⚠️ High latency detected (${latency}ms) - this may cause session timeouts`);
      }
      
    } catch (error) {
      results.networkConnectivity = {
        success: false,
        error: error.message
      };
      console.error('❌ Network connectivity failed:', error.message);
    }
    
    // Step 2: Test Supabase configuration
    console.log('\n⚙️ Step 2: Testing Supabase configuration...');
    
    try {
      const { supabase } = await import('/src/lib/supabase.ts');
      
      // Check if client is properly configured
      const clientConfig = {
        hasUrl: !!supabase.supabaseUrl,
        hasKey: !!supabase.supabaseKey,
        authSettings: supabase.auth?.settings || 'not accessible'
      };
      
      results.supabaseConfig = {
        success: true,
        config: clientConfig
      };
      
      console.log('✅ Supabase client configuration:', clientConfig);
      
    } catch (error) {
      results.supabaseConfig = {
        success: false,
        error: error.message
      };
      console.error('❌ Supabase configuration error:', error.message);
    }
    
    // Step 3: Test auth endpoint specifically
    console.log('\n🔐 Step 3: Testing auth endpoint...');
    
    try {
      const authUrl = `${supabaseUrl}/auth/v1/settings`;
      const authStartTime = Date.now();
      
      const authResponse = await fetch(authUrl, {
        signal: AbortSignal.timeout(8000)
      });
      
      const authEndTime = Date.now();
      const authLatency = authEndTime - authStartTime;
      
      results.authEndpoint = {
        success: authResponse.ok,
        latency: authLatency,
        status: authResponse.status,
        statusText: authResponse.statusText
      };
      
      console.log(`✅ Auth endpoint: ${authLatency}ms latency, status: ${authResponse.status}`);
      
      if (authLatency > 5000) {
        console.warn(`⚠️ Auth endpoint slow (${authLatency}ms) - this may cause session timeouts`);
      }
      
    } catch (error) {
      results.authEndpoint = {
        success: false,
        error: error.message
      };
      console.error('❌ Auth endpoint test failed:', error.message);
    }
    
    // Step 4: Test actual session check
    console.log('\n📋 Step 4: Testing session check performance...');
    
    try {
      const { supabase } = await import('/src/lib/supabase.ts');
      
      const sessionStartTime = Date.now();
      const { data, error } = await supabase.auth.getSession();
      const sessionEndTime = Date.now();
      const sessionLatency = sessionEndTime - sessionStartTime;
      
      results.sessionCheck = {
        success: !error,
        latency: sessionLatency,
        hasSession: !!data?.session,
        hasUser: !!data?.session?.user,
        error: error?.message
      };
      
      console.log(`✅ Session check: ${sessionLatency}ms latency`);
      console.log('Session data:', {
        hasSession: !!data?.session,
        hasUser: !!data?.session?.user,
        userEmail: data?.session?.user?.email
      });
      
      if (error) {
        console.error('❌ Session check error:', error.message);
      }
      
      if (sessionLatency > 8000) {
        console.warn(`⚠️ Session check very slow (${sessionLatency}ms) - exceeds timeout threshold`);
      }
      
    } catch (error) {
      results.sessionCheck = {
        success: false,
        error: error.message
      };
      console.error('❌ Session check failed:', error.message);
    }
    
    // Step 5: Check browser environment
    console.log('\n🌍 Step 5: Checking browser environment...');
    
    const browserInfo = {
      userAgent: navigator.userAgent,
      cookiesEnabled: navigator.cookieEnabled,
      localStorage: typeof localStorage !== 'undefined',
      sessionStorage: typeof sessionStorage !== 'undefined',
      indexedDB: typeof indexedDB !== 'undefined',
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : 'not available'
    };
    
    results.browserEnvironment = {
      success: true,
      info: browserInfo
    };
    
    console.log('✅ Browser environment:', browserInfo);
    
    // Check for potential issues
    if (!browserInfo.cookiesEnabled) {
      console.warn('⚠️ Cookies are disabled - this may affect authentication');
    }
    
    if (browserInfo.connection && browserInfo.connection.effectiveType === 'slow-2g') {
      console.warn('⚠️ Slow network connection detected - this may cause timeouts');
    }
    
    // Step 6: Generate recommendations
    console.log('\n💡 Recommendations:');
    console.log('===================');
    
    if (results.networkConnectivity?.latency > 3000) {
      console.log('🔧 Consider increasing session timeout to 10-15 seconds for slow networks');
    }
    
    if (results.authEndpoint?.latency > 5000) {
      console.log('🔧 Auth endpoint is slow - consider implementing retry logic');
    }
    
    if (results.sessionCheck?.latency > 8000) {
      console.log('🔧 Session check exceeds current timeout - increase to at least 12 seconds');
    }
    
    if (!results.networkConnectivity?.success) {
      console.log('🔧 Network connectivity issues detected - check firewall/proxy settings');
    }
    
    console.log('\n📊 Diagnostic Summary:');
    console.log('======================');
    console.table(results);
    
    return results;
    
  } catch (error) {
    console.error('❌ Diagnostic script failed:', error);
    return { error: error.message };
  }
}

// Auto-run the diagnostic
if (typeof window !== 'undefined') {
  console.log('🚀 Session Timeout Diagnostic Script Loaded');
  console.log('Run diagnoseSessionTimeout() to start the diagnostic');
  
  // Make the function globally available
  window.diagnoseSessionTimeout = diagnoseSessionTimeout;
}

export { diagnoseSessionTimeout };
