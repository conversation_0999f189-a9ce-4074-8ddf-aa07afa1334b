<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Configuration Fixes</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2rem;
        }
        .fix-header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
        }
        .fix-header h3 {
            margin: 0 0 15px 0;
            font-size: 1.4rem;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.2s;
            margin: 10px 5px;
        }
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        .button.large {
            font-size: 18px;
            padding: 20px 40px;
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        #console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.6;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 25px;
            border: 1px solid #374151;
        }
        .success-box {
            background: #f0fdf4;
            border: 1px solid #10b981;
            color: #065f46;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .success-box h3 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
        }
        .warning-box {
            background: #fffbeb;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Configuration Fixes</h1>
        
        <div class="fix-header">
            <h3>🎯 Supabase Configuration Fixes Applied</h3>
            <p>The following configuration fixes have been applied to resolve authentication issues:</p>
            <ul>
                <li>✅ Eliminated duplicate Supabase client creation</li>
                <li>✅ Disabled secondary client to prevent Multiple GoTrueClient warning</li>
                <li>✅ Simplified client configuration to use single auth-enabled client</li>
                <li>✅ Restored working authentication initialization pattern</li>
                <li>✅ Enhanced error handling and debugging</li>
            </ul>
        </div>

        <div class="warning-box">
            <h4>⚠️ Testing Instructions</h4>
            <p><strong>Important:</strong> For complete testing, you should be logged in. If you're not logged in, please log in first, then return to test the fixes.</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button large" onclick="testConfigurationFixes()">
                🚀 Test Configuration Fixes
            </button>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button" onclick="runSupabaseConfigDiagnostic()">🔧 Run Config Diagnostic</button>
            <button class="button" onclick="testAuthInitialization()">🔄 Test Auth Initialization</button>
            <button class="button" onclick="testMultipleClientsWarning()">⚠️ Test Multiple Clients</button>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button secondary" onclick="goToLogin()">🔐 Go to Login</button>
            <button class="button secondary" onclick="goToDashboard()">🏠 Go to Dashboard</button>
            <button class="button secondary" onclick="clearConsole()">🧹 Clear Console</button>
        </div>

        <div id="console"></div>
    </div>

    <script>
        let consoleOutput = '';
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('console').textContent = consoleOutput;
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };
        
        function clearConsole() {
            consoleOutput = '';
            document.getElementById('console').textContent = '';
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        function goToDashboard() {
            window.location.href = '/dashboard';
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function runSupabaseConfigDiagnostic() {
            console.log('🔧 Running Supabase Configuration Diagnostic...');
            try {
                await loadScript('/supabase-config-diagnostic.js');
            } catch (error) {
                console.error('❌ Failed to run config diagnostic:', error);
            }
        }
        
        async function testAuthInitialization() {
            console.log('🔄 Testing Auth Initialization...');
            
            try {
                const { supabase } = await import('/src/lib/supabase.ts');
                
                console.log('✅ Supabase client imported successfully');
                
                // Test session retrieval
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    console.log('❌ Auth initialization error:', error.message);
                } else if (session?.user) {
                    console.log('✅ Auth initialization successful - user found:', session.user.email);
                } else {
                    console.log('ℹ️ Auth initialization successful - no active session');
                }
                
            } catch (error) {
                console.error('❌ Auth initialization test failed:', error);
            }
        }
        
        async function testMultipleClientsWarning() {
            console.log('⚠️ Testing for Multiple GoTrueClient Warning...');
            
            let warningDetected = false;
            const originalWarn = console.warn;
            
            console.warn = function(...args) {
                const message = args.join(' ');
                if (message.includes('Multiple GoTrueClient instances')) {
                    warningDetected = true;
                    console.error('❌ Multiple GoTrueClient warning detected!');
                }
                originalWarn.apply(console, args);
            };
            
            try {
                const { supabase, supabaseApi } = await import('/src/lib/supabase.ts');
                
                // Access both clients to trigger potential warning
                await supabase.auth.getSession();
                
                // Wait for potential warnings
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.warn = originalWarn;
                
                if (!warningDetected) {
                    console.log('✅ No Multiple GoTrueClient warning detected - fix successful!');
                } else {
                    console.log('❌ Multiple GoTrueClient warning still present');
                }
                
            } catch (error) {
                console.warn = originalWarn;
                console.error('❌ Multiple clients test failed:', error);
            }
        }
        
        async function testConfigurationFixes() {
            clearConsole();
            console.log('🚀 Testing Configuration Fixes...');
            console.log('=================================');
            
            try {
                console.log('\n📊 Phase 1: Configuration Diagnostic');
                await runSupabaseConfigDiagnostic();
                
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                console.log('\n🔄 Phase 2: Auth Initialization Test');
                await testAuthInitialization();
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.log('\n⚠️ Phase 3: Multiple Clients Warning Test');
                await testMultipleClientsWarning();
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.log('\n🎯 Phase 4: Complete Authentication Test');
                await testCompleteAuth();
                
                console.log('\n✅ CONFIGURATION FIXES TEST COMPLETE');
                console.log('===================================');
                console.log('Review the detailed results above to verify all fixes are working.');
                
            } catch (error) {
                console.error('❌ Configuration fixes test failed:', error);
            }
        }
        
        async function testCompleteAuth() {
            try {
                const { supabase } = await import('/src/lib/supabase.ts');
                
                // Test user retrieval
                const { data: { user }, error } = await supabase.auth.getUser();
                
                if (error) {
                    console.log('❌ User retrieval error:', error.message);
                    return;
                }
                
                if (!user) {
                    console.log('ℹ️ No user logged in - cannot test complete auth flow');
                    console.log('💡 Please log in to test the complete authentication system');
                    return;
                }
                
                console.log('✅ User found:', user.email);
                
                // Test user transformation
                let username = "Usuario";
                if (user.user_metadata?.full_name) {
                    username = user.user_metadata.full_name;
                } else if (user.user_metadata?.name) {
                    username = user.user_metadata.name;
                } else if (user.email) {
                    username = user.email.split("@")[0];
                }
                
                console.log('🔄 Username extracted:', username);
                
                if (username !== 'Usuario') {
                    console.log('✅ Username extraction working correctly');
                    
                    // Show success indicator
                    const successDiv = document.createElement('div');
                    successDiv.className = 'success-box';
                    successDiv.innerHTML = `
                        <h3>🎉 Configuration Fixes Successful!</h3>
                        <p>All configuration issues have been resolved. The authentication system is now working correctly.</p>
                    `;
                    document.querySelector('.container').insertBefore(successDiv, document.getElementById('console'));
                    
                } else {
                    console.log('⚠️ Username still using fallback - metadata may need updating');
                }
                
            } catch (error) {
                console.error('❌ Complete auth test failed:', error);
            }
        }
        
        // Auto-run message on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔧 Configuration Fixes Test Ready');
                console.log('=================================');
                console.log('🎯 This test verifies that Supabase configuration fixes are working');
                console.log('📊 The fixes address Multiple GoTrueClient warnings and auth issues');
                console.log('🚀 Click "Test Configuration Fixes" for comprehensive verification');
                console.log('');
                console.log('💡 Expected results after successful fixes:');
                console.log('- No Multiple GoTrueClient warnings');
                console.log('- Successful auth initialization');
                console.log('- Proper user data retrieval');
                console.log('- Working authentication flow');
            }, 500);
        });
    </script>
</body>
</html>
