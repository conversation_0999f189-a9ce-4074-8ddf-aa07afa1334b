<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Timeout Fix Verification</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #console { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            max-height: 400px; 
            overflow-y: auto; 
        }
    </style>
</head>
<body>
    <h1>🔐 Authentication Timeout Fix Verification</h1>
    
    <div class="info">
        <strong>Purpose:</strong> This page verifies that the authentication timeout fix is working correctly.
    </div>
    
    <h2>Test Results</h2>
    <div id="results"></div>
    
    <h2>Actions</h2>
    <button onclick="runAuthTest()">🧪 Run Auth Test</button>
    <button onclick="checkCurrentAuth()">👤 Check Current Auth</button>
    <button onclick="clearResults()">🗑️ Clear Results</button>
    <button onclick="goToLogin()">🔑 Go to Login</button>
    
    <h2>Console Output</h2>
    <div id="console"></div>
    
    <script type="module">
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            
            console.log(logMessage);
            
            const consoleDiv = document.getElementById('console');
            consoleDiv.textContent += logMessage + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            log(message, type);
        }
        
        async function runAuthTest() {
            addResult('🧪 Starting authentication timeout fix test...', 'info');
            
            try {
                // Import Supabase client
                const { supabase } = await import('/src/lib/supabase.ts');
                addResult('✅ Supabase client imported successfully', 'success');
                
                // Test session check with timeout
                const startTime = Date.now();
                
                const sessionPromise = supabase.auth.getSession();
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Session check timeout')), 8000);
                });
                
                try {
                    const result = await Promise.race([sessionPromise, timeoutPromise]);
                    const duration = Date.now() - startTime;
                    
                    addResult(`✅ Session check completed in ${duration}ms`, 'success');
                    
                    if (result.data?.session?.user) {
                        addResult(`✅ User authenticated: ${result.data.session.user.email}`, 'success');
                    } else {
                        addResult('ℹ️ No user session found', 'info');
                    }
                    
                    if (result.error) {
                        addResult(`⚠️ Session error: ${result.error.message}`, 'warning');
                    }
                    
                } catch (error) {
                    const duration = Date.now() - startTime;
                    
                    if (error.message === 'Session check timeout') {
                        addResult(`⚠️ Session check timed out after ${duration}ms (this is expected for slow connections)`, 'warning');
                        addResult('✅ Timeout handling is working correctly', 'success');
                    } else {
                        addResult(`❌ Unexpected error: ${error.message}`, 'error');
                    }
                }
                
                // Test auth state listener
                addResult('👂 Testing auth state listener...', 'info');
                
                let authStateReceived = false;
                const authStatePromise = new Promise((resolve) => {
                    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
                        if (!authStateReceived) {
                            authStateReceived = true;
                            addResult(`✅ Auth state change: ${event}`, 'success');
                            subscription.unsubscribe();
                            resolve({ event, session });
                        }
                    });
                    
                    setTimeout(() => {
                        if (!authStateReceived) {
                            subscription.unsubscribe();
                            resolve({ event: 'TIMEOUT', session: null });
                        }
                    }, 3000);
                });
                
                const authState = await authStatePromise;
                
                if (authState.event === 'TIMEOUT') {
                    addResult('⚠️ No auth state change received within 3 seconds', 'warning');
                } else {
                    addResult('✅ Auth state listener is working correctly', 'success');
                }
                
                addResult('🎉 Authentication timeout fix test completed!', 'success');
                
            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, 'error');
            }
        }
        
        async function checkCurrentAuth() {
            addResult('👤 Checking current authentication status...', 'info');
            
            try {
                const { supabase } = await import('/src/lib/supabase.ts');
                const { data: { user }, error } = await supabase.auth.getUser();
                
                if (error) {
                    addResult(`❌ Auth error: ${error.message}`, 'error');
                } else if (user) {
                    addResult(`✅ User authenticated: ${user.email} (ID: ${user.id})`, 'success');
                } else {
                    addResult('ℹ️ No user currently authenticated', 'info');
                }
            } catch (error) {
                addResult(`❌ Error checking auth: ${error.message}`, 'error');
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('console').textContent = '';
            log('🗑️ Results cleared');
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        // Make functions globally available
        window.runAuthTest = runAuthTest;
        window.checkCurrentAuth = checkCurrentAuth;
        window.clearResults = clearResults;
        window.goToLogin = goToLogin;
        
        // Auto-run initial check
        log('🚀 Auth verification page loaded');
        checkCurrentAuth();
    </script>
</body>
</html>
