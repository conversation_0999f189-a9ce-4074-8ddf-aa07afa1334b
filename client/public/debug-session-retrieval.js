/**
 * Debug Session Retrieval Process
 * 
 * This script specifically investigates the Supabase session retrieval process
 * to identify why user data is not being retrieved correctly.
 */

console.log('🔍 Starting Session Retrieval Debug...');

async function debugSessionRetrieval() {
  console.log('\n🔍 SESSION RETRIEVAL DEBUG');
  console.log('=========================');
  console.log('Investigating Supabase session retrieval and user data flow.\n');

  try {
    // Step 1: Import and verify Supabase client
    console.log('📊 Step 1: Supabase Client Verification');
    console.log('=======================================');
    
    const { supabase } = await import('/src/lib/supabase.ts');
    
    if (!supabase) {
      console.error('❌ CRITICAL: Supabase client not available');
      return;
    }
    
    console.log('✅ Supabase client imported successfully');
    console.log('🔍 Client details:');
    console.log('- Client type:', typeof supabase);
    console.log('- Has auth:', !!supabase.auth);
    console.log('- Auth type:', typeof supabase.auth);
    
    // Step 2: Test session retrieval with detailed logging
    console.log('\n📊 Step 2: Session Retrieval Test');
    console.log('================================');
    
    console.log('🔄 Calling supabase.auth.getSession()...');
    
    const sessionStart = performance.now();
    
    try {
      const sessionResult = await supabase.auth.getSession();
      const sessionEnd = performance.now();
      
      console.log(`⏱️ Session call completed in ${(sessionEnd - sessionStart).toFixed(2)}ms`);
      console.log('📋 Raw session result:', sessionResult);
      
      const { data, error } = sessionResult;
      
      console.log('🔍 Session result analysis:');
      console.log('- Has data:', !!data);
      console.log('- Has error:', !!error);
      
      if (error) {
        console.error('❌ Session retrieval error:', error);
        console.log('🔍 Error details:');
        console.log('- Message:', error.message);
        console.log('- Status:', error.status);
        console.log('- Code:', error.code);
        return;
      }
      
      if (data) {
        console.log('✅ Session data retrieved');
        console.log('🔍 Data structure:');
        console.log('- Has session:', !!data.session);
        console.log('- Session type:', typeof data.session);
        
        if (data.session) {
          const session = data.session;
          console.log('\n📋 Session details:');
          console.log('- Access token exists:', !!session.access_token);
          console.log('- Access token length:', session.access_token?.length || 0);
          console.log('- Refresh token exists:', !!session.refresh_token);
          console.log('- Token type:', session.token_type);
          console.log('- Expires at:', session.expires_at);
          console.log('- Expires in:', session.expires_in);
          
          console.log('\n👤 User in session:');
          console.log('- Has user:', !!session.user);
          console.log('- User type:', typeof session.user);
          
          if (session.user) {
            const user = session.user;
            console.log('\n✅ USER DATA FOUND!');
            console.log('==================');
            console.log('🔍 User object analysis:');
            console.log('- ID:', user.id);
            console.log('- Email:', user.email);
            console.log('- Created at:', user.created_at);
            console.log('- Updated at:', user.updated_at);
            console.log('- Last sign in:', user.last_sign_in_at);
            console.log('- Email confirmed:', user.email_confirmed_at ? 'Yes' : 'No');
            console.log('- Phone confirmed:', user.phone_confirmed_at ? 'Yes' : 'No');
            
            console.log('\n📋 User metadata:');
            console.log('- user_metadata exists:', !!user.user_metadata);
            console.log('- user_metadata type:', typeof user.user_metadata);
            console.log('- user_metadata keys:', user.user_metadata ? Object.keys(user.user_metadata) : 'None');
            console.log('- user_metadata content:', JSON.stringify(user.user_metadata, null, 2));
            
            console.log('\n📋 App metadata:');
            console.log('- app_metadata exists:', !!user.app_metadata);
            console.log('- app_metadata content:', JSON.stringify(user.app_metadata, null, 2));
            
            console.log('\n🔗 User identities:');
            console.log('- Identities count:', user.identities?.length || 0);
            if (user.identities && user.identities.length > 0) {
              user.identities.forEach((identity, index) => {
                console.log(`\n🔍 Identity ${index + 1}:`);
                console.log('- Provider:', identity.provider);
                console.log('- ID:', identity.id);
                console.log('- User ID:', identity.user_id);
                console.log('- Created at:', identity.created_at);
                console.log('- Updated at:', identity.updated_at);
                console.log('- Identity data:', JSON.stringify(identity.identity_data, null, 2));
              });
            }
            
            // Step 3: Test user transformation with this data
            console.log('\n🔄 Step 3: User Transformation Test');
            console.log('==================================');
            
            console.log('🧪 Testing createAppUserFromSupabase with retrieved user...');
            
            try {
              // Execute the exact transformation logic
              let username = "Usuario";
              let usernameSource = "fallback";

              if (user.user_metadata?.full_name) {
                username = user.user_metadata.full_name;
                usernameSource = "user_metadata.full_name";
              } else if (user.user_metadata?.name) {
                username = user.user_metadata.name;
                usernameSource = "user_metadata.name";
              } else if (user.identities?.[0]?.identity_data?.full_name) {
                username = user.identities[0].identity_data.full_name;
                usernameSource = "identities[0].identity_data.full_name";
              } else if (user.identities?.[0]?.identity_data?.name) {
                username = user.identities[0].identity_data.name;
                usernameSource = "identities[0].identity_data.name";
              } else if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
                username = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
                usernameSource = "user_metadata.first_name + last_name";
              } else if (user.email) {
                username = user.email.split("@")[0];
                usernameSource = "email prefix";
              }

              const appUser = {
                id: user.id,
                username: username,
                email: user.email || "",
                role: "user",
                isActive: true,
                createdAt: new Date(),
              };
              
              console.log('✅ User transformation successful!');
              console.log('📊 Transformation results:');
              console.log(`- Extracted username: "${username}"`);
              console.log(`- Username source: ${usernameSource}`);
              console.log('- App user object:', appUser);
              console.log('- Valid app user:', !!(appUser.id && appUser.username && appUser.email));
              
              if (username === 'Usuario') {
                console.log('⚠️ Using fallback username - user metadata incomplete');
                console.log('💡 This explains why "Usuario Demo" appears in the UI');
                console.log('🔧 Solution: Update user metadata with proper name');
              } else {
                console.log('✅ Username extraction working correctly');
                console.log('💡 User should see actual name in UI');
              }
              
            } catch (transformError) {
              console.error('❌ User transformation failed:', transformError);
            }
            
          } else {
            console.log('❌ NO USER IN SESSION');
            console.log('=====================');
            console.log('🔍 This means the user is not logged in');
            console.log('💡 User needs to log in to see their data');
            console.log('🔧 Solution: Navigate to /login and authenticate');
          }
          
        } else {
          console.log('❌ NO SESSION DATA');
          console.log('==================');
          console.log('🔍 This means no active session exists');
          console.log('💡 User is not authenticated');
          console.log('🔧 Solution: User needs to log in');
        }
        
      } else {
        console.log('❌ NO DATA IN SESSION RESULT');
        console.log('============================');
        console.log('🔍 This indicates a fundamental issue with session retrieval');
      }
      
    } catch (sessionError) {
      console.error('❌ Session retrieval failed:', sessionError);
      console.log('🔍 Error analysis:');
      console.log('- Error type:', typeof sessionError);
      console.log('- Error message:', sessionError.message);
      console.log('- Error stack:', sessionError.stack);
    }
    
    // Step 4: Test auth state listener
    console.log('\n📊 Step 4: Auth State Listener Test');
    console.log('==================================');
    
    console.log('🔄 Testing auth state change listener...');
    
    let listenerTriggered = false;
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      listenerTriggered = true;
      console.log('🔔 Auth state change detected:');
      console.log('- Event:', event);
      console.log('- Has session:', !!session);
      console.log('- Has user:', !!session?.user);
      
      if (session?.user) {
        console.log('- User email:', session.user.email);
      }
    });
    
    // Wait a moment to see if listener triggers
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (listenerTriggered) {
      console.log('✅ Auth state listener is working');
    } else {
      console.log('⚠️ Auth state listener did not trigger');
      console.log('💡 This might indicate the auth state is stable (no changes)');
    }
    
    // Clean up listener
    subscription.unsubscribe();
    
    // Step 5: Diagnosis and Recommendations
    console.log('\n🎯 DIAGNOSIS AND RECOMMENDATIONS');
    console.log('===============================');
    
    console.log('📊 Based on the session retrieval analysis:');
    
    // Provide specific recommendations based on findings
    console.log('\n💡 NEXT STEPS:');
    console.log('1. If no session/user found: User needs to log in');
    console.log('2. If user found but username is "Usuario": Update user metadata');
    console.log('3. If transformation works: Check auth state management in useAuth hook');
    console.log('4. If auth state not updating: Check AuthProvider and component integration');
    
    console.log('\n✅ SESSION RETRIEVAL DEBUG COMPLETE');
    console.log('==================================');
    
  } catch (error) {
    console.error('❌ Session retrieval debug failed:', error);
  }
}

// Auto-run the debug
debugSessionRetrieval();

// Make it available globally
window.debugSessionRetrieval = debugSessionRetrieval;

console.log('\n💡 You can run this debug again with: debugSessionRetrieval()');
