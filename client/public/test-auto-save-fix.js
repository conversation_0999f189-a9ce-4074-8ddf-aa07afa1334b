/**
 * Quick Auto-Save Fix Test
 * Run this directly in the browser console to test the schema fix
 */

console.log('🧪 QUICK AUTO-SAVE FIX TEST');

async function testAutoSaveFix() {
  try {
    console.log('🔍 Testing auto-save fix...');
    
    // Check authentication
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ Not authenticated');
      return;
    }
    
    console.log('✅ User:', user.email);
    
    // Test 1: getUserAnalyses (this was failing before)
    console.log('\n1️⃣ Testing getUserAnalyses...');
    try {
      const analyses = await window.designAnalysisService.getUserAnalyses(user.id, { limit: 3 });
      console.log('✅ getUserAnalyses SUCCESS!');
      console.log('📊 Found', analyses.length, 'analyses');
      
      if (analyses.length > 0) {
        console.log('📝 Latest:', analyses[0].original_filename, 'Score:', analyses[0].overall_score);
      }
    } catch (err) {
      console.log('❌ getUserAnalyses FAILED:', err.message);
      return;
    }
    
    // Test 2: saveAnalysis
    console.log('\n2️⃣ Testing saveAnalysis...');
    const testData = {
      user_id: user.id,
      original_filename: 'schema-fix-test.png',
      file_size: 1024,
      file_type: 'image/png',
      file_url: null,
      overall_score: 88,
      complexity_scores: { visual: 85, cognitive: 88, structural: 90 },
      analysis_areas: [{
        name: 'Layout',
        score: 88,
        description: 'Schema fix test',
        recommendations: ['Test passed']
      }],
      recommendations: ['Schema fix successful'],
      ai_analysis_summary: 'Auto-save fix working',
      gemini_analysis: 'Database schema corrected',
      agent_message: 'All systems operational',
      visuai_insights: 'Fix verified',
      tags: ['schema-fix-test']
    };
    
    try {
      const saved = await window.designAnalysisService.saveAnalysis(testData, null);
      console.log('✅ saveAnalysis SUCCESS!');
      console.log('📝 Saved ID:', saved.id);
      
      // Test 3: Verify it was saved
      console.log('\n3️⃣ Verifying save...');
      const updated = await window.designAnalysisService.getUserAnalyses(user.id, { limit: 5 });
      const found = updated.find(a => a.id === saved.id);
      
      if (found) {
        console.log('✅ Verification SUCCESS!');
        console.log('📝 Found in database:', found.original_filename);
      } else {
        console.log('❌ Verification FAILED - not found in database');
      }
      
      // Clean up
      console.log('\n4️⃣ Cleaning up...');
      await window.designAnalysisService.deleteAnalysis(saved.id);
      console.log('✅ Test data cleaned up');
      
      // Test 4: Query invalidation
      console.log('\n5️⃣ Testing query invalidation...');
      if (window.queryClient) {
        await window.queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
        console.log('✅ Query invalidation SUCCESS!');
      } else {
        console.log('⚠️ QueryClient not available');
      }
      
      console.log('\n🎉 AUTO-SAVE FIX TEST: COMPLETE SUCCESS!');
      console.log('✅ Database schema issue resolved');
      console.log('✅ getUserAnalyses working');
      console.log('✅ saveAnalysis working');
      console.log('✅ Data persistence verified');
      console.log('✅ Query invalidation working');
      
      return true;
      
    } catch (err) {
      console.log('❌ saveAnalysis FAILED:', err.message);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
testAutoSaveFix();

// Make available globally
window.testAutoSaveFix = testAutoSaveFix;
