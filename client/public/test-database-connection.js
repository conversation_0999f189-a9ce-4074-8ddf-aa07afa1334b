// Database Connection Test Script
// Run this in the browser console to test the buyer persona database connection

window.buyerPersonaValidation = {
  async testDatabaseConnection() {
    console.log('🧪 Testing Buyer Persona Database Connection...');
    
    try {
      // Import the Supabase client
      const { supabase } = await import('/src/lib/supabase.ts');
      console.log('✅ Supabase client imported successfully');
      
      // Test basic connection
      const { data: authData, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.error('❌ Authentication error:', authError);
        return false;
      }
      
      console.log('👤 Current user:', authData.user?.id);
      
      // Test projects table access
      const { data: projects, error: projectsError } = await supabase
        .from('buyer_persona_projects')
        .select('*')
        .limit(5);
        
      if (projectsError) {
        console.error('❌ Projects query error:', projectsError);
        return false;
      }
      
      console.log('📊 Projects found:', projects?.length || 0);
      console.log('📋 Projects data:', projects);
      
      return true;
    } catch (error) {
      console.error('❌ Test failed:', error);
      return false;
    }
  },
  
  async testProjectCreation() {
    console.log('🧪 Testing Project Creation...');
    
    try {
      const { buyerPersonaService } = await import('/src/services/buyerPersonaService.ts');
      
      const testProject = {
        project_name: 'Test Project ' + Date.now(),
        product_description: 'Test product for database validation - created at ' + new Date().toISOString(),
        industry: 'Tecnología',
        target_market: 'Developers',
        business_goals: 'Test database integration',
        num_personas: 2
      };
      
      console.log('📝 Creating test project:', testProject);
      const result = await buyerPersonaService.createProject(testProject);
      console.log('✅ Project creation successful!', result);
      
      // Test loading projects
      console.log('🔄 Loading projects after creation...');
      const projects = await buyerPersonaService.getProjects();
      console.log('📊 Projects loaded:', projects.length);
      
      // Clean up test project
      console.log('🧹 Cleaning up test project...');
      await buyerPersonaService.deleteProject(result.id);
      console.log('✅ Test project cleaned up');
      
      return true;
    } catch (error) {
      console.error('❌ Project creation test failed:', error);
      return false;
    }
  },
  
  async runCompleteValidation() {
    console.log('🚀 Running Complete Buyer Persona Database Validation...');
    console.log('================================================');
    
    const connectionTest = await this.testDatabaseConnection();
    const creationTest = await this.testProjectCreation();
    
    console.log('================================================');
    console.log('📊 VALIDATION RESULTS:');
    console.log('Database Connection:', connectionTest ? '✅ PASS' : '❌ FAIL');
    console.log('Project Creation:', creationTest ? '✅ PASS' : '❌ FAIL');
    console.log('Overall Status:', (connectionTest && creationTest) ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
    
    return connectionTest && creationTest;
  }
};

console.log('🧪 Buyer Persona Database Validation loaded!');
console.log('Run: window.buyerPersonaValidation.runCompleteValidation()');
