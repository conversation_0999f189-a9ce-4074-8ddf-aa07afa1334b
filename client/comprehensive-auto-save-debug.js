/**
 * Comprehensive Auto-Save Debug
 * Complete diagnostic to identify the exact issue with auto-save UI updates
 */

console.log('🔍 COMPREHENSIVE AUTO-SAVE DEBUG');
console.log('This will run all diagnostic tests to identify the exact issue');

// Global debug state
window.autoSaveDebug = {
  results: {},
  logs: [],
  issues: []
};

function addIssue(issue) {
  window.autoSaveDebug.issues.push(issue);
  console.log('⚠️ ISSUE IDENTIFIED:', issue);
}

function addResult(test, result, details = null) {
  window.autoSaveDebug.results[test] = { result, details };
  console.log(`${result ? '✅' : '❌'} ${test}:`, result ? 'PASS' : 'FAIL', details || '');
}

async function comprehensiveAutoSaveDebug() {
  console.log('🚀 Starting comprehensive auto-save debug...');
  
  try {
    // Test 1: Authentication
    console.log('\n1️⃣ AUTHENTICATION TEST');
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      addResult('Authentication', false, 'User not authenticated');
      addIssue('User must be logged in for auto-save to work');
      return false;
    }
    addResult('Authentication', true, `User: ${user.email}`);
    
    // Test 2: Service availability
    console.log('\n2️⃣ SERVICE AVAILABILITY TEST');
    const serviceAvailable = !!window.designAnalysisService;
    addResult('Service Available', serviceAvailable);
    if (!serviceAvailable) {
      addIssue('designAnalysisService not available on window object');
      return false;
    }
    
    // Test 3: QueryClient availability
    console.log('\n3️⃣ QUERY CLIENT AVAILABILITY TEST');
    const queryClientAvailable = !!window.queryClient;
    addResult('QueryClient Available', queryClientAvailable);
    if (!queryClientAvailable) {
      addIssue('QueryClient not available on window object - this is a major issue');
      return false;
    }
    
    const queryClient = window.queryClient;
    
    // Test 4: Query cache inspection
    console.log('\n4️⃣ QUERY CACHE INSPECTION');
    const cache = queryClient.getQueryCache();
    const allQueries = cache.getAll();
    const designQueries = allQueries.filter(q => q.queryKey[0] === 'design-analyses');
    const userQuery = cache.find(['design-analyses', user.id]);
    
    addResult('Total Queries in Cache', true, allQueries.length);
    addResult('Design Analysis Queries', true, designQueries.length);
    addResult('User-Specific Query Exists', !!userQuery, userQuery ? 'Found' : 'Not found');
    
    if (!userQuery) {
      addIssue('User-specific design analysis query not found in cache');
    }
    
    // Test 5: Query configuration
    console.log('\n5️⃣ QUERY CONFIGURATION TEST');
    if (userQuery) {
      const config = {
        enabled: userQuery.options?.enabled,
        staleTime: userQuery.options?.staleTime,
        status: userQuery.state.status,
        isFetching: userQuery.state.isFetching,
        isStale: userQuery.state.isStale
      };
      addResult('Query Configuration', true, config);
      
      if (userQuery.options?.staleTime === Infinity) {
        addIssue('Query has infinite staleTime - this prevents refetching');
      }
    }
    
    // Test 6: Service vs Query data comparison
    console.log('\n6️⃣ SERVICE VS QUERY DATA COMPARISON');
    const serviceData = await window.designAnalysisService.getUserAnalyses(user.id, 10);
    const queryData = userQuery?.state.data || [];
    
    addResult('Service Data Count', true, serviceData.length);
    addResult('Query Data Count', true, queryData.length);
    addResult('Data Counts Match', serviceData.length === queryData.length);
    
    if (serviceData.length !== queryData.length) {
      addIssue(`Data mismatch: Service has ${serviceData.length} items, Query has ${queryData.length} items`);
    }
    
    // Test 7: Invalidation test
    console.log('\n7️⃣ INVALIDATION TEST');
    
    let invalidationCalled = false;
    let matchedQueries = 0;
    
    const originalInvalidate = queryClient.invalidateQueries.bind(queryClient);
    queryClient.invalidateQueries = function(filters) {
      invalidationCalled = true;
      matchedQueries = cache.findAll(filters).length;
      console.log('🔄 Invalidation triggered:', { filters, matchedQueries });
      return originalInvalidate(filters);
    };
    
    // Trigger invalidation
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    addResult('Invalidation Called', invalidationCalled);
    addResult('Queries Matched by Invalidation', matchedQueries > 0, matchedQueries);
    
    if (!invalidationCalled) {
      addIssue('Query invalidation was not triggered');
    }
    
    if (matchedQueries === 0) {
      addIssue('Query invalidation matched 0 queries - key mismatch issue');
    }
    
    // Test 8: Save and update cycle
    console.log('\n8️⃣ SAVE AND UPDATE CYCLE TEST');
    
    const testData = {
      user_id: user.id,
      original_filename: 'comprehensive-debug-test.png',
      file_size: 2048,
      file_type: 'image/png',
      file_url: null,
      overall_score: 91,
      complexity_scores: { visual: 89, cognitive: 91, structural: 93 },
      analysis_areas: [{
        name: 'Layout',
        score: 91,
        description: 'Comprehensive debug test',
        recommendations: ['Debug recommendation']
      }],
      recommendations: ['Debug recommendation'],
      ai_analysis_summary: 'Comprehensive debug test',
      gemini_analysis: 'Comprehensive debug test',
      agent_message: 'Comprehensive debug test',
      visuai_insights: 'Comprehensive debug test',
      tags: ['comprehensive-debug-test']
    };
    
    // Get initial state
    const initialCount = userQuery?.state.data?.length || 0;
    
    // Save
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testData, null);
    addResult('Analysis Save', !!savedAnalysis, savedAnalysis?.id);
    
    if (!savedAnalysis) {
      addIssue('Failed to save test analysis');
      return false;
    }
    
    // Invalidate
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    // Wait for refetch
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check results
    const updatedQuery = cache.find(['design-analyses', user.id]);
    const finalCount = updatedQuery?.state.data?.length || 0;
    const foundInQuery = updatedQuery?.state.data?.find(a => a.id === savedAnalysis.id);
    
    addResult('Query Refetched', updatedQuery?.state.status === 'success');
    addResult('Count Increased', finalCount > initialCount, `${initialCount} -> ${finalCount}`);
    addResult('New Analysis in Query', !!foundInQuery);
    
    if (!foundInQuery) {
      addIssue('New analysis not found in query data after save and invalidation');
      
      // Additional debugging
      const freshServiceData = await window.designAnalysisService.getUserAnalyses(user.id, 10);
      const foundInService = freshServiceData.find(a => a.id === savedAnalysis.id);
      
      if (foundInService) {
        addIssue('Analysis exists in service but not in query - cache sync issue');
      } else {
        addIssue('Analysis not found in service either - save may have failed');
      }
    }
    
    // Test 9: UI state check
    console.log('\n9️⃣ UI STATE CHECK');
    
    // Switch to history tab
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Check UI elements
    const analysisCards = document.querySelectorAll('[data-testid*="analysis"], [class*="analysis"]');
    const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"], .animate-spin');
    const noAnalysesMsg = document.body.textContent?.includes('No hay análisis') || 
                         document.body.textContent?.includes('Sin análisis');
    
    addResult('Analysis Cards in UI', analysisCards.length > 0, analysisCards.length);
    addResult('Loading State', loadingElements.length === 0, `${loadingElements.length} loading elements`);
    addResult('No Analyses Message', !noAnalysesMsg, noAnalysesMsg ? 'Showing' : 'Not showing');
    
    // Look for our specific test analysis
    const testAnalysisInUI = Array.from(document.querySelectorAll('*')).find(el => 
      el.textContent && el.textContent.includes('comprehensive-debug-test.png')
    );
    
    addResult('Test Analysis in UI', !!testAnalysisInUI);
    
    if (!testAnalysisInUI && foundInQuery) {
      addIssue('Analysis exists in query but not visible in UI - React rendering issue');
    }
    
    // Clean up
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    queryClient.invalidateQueries = originalInvalidate;
    
    console.log('\n✅ Comprehensive debug completed');
    return true;
    
  } catch (error) {
    console.error('❌ Comprehensive debug failed:', error);
    addIssue(`Debug test crashed: ${error.message}`);
    return false;
  }
}

// Generate summary report
function generateSummaryReport() {
  console.log('\n' + '='.repeat(60));
  console.log('📋 AUTO-SAVE DEBUG SUMMARY REPORT');
  console.log('='.repeat(60));
  
  console.log('\n🧪 TEST RESULTS:');
  Object.entries(window.autoSaveDebug.results).forEach(([test, result]) => {
    const status = result.result ? '✅ PASS' : '❌ FAIL';
    const details = result.details ? ` (${JSON.stringify(result.details)})` : '';
    console.log(`  ${status} ${test}${details}`);
  });
  
  console.log('\n⚠️ ISSUES IDENTIFIED:');
  if (window.autoSaveDebug.issues.length === 0) {
    console.log('  🎉 No issues found!');
  } else {
    window.autoSaveDebug.issues.forEach((issue, i) => {
      console.log(`  ${i + 1}. ${issue}`);
    });
  }
  
  console.log('\n🔧 RECOMMENDED ACTIONS:');
  const issues = window.autoSaveDebug.issues;
  
  if (issues.some(i => i.includes('QueryClient not available'))) {
    console.log('  1. Fix QueryClient availability - check App.tsx setup');
  }
  
  if (issues.some(i => i.includes('infinite staleTime'))) {
    console.log('  2. Fix staleTime configuration in queryClient.ts');
  }
  
  if (issues.some(i => i.includes('key mismatch'))) {
    console.log('  3. Fix query key mismatch in invalidation calls');
  }
  
  if (issues.some(i => i.includes('cache sync issue'))) {
    console.log('  4. Investigate React Query cache synchronization');
  }
  
  if (issues.some(i => i.includes('React rendering issue'))) {
    console.log('  5. Check React component re-rendering and useMemo dependencies');
  }
  
  console.log('\n' + '='.repeat(60));
}

// Run comprehensive debug
comprehensiveAutoSaveDebug().then(success => {
  generateSummaryReport();
  
  if (success) {
    console.log('🎉 COMPREHENSIVE DEBUG: COMPLETED');
  } else {
    console.log('💥 COMPREHENSIVE DEBUG: FAILED');
  }
}).catch(error => {
  console.error('💥 Comprehensive debug crashed:', error);
  generateSummaryReport();
});

// Export functions
window.comprehensiveAutoSaveDebug = comprehensiveAutoSaveDebug;
window.generateSummaryReport = generateSummaryReport;
console.log('🔧 Functions available: window.comprehensiveAutoSaveDebug(), window.generateSummaryReport()');
