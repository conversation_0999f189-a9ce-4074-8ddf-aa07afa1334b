// Test script to verify SEO Analyzer fix
// Run this in the browser console after navigating to the SEO Analyzer

console.log('🧪 Testing SEO Analyzer Fix');
console.log('============================');

async function testSEOAnalyzerFix() {
  console.log('1. Testing component loading...');
  
  // Wait for component to load
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Check if component loaded without errors
  const errorElements = document.querySelectorAll('[data-testid="error-boundary"], .error-boundary');
  if (errorElements.length > 0) {
    console.error('❌ Error boundary detected - component failed to load');
    errorElements.forEach(el => console.error('Error:', el.textContent));
    return false;
  }
  
  console.log('✅ Component loaded without error boundary');
  
  // Check if tabs are present
  const tabs = document.querySelectorAll('[role="tab"]');
  console.log(`✅ Found ${tabs.length} tabs`);
  
  // Check for specific tabs
  const expectedTabs = ['Analizador', 'Historial', 'Favoritos', 'Dashboard'];
  const foundTabs = Array.from(tabs).map(tab => tab.textContent.trim());
  
  expectedTabs.forEach(expectedTab => {
    if (foundTabs.includes(expectedTab)) {
      console.log(`✅ Tab "${expectedTab}" found`);
    } else {
      console.warn(`⚠️ Tab "${expectedTab}" not found`);
    }
  });
  
  // Test database integration
  console.log('\n2. Testing database integration...');
  
  try {
    // Import and test service
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    console.log('✅ Service imported successfully');
    
    // Test authentication
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      console.log('✅ User authenticated:', user.email);
      
      // Test getting recent analyses
      const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
      console.log(`✅ Retrieved ${recentAnalyses.length} recent analyses`);
      
      // Test getting favorites
      const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
      console.log(`✅ Retrieved ${favoriteAnalyses.length} favorite analyses`);
      
    } else {
      console.warn('⚠️ User not authenticated - database tests skipped');
    }
    
  } catch (error) {
    console.error('❌ Database integration test failed:', error);
    return false;
  }
  
  console.log('\n3. Testing React hooks...');
  
  try {
    const { useSEOAnalysisHistory } = await import('/src/components/tools/seo-analyzer/hooks/useSEOAnalysisHistory.ts');
    console.log('✅ React hook imported successfully');
  } catch (error) {
    console.error('❌ React hook test failed:', error);
    return false;
  }
  
  console.log('\n🎉 All tests passed! SEO Analyzer should be working correctly.');
  return true;
}

// Function to test specific functionality
async function testSEOAnalyzerFunctionality() {
  console.log('\n🔧 Testing SEO Analyzer Functionality');
  console.log('=====================================');
  
  // Test form submission
  const urlInput = document.querySelector('input[placeholder*="URL"], input[name="url"]');
  if (urlInput) {
    console.log('✅ URL input found');
    
    // Test with a sample URL
    urlInput.value = 'https://example.com';
    urlInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log('✅ Sample URL entered');
    
    // Look for analyze button
    const analyzeButton = document.querySelector('button:contains("Analizar"), button[type="submit"]');
    if (analyzeButton) {
      console.log('✅ Analyze button found');
      console.log('💡 You can now test the analysis by clicking the analyze button');
    } else {
      console.warn('⚠️ Analyze button not found');
    }
  } else {
    console.warn('⚠️ URL input not found');
  }
  
  // Test tab switching
  const historyTab = Array.from(document.querySelectorAll('[role="tab"]'))
    .find(tab => tab.textContent.includes('Historial'));
  
  if (historyTab) {
    console.log('✅ History tab found');
    historyTab.click();
    
    setTimeout(() => {
      const historyContent = document.querySelector('[role="tabpanel"]');
      if (historyContent && historyContent.style.display !== 'none') {
        console.log('✅ History tab content displayed');
      } else {
        console.warn('⚠️ History tab content not displayed');
      }
    }, 500);
  }
}

// Auto-run tests
testSEOAnalyzerFix().then(success => {
  if (success) {
    testSEOAnalyzerFunctionality();
  }
});

// Export for manual testing
window.testSEOAnalyzer = {
  testFix: testSEOAnalyzerFix,
  testFunctionality: testSEOAnalyzerFunctionality,
  
  // Quick navigation helper
  navigateToSEOAnalyzer() {
    const seoLink = document.querySelector('a[href*="seo"], a:contains("SEO")');
    if (seoLink) {
      seoLink.click();
      console.log('✅ Navigated to SEO Analyzer');
    } else {
      console.warn('⚠️ SEO Analyzer link not found');
      console.log('💡 Please navigate manually to the SEO Analyzer tool');
    }
  }
};

console.log('\n💡 Manual testing available:');
console.log('- window.testSEOAnalyzer.testFix()');
console.log('- window.testSEOAnalyzer.testFunctionality()');
console.log('- window.testSEOAnalyzer.navigateToSEOAnalyzer()');
