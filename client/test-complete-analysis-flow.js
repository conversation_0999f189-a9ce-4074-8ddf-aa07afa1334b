/**
 * Complete Analysis Flow Test
 * Tests the entire analysis flow including auto-save functionality
 * Run this in the browser console on the Design Complexity Analyzer page
 */

console.log('🧪 Testing Complete Analysis Flow with Auto-Save');

// Create a test image file
function createTestImageFile() {
  // Create a simple canvas with test content
  const canvas = document.createElement('canvas');
  canvas.width = 200;
  canvas.height = 200;
  const ctx = canvas.getContext('2d');
  
  // Draw a simple test design
  ctx.fillStyle = '#3498db';
  ctx.fillRect(0, 0, 200, 200);
  ctx.fillStyle = '#ffffff';
  ctx.font = '20px Arial';
  ctx.fillText('Test Design', 50, 100);
  
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      const file = new File([blob], 'test-design.png', { type: 'image/png' });
      resolve(file);
    }, 'image/png');
  });
}

// Test the complete analysis flow
async function testCompleteAnalysisFlow() {
  console.log('🔍 Starting complete analysis flow test...');
  
  try {
    // Check authentication
    const user = window.supabase?.auth?.getUser ? await window.supabase.auth.getUser() : null;
    if (!user?.data?.user) {
      console.log('❌ User not authenticated. Please log in first.');
      return false;
    }
    
    console.log('✅ User authenticated:', user.data.user.email);
    
    // Create test image file
    console.log('🖼️ Creating test image file...');
    const testFile = await createTestImageFile();
    console.log('✅ Test image file created:', testFile.name, testFile.size, 'bytes');
    
    // Check if the component is available
    const analyzeButton = document.querySelector('[data-testid="analyze-button"]') || 
                         document.querySelector('button:contains("Analizar")') ||
                         document.querySelector('button[type="submit"]');
    
    if (!analyzeButton) {
      console.log('⚠️ Analyze button not found, testing service directly...');
      return await testServiceDirectly(testFile, user.data.user);
    }
    
    // Test with actual component
    console.log('🎯 Testing with actual component...');
    
    // Find file input
    const fileInput = document.querySelector('input[type="file"]');
    if (!fileInput) {
      console.log('❌ File input not found');
      return false;
    }
    
    // Simulate file selection
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(testFile);
    fileInput.files = dataTransfer.files;
    
    // Trigger change event
    const changeEvent = new Event('change', { bubbles: true });
    fileInput.dispatchEvent(changeEvent);
    
    console.log('✅ File selected, waiting for analysis to complete...');
    
    // Wait for analysis to complete and auto-save to trigger
    return new Promise((resolve) => {
      let checkCount = 0;
      const maxChecks = 30; // 30 seconds timeout
      
      const checkInterval = setInterval(() => {
        checkCount++;
        
        // Check if analysis is complete
        const resultsSection = document.querySelector('[data-testid="analysis-results"]') ||
                              document.querySelector('.analysis-results') ||
                              document.querySelector('[class*="result"]');
        
        if (resultsSection && resultsSection.style.display !== 'none') {
          console.log('✅ Analysis completed, checking auto-save...');
          clearInterval(checkInterval);
          
          // Wait a bit more for auto-save to complete
          setTimeout(async () => {
            const success = await verifyAutoSave(user.data.user.id);
            resolve(success);
          }, 2000);
          
        } else if (checkCount >= maxChecks) {
          console.log('❌ Analysis timeout');
          clearInterval(checkInterval);
          resolve(false);
        }
      }, 1000);
    });
    
  } catch (error) {
    console.error('❌ Complete analysis flow test failed:', error);
    return false;
  }
}

// Test service directly
async function testServiceDirectly(testFile, user) {
  console.log('🔧 Testing service directly...');
  
  if (!window.designAnalysisService) {
    console.log('❌ designAnalysisService not available');
    return false;
  }
  
  const testAnalysisData = {
    user_id: user.id,
    original_filename: testFile.name,
    file_size: testFile.size,
    file_type: testFile.type,
    file_url: null,
    overall_score: 88,
    complexity_scores: {
      visual: 85,
      cognitive: 90,
      structural: 88
    },
    analysis_areas: [
      {
        name: 'Layout',
        score: 88,
        description: 'Clean and organized layout structure',
        recommendations: ['Consider adding more white space']
      },
      {
        name: 'Typography',
        score: 85,
        description: 'Good font choices and hierarchy',
        recommendations: ['Increase line height for better readability']
      }
    ],
    recommendations: ['Overall good design with room for improvement'],
    ai_analysis_summary: 'This design shows good structure and visual hierarchy',
    gemini_analysis: 'The design demonstrates effective use of color and space',
    agent_message: 'Analysis completed successfully with auto-save',
    visuai_insights: 'Strong visual impact with clear focal points',
    tags: ['test', 'auto-save-verification']
  };
  
  try {
    console.log('💾 Saving analysis...');
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testAnalysisData, testFile);
    
    if (savedAnalysis && savedAnalysis.id) {
      console.log('✅ Analysis saved successfully!');
      console.log('📝 Analysis ID:', savedAnalysis.id);
      console.log('📅 Created at:', savedAnalysis.created_at);
      console.log('🔗 File URL:', savedAnalysis.file_url || 'No file URL');
      
      // Verify it appears in history
      const userAnalyses = await window.designAnalysisService.getUserAnalyses(user.id, 10);
      const foundAnalysis = userAnalyses.find(a => a.id === savedAnalysis.id);
      
      if (foundAnalysis) {
        console.log('✅ Analysis found in user history');
        
        // Clean up
        await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
        console.log('🧹 Test data cleaned up');
        
        return true;
      } else {
        console.log('❌ Analysis not found in user history');
        return false;
      }
      
    } else {
      console.log('❌ Failed to save analysis');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Service test failed:', error);
    return false;
  }
}

// Verify auto-save worked
async function verifyAutoSave(userId) {
  console.log('🔍 Verifying auto-save...');
  
  try {
    const userAnalyses = await window.designAnalysisService.getUserAnalyses(userId, 5);
    const recentAnalysis = userAnalyses[0];
    
    if (recentAnalysis && new Date(recentAnalysis.created_at) > new Date(Date.now() - 60000)) {
      console.log('✅ Recent analysis found - auto-save worked!');
      console.log('📝 Analysis:', recentAnalysis.original_filename, recentAnalysis.overall_score);
      return true;
    } else {
      console.log('❌ No recent analysis found - auto-save may have failed');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Failed to verify auto-save:', error);
    return false;
  }
}

// Run the test
testCompleteAnalysisFlow().then(success => {
  if (success) {
    console.log('🎉 COMPLETE ANALYSIS FLOW TEST: SUCCESS');
    console.log('✅ Auto-save functionality is working correctly');
  } else {
    console.log('💥 COMPLETE ANALYSIS FLOW TEST: FAILED');
    console.log('❌ Auto-save functionality needs further investigation');
  }
}).catch(error => {
  console.error('💥 Test execution failed:', error);
});

// Export for manual testing
window.testCompleteAnalysisFlow = testCompleteAnalysisFlow;
console.log('🔧 Test function available as window.testCompleteAnalysisFlow()');
