// Comprehensive test to debug SEO History tab issues
// This will test every step of the process to identify the exact problem

console.log('🔬 Comprehensive SEO History Debug');
console.log('==================================');

async function step1_CheckAuthentication() {
  console.log('\n🔐 Step 1: Authentication Check');
  console.log('--------------------------------');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Authentication error:', error);
      return { success: false, user: null, error };
    }
    
    if (!user) {
      console.error('❌ No user found - not authenticated');
      console.log('💡 Please sign in and run this test again');
      return { success: false, user: null, error: 'Not authenticated' };
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      created_at: user.created_at
    });
    
    return { success: true, user, error: null };
  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    return { success: false, user: null, error };
  }
}

async function step2_TestDirectDatabaseAccess(userId) {
  console.log('\n💾 Step 2: Direct Database Access Test');
  console.log('--------------------------------------');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test basic connection
    console.log('🔄 Testing basic database connection...');
    const { data: testData, error: testError } = await supabase
      .from('seo_analyses')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('❌ Database connection failed:', testError);
      return { success: false, error: testError };
    }
    
    console.log('✅ Database connection successful');
    
    // Test user-specific query
    console.log('🔄 Testing user-specific query...');
    const { data: userData, error: userError } = await supabase
      .from('seo_analyses')
      .select('id, created_at, url, overall_score')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (userError) {
      console.error('❌ User query failed:', userError);
      return { success: false, error: userError };
    }
    
    console.log('✅ User query successful:', {
      recordCount: userData?.length || 0,
      records: userData || []
    });
    
    return { success: true, data: userData, error: null };
  } catch (error) {
    console.error('❌ Database access test failed:', error);
    return { success: false, error };
  }
}

async function step3_TestServiceLayer(userId) {
  console.log('\n🔧 Step 3: Service Layer Test');
  console.log('------------------------------');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Test getRecentAnalyses
    console.log('🔄 Testing seoAnalysisService.getRecentAnalyses()...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    console.log('✅ getRecentAnalyses result:', {
      count: recentAnalyses.length,
      analyses: recentAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        created_at: a.created_at
      }))
    });
    
    // Test getFavoriteAnalyses
    console.log('🔄 Testing seoAnalysisService.getFavoriteAnalyses()...');
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    console.log('✅ getFavoriteAnalyses result:', {
      count: favoriteAnalyses.length,
      analyses: favoriteAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        is_favorite: a.is_favorite
      }))
    });
    
    // Test getUserAnalyses directly
    console.log('🔄 Testing seoAnalysisService.getUserAnalyses()...');
    const userAnalyses = await seoAnalysisService.getUserAnalyses(userId, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    console.log('✅ getUserAnalyses result:', {
      count: userAnalyses.length,
      analyses: userAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        created_at: a.created_at
      }))
    });
    
    return { 
      success: true, 
      recentAnalyses, 
      favoriteAnalyses, 
      userAnalyses,
      error: null 
    };
  } catch (error) {
    console.error('❌ Service layer test failed:', error);
    return { success: false, error };
  }
}

async function step4_TestReactQueryHook(userId) {
  console.log('\n🪝 Step 4: React Query Hook Test');
  console.log('---------------------------------');
  
  try {
    // We can't actually test the hook outside of React, but we can check if it's importable
    const { useSEOAnalysisHistory } = await import('/src/components/tools/seo-analyzer/hooks/useSEOAnalysisHistory.ts');
    console.log('✅ useSEOAnalysisHistory hook imported successfully');
    
    // Check if useAuth hook is working
    const { useAuth } = await import('/src/hooks/use-auth.ts');
    console.log('✅ useAuth hook imported successfully');
    
    return { success: true, error: null };
  } catch (error) {
    console.error('❌ Hook test failed:', error);
    return { success: false, error };
  }
}

async function step5_CreateTestAnalysis(userId) {
  console.log('\n💾 Step 5: Create Test Analysis');
  console.log('-------------------------------');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    const testAnalysisData = {
      user_id: userId,
      url: `https://example.com/comprehensive-test-${Date.now()}`,
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 78,
      basic_info: { 
        title: 'Comprehensive Test Analysis', 
        title_length: 28,
        meta_description: 'This is a comprehensive test to debug the History tab',
        meta_description_length: 54,
        h1_tags: ['Comprehensive Test'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 600,
        images: { total: 4, without_alt: 1 },
        links: { total: 12, internal: 8, external: 4 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Images',
          issue: 'One image missing alt text',
          importance: 'medium',
          recommendation: 'Add descriptive alt text to improve accessibility'
        }
      ],
      achievements: [
        {
          category: 'Technical',
          achievement: 'HTTPS Enabled',
          description: 'Site uses secure HTTPS protocol',
          icon: '🔒',
          impact: 'positive'
        }
      ],
      status: 'completed'
    };

    console.log('🔄 Saving test analysis...');
    const savedAnalysis = await seoAnalysisService.saveAnalysis(testAnalysisData);
    console.log('✅ Test analysis saved:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      score: savedAnalysis.overall_score,
      created_at: savedAnalysis.created_at
    });
    
    // Wait a moment for any cache updates
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Verify it appears in recent analyses
    console.log('🔄 Verifying analysis appears in recent analyses...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    const foundAnalysis = recentAnalyses.find(a => a.id === savedAnalysis.id);
    
    if (foundAnalysis) {
      console.log('✅ Analysis found in recent analyses');
    } else {
      console.error('❌ Analysis NOT found in recent analyses');
      console.log('🔍 Current recent analyses:', recentAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        created_at: a.created_at
      })));
    }
    
    return { success: true, savedAnalysis, foundInRecent: !!foundAnalysis, error: null };
  } catch (error) {
    console.error('❌ Create test analysis failed:', error);
    return { success: false, error };
  }
}

async function step6_CheckUIComponents() {
  console.log('\n🎨 Step 6: UI Components Check');
  console.log('-------------------------------');
  
  try {
    // Check if we're on the right page
    const currentUrl = window.location.href;
    console.log('📍 Current URL:', currentUrl);
    
    // Look for SEO Analyzer components
    const seoAnalyzer = document.querySelector('[data-testid="seo-analyzer"]') ||
                       document.querySelector('.seo-analyzer') ||
                       document.querySelector('#seo-analyzer');
    
    if (seoAnalyzer) {
      console.log('✅ SEO Analyzer component found');
    } else {
      console.log('⚠️ SEO Analyzer component not found in current page');
    }
    
    // Look for History tab
    const historyTab = Array.from(document.querySelectorAll('button, [role="tab"]'))
      .find(el => el.textContent?.includes('Historial') || el.textContent?.includes('History'));
    
    if (historyTab) {
      console.log('✅ History tab found');
      console.log('🖱️ Tab text:', historyTab.textContent);
      console.log('🔧 Tab disabled:', historyTab.disabled);
    } else {
      console.log('⚠️ History tab not found');
    }
    
    return { success: true, seoAnalyzer: !!seoAnalyzer, historyTab: !!historyTab };
  } catch (error) {
    console.error('❌ UI components check failed:', error);
    return { success: false, error };
  }
}

async function runComprehensiveTest() {
  console.log('🚀 Starting Comprehensive SEO History Debug...\n');
  
  // Step 1: Authentication
  const authResult = await step1_CheckAuthentication();
  if (!authResult.success) {
    console.log('\n❌ STOPPED: Authentication required');
    console.log('🔑 Please sign in and run this test again');
    return;
  }
  
  const userId = authResult.user.id;
  
  // Step 2: Database Access
  const dbResult = await step2_TestDirectDatabaseAccess(userId);
  
  // Step 3: Service Layer
  const serviceResult = await step3_TestServiceLayer(userId);
  
  // Step 4: React Query Hook
  const hookResult = await step4_TestReactQueryHook(userId);
  
  // Step 5: Create Test Analysis
  const testResult = await step5_CreateTestAnalysis(userId);
  
  // Step 6: UI Components
  const uiResult = await step6_CheckUIComponents();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 COMPREHENSIVE TEST RESULTS');
  console.log('='.repeat(60));
  
  console.log('🔐 Authentication:', authResult.success ? '✅ PASS' : '❌ FAIL');
  console.log('💾 Database Access:', dbResult.success ? '✅ PASS' : '❌ FAIL');
  console.log('🔧 Service Layer:', serviceResult.success ? '✅ PASS' : '❌ FAIL');
  console.log('🪝 React Hooks:', hookResult.success ? '✅ PASS' : '❌ FAIL');
  console.log('💾 Test Analysis:', testResult.success ? '✅ PASS' : '❌ FAIL');
  console.log('🎨 UI Components:', uiResult.success ? '✅ PASS' : '❌ FAIL');
  
  if (testResult.success && testResult.foundInRecent) {
    console.log('\n🎉 SUCCESS: All systems working!');
    console.log('✅ Analysis saved and retrieved successfully');
    console.log('💡 The History tab should now show your test analysis');
    console.log('🔗 Navigate to the SEO Analyzer and check the History tab');
  } else if (testResult.success && !testResult.foundInRecent) {
    console.log('\n⚠️ PARTIAL SUCCESS: Analysis saved but not found in recent');
    console.log('🔧 There may be a caching or query issue');
  } else {
    console.log('\n❌ ISSUES DETECTED');
    console.log('🔧 Check the errors above for specific problems');
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Navigate to the SEO Analyzer tool');
  console.log('2. Click on the "Historial" tab');
  console.log('3. Look for your test analysis');
  console.log('4. If still empty, check browser console for React errors');
}

// Auto-run the comprehensive test
runComprehensiveTest();

// Export for manual use
window.comprehensiveSEOTest = {
  runComprehensiveTest,
  step1_CheckAuthentication,
  step2_TestDirectDatabaseAccess,
  step3_TestServiceLayer,
  step4_TestReactQueryHook,
  step5_CreateTestAnalysis,
  step6_CheckUIComponents
};

console.log('\n💡 Manual testing available:');
console.log('- window.comprehensiveSEOTest.runComprehensiveTest()');
console.log('- window.comprehensiveSEOTest.step5_CreateTestAnalysis(userId)');
