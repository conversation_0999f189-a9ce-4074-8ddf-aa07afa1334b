/**
 * Auto-Save Scenarios Test
 * Tests different auto-save scenarios to identify the specific issue
 */

console.log('🧪 Testing Auto-Save Scenarios');

// Test different auto-save scenarios
async function testAutoSaveScenarios() {
  console.log('🔍 Starting auto-save scenario tests...');
  
  try {
    // Check authentication first
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ User not authenticated');
      return;
    }
    
    console.log('✅ User authenticated:', user.email);
    
    // Scenario 1: Backend successful save (should skip frontend save)
    console.log('\n📋 SCENARIO 1: Backend successful save');
    await testBackendSuccessfulSave(user);
    
    // Scenario 2: Backend failed save (should do frontend save)
    console.log('\n📋 SCENARIO 2: Backend failed save');
    await testBackendFailedSave(user);
    
    // Scenario 3: Fallback analysis (should do frontend save)
    console.log('\n📋 SCENARIO 3: Fallback analysis');
    await testFallback<PERSON>nalysis(user);
    
    // Scenario 4: Emergency fallback (should do frontend save)
    console.log('\n📋 SCENARIO 4: Emergency fallback');
    await testEmergencyFallback(user);
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

// Test backend successful save scenario
async function testBackendSuccessfulSave(user) {
  console.log('🔄 Testing backend successful save scenario...');
  
  // Simulate analysis data from successful backend save
  const analysisData = {
    analysis_id: 'test-backend-id-123',
    saved_to_database: true,
    file_url: 'test-file-url.png'
  };
  
  const enhancedResults = {
    score: 85,
    complexity: { visual: 80, cognitive: 85, structural: 90 },
    areas: [{ name: 'Layout', score: 85, description: 'Test', recommendations: [] }],
    recommendations: ['Test recommendation'],
    analysis_summary: 'Test summary',
    gemini_analysis: 'Test analysis',
    agent_message: 'Test message',
    visuai_insights: 'Test insights'
  };
  
  const selectedFile = new File(['test'], 'test.png', { type: 'image/png' });
  
  // This should skip frontend save and just update UI
  console.log('📤 Calling handleAutoSave with backend success data...');
  
  // We can't directly call handleAutoSave since it's not exposed, but we can simulate the logic
  const backendSavedSuccessfully = analysisData.saved_to_database === true ||
                                  (analysisData.analysis_id && !false && !false);
  
  if (backendSavedSuccessfully) {
    console.log('✅ Backend save detected - would skip frontend save');
    console.log('📝 Analysis ID:', analysisData.analysis_id);
  } else {
    console.log('❌ Backend save not detected - would proceed with frontend save');
  }
}

// Test backend failed save scenario
async function testBackendFailedSave(user) {
  console.log('🔄 Testing backend failed save scenario...');
  
  // Simulate analysis data from failed backend save
  const analysisData = {
    analysis_id: null,
    saved_to_database: false,
    file_url: null
  };
  
  const enhancedResults = {
    score: 85,
    complexity: { visual: 80, cognitive: 85, structural: 90 },
    areas: [{ name: 'Layout', score: 85, description: 'Test', recommendations: [] }],
    recommendations: ['Test recommendation'],
    analysis_summary: 'Test summary',
    gemini_analysis: 'Test analysis',
    agent_message: 'Test message',
    visuai_insights: 'Test insights'
  };
  
  const selectedFile = new File(['test'], 'test-backend-failed.png', { type: 'image/png' });
  
  console.log('📤 Testing frontend save for backend failure...');
  
  // Test the actual save
  const autoSaveData = {
    user_id: user.id,
    original_filename: selectedFile.name,
    file_size: selectedFile.size,
    file_type: selectedFile.type,
    file_url: analysisData.file_url || null,
    overall_score: enhancedResults.score,
    complexity_scores: enhancedResults.complexity,
    analysis_areas: enhancedResults.areas,
    recommendations: enhancedResults.recommendations || [],
    ai_analysis_summary: enhancedResults.analysis_summary,
    gemini_analysis: enhancedResults.gemini_analysis,
    agent_message: enhancedResults.agent_message,
    visuai_insights: enhancedResults.visuai_insights,
    tags: ['backend-failed', 'test']
  };
  
  try {
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(autoSaveData, null);
    if (savedAnalysis) {
      console.log('✅ Frontend save successful for backend failure');
      console.log('📝 Saved analysis ID:', savedAnalysis.id);
      
      // Clean up
      await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('🧹 Test data cleaned up');
    } else {
      console.log('❌ Frontend save failed for backend failure');
    }
  } catch (error) {
    console.log('❌ Frontend save error for backend failure:', error.message);
  }
}

// Test fallback analysis scenario
async function testFallbackAnalysis(user) {
  console.log('🔄 Testing fallback analysis scenario...');
  
  // Simulate fallback analysis data
  const analysisData = {
    analysis_id: null,
    saved_to_database: false,
    file_url: null
  };
  
  const enhancedResults = {
    score: 75,
    complexity: { visual: 70, cognitive: 75, structural: 80 },
    areas: [{ name: 'Layout', score: 75, description: 'Fallback test', recommendations: [] }],
    recommendations: ['Fallback recommendation'],
    analysis_summary: 'Fallback summary',
    gemini_analysis: 'Fallback analysis',
    agent_message: 'Fallback message',
    visuai_insights: 'Fallback insights'
  };
  
  const selectedFile = new File(['test'], 'test-fallback.png', { type: 'image/png' });
  
  console.log('📤 Testing frontend save for fallback analysis...');
  
  const autoSaveData = {
    user_id: user.id,
    original_filename: selectedFile.name,
    file_size: selectedFile.size,
    file_type: selectedFile.type,
    file_url: null,
    overall_score: enhancedResults.score,
    complexity_scores: enhancedResults.complexity,
    analysis_areas: enhancedResults.areas,
    recommendations: enhancedResults.recommendations || [],
    ai_analysis_summary: enhancedResults.analysis_summary,
    gemini_analysis: enhancedResults.gemini_analysis,
    agent_message: enhancedResults.agent_message,
    visuai_insights: enhancedResults.visuai_insights,
    tags: ['fallback', 'local-analysis', 'test']
  };
  
  try {
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(autoSaveData, null);
    if (savedAnalysis) {
      console.log('✅ Frontend save successful for fallback');
      console.log('📝 Saved analysis ID:', savedAnalysis.id);
      
      // Clean up
      await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('🧹 Test data cleaned up');
    } else {
      console.log('❌ Frontend save failed for fallback');
    }
  } catch (error) {
    console.log('❌ Frontend save error for fallback:', error.message);
  }
}

// Test emergency fallback scenario
async function testEmergencyFallback(user) {
  console.log('🔄 Testing emergency fallback scenario...');
  
  // Simulate emergency fallback analysis data
  const analysisData = {
    analysis_id: null,
    saved_to_database: false,
    file_url: null
  };
  
  const enhancedResults = {
    score: 65,
    complexity: { visual: 60, cognitive: 65, structural: 70 },
    areas: [{ name: 'Layout', score: 65, description: 'Emergency test', recommendations: [] }],
    recommendations: ['Emergency recommendation'],
    analysis_summary: 'Emergency summary',
    gemini_analysis: 'Emergency analysis',
    agent_message: 'Emergency message',
    visuai_insights: 'Emergency insights'
  };
  
  const selectedFile = new File(['test'], 'test-emergency.png', { type: 'image/png' });
  
  console.log('📤 Testing frontend save for emergency fallback...');
  
  const autoSaveData = {
    user_id: user.id,
    original_filename: selectedFile.name,
    file_size: selectedFile.size,
    file_type: selectedFile.type,
    file_url: null,
    overall_score: enhancedResults.score,
    complexity_scores: enhancedResults.complexity,
    analysis_areas: enhancedResults.areas,
    recommendations: enhancedResults.recommendations || [],
    ai_analysis_summary: enhancedResults.analysis_summary,
    gemini_analysis: enhancedResults.gemini_analysis,
    agent_message: enhancedResults.agent_message,
    visuai_insights: enhancedResults.visuai_insights,
    tags: ['emergency-fallback', 'local-analysis', 'test']
  };
  
  try {
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(autoSaveData, null);
    if (savedAnalysis) {
      console.log('✅ Frontend save successful for emergency fallback');
      console.log('📝 Saved analysis ID:', savedAnalysis.id);
      
      // Clean up
      await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('🧹 Test data cleaned up');
    } else {
      console.log('❌ Frontend save failed for emergency fallback');
    }
  } catch (error) {
    console.log('❌ Frontend save error for emergency fallback:', error.message);
  }
}

// Run the tests
testAutoSaveScenarios().then(() => {
  console.log('\n🎉 Auto-save scenario tests completed');
}).catch(error => {
  console.error('💥 Scenario tests failed:', error);
});

// Export for manual testing
window.testAutoSaveScenarios = testAutoSaveScenarios;
console.log('🔧 Test function available as window.testAutoSaveScenarios()');
