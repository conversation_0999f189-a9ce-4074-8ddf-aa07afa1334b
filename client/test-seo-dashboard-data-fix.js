// Test script to verify the SEO Dashboard data source fix
// Run this to test that Dashboard shows current user's actual saved analyses

console.log('🔄 Testing SEO Dashboard Data Source Fix');
console.log('========================================');

async function testDataSourceFix() {
  console.log('\n🔐 Step 1: Authentication Check');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Not authenticated - please sign in first');
      return false;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email
    });
    
    return user;
  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    return false;
  }
}

async function testSupabaseDataSource(user) {
  console.log('\n📊 Step 2: Testing Supabase Data Source');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('🔄 Fetching analyses from Supabase...');
    const analyses = await seoAnalysisService.getUserAnalyses(user.id, {
      limit: 100,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    console.log('✅ Supabase analyses:', {
      count: analyses.length,
      analyses: analyses.map(a => ({
        id: a.id,
        url: a.url,
        status: a.status,
        score: a.overall_score,
        created_at: a.created_at,
        analysis_mode: a.analysis_mode
      }))
    });
    
    return analyses;
  } catch (error) {
    console.error('❌ Supabase data fetch failed:', error);
    return [];
  }
}

async function testOldAPIDataSource() {
  console.log('\n🔍 Step 3: Testing Old API Data Source (for comparison)');
  
  try {
    console.log('🔄 Fetching analyses from old API...');
    const response = await fetch('/api/seo/analyses');
    
    if (!response.ok) {
      console.log('⚠️ Old API not accessible or returned error');
      return null;
    }
    
    const data = await response.json();
    console.log('📊 Old API analyses:', {
      count: data.analyses?.length || 0,
      analyses: data.analyses?.slice(0, 3).map(a => ({
        id: a.analysis_id,
        url: a.url,
        status: a.status,
        created_at: a.created_at
      })) || []
    });
    
    return data.analyses || [];
  } catch (error) {
    console.error('❌ Old API data fetch failed:', error);
    return null;
  }
}

async function testDashboardDisplay() {
  console.log('\n🎨 Step 4: Testing Dashboard Display');
  
  try {
    // Navigate to Dashboard tab
    const dashboardTab = Array.from(document.querySelectorAll('button[role="tab"]'))
      .find(tab => tab.textContent?.includes('Dashboard'));
    
    if (dashboardTab) {
      console.log('🔄 Clicking Dashboard tab...');
      dashboardTab.click();
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Look for analysis cards
    const analysisCards = document.querySelectorAll('[class*="card"], .card');
    console.log('📋 Found analysis cards:', analysisCards.length);
    
    // Look for analysis URLs in the DOM
    const urlElements = Array.from(document.querySelectorAll('*'))
      .filter(el => el.textContent && el.textContent.includes('http'))
      .map(el => el.textContent.trim())
      .filter(text => text.startsWith('http'));
    
    console.log('🔗 URLs found in Dashboard:', urlElements.slice(0, 5));
    
    // Look for "Ver resultados" buttons
    const resultButtons = Array.from(document.querySelectorAll('button'))
      .filter(btn => btn.textContent?.includes('Ver resultados'));
    
    console.log('🔘 "Ver resultados" buttons found:', resultButtons.length);
    
    // Look for status badges
    const statusBadges = Array.from(document.querySelectorAll('[class*="badge"], .badge'))
      .map(badge => badge.textContent?.trim())
      .filter(text => text && ['Completado', 'Pendiente', 'Error', 'En progreso'].some(status => text.includes(status)));
    
    console.log('🏷️ Status badges found:', statusBadges);
    
    return {
      analysisCards: analysisCards.length,
      urlElements: urlElements.length,
      resultButtons: resultButtons.length,
      statusBadges: statusBadges.length
    };
  } catch (error) {
    console.error('❌ Dashboard display test failed:', error);
    return null;
  }
}

async function createTestAnalysisAndVerify(user) {
  console.log('\n💾 Step 5: Creating Test Analysis and Verifying Display');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Create a test analysis
    const testData = {
      user_id: user.id,
      url: `https://example.com/dashboard-test-${Date.now()}`,
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 92,
      basic_info: { 
        title: 'Dashboard Test Analysis', 
        title_length: 24,
        meta_description: 'Testing dashboard data source fix',
        meta_description_length: 33,
        h1_tags: ['Dashboard Test'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 650,
        images: { total: 5, without_alt: 0 },
        links: { total: 15, internal: 10, external: 5 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Dashboard Test',
          issue: 'Testing dashboard data source',
          importance: 'low',
          recommendation: 'Verify that new analyses appear in dashboard'
        }
      ],
      achievements: [
        {
          category: 'Test',
          achievement: 'Dashboard Data Source Test',
          description: 'Testing the dashboard data source fix',
          icon: '🔄',
          impact: 'positive'
        }
      ],
      status: 'completed'
    };

    console.log('🔄 Creating test analysis...');
    const savedAnalysis = await seoAnalysisService.saveAnalysis(testData);
    console.log('✅ Test analysis created:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      score: savedAnalysis.overall_score
    });
    
    // Wait for potential cache updates
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if it appears in Dashboard
    console.log('🔍 Checking if analysis appears in Dashboard...');
    
    // Refresh the Dashboard tab
    const dashboardTab = Array.from(document.querySelectorAll('button[role="tab"]'))
      .find(tab => tab.textContent?.includes('Dashboard'));
    
    if (dashboardTab) {
      dashboardTab.click();
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Look for the test analysis URL in the DOM
    const testUrlFound = Array.from(document.querySelectorAll('*'))
      .some(el => el.textContent && el.textContent.includes(savedAnalysis.url));
    
    console.log('📊 Test analysis visibility:', {
      testUrlFound: testUrlFound,
      testUrl: savedAnalysis.url
    });
    
    // Clean up test data
    try {
      console.log('🧹 Cleaning up test analysis...');
      await seoAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('✅ Test analysis cleaned up');
    } catch (cleanupError) {
      console.log('⚠️ Could not clean up test analysis:', cleanupError.message);
    }
    
    return { savedAnalysis, testUrlFound };
  } catch (error) {
    console.error('❌ Test analysis creation failed:', error);
    return null;
  }
}

async function runDataSourceFixTest() {
  console.log('🚀 Starting SEO Dashboard Data Source Fix Test...\n');
  
  // Step 1: Authentication
  const user = await testDataSourceFix();
  if (!user) {
    console.log('\n❌ Authentication failed - cannot proceed');
    return;
  }
  
  // Step 2: Test Supabase data source
  const supabaseAnalyses = await testSupabaseDataSource(user);
  
  // Step 3: Test old API data source (for comparison)
  const oldAPIAnalyses = await testOldAPIDataSource();
  
  // Step 4: Test Dashboard display
  const dashboardDisplay = await testDashboardDisplay();
  
  // Step 5: Create test analysis and verify
  const testResult = await createTestAnalysisAndVerify(user);
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 DASHBOARD DATA SOURCE FIX TEST RESULTS');
  console.log('='.repeat(60));
  
  const hasSupabaseData = supabaseAnalyses.length > 0;
  const hasOldAPIData = oldAPIAnalyses && oldAPIAnalyses.length > 0;
  const dashboardWorking = dashboardDisplay && dashboardDisplay.analysisCards > 0;
  const testAnalysisWorking = testResult && testResult.testUrlFound;
  
  console.log('📈 Data Source Comparison:');
  console.log(`  Supabase analyses: ${supabaseAnalyses.length}`);
  console.log(`  Old API analyses: ${oldAPIAnalyses ? oldAPIAnalyses.length : 'N/A'}`);
  
  console.log('🎨 Dashboard Display:');
  if (dashboardDisplay) {
    console.log(`  Analysis cards: ${dashboardDisplay.analysisCards}`);
    console.log(`  Result buttons: ${dashboardDisplay.resultButtons}`);
    console.log(`  Status badges: ${dashboardDisplay.statusBadges}`);
  }
  
  if (hasSupabaseData && dashboardWorking && testAnalysisWorking) {
    console.log('\n🎉 SUCCESS: Dashboard data source fix working!');
    console.log('✅ Dashboard now fetches from Supabase');
    console.log('✅ Current user analyses displayed');
    console.log('✅ New analyses appear in Dashboard');
    console.log('✅ Real-time updates working');
  } else if (!hasSupabaseData) {
    console.log('\n⚠️ NO ANALYSES TO DISPLAY');
    console.log('💡 Create some SEO analyses first:');
    console.log('1. Go to Analizador tab');
    console.log('2. Run an SEO analysis');
    console.log('3. Check Dashboard tab for the new analysis');
  } else {
    console.log('\n❌ ISSUES DETECTED');
    console.log('🔧 Check the errors above for specific problems');
    
    if (!dashboardWorking) {
      console.log('⚠️ Dashboard not displaying analyses correctly');
    }
    if (!testAnalysisWorking) {
      console.log('⚠️ New analyses not appearing in Dashboard');
    }
  }
  
  console.log('\n📋 EXPECTED BEHAVIOR:');
  console.log('1. Dashboard shows current user\'s saved analyses from Supabase');
  console.log('2. New analyses appear immediately after completion');
  console.log('3. No more cached/stale data from old API');
  console.log('4. Real-time updates every 10 seconds');
}

// Auto-run the test
runDataSourceFixTest();

// Export for manual use
window.testDashboardDataFix = {
  runDataSourceFixTest,
  testDataSourceFix,
  testSupabaseDataSource,
  testOldAPIDataSource,
  testDashboardDisplay,
  createTestAnalysisAndVerify
};

console.log('\n💡 Manual testing available:');
console.log('- window.testDashboardDataFix.runDataSourceFixTest()');
console.log('- window.testDashboardDataFix.testSupabaseDataSource(user)');
