// Test script to verify SEO analyses are appearing in Dashboard
// Run this to test the complete data flow from saving to displaying

console.log('🔍 Testing SEO Dashboard Visibility Fix');
console.log('======================================');

async function testCompleteDataFlow() {
  console.log('\n🔐 Step 1: Authentication Check');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Not authenticated - please sign in first');
      return false;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email
    });
    
    return user;
  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    return false;
  }
}

async function testDataSaving(user) {
  console.log('\n💾 Step 2: Testing Data Saving');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Create a test analysis
    const testData = {
      user_id: user.id,
      url: `https://example.com/visibility-test-${Date.now()}`,
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 89,
      basic_info: { 
        title: 'Dashboard Visibility Test', 
        title_length: 26,
        meta_description: 'Testing dashboard visibility fix',
        meta_description_length: 32,
        h1_tags: ['Visibility Test'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 750,
        images: { total: 7, without_alt: 0 },
        links: { total: 20, internal: 14, external: 6 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Visibility Test',
          issue: 'Testing dashboard visibility',
          importance: 'low',
          recommendation: 'Verify that analyses appear in dashboard'
        }
      ],
      achievements: [
        {
          category: 'Test',
          achievement: 'Dashboard Visibility Test',
          description: 'Testing the dashboard visibility fix',
          icon: '👁️',
          impact: 'positive'
        }
      ],
      status: 'completed',
      is_favorite: false
    };

    console.log('🔄 Saving test analysis...');
    const savedAnalysis = await seoAnalysisService.saveAnalysis(testData);
    console.log('✅ Test analysis saved:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      status: savedAnalysis.status,
      created_at: savedAnalysis.created_at
    });
    
    return savedAnalysis;
  } catch (error) {
    console.error('❌ Data saving test failed:', error);
    return null;
  }
}

async function testDataRetrieval(user) {
  console.log('\n📊 Step 3: Testing Data Retrieval');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('🔄 Testing getUserAnalyses...');
    const analyses = await seoAnalysisService.getUserAnalyses(user.id, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    console.log('📊 Retrieved analyses:', {
      count: analyses.length,
      type: typeof analyses,
      isArray: Array.isArray(analyses),
      sample: analyses.slice(0, 2).map(a => ({
        id: a.id,
        url: a.url,
        status: a.status,
        created_at: a.created_at
      }))
    });
    
    return analyses;
  } catch (error) {
    console.error('❌ Data retrieval test failed:', error);
    return [];
  }
}

async function testQueryCacheConsistency(user) {
  console.log('\n🗄️ Step 4: Testing Query Cache Consistency');
  
  try {
    // Check if React Query is available
    if (!window.__REACT_QUERY_CLIENT__) {
      console.log('⚠️ React Query client not accessible from window');
      return false;
    }
    
    const queryClient = window.__REACT_QUERY_CLIENT__;
    const queryKey = ['seo-analyses', user.id];
    
    console.log('🔍 Checking query cache for key:', queryKey);
    
    // Get cached data
    const cachedData = queryClient.getQueryData(queryKey);
    console.log('📊 Cached data:', {
      exists: !!cachedData,
      type: typeof cachedData,
      isArray: Array.isArray(cachedData),
      length: Array.isArray(cachedData) ? cachedData.length : 'N/A',
      sample: Array.isArray(cachedData) && cachedData.length > 0 ? {
        id: cachedData[0].id,
        url: cachedData[0].url,
        status: cachedData[0].status
      } : 'No data'
    });
    
    // Check query state
    const queryState = queryClient.getQueryState(queryKey);
    console.log('🔍 Query state:', {
      status: queryState?.status,
      dataUpdatedAt: queryState?.dataUpdatedAt ? new Date(queryState.dataUpdatedAt).toLocaleString() : 'Never',
      error: queryState?.error?.message || 'No error'
    });
    
    return { cachedData, queryState };
  } catch (error) {
    console.error('❌ Query cache test failed:', error);
    return false;
  }
}

async function testDashboardDisplay() {
  console.log('\n🎨 Step 5: Testing Dashboard Display');
  
  try {
    // Navigate to Dashboard tab
    const dashboardTab = Array.from(document.querySelectorAll('button[role="tab"]'))
      .find(tab => tab.textContent?.includes('Dashboard'));
    
    if (!dashboardTab) {
      console.log('⚠️ Dashboard tab not found');
      return false;
    }
    
    console.log('🔄 Clicking Dashboard tab...');
    dashboardTab.click();
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check for loading state
    const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"]');
    console.log('⏳ Loading elements found:', loadingElements.length);
    
    // Check for analysis cards
    const analysisCards = document.querySelectorAll('[class*="card"]');
    console.log('📋 Analysis cards found:', analysisCards.length);
    
    // Check for "Ver resultados" buttons
    const resultButtons = Array.from(document.querySelectorAll('button'))
      .filter(btn => btn.textContent?.includes('Ver resultados'));
    console.log('🔘 "Ver resultados" buttons found:', resultButtons.length);
    
    // Check for empty state messages
    const emptyMessages = Array.from(document.querySelectorAll('*'))
      .filter(el => el.textContent && (
        el.textContent.includes('No hay análisis') ||
        el.textContent.includes('No tienes análisis') ||
        el.textContent.includes('Cargando') ||
        el.textContent.includes('Loading')
      ));
    console.log('📝 Empty/loading messages found:', emptyMessages.map(el => el.textContent.trim()));
    
    // Check for analysis URLs in the DOM
    const urlElements = Array.from(document.querySelectorAll('*'))
      .filter(el => el.textContent && el.textContent.includes('example.com'))
      .map(el => el.textContent.trim());
    console.log('🔗 Test URLs found in DOM:', urlElements.slice(0, 3));
    
    return {
      analysisCards: analysisCards.length,
      resultButtons: resultButtons.length,
      emptyMessages: emptyMessages.length,
      testUrls: urlElements.length
    };
  } catch (error) {
    console.error('❌ Dashboard display test failed:', error);
    return null;
  }
}

async function testRealTimeUpdates(user) {
  console.log('\n🔄 Step 6: Testing Real-time Updates');
  
  try {
    // Get initial count
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    const initialAnalyses = await seoAnalysisService.getUserAnalyses(user.id, { limit: 5 });
    const initialCount = initialAnalyses.length;
    
    console.log('📊 Initial analysis count:', initialCount);
    
    // Wait for potential refetch (Dashboard has 10-second interval)
    console.log('⏳ Waiting 12 seconds for potential refetch...');
    await new Promise(resolve => setTimeout(resolve, 12000));
    
    // Check if Dashboard updated
    const resultButtons = Array.from(document.querySelectorAll('button'))
      .filter(btn => btn.textContent?.includes('Ver resultados'));
    
    console.log('📊 Dashboard buttons after wait:', resultButtons.length);
    
    // Check query cache again
    if (window.__REACT_QUERY_CLIENT__) {
      const queryClient = window.__REACT_QUERY_CLIENT__;
      const cachedData = queryClient.getQueryData(['seo-analyses', user.id]);
      console.log('📊 Cached data after wait:', {
        exists: !!cachedData,
        length: Array.isArray(cachedData) ? cachedData.length : 'N/A'
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Real-time updates test failed:', error);
    return false;
  }
}

async function runVisibilityFixTest() {
  console.log('🚀 Starting SEO Dashboard Visibility Fix Test...\n');
  
  // Step 1: Authentication
  const user = await testCompleteDataFlow();
  if (!user) {
    console.log('\n❌ Authentication failed - cannot proceed');
    return;
  }
  
  // Step 2: Test data saving
  const savedAnalysis = await testDataSaving(user);
  
  // Step 3: Test data retrieval
  const retrievedAnalyses = await testDataRetrieval(user);
  
  // Step 4: Test query cache
  const cacheResult = await testQueryCacheConsistency(user);
  
  // Step 5: Test dashboard display
  const displayResult = await testDashboardDisplay();
  
  // Step 6: Test real-time updates
  const updateResult = await testRealTimeUpdates(user);
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 DASHBOARD VISIBILITY FIX TEST RESULTS');
  console.log('='.repeat(60));
  
  const hasData = retrievedAnalyses.length > 0;
  const dashboardWorking = displayResult && displayResult.resultButtons > 0;
  const cacheWorking = cacheResult && Array.isArray(cacheResult.cachedData);
  
  console.log('📈 Data Flow Status:');
  console.log(`  Data saving: ${savedAnalysis ? '✅ Working' : '❌ Failed'}`);
  console.log(`  Data retrieval: ${hasData ? '✅ Working' : '⚠️ No data'} (${retrievedAnalyses.length} analyses)`);
  console.log(`  Query cache: ${cacheWorking ? '✅ Working' : '❌ Issues'}`);
  console.log(`  Dashboard display: ${dashboardWorking ? '✅ Working' : '❌ Not working'}`);
  
  if (hasData && dashboardWorking && cacheWorking) {
    console.log('\n🎉 SUCCESS: Dashboard visibility fix working!');
    console.log('✅ Data structure consistency fixed');
    console.log('✅ Query cache synchronization working');
    console.log('✅ Analyses appearing in Dashboard');
    console.log('✅ Real-time updates functional');
  } else if (!hasData) {
    console.log('\n⚠️ NO DATA TO DISPLAY');
    console.log('💡 Try running an SEO analysis first:');
    console.log('1. Go to Analizador tab');
    console.log('2. Enter a URL and run analysis');
    console.log('3. Wait for completion');
    console.log('4. Check Dashboard tab');
  } else {
    console.log('\n❌ ISSUES DETECTED');
    console.log('🔧 Check the errors above for specific problems');
    
    if (!cacheWorking) {
      console.log('⚠️ Query cache issues detected');
    }
    if (!dashboardWorking) {
      console.log('⚠️ Dashboard display issues detected');
    }
  }
  
  console.log('\n📋 EXPECTED BEHAVIOR:');
  console.log('1. New analyses should appear in Dashboard within 10 seconds');
  console.log('2. Dashboard should show actual user analyses from Supabase');
  console.log('3. Query cache should be consistent between components');
  console.log('4. Real-time updates should work without page refresh');
  
  // Clean up test data
  if (savedAnalysis && savedAnalysis.id) {
    try {
      console.log('\n🧹 Cleaning up test analysis...');
      await seoAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('✅ Test analysis cleaned up');
    } catch (cleanupError) {
      console.log('⚠️ Could not clean up test analysis:', cleanupError.message);
    }
  }
}

// Auto-run the test
runVisibilityFixTest();

// Export for manual use
window.testDashboardVisibility = {
  runVisibilityFixTest,
  testCompleteDataFlow,
  testDataSaving,
  testDataRetrieval,
  testQueryCacheConsistency,
  testDashboardDisplay,
  testRealTimeUpdates
};

console.log('\n💡 Manual testing available:');
console.log('- window.testDashboardVisibility.runVisibilityFixTest()');
console.log('- window.testDashboardVisibility.testDashboardDisplay()');
