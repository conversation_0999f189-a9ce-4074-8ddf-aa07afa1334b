/**
 * Test Query Invalidation Fix
 * Tests that query invalidation is working correctly with proper query keys
 */

console.log('🧪 Testing Query Invalidation Fix');

async function testQueryInvalidation() {
  console.log('🔍 Starting query invalidation test...');
  
  try {
    // Check authentication
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ User not authenticated');
      return false;
    }
    
    console.log('✅ User authenticated:', user.email);
    console.log('📝 User ID:', user.id);
    
    // Check if React Query DevTools are available
    if (window.__REACT_QUERY_DEVTOOLS__) {
      console.log('✅ React Query DevTools available');
    } else {
      console.log('⚠️ React Query DevTools not available');
    }
    
    // Test 1: Check current queries in cache
    console.log('\n📋 STEP 1: Checking current queries in cache');
    
    // Get the query client from the component
    const queryClient = window.queryClient || 
                       document.querySelector('[data-reactroot]')?.__reactInternalInstance?.memoizedProps?.queryClient;
    
    if (!queryClient) {
      console.log('❌ Could not access query client');
      return false;
    }
    
    console.log('✅ Query client accessible');
    
    // Check current cache
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    console.log('📊 Current queries in cache:', queries.length);
    queries.forEach(query => {
      console.log('  -', query.queryKey);
    });
    
    // Test 2: Get initial analyses count
    console.log('\n📋 STEP 2: Getting initial analyses count');
    
    const initialAnalyses = await window.designAnalysisService.getUserAnalyses(user.id, 10);
    console.log('📊 Initial analyses count:', initialAnalyses.length);
    
    // Test 3: Save a test analysis
    console.log('\n📋 STEP 3: Saving test analysis');
    
    const testAnalysisData = {
      user_id: user.id,
      original_filename: 'query-test.png',
      file_size: 2048,
      file_type: 'image/png',
      file_url: null,
      overall_score: 88,
      complexity_scores: { visual: 85, cognitive: 88, structural: 90 },
      analysis_areas: [{
        name: 'Layout',
        score: 88,
        description: 'Query invalidation test',
        recommendations: ['Test recommendation']
      }],
      recommendations: ['Test overall recommendation'],
      ai_analysis_summary: 'Query invalidation test summary',
      gemini_analysis: 'Query invalidation test analysis',
      agent_message: 'Query invalidation test message',
      visuai_insights: 'Query invalidation test insights',
      tags: ['query-test', 'invalidation-test']
    };
    
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testAnalysisData, null);
    
    if (!savedAnalysis) {
      console.log('❌ Failed to save test analysis');
      return false;
    }
    
    console.log('✅ Test analysis saved:', savedAnalysis.id);
    
    // Test 4: Manually invalidate queries with correct keys
    console.log('\n📋 STEP 4: Manually invalidating queries');
    
    console.log('🔄 Invalidating design-analyses query...');
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    console.log('🔄 Invalidating design-analysis-stats query...');
    await queryClient.invalidateQueries({ queryKey: ['design-analysis-stats', user.id] });
    
    // Wait a moment for queries to refetch
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 5: Check if analyses count increased
    console.log('\n📋 STEP 5: Checking if analyses count increased');
    
    const updatedAnalyses = await window.designAnalysisService.getUserAnalyses(user.id, 10);
    console.log('📊 Updated analyses count:', updatedAnalyses.length);
    
    const foundTestAnalysis = updatedAnalyses.find(a => a.id === savedAnalysis.id);
    
    if (foundTestAnalysis) {
      console.log('✅ Test analysis found in updated list');
      console.log('📝 Analysis details:', {
        id: foundTestAnalysis.id,
        filename: foundTestAnalysis.original_filename,
        score: foundTestAnalysis.overall_score,
        created_at: foundTestAnalysis.created_at
      });
    } else {
      console.log('❌ Test analysis NOT found in updated list');
    }
    
    // Test 6: Check UI state
    console.log('\n📋 STEP 6: Checking UI state');
    
    // Check if the history tab shows the new analysis
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      console.log('✅ History tab found');
      
      // Check if there are analysis cards
      const analysisCards = document.querySelectorAll('[data-testid="analysis-card"], .analysis-card, [class*="analysis"]');
      console.log('📊 Analysis cards in UI:', analysisCards.length);
      
      // Look for our test analysis in the UI
      const testAnalysisInUI = Array.from(document.querySelectorAll('*')).find(el => 
        el.textContent && el.textContent.includes('query-test.png')
      );
      
      if (testAnalysisInUI) {
        console.log('✅ Test analysis found in UI');
      } else {
        console.log('❌ Test analysis NOT found in UI');
      }
    } else {
      console.log('⚠️ History tab not found');
    }
    
    // Test 7: Clean up
    console.log('\n📋 STEP 7: Cleaning up test data');
    
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    console.log('🧹 Test analysis deleted');
    
    // Invalidate queries again after deletion
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    await queryClient.invalidateQueries({ queryKey: ['design-analysis-stats', user.id] });
    
    console.log('✅ Query invalidation test completed successfully');
    return true;
    
  } catch (error) {
    console.error('❌ Query invalidation test failed:', error);
    return false;
  }
}

// Test query key matching
function testQueryKeyMatching() {
  console.log('\n🔍 Testing Query Key Matching');
  
  // Simulate the query keys used in the hook vs invalidation
  const userId = 'test-user-123';
  
  const hookQueryKey = ['design-analyses', userId];
  const invalidationKey = ['design-analyses', userId];
  
  console.log('🔑 Hook query key:', hookQueryKey);
  console.log('🔑 Invalidation key:', invalidationKey);
  
  const keysMatch = JSON.stringify(hookQueryKey) === JSON.stringify(invalidationKey);
  
  if (keysMatch) {
    console.log('✅ Query keys match correctly');
  } else {
    console.log('❌ Query keys do NOT match');
  }
  
  return keysMatch;
}

// Run the tests
console.log('🚀 Starting Query Invalidation Tests');

testQueryKeyMatching();

testQueryInvalidation().then(success => {
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('🎉 QUERY INVALIDATION TEST: PASSED');
    console.log('✅ Query invalidation is working correctly');
    console.log('✅ Auto-save should now update the UI properly');
  } else {
    console.log('💥 QUERY INVALIDATION TEST: FAILED');
    console.log('❌ Query invalidation needs further investigation');
  }
  console.log('='.repeat(50));
}).catch(error => {
  console.error('💥 Test execution failed:', error);
});

// Export for manual testing
window.testQueryInvalidation = testQueryInvalidation;
console.log('🔧 Test function available as window.testQueryInvalidation()');
