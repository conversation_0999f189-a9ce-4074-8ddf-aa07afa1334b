// Test script to verify the SEO Analyzer filter error fix
// Run this to test that the "allAnalyses.filter is not a function" error is resolved

console.log('🔧 Testing SEO Analyzer Filter Error Fix');
console.log('========================================');

async function testFilterErrorFix() {
  console.log('\n🔐 Step 1: Authentication Check');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Not authenticated - please sign in first');
      return false;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email
    });
    
    return user;
  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    return false;
  }
}

async function testSeoAnalysisService(user) {
  console.log('\n📊 Step 2: Testing seoAnalysisService Data Structure');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('🔄 Testing getUserAnalyses method...');
    const result = await seoAnalysisService.getUserAnalyses(user.id, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    console.log('📊 Service result analysis:', {
      type: typeof result,
      isArray: Array.isArray(result),
      length: Array.isArray(result) ? result.length : 'N/A',
      hasAnalysesProperty: result && typeof result === 'object' && 'analyses' in result,
      keys: result && typeof result === 'object' ? Object.keys(result) : 'N/A'
    });
    
    if (Array.isArray(result)) {
      console.log('✅ Service returns array directly');
      console.log('📋 Sample items:', result.slice(0, 2).map(item => ({
        id: item.id,
        url: item.url,
        is_favorite: item.is_favorite,
        status: item.status
      })));
    } else if (result && typeof result === 'object' && Array.isArray(result.analyses)) {
      console.log('✅ Service returns object with analyses array');
      console.log('📋 Sample items:', result.analyses.slice(0, 2).map(item => ({
        id: item.id,
        url: item.url,
        is_favorite: item.is_favorite,
        status: item.status
      })));
    } else {
      console.log('⚠️ Unexpected service result format');
    }
    
    return result;
  } catch (error) {
    console.error('❌ seoAnalysisService test failed:', error);
    return null;
  }
}

async function testHookDataProcessing(user) {
  console.log('\n🪝 Step 3: Testing Hook Data Processing');
  
  try {
    // We can't directly test the hook outside React, but we can simulate the logic
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('🔄 Simulating hook data processing...');
    
    // Simulate the query function
    const queryData = await seoAnalysisService.getUserAnalyses(user.id, {
      limit: 100,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    console.log('📊 Query data received:', {
      type: typeof queryData,
      isArray: Array.isArray(queryData),
      length: Array.isArray(queryData) ? queryData.length : 'N/A'
    });
    
    // Simulate the allAnalyses processing logic
    let allAnalyses;
    
    if (!queryData) {
      console.log('📊 No query data, using empty array');
      allAnalyses = [];
    } else if (Array.isArray(queryData)) {
      console.log('✅ Query data is array');
      allAnalyses = queryData;
    } else if (queryData && typeof queryData === 'object' && Array.isArray(queryData.analyses)) {
      console.log('✅ Query data has analyses array');
      allAnalyses = queryData.analyses;
    } else {
      console.warn('⚠️ Unexpected query data format, using empty array');
      allAnalyses = [];
    }
    
    console.log('📋 Final allAnalyses:', {
      type: typeof allAnalyses,
      isArray: Array.isArray(allAnalyses),
      length: allAnalyses.length
    });
    
    // Test the filter operations
    console.log('🔄 Testing filter operations...');
    
    try {
      const favoriteAnalyses = allAnalyses.filter(analysis => analysis.is_favorite);
      console.log('✅ Favorite filter successful:', favoriteAnalyses.length, 'favorites');
    } catch (error) {
      console.error('❌ Favorite filter failed:', error);
      return false;
    }
    
    try {
      const nonFavorites = allAnalyses.filter(analysis => !analysis.is_favorite);
      const recentAnalyses = nonFavorites.slice(0, 10);
      console.log('✅ Recent filter successful:', recentAnalyses.length, 'recent');
    } catch (error) {
      console.error('❌ Recent filter failed:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Hook data processing test failed:', error);
    return false;
  }
}

async function testDashboardToAnalyzerFlow() {
  console.log('\n🔄 Step 4: Testing Dashboard to Analyzer Flow');
  
  try {
    // Check if we're on the SEO Analyzer page
    const currentUrl = window.location.href;
    if (!currentUrl.includes('seo-analyzer')) {
      console.log('⚠️ Not on SEO Analyzer page - navigate there first');
      return false;
    }
    
    // Navigate to Dashboard tab
    const dashboardTab = Array.from(document.querySelectorAll('button[role="tab"]'))
      .find(tab => tab.textContent?.includes('Dashboard'));
    
    if (!dashboardTab) {
      console.log('⚠️ Dashboard tab not found');
      return false;
    }
    
    console.log('🔄 Clicking Dashboard tab...');
    dashboardTab.click();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Look for "Ver resultados" buttons
    const resultButtons = Array.from(document.querySelectorAll('button'))
      .filter(btn => btn.textContent?.includes('Ver resultados'));
    
    console.log('📋 Found "Ver resultados" buttons:', resultButtons.length);
    
    if (resultButtons.length === 0) {
      console.log('⚠️ No analyses to test with');
      return false;
    }
    
    // Monitor for errors
    let errorOccurred = false;
    const originalConsoleError = console.error;
    console.error = function(...args) {
      if (args.some(arg => typeof arg === 'string' && arg.includes('filter is not a function'))) {
        errorOccurred = true;
        console.log('❌ Filter error detected:', ...args);
      }
      originalConsoleError.apply(console, args);
    };
    
    // Try clicking a result button
    console.log('🔄 Testing analysis loading...');
    resultButtons[0].click();
    
    // Wait for potential errors
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Restore console.error
    console.error = originalConsoleError;
    
    if (errorOccurred) {
      console.log('❌ Filter error still occurring');
      return false;
    } else {
      console.log('✅ No filter errors detected');
      return true;
    }
  } catch (error) {
    console.error('❌ Dashboard to Analyzer flow test failed:', error);
    return false;
  }
}

async function createTestAnalysisForTesting(user) {
  console.log('\n💾 Step 5: Creating Test Analysis (if needed)');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Check if we have existing analyses
    const existing = await seoAnalysisService.getUserAnalyses(user.id, { limit: 1 });
    
    if (Array.isArray(existing) && existing.length > 0) {
      console.log('✅ Existing analyses found, skipping creation');
      return existing[0];
    }
    
    if (existing && existing.analyses && existing.analyses.length > 0) {
      console.log('✅ Existing analyses found in analyses property, skipping creation');
      return existing.analyses[0];
    }
    
    console.log('🔄 Creating test analysis for testing...');
    
    const testData = {
      user_id: user.id,
      url: `https://example.com/filter-fix-test-${Date.now()}`,
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 87,
      basic_info: { 
        title: 'Filter Fix Test Analysis', 
        title_length: 25,
        meta_description: 'Testing the filter error fix',
        meta_description_length: 29,
        h1_tags: ['Filter Fix Test'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 700,
        images: { total: 6, without_alt: 1 },
        links: { total: 18, internal: 12, external: 6 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Filter Fix',
          issue: 'Testing filter error resolution',
          importance: 'low',
          recommendation: 'Verify that filter operations work correctly'
        }
      ],
      achievements: [
        {
          category: 'Test',
          achievement: 'Filter Fix Test',
          description: 'Testing the allAnalyses.filter error fix',
          icon: '🔧',
          impact: 'positive'
        }
      ],
      status: 'completed',
      is_favorite: false
    };

    const savedAnalysis = await seoAnalysisService.saveAnalysis(testData);
    console.log('✅ Test analysis created:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      is_favorite: savedAnalysis.is_favorite
    });
    
    return savedAnalysis;
  } catch (error) {
    console.error('❌ Test analysis creation failed:', error);
    return null;
  }
}

async function runFilterErrorFixTest() {
  console.log('🚀 Starting SEO Analyzer Filter Error Fix Test...\n');
  
  // Step 1: Authentication
  const user = await testFilterErrorFix();
  if (!user) {
    console.log('\n❌ Authentication failed - cannot proceed');
    return;
  }
  
  // Step 2: Test service data structure
  const serviceResult = await testSeoAnalysisService(user);
  
  // Step 3: Test hook data processing
  const hookResult = await testHookDataProcessing(user);
  
  // Step 4: Create test analysis if needed
  const testAnalysis = await createTestAnalysisForTesting(user);
  
  // Step 5: Test Dashboard to Analyzer flow
  const flowResult = await testDashboardToAnalyzerFlow();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 FILTER ERROR FIX TEST RESULTS');
  console.log('='.repeat(60));
  
  if (hookResult && flowResult) {
    console.log('🎉 SUCCESS: Filter error fix working!');
    console.log('✅ Data structure handling improved');
    console.log('✅ Defensive programming added');
    console.log('✅ Filter operations working correctly');
    console.log('✅ Dashboard to Analyzer flow functional');
    console.log('\n💡 The "allAnalyses.filter is not a function" error has been resolved!');
  } else {
    console.log('❌ ISSUES DETECTED');
    console.log('🔧 Check the errors above for specific problems');
    
    if (!hookResult) {
      console.log('⚠️ Hook data processing issues detected');
    }
    if (!flowResult) {
      console.log('⚠️ Dashboard to Analyzer flow issues detected');
    }
  }
  
  console.log('\n📋 EXPECTED BEHAVIOR:');
  console.log('1. No "filter is not a function" errors');
  console.log('2. Dashboard analyses load correctly');
  console.log('3. Clicking "Ver resultados" works without errors');
  console.log('4. SEO Analyzer displays analysis results properly');
  
  // Clean up test data if created
  if (testAnalysis && testAnalysis.id) {
    try {
      console.log('\n🧹 Cleaning up test analysis...');
      await seoAnalysisService.deleteAnalysis(testAnalysis.id);
      console.log('✅ Test analysis cleaned up');
    } catch (cleanupError) {
      console.log('⚠️ Could not clean up test analysis:', cleanupError.message);
    }
  }
}

// Auto-run the test
runFilterErrorFixTest();

// Export for manual use
window.testFilterErrorFix = {
  runFilterErrorFixTest,
  testFilterErrorFix,
  testSeoAnalysisService,
  testHookDataProcessing,
  testDashboardToAnalyzerFlow,
  createTestAnalysisForTesting
};

console.log('\n💡 Manual testing available:');
console.log('- window.testFilterErrorFix.runFilterErrorFixTest()');
console.log('- window.testFilterErrorFix.testHookDataProcessing(user)');
