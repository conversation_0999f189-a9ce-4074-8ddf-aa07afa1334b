/**
 * Test Hook Behavior
 * Test the useDesignAnalysis hook behavior and React component updates
 */

console.log('🔍 Testing Hook Behavior and React Updates');

async function testHookBehavior() {
  console.log('🚀 Starting hook behavior test...');
  
  try {
    // Step 1: Check authentication
    console.log('1️⃣ AUTHENTICATION CHECK');
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ User not authenticated');
      return false;
    }
    console.log('✅ User authenticated:', user.email, 'ID:', user.id);
    
    // Step 2: Check if the hook is enabled
    console.log('2️⃣ HOOK ENABLED CHECK');
    const isEnabled = !!user?.id && user.id !== 'anonymous';
    console.log('📊 Hook enabled condition:', isEnabled);
    console.log('📊 User ID exists:', !!user?.id);
    console.log('📊 User ID not anonymous:', user.id !== 'anonymous');
    
    // Step 3: Check query client and cache
    console.log('3️⃣ QUERY CLIENT AND CACHE CHECK');
    if (!window.queryClient) {
      console.log('❌ QueryClient not available');
      return false;
    }
    
    const queryClient = window.queryClient;
    const cache = queryClient.getQueryCache();
    
    // Step 4: Monitor query creation and updates
    console.log('4️⃣ QUERY MONITORING SETUP');
    
    const originalSetQueryData = queryClient.setQueryData.bind(queryClient);
    const originalInvalidateQueries = queryClient.invalidateQueries.bind(queryClient);
    
    let queryUpdates = [];
    let invalidations = [];
    
    queryClient.setQueryData = function(queryKey, updater) {
      const update = {
        timestamp: new Date().toISOString(),
        queryKey: queryKey,
        type: 'setQueryData'
      };
      queryUpdates.push(update);
      console.log('📝 setQueryData called:', update);
      return originalSetQueryData(queryKey, updater);
    };
    
    queryClient.invalidateQueries = function(filters) {
      const invalidation = {
        timestamp: new Date().toISOString(),
        filters: filters,
        matchedQueries: cache.findAll(filters).length
      };
      invalidations.push(invalidation);
      console.log('🔄 invalidateQueries called:', invalidation);
      return originalInvalidateQueries(filters);
    };
    
    // Step 5: Check current query state
    console.log('5️⃣ CURRENT QUERY STATE');
    const currentQuery = cache.find(['design-analyses', user.id]);
    
    if (currentQuery) {
      console.log('✅ Query exists in cache');
      console.log('📊 Query details:', {
        status: currentQuery.state.status,
        isFetching: currentQuery.state.isFetching,
        isStale: currentQuery.state.isStale,
        dataLength: currentQuery.state.data?.length || 0,
        lastUpdated: new Date(currentQuery.state.dataUpdatedAt),
        enabled: currentQuery.options?.enabled
      });
    } else {
      console.log('❌ Query does not exist in cache');
      console.log('🔍 Available queries:');
      cache.getAll().forEach(q => {
        console.log('  -', JSON.stringify(q.queryKey));
      });
    }
    
    // Step 6: Test manual query trigger
    console.log('6️⃣ MANUAL QUERY TRIGGER TEST');
    
    console.log('🔄 Manually fetching data via service...');
    const serviceData = await window.designAnalysisService.getUserAnalyses(user.id, 10);
    console.log('📊 Service returned:', serviceData.length, 'analyses');
    
    // Check if this created/updated the query
    const afterServiceQuery = cache.find(['design-analyses', user.id]);
    if (afterServiceQuery) {
      console.log('📊 Query state after service call:', {
        status: afterServiceQuery.state.status,
        dataLength: afterServiceQuery.state.data?.length || 0
      });
    }
    
    // Step 7: Test save and invalidate cycle
    console.log('7️⃣ SAVE AND INVALIDATE CYCLE TEST');
    
    const testData = {
      user_id: user.id,
      original_filename: 'hook-behavior-test.png',
      file_size: 1536,
      file_type: 'image/png',
      file_url: null,
      overall_score: 89,
      complexity_scores: { visual: 87, cognitive: 89, structural: 91 },
      analysis_areas: [{
        name: 'Layout',
        score: 89,
        description: 'Hook behavior test',
        recommendations: ['Test recommendation']
      }],
      recommendations: ['Test recommendation'],
      ai_analysis_summary: 'Hook behavior test',
      gemini_analysis: 'Hook behavior test',
      agent_message: 'Hook behavior test',
      visuai_insights: 'Hook behavior test',
      tags: ['hook-behavior-test']
    };
    
    // Get initial state
    const initialQuery = cache.find(['design-analyses', user.id]);
    const initialCount = initialQuery?.state.data?.length || 0;
    console.log('📊 Initial count:', initialCount);
    
    // Save
    console.log('💾 Saving test analysis...');
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testData, null);
    
    if (!savedAnalysis) {
      console.log('❌ Failed to save analysis');
      return false;
    }
    
    console.log('✅ Analysis saved:', savedAnalysis.id);
    
    // Invalidate (like auto-save does)
    console.log('🔄 Invalidating queries (simulating auto-save)...');
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    // Wait for potential refetch
    console.log('⏳ Waiting for refetch...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check final state
    const finalQuery = cache.find(['design-analyses', user.id]);
    const finalCount = finalQuery?.state.data?.length || 0;
    const foundInQuery = finalQuery?.state.data?.find(a => a.id === savedAnalysis.id);
    
    console.log('📊 Final count:', finalCount);
    console.log('📊 Count difference:', finalCount - initialCount);
    
    if (foundInQuery) {
      console.log('✅ New analysis found in query data');
    } else {
      console.log('❌ New analysis NOT found in query data');
      
      // Debug: Check service vs query data
      const freshServiceData = await window.designAnalysisService.getUserAnalyses(user.id, 10);
      const foundInService = freshServiceData.find(a => a.id === savedAnalysis.id);
      
      console.log('🔍 Debug info:');
      console.log('  - Service data length:', freshServiceData.length);
      console.log('  - Query data length:', finalQuery?.state.data?.length || 0);
      console.log('  - Found in service:', !!foundInService);
      console.log('  - Query status:', finalQuery?.state.status);
      console.log('  - Query isFetching:', finalQuery?.state.isFetching);
      console.log('  - Query isStale:', finalQuery?.state.isStale);
    }
    
    // Step 8: Check React component state
    console.log('8️⃣ REACT COMPONENT STATE CHECK');
    
    // Try to find the component in the DOM and check its state
    const historySection = document.querySelector('[data-testid="history-section"], [class*="history"]');
    if (historySection) {
      console.log('✅ Found history section in DOM');
      
      // Check for loading states
      const loadingElements = historySection.querySelectorAll('[class*="loading"], [class*="spinner"], .animate-spin');
      console.log('📊 Loading elements:', loadingElements.length);
      
      // Check for analysis cards
      const analysisCards = historySection.querySelectorAll('[data-testid*="analysis"], [class*="analysis"]');
      console.log('📊 Analysis cards:', analysisCards.length);
      
      // Check for "no analyses" message
      const noAnalysesMsg = historySection.textContent?.includes('No hay análisis') || 
                           historySection.textContent?.includes('Sin análisis');
      console.log('📊 Shows "no analyses" message:', noAnalysesMsg);
      
    } else {
      console.log('❌ History section not found in DOM');
    }
    
    // Step 9: Summary of monitoring data
    console.log('9️⃣ MONITORING SUMMARY');
    console.log('📊 Query updates:', queryUpdates.length);
    queryUpdates.forEach((update, i) => {
      console.log(`  ${i + 1}. ${update.timestamp} - ${update.type}`);
    });
    
    console.log('📊 Invalidations:', invalidations.length);
    invalidations.forEach((inv, i) => {
      console.log(`  ${i + 1}. ${inv.timestamp} - Matched: ${inv.matchedQueries}`);
    });
    
    // Clean up
    console.log('🧹 Cleaning up...');
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    
    // Restore original functions
    queryClient.setQueryData = originalSetQueryData;
    queryClient.invalidateQueries = originalInvalidateQueries;
    
    console.log('✅ Hook behavior test completed');
    return foundInQuery !== undefined;
    
  } catch (error) {
    console.error('❌ Hook behavior test failed:', error);
    return false;
  }
}

// Helper to check React component updates
function checkReactUpdates() {
  console.log('🔍 Checking React component updates...');
  
  // Try to find React components and their update cycles
  const reactElements = document.querySelectorAll('[data-reactroot] *');
  let reactComponentsFound = 0;
  
  for (const element of reactElements) {
    if (element._reactInternalFiber || element._reactInternalInstance) {
      reactComponentsFound++;
    }
  }
  
  console.log('📊 React components found:', reactComponentsFound);
  
  // Check for React DevTools
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('✅ React DevTools available');
    const hook = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
    console.log('📊 React renderers:', hook.renderers?.size || 0);
  } else {
    console.log('❌ React DevTools not available');
  }
}

// Run the test
testHookBehavior().then(success => {
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('🎉 HOOK BEHAVIOR TEST: SUCCESS');
  } else {
    console.log('💥 HOOK BEHAVIOR TEST: FAILED');
  }
  console.log('='.repeat(50));
  
  checkReactUpdates();
}).catch(error => {
  console.error('💥 Hook behavior test crashed:', error);
});

// Export functions
window.testHookBehavior = testHookBehavior;
window.checkReactUpdates = checkReactUpdates;
console.log('🔧 Functions available: window.testHookBehavior(), window.checkReactUpdates()');
