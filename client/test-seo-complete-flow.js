// Complete SEO Analysis Flow Test
// Run this in the browser console on the SEO Analyzer page

console.log('🧪 Testing Complete SEO Analysis Flow...');

async function testCompleteFlow() {
  try {
    console.log('\n=== 🔐 STEP 1: Authentication Check ===');
    
    // Import required modules
    const { supabase } = await import('/src/lib/supabase.ts');
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    const isAuthenticated = !!user && user.id !== 'anonymous';
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      isAuthenticated
    });
    
    if (!isAuthenticated) {
      console.error('❌ User not properly authenticated');
      return false;
    }
    
    console.log('\n=== 📊 STEP 2: Check Current Analyses ===');
    
    const initialAnalyses = await seoAnalysisService.getUserAnalyses(user.id);
    console.log('📋 Initial analyses count:', initialAnalyses.length);
    
    console.log('\n=== 💾 STEP 3: Test Save Functionality ===');
    
    // Create a test analysis
    const testAnalysis = {
      user_id: user.id,
      url: 'https://test-flow-' + Date.now() + '.com',
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 85,
      basic_info: {
        title: 'Test Flow Analysis',
        description: 'Testing complete flow functionality',
        h1: 'Test Page Header'
      },
      content_analysis: {
        word_count: 500,
        heading_structure: { h1: 1, h2: 3, h3: 2 }
      },
      seo_checks: {
        meta_description: true,
        title_tag: true,
        h1_tag: true,
        https: true
      },
      recommendations: [
        'Improve meta description length',
        'Add more internal links',
        'Optimize images with alt text'
      ],
      achievements: [
        'Good title tag length',
        'HTTPS enabled',
        'H1 tag present'
      ],
      open_graph: {
        title: 'Test OG Title',
        description: 'Test OG Description'
      },
      twitter_card: {
        card: 'summary',
        title: 'Test Twitter Title'
      },
      preview_data: {
        title: 'Test Preview',
        description: 'Test Preview Description'
      },
      performance_metrics: {
        load_time: 2.1,
        page_size: 2048
      },
      analysis_duration_ms: 3000,
      status: 'completed',
      ai_enhanced: false,
      is_favorite: false
    };
    
    console.log('🔄 Saving test analysis...');
    const savedAnalysis = await seoAnalysisService.saveAnalysis(testAnalysis);
    console.log('✅ Analysis saved successfully:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      created_at: savedAnalysis.created_at
    });
    
    console.log('\n=== 🔍 STEP 4: Verify Save ===');
    
    const updatedAnalyses = await seoAnalysisService.getUserAnalyses(user.id);
    console.log('📋 Updated analyses count:', updatedAnalyses.length);
    console.log('📊 Analyses:', updatedAnalyses.map(a => ({
      id: a.id,
      url: a.url,
      status: a.status,
      created_at: a.created_at
    })));
    
    const testAnalysisFound = updatedAnalyses.find(a => a.id === savedAnalysis.id);
    if (testAnalysisFound) {
      console.log('✅ Test analysis found in user analyses!');
    } else {
      console.error('❌ Test analysis not found in user analyses');
      return false;
    }
    
    console.log('\n=== 🔄 STEP 5: Test React Query Cache ===');
    
    // Try to access React Query client and invalidate cache
    try {
      // Look for React Query client in various places
      const queryClient = window.__REACT_QUERY_CLIENT__ || 
                         window.queryClient ||
                         document.querySelector('[data-react-query-client]')?.__reactInternalInstance?.memoizedProps?.client;
      
      if (queryClient) {
        console.log('🔄 Invalidating React Query cache...');
        await queryClient.invalidateQueries(['seo-analyses']);
        console.log('✅ Cache invalidated');
      } else {
        console.warn('⚠️ Could not access React Query client for cache invalidation');
      }
    } catch (cacheError) {
      console.warn('⚠️ Cache invalidation failed:', cacheError);
    }
    
    console.log('\n=== 🎯 STEP 6: Dashboard Test ===');
    
    // Simulate what the Dashboard component does
    const authLoading = false;
    const dashboardIsAuthenticated = !!user && user.id !== 'anonymous';
    const dashboardQueryEnabled = !authLoading && !!user?.id && dashboardIsAuthenticated;
    
    console.log('🔍 Dashboard query conditions:', {
      authLoading,
      userExists: !!user,
      userId: user?.id,
      dashboardIsAuthenticated,
      dashboardQueryEnabled
    });
    
    if (dashboardQueryEnabled) {
      console.log('✅ Dashboard queries should be ENABLED');
      
      // Test the exact query the Dashboard uses
      const dashboardAnalyses = await seoAnalysisService.getUserAnalyses(user.id, {
        limit: 100,
        orderBy: 'created_at',
        orderDirection: 'desc'
      });
      
      console.log('📊 Dashboard would show:', dashboardAnalyses.length, 'analyses');
      
      if (dashboardAnalyses.length > 0) {
        console.log('✅ SUCCESS: Dashboard should now display analyses!');
        console.log('📋 Analyses that should appear:', dashboardAnalyses.map(a => ({
          id: a.id,
          url: a.url,
          overall_score: a.overall_score,
          created_at: a.created_at
        })));
      } else {
        console.error('❌ Dashboard would still show empty state');
      }
    } else {
      console.error('❌ Dashboard queries would be DISABLED');
    }
    
    console.log('\n=== 🧹 STEP 7: Cleanup ===');
    
    const shouldCleanup = confirm('Delete the test analysis? (Recommended to keep database clean)');
    if (shouldCleanup) {
      try {
        await seoAnalysisService.deleteAnalysis(savedAnalysis.id);
        console.log('🗑️ Test analysis cleaned up');
      } catch (cleanupError) {
        console.warn('⚠️ Could not clean up test analysis:', cleanupError);
      }
    }
    
    console.log('\n=== 🏁 TEST COMPLETE ===');
    console.log('✅ All steps completed successfully!');
    console.log('💡 If Dashboard still shows empty, try refreshing the page or switching tabs');
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  }
}

// Run the complete test
testCompleteFlow();
