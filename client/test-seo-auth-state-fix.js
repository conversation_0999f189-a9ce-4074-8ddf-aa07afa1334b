// Test script to verify SEO authentication state detection fix
// Run this to test that Dashboard properly detects user authentication

console.log('🔐 Testing SEO Authentication State Detection Fix');
console.log('================================================');

async function testAuthStateDetection() {
  console.log('\n🔍 Step 1: Testing useAuth Hook Properties');
  
  try {
    // Import the useAuth hook to check its structure
    const authModule = await import('/src/hooks/use-auth.tsx');
    console.log('✅ useAuth hook imported successfully');
    
    // We can't directly call the hook outside React, but we can check what's available
    console.log('📊 useAuth module exports:', Object.keys(authModule));
    
    return true;
  } catch (error) {
    console.error('❌ useAuth hook import failed:', error);
    return false;
  }
}

async function testSupabaseAuthState() {
  console.log('\n🔐 Step 2: Testing Supabase Authentication State');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    console.log('🔄 Getting current user session...');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Supabase auth error:', error);
      return { user: null, error };
    }
    
    console.log('📊 Supabase user state:', {
      exists: !!user,
      id: user?.id || 'No ID',
      email: user?.email || 'No email',
      isAnonymous: user?.id === 'anonymous',
      createdAt: user?.created_at || 'No creation date'
    });
    
    // Test authentication state derivation logic
    const isAuthenticated = !!user && user.id !== 'anonymous';
    console.log('✅ Derived authentication state:', isAuthenticated);
    
    return { user, error: null, isAuthenticated };
  } catch (error) {
    console.error('❌ Supabase auth test failed:', error);
    return { user: null, error };
  }
}

async function testAuthStateInComponents() {
  console.log('\n🧩 Step 3: Testing Auth State in Components');
  
  try {
    // Check if we can access React context or component state
    // Look for auth-related elements in the DOM
    const authElements = {
      loginButtons: document.querySelectorAll('[class*="login"], [class*="sign-in"]').length,
      userMenus: document.querySelectorAll('[class*="user-menu"], [class*="profile"]').length,
      authForms: document.querySelectorAll('form[class*="auth"], form[class*="login"]').length
    };
    
    console.log('🔍 Auth-related DOM elements:', authElements);
    
    // Check for user-specific content
    const userContent = {
      dashboardTabs: document.querySelectorAll('[role="tab"]').length,
      analysisCards: document.querySelectorAll('[class*="card"]').length,
      userSpecificText: Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent && (
          el.textContent.includes('Inicia sesión') ||
          el.textContent.includes('Sign in') ||
          el.textContent.includes('Bienvenido') ||
          el.textContent.includes('Welcome')
        )).length
    };
    
    console.log('👤 User-specific content:', userContent);
    
    return { authElements, userContent };
  } catch (error) {
    console.error('❌ Component auth state test failed:', error);
    return null;
  }
}

async function testDashboardAuthDetection() {
  console.log('\n📊 Step 4: Testing Dashboard Authentication Detection');
  
  try {
    // Navigate to Dashboard tab
    const dashboardTab = Array.from(document.querySelectorAll('button[role="tab"]'))
      .find(tab => tab.textContent?.includes('Dashboard'));
    
    if (!dashboardTab) {
      console.log('⚠️ Dashboard tab not found');
      return false;
    }
    
    console.log('🔄 Clicking Dashboard tab...');
    dashboardTab.click();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check for authentication-related messages
    const authMessages = Array.from(document.querySelectorAll('*'))
      .filter(el => el.textContent && (
        el.textContent.includes('Inicia sesión') ||
        el.textContent.includes('Sign in') ||
        el.textContent.includes('not authenticated') ||
        el.textContent.includes('User not authenticated') ||
        el.textContent.includes('Cargando') ||
        el.textContent.includes('Loading')
      ))
      .map(el => el.textContent.trim());
    
    console.log('🔐 Authentication messages found:', authMessages);
    
    // Check for analysis content
    const analysisContent = {
      cards: document.querySelectorAll('[class*="card"]').length,
      buttons: Array.from(document.querySelectorAll('button'))
        .filter(btn => btn.textContent?.includes('Ver resultados')).length,
      emptyStates: Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent && (
          el.textContent.includes('No hay análisis') ||
          el.textContent.includes('No tienes análisis')
        )).length
    };
    
    console.log('📋 Analysis content found:', analysisContent);
    
    return { authMessages, analysisContent };
  } catch (error) {
    console.error('❌ Dashboard auth detection test failed:', error);
    return null;
  }
}

async function testQueryExecution(user) {
  console.log('\n🔄 Step 5: Testing Query Execution with Fixed Auth');
  
  if (!user) {
    console.log('⚠️ No user provided, skipping query test');
    return false;
  }
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Test the authentication logic that the components use
    const authLoading = false; // Simulate auth loading complete
    const isAuthenticated = !!user && user.id !== 'anonymous';
    const queryEnabled = !authLoading && !!user?.id && isAuthenticated;
    
    console.log('🔍 Query execution conditions:', {
      authLoading,
      userExists: !!user,
      userId: user?.id,
      isAuthenticated,
      queryEnabled
    });
    
    if (!queryEnabled) {
      console.log('❌ Query would not execute due to auth conditions');
      return false;
    }
    
    console.log('🔄 Testing getUserAnalyses with fixed auth...');
    const analyses = await seoAnalysisService.getUserAnalyses(user.id, {
      limit: 5,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    console.log('✅ Query executed successfully:', {
      count: analyses.length,
      sample: analyses.slice(0, 2).map(a => ({
        id: a.id,
        url: a.url,
        status: a.status,
        created_at: a.created_at
      }))
    });
    
    return analyses;
  } catch (error) {
    console.error('❌ Query execution test failed:', error);
    return null;
  }
}

async function testSessionConsistency() {
  console.log('\n🔄 Step 6: Testing Session Consistency');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Get session multiple times to check consistency
    console.log('🔄 Testing session consistency...');
    
    const session1 = await supabase.auth.getUser();
    await new Promise(resolve => setTimeout(resolve, 100));
    const session2 = await supabase.auth.getUser();
    await new Promise(resolve => setTimeout(resolve, 100));
    const session3 = await supabase.auth.getUser();
    
    const user1 = session1.data?.user;
    const user2 = session2.data?.user;
    const user3 = session3.data?.user;
    
    const consistent = (
      user1?.id === user2?.id &&
      user2?.id === user3?.id &&
      user1?.email === user2?.email &&
      user2?.email === user3?.email
    );
    
    console.log('📊 Session consistency test:', {
      consistent,
      user1Id: user1?.id,
      user2Id: user2?.id,
      user3Id: user3?.id,
      allSame: user1?.id === user2?.id && user2?.id === user3?.id
    });
    
    return { consistent, user: user1 };
  } catch (error) {
    console.error('❌ Session consistency test failed:', error);
    return { consistent: false, user: null };
  }
}

async function runAuthStateFixTest() {
  console.log('🚀 Starting SEO Authentication State Fix Test...\n');
  
  // Step 1: Test useAuth hook
  const hookResult = await testAuthStateDetection();
  
  // Step 2: Test Supabase auth state
  const supabaseResult = await testSupabaseAuthState();
  
  // Step 3: Test auth state in components
  const componentResult = await testAuthStateInComponents();
  
  // Step 4: Test Dashboard auth detection
  const dashboardResult = await testDashboardAuthDetection();
  
  // Step 5: Test query execution
  const queryResult = await testQueryExecution(supabaseResult?.user);
  
  // Step 6: Test session consistency
  const sessionResult = await testSessionConsistency();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 AUTHENTICATION STATE FIX TEST RESULTS');
  console.log('='.repeat(60));
  
  const authWorking = supabaseResult?.isAuthenticated;
  const sessionConsistent = sessionResult?.consistent;
  const queryWorking = queryResult && Array.isArray(queryResult);
  const dashboardDetecting = dashboardResult && dashboardResult.analysisContent.cards > 0;
  
  console.log('🔐 Authentication Status:');
  console.log(`  Hook import: ${hookResult ? '✅ Working' : '❌ Failed'}`);
  console.log(`  Supabase auth: ${authWorking ? '✅ Authenticated' : '❌ Not authenticated'}`);
  console.log(`  Session consistency: ${sessionConsistent ? '✅ Consistent' : '❌ Inconsistent'}`);
  console.log(`  Query execution: ${queryWorking ? '✅ Working' : '❌ Failed'}`);
  console.log(`  Dashboard detection: ${dashboardDetecting ? '✅ Working' : '❌ Not working'}`);
  
  if (authWorking && sessionConsistent && queryWorking) {
    console.log('\n🎉 SUCCESS: Authentication state fix working!');
    console.log('✅ useAuth hook properties corrected');
    console.log('✅ Authentication state properly derived');
    console.log('✅ Query enabled conditions fixed');
    console.log('✅ Session consistency maintained');
    console.log('✅ Dashboard should now display analyses');
  } else if (!authWorking) {
    console.log('\n⚠️ USER NOT AUTHENTICATED');
    console.log('💡 Please sign in and run this test again');
    console.log('🔑 The fix should work once authenticated');
  } else {
    console.log('\n❌ ISSUES DETECTED');
    console.log('🔧 Check the errors above for specific problems');
    
    if (!sessionConsistent) {
      console.log('⚠️ Session consistency issues detected');
    }
    if (!queryWorking) {
      console.log('⚠️ Query execution issues detected');
    }
    if (!dashboardDetecting) {
      console.log('⚠️ Dashboard still not detecting auth properly');
    }
  }
  
  console.log('\n📋 EXPECTED BEHAVIOR AFTER FIX:');
  console.log('1. Dashboard detects authentication state correctly');
  console.log('2. Queries execute when user is authenticated');
  console.log('3. Same user session used for saving and retrieving');
  console.log('4. No more "isAuthenticated" undefined errors');
  console.log('5. Dashboard displays analyses for authenticated users');
}

// Auto-run the test
runAuthStateFixTest();

// Export for manual use
window.testAuthStateFix = {
  runAuthStateFixTest,
  testAuthStateDetection,
  testSupabaseAuthState,
  testAuthStateInComponents,
  testDashboardAuthDetection,
  testQueryExecution,
  testSessionConsistency
};

console.log('\n💡 Manual testing available:');
console.log('- window.testAuthStateFix.runAuthStateFixTest()');
console.log('- window.testAuthStateFix.testSupabaseAuthState()');
