/**
 * Test Query Client Access and Invalidation
 * Verify that the queryClient is accessible and invalidation works
 */

console.log('🔍 Testing Query Client Access and Invalidation');

async function testQueryClientAccess() {
  console.log('🚀 Starting query client access test...');
  
  try {
    // Step 1: Check if queryClient is available
    console.log('1️⃣ QUERY CLIENT AVAILABILITY CHECK');
    
    if (!window.queryClient) {
      console.log('❌ window.queryClient not available');
      console.log('🔍 Available window properties:', Object.keys(window).filter(k => k.includes('query')));
      return false;
    }
    
    console.log('✅ window.queryClient is available');
    console.log('📊 QueryClient type:', typeof window.queryClient);
    
    const queryClient = window.queryClient;
    
    // Step 2: Check authentication
    console.log('2️⃣ AUTHENTICATION CHECK');
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ User not authenticated');
      return false;
    }
    console.log('✅ User authenticated:', user.email);
    
    // Step 3: Examine query cache
    console.log('3️⃣ QUERY CACHE EXAMINATION');
    const cache = queryClient.getQueryCache();
    const allQueries = cache.getAll();
    
    console.log('📊 Total queries in cache:', allQueries.length);
    
    // List all queries
    allQueries.forEach((query, index) => {
      console.log(`  ${index + 1}. ${JSON.stringify(query.queryKey)} - ${query.state.status} - Data: ${query.state.data?.length || 'no data'}`);
    });
    
    // Step 4: Find design analysis query
    console.log('4️⃣ DESIGN ANALYSIS QUERY CHECK');
    const designQuery = cache.find(['design-analyses', user.id]);
    
    if (designQuery) {
      console.log('✅ Found design analysis query');
      console.log('📊 Query state:', {
        status: designQuery.state.status,
        isFetching: designQuery.state.isFetching,
        isStale: designQuery.state.isStale,
        dataLength: designQuery.state.data?.length || 0,
        lastUpdated: new Date(designQuery.state.dataUpdatedAt),
        staleTime: designQuery.options?.staleTime
      });
    } else {
      console.log('❌ Design analysis query not found');
      console.log('🔍 Creating query by fetching data...');
      
      // Try to trigger the query by calling the service
      try {
        const data = await window.designAnalysisService.getUserAnalyses(user.id, 10);
        console.log('📊 Service returned:', data.length, 'analyses');
        
        // Check if query was created
        const newQuery = cache.find(['design-analyses', user.id]);
        if (newQuery) {
          console.log('✅ Query created after service call');
        } else {
          console.log('❌ Query still not found after service call');
        }
      } catch (serviceError) {
        console.log('❌ Service call failed:', serviceError.message);
      }
    }
    
    // Step 5: Test invalidation
    console.log('5️⃣ INVALIDATION TEST');
    
    // Monitor invalidation calls
    const originalInvalidate = queryClient.invalidateQueries.bind(queryClient);
    let invalidationCalls = [];
    
    queryClient.invalidateQueries = function(filters) {
      const call = {
        timestamp: new Date().toISOString(),
        filters: filters,
        matchedQueries: cache.findAll(filters).length
      };
      invalidationCalls.push(call);
      console.log('🔄 invalidateQueries called:', call);
      
      return originalInvalidate(filters);
    };
    
    // Test invalidation
    console.log('🔄 Testing invalidation with correct key...');
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    console.log('📊 Invalidation calls made:', invalidationCalls.length);
    invalidationCalls.forEach((call, index) => {
      console.log(`  ${index + 1}. ${call.timestamp} - Matched: ${call.matchedQueries} queries`);
    });
    
    // Step 6: Check query state after invalidation
    console.log('6️⃣ POST-INVALIDATION STATE CHECK');
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const postInvalidationQuery = cache.find(['design-analyses', user.id]);
    if (postInvalidationQuery) {
      console.log('📊 Query state after invalidation:', {
        status: postInvalidationQuery.state.status,
        isFetching: postInvalidationQuery.state.isFetching,
        isStale: postInvalidationQuery.state.isStale,
        dataLength: postInvalidationQuery.state.data?.length || 0
      });
    }
    
    // Step 7: Test complete save and invalidate cycle
    console.log('7️⃣ COMPLETE SAVE AND INVALIDATE CYCLE');
    
    const testData = {
      user_id: user.id,
      original_filename: 'query-client-test.png',
      file_size: 2048,
      file_type: 'image/png',
      file_url: null,
      overall_score: 87,
      complexity_scores: { visual: 85, cognitive: 87, structural: 89 },
      analysis_areas: [{
        name: 'Layout',
        score: 87,
        description: 'Query client test',
        recommendations: ['Test recommendation']
      }],
      recommendations: ['Test recommendation'],
      ai_analysis_summary: 'Query client test',
      gemini_analysis: 'Query client test',
      agent_message: 'Query client test',
      visuai_insights: 'Query client test',
      tags: ['query-client-test']
    };
    
    // Get initial count
    const initialQuery = cache.find(['design-analyses', user.id]);
    const initialCount = initialQuery?.state.data?.length || 0;
    console.log('📊 Initial analysis count:', initialCount);
    
    // Save analysis
    console.log('💾 Saving test analysis...');
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testData, null);
    
    if (!savedAnalysis) {
      console.log('❌ Failed to save analysis');
      return false;
    }
    
    console.log('✅ Analysis saved:', savedAnalysis.id);
    
    // Invalidate
    console.log('🔄 Invalidating queries...');
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    // Wait for refetch
    console.log('⏳ Waiting for refetch...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check final state
    const finalQuery = cache.find(['design-analyses', user.id]);
    const finalCount = finalQuery?.state.data?.length || 0;
    const foundInQuery = finalQuery?.state.data?.find(a => a.id === savedAnalysis.id);
    
    console.log('📊 Final analysis count:', finalCount);
    console.log('📊 Count difference:', finalCount - initialCount);
    
    if (foundInQuery) {
      console.log('✅ New analysis found in query data');
    } else {
      console.log('❌ New analysis NOT found in query data');
      
      // Debug: Check if it's in the service but not in the query
      const serviceData = await window.designAnalysisService.getUserAnalyses(user.id, 10);
      const foundInService = serviceData.find(a => a.id === savedAnalysis.id);
      
      if (foundInService) {
        console.log('⚠️ Analysis found in service but not in query - cache issue!');
      } else {
        console.log('❌ Analysis not found in service either');
      }
    }
    
    // Clean up
    console.log('🧹 Cleaning up...');
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    
    // Final invalidation
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    // Restore original function
    queryClient.invalidateQueries = originalInvalidate;
    
    console.log('✅ Query client access test completed');
    return foundInQuery !== undefined;
    
  } catch (error) {
    console.error('❌ Query client access test failed:', error);
    return false;
  }
}

// Helper function to force refresh the query
async function forceRefreshQuery() {
  if (!window.queryClient) {
    console.log('❌ QueryClient not available');
    return;
  }
  
  const { data: { user } } = await window.supabase.auth.getUser();
  if (!user) {
    console.log('❌ User not authenticated');
    return;
  }
  
  console.log('🔄 Force refreshing design analysis query...');
  
  const queryClient = window.queryClient;
  
  // Remove the query from cache and refetch
  queryClient.removeQueries({ queryKey: ['design-analyses', user.id] });
  
  // Trigger a fresh fetch
  const data = await window.designAnalysisService.getUserAnalyses(user.id, 10);
  console.log('📊 Fresh data:', data.length, 'analyses');
  
  // Check if query was recreated
  const cache = queryClient.getQueryCache();
  const newQuery = cache.find(['design-analyses', user.id]);
  
  if (newQuery) {
    console.log('✅ Query recreated with fresh data');
    console.log('📊 Query data length:', newQuery.state.data?.length || 0);
  } else {
    console.log('❌ Query not recreated');
  }
}

// Run the test
testQueryClientAccess().then(success => {
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('🎉 QUERY CLIENT ACCESS TEST: SUCCESS');
    console.log('✅ QueryClient is accessible and working');
  } else {
    console.log('💥 QUERY CLIENT ACCESS TEST: FAILED');
    console.log('❌ QueryClient access or invalidation issues detected');
  }
  console.log('='.repeat(50));
}).catch(error => {
  console.error('💥 Test execution failed:', error);
});

// Export functions
window.testQueryClientAccess = testQueryClientAccess;
window.forceRefreshQuery = forceRefreshQuery;
console.log('🔧 Functions available: window.testQueryClientAccess(), window.forceRefreshQuery()');
