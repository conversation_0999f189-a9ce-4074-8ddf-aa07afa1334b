// Test script to verify the SEO Dashboard analysis loading fix
// Run this to test that clicking saved analyses loads results in current tab

console.log('🔧 Testing SEO Dashboard Analysis Loading Fix');
console.log('==============================================');

async function testDashboardFix() {
  console.log('\n🔍 Step 1: Checking Current Interface');
  
  try {
    // Check current URL
    const currentUrl = window.location.href;
    console.log('📍 Current URL:', currentUrl);
    
    // Check if we're on the SEO Analyzer page
    if (!currentUrl.includes('seo-analyzer')) {
      console.log('⚠️ Not on SEO Analyzer page - navigate there first');
      console.log('💡 Go to: /dashboard/herramientas/seo-analyzer');
      return false;
    }
    
    // Look for tabs
    const analyzerTab = Array.from(document.querySelectorAll('button[role="tab"]'))
      .find(tab => tab.textContent?.includes('Analizador'));
    const dashboardTab = Array.from(document.querySelectorAll('button[role="tab"]'))
      .find(tab => tab.textContent?.includes('Dashboard'));
    
    if (!analyzerTab || !dashboardTab) {
      console.error('❌ Required tabs not found');
      return false;
    }
    
    console.log('✅ Found required tabs:', {
      analyzer: !!analyzerTab,
      dashboard: !!dashboardTab
    });
    
    return { analyzerTab, dashboardTab };
  } catch (error) {
    console.error('❌ Interface check failed:', error);
    return false;
  }
}

async function testDashboardNavigation(tabData) {
  console.log('\n📊 Step 2: Testing Dashboard Navigation');
  
  try {
    const { dashboardTab } = tabData;
    
    // Click on Dashboard tab
    console.log('🔄 Clicking Dashboard tab...');
    dashboardTab.click();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Look for analysis cards or buttons
    const analysisButtons = Array.from(document.querySelectorAll('button'))
      .filter(btn => btn.textContent?.includes('Ver resultados') || btn.textContent?.includes('Ver análisis'));
    
    console.log('📋 Found analysis buttons:', analysisButtons.length);
    
    if (analysisButtons.length === 0) {
      console.log('⚠️ No analysis buttons found');
      console.log('💡 This might mean:');
      console.log('  - No saved analyses exist');
      console.log('  - Dashboard is still loading');
      console.log('  - User is not authenticated');
      return null;
    }
    
    // Check button properties
    const firstButton = analysisButtons[0];
    console.log('🔍 First analysis button:', {
      text: firstButton.textContent?.trim(),
      disabled: firstButton.disabled,
      hasClickHandler: !!firstButton.onclick || firstButton.getAttribute('onclick')
    });
    
    return analysisButtons;
  } catch (error) {
    console.error('❌ Dashboard navigation test failed:', error);
    return null;
  }
}

async function testAnalysisLoading(analysisButtons, tabData) {
  console.log('\n🚀 Step 3: Testing Analysis Loading');
  
  if (!analysisButtons || analysisButtons.length === 0) {
    console.log('⚠️ No analysis buttons to test');
    return false;
  }
  
  try {
    const { analyzerTab } = tabData;
    const firstButton = analysisButtons[0];
    
    // Monitor tab changes
    let tabChanged = false;
    const originalActiveTab = document.querySelector('[role="tab"][aria-selected="true"]');
    
    console.log('🔄 Clicking first analysis button...');
    
    // Set up a listener for tab changes
    const checkTabChange = () => {
      const newActiveTab = document.querySelector('[role="tab"][aria-selected="true"]');
      if (newActiveTab !== originalActiveTab) {
        tabChanged = true;
        console.log('✅ Tab changed to:', newActiveTab?.textContent?.trim());
      }
    };
    
    // Monitor for changes
    const observer = new MutationObserver(checkTabChange);
    observer.observe(document.body, { 
      attributes: true, 
      subtree: true, 
      attributeFilter: ['aria-selected'] 
    });
    
    // Click the button
    firstButton.click();
    
    // Wait for potential changes
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    observer.disconnect();
    
    // Check if we're now on the Analyzer tab
    const currentActiveTab = document.querySelector('[role="tab"][aria-selected="true"]');
    const isOnAnalyzerTab = currentActiveTab?.textContent?.includes('Analizador');
    
    console.log('📊 After clicking analysis button:', {
      tabChanged: tabChanged,
      currentTab: currentActiveTab?.textContent?.trim(),
      isOnAnalyzerTab: isOnAnalyzerTab
    });
    
    // Check for analysis results
    const resultsElements = {
      seoScore: document.querySelector('[class*="score"], [data-testid*="score"]'),
      recommendations: document.querySelectorAll('[class*="recommendation"]').length,
      achievements: document.querySelectorAll('[class*="achievement"]').length,
      charts: document.querySelectorAll('canvas, svg').length,
      analysisContent: document.querySelector('[class*="analysis"], [class*="result"]')
    };
    
    console.log('📈 Analysis results found:', resultsElements);
    
    const hasResults = Object.values(resultsElements).some(val => 
      typeof val === 'number' ? val > 0 : !!val
    );
    
    if (hasResults) {
      console.log('✅ Analysis results detected in current tab');
    } else {
      console.log('⚠️ No analysis results detected');
    }
    
    return { tabChanged, isOnAnalyzerTab, hasResults };
  } catch (error) {
    console.error('❌ Analysis loading test failed:', error);
    return false;
  }
}

async function testNewTabPrevention() {
  console.log('\n🚫 Step 4: Testing New Tab Prevention');
  
  try {
    // Count current tabs/windows
    const initialTabCount = window.history.length;
    
    // Override window.open to detect if it's called
    let windowOpenCalled = false;
    const originalWindowOpen = window.open;
    window.open = function(...args) {
      windowOpenCalled = true;
      console.log('⚠️ window.open() was called with:', args);
      return originalWindowOpen.apply(this, args);
    };
    
    // Find and click an analysis button again
    const analysisButtons = Array.from(document.querySelectorAll('button'))
      .filter(btn => btn.textContent?.includes('Ver resultados'));
    
    if (analysisButtons.length > 0) {
      console.log('🔄 Testing new tab prevention...');
      analysisButtons[0].click();
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('📊 New tab prevention test:', {
        windowOpenCalled: windowOpenCalled,
        tabCountChanged: window.history.length !== initialTabCount
      });
      
      if (!windowOpenCalled) {
        console.log('✅ No new tab opened - fix working correctly');
      } else {
        console.log('❌ New tab was opened - fix not working');
      }
    } else {
      console.log('⚠️ No analysis buttons found for new tab test');
    }
    
    // Restore original window.open
    window.open = originalWindowOpen;
    
    return !windowOpenCalled;
  } catch (error) {
    console.error('❌ New tab prevention test failed:', error);
    return false;
  }
}

async function runDashboardFixTest() {
  console.log('🚀 Starting SEO Dashboard Fix Test...\n');
  
  // Step 1: Check interface
  const interfaceResult = await testDashboardFix();
  if (!interfaceResult) {
    console.log('\n❌ Interface check failed - stopping tests');
    return;
  }
  
  // Step 2: Test dashboard navigation
  const analysisButtons = await testDashboardNavigation(interfaceResult);
  
  // Step 3: Test analysis loading
  const loadingResult = await testAnalysisLoading(analysisButtons, interfaceResult);
  
  // Step 4: Test new tab prevention
  const preventionResult = await testNewTabPrevention();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 DASHBOARD FIX TEST RESULTS');
  console.log('='.repeat(60));
  
  if (loadingResult && loadingResult.isOnAnalyzerTab && loadingResult.hasResults && preventionResult) {
    console.log('🎉 SUCCESS: Dashboard fix working correctly!');
    console.log('✅ Analysis buttons found');
    console.log('✅ Clicking loads results in current tab');
    console.log('✅ Switches to Analyzer tab automatically');
    console.log('✅ Analysis results displayed');
    console.log('✅ No new tabs opened');
    console.log('\n💡 The Dashboard now properly loads analyses in the current tab!');
  } else if (!analysisButtons || analysisButtons.length === 0) {
    console.log('⚠️ NO ANALYSES TO TEST');
    console.log('💡 Create some SEO analyses first, then test the Dashboard');
    console.log('📋 Steps to test:');
    console.log('1. Go to Analizador tab');
    console.log('2. Run an SEO analysis');
    console.log('3. Go to Dashboard tab');
    console.log('4. Click "Ver resultados" on a saved analysis');
  } else {
    console.log('❌ ISSUES DETECTED');
    console.log('🔧 Check the errors above for specific problems');
    
    if (loadingResult) {
      console.log('📊 Loading test results:', {
        tabChanged: loadingResult.tabChanged,
        isOnAnalyzerTab: loadingResult.isOnAnalyzerTab,
        hasResults: loadingResult.hasResults
      });
    }
  }
  
  console.log('\n📋 EXPECTED BEHAVIOR:');
  console.log('1. Click "Ver resultados" on any saved analysis');
  console.log('2. Should switch to "Analizador" tab automatically');
  console.log('3. Should display the complete analysis results');
  console.log('4. Should NOT open a new browser tab');
}

// Auto-run the test
runDashboardFixTest();

// Export for manual use
window.testDashboardFix = {
  runDashboardFixTest,
  testDashboardFix,
  testDashboardNavigation,
  testAnalysisLoading,
  testNewTabPrevention
};

console.log('\n💡 Manual testing available:');
console.log('- window.testDashboardFix.runDashboardFixTest()');
console.log('- window.testDashboardFix.testAnalysisLoading(buttons, tabs)');
