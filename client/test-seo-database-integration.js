// Test script for SEO Analyzer database integration
// Run this in the browser console to test the database connection and functionality

console.log('🧪 SEO Analyzer Database Integration Test');

async function testSEODatabaseIntegration() {
  console.log('\n📡 Step 1: Testing Database Connection...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('seo_analyses')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error);
      return false;
    }
    
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Failed to test database connection:', error);
    return false;
  }
}

async function testAuthentication() {
  console.log('\n🔐 Step 2: Testing Authentication...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.warn('⚠️ User not authenticated - some tests will be skipped');
      return null;
    }
    
    console.log('✅ User authenticated:', {
      userId: user.id,
      email: user.email
    });
    
    return user;
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return null;
  }
}

async function testSEOAnalysisService() {
  console.log('\n🔧 Step 3: Testing SEO Analysis Service...');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('✅ SEO Analysis Service imported successfully');
    
    // Test getting recent analyses (should work even with empty data)
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    console.log('✅ Recent analyses retrieved:', {
      count: recentAnalyses.length
    });
    
    // Test getting favorite analyses
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    console.log('✅ Favorite analyses retrieved:', {
      count: favoriteAnalyses.length
    });
    
    return true;
  } catch (error) {
    console.error('❌ SEO Analysis Service test failed:', error);
    return false;
  }
}

async function testDirectQuery(userId) {
  console.log('\n📊 Step 4: Testing Direct Database Query...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test with explicit column selection to avoid schema issues
    const { data, error } = await supabase
      .from('seo_analyses')
      .select(`
        id,
        created_at,
        user_id,
        url,
        analysis_mode,
        tool_type,
        overall_score,
        is_favorite
      `)
      .eq('user_id', userId)
      .limit(5);
    
    if (error) {
      console.error('❌ Direct query failed:', {
        error: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return false;
    }
    
    console.log('✅ Direct query successful:', {
      recordsFound: data?.length || 0,
      sampleRecord: data?.[0] || 'No records'
    });
    
    return true;
  } catch (error) {
    console.error('❌ Direct query error:', error);
    return false;
  }
}

async function testRLSPolicies(userId) {
  console.log('\n🔒 Step 5: Testing RLS Policies...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test that we can only see our own data
    const { data: userAnalyses, error } = await supabase
      .from('seo_analyses')
      .select('user_id')
      .limit(10);
    
    if (error) {
      console.error('❌ RLS test failed:', error);
      return false;
    }
    
    // Check that all returned analyses belong to the current user
    const allBelongToUser = userAnalyses.every(analysis => analysis.user_id === userId);
    
    if (allBelongToUser) {
      console.log('✅ RLS Policy Working: All analyses belong to current user');
    } else {
      console.error('❌ RLS Policy Failed: Found analyses from other users');
      return false;
    }
    
    console.log(`✅ Direct query returned ${userAnalyses.length} analyses`);
    
    return true;
  } catch (error) {
    console.error('❌ RLS test error:', error);
    return false;
  }
}

async function testSEOAnalysisHook() {
  console.log('\n🪝 Step 6: Testing SEO Analysis Hook...');
  
  try {
    const { useSEOAnalysisHistory } = await import('/src/components/tools/seo-analyzer/hooks/useSEOAnalysisHistory.ts');
    
    console.log('✅ SEO Analysis Hook imported successfully');
    
    return true;
  } catch (error) {
    console.error('❌ SEO Analysis Hook test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting comprehensive SEO database integration tests...\n');
  
  const dbTestResult = await testSEODatabaseIntegration();
  if (!dbTestResult) {
    console.log('\n❌ Database connection failed - stopping tests');
    return;
  }
  
  const user = await testAuthentication();
  const serviceTestResult = await testSEOAnalysisService();
  const hookTestResult = await testSEOAnalysisHook();
  
  let directQueryResult = true;
  let rlsTestResult = true;
  
  if (user) {
    directQueryResult = await testDirectQuery(user.id);
    rlsTestResult = await testRLSPolicies(user.id);
  } else {
    console.log('\n⚠️ Skipping user-specific tests (not authenticated)');
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL RESULT');
  console.log('='.repeat(60));
  
  if (dbTestResult && serviceTestResult && hookTestResult && directQueryResult && rlsTestResult) {
    console.log('🎉 SUCCESS: SEO Analyzer database integration is working correctly!');
    console.log('✅ Supabase connection established');
    console.log('✅ Authentication working');
    console.log('✅ RLS policies enforced');
    console.log('✅ Service layer functional');
    console.log('✅ React hooks ready');
    console.log('✅ Database schema properly configured');
  } else {
    console.log('❌ FAILURE: SEO Analyzer database integration has issues');
    console.log('🔧 Please check the errors above and fix them');
  }
}

// Auto-run the tests
runAllTests();
