/**
 * Comprehensive test for Visual Complexity Analyzer image loading fixes
 * Tests the complete flow from loading saved analyses to displaying images
 */

console.log('🧪 Starting Image Loading Fixes Tests...');

// Test configuration
const TEST_CONFIG = {
  waitTime: 2000,
  maxRetries: 3,
  testTimeout: 30000
};

// Helper function to wait
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to find elements with retry
const findElement = async (selector, retries = 3) => {
  for (let i = 0; i < retries; i++) {
    const element = document.querySelector(selector);
    if (element) return element;
    await wait(1000);
  }
  return null;
};

// Test 1: Verify tab switching behavior when loading analysis
async function testTabSwitchingBehavior() {
  console.log('\n📋 Test 1: Tab Switching Behavior');
  
  try {
    // Navigate to history tab
    const historyTab = await findElement('[value="history"]');
    if (!historyTab) {
      console.log('❌ History tab not found');
      return false;
    }
    
    historyTab.click();
    await wait(TEST_CONFIG.waitTime);
    
    // Find analysis cards
    const analysisCards = document.querySelectorAll('[class*="analysis-card"], .card');
    if (analysisCards.length === 0) {
      console.log('⚠️ No saved analyses found to test');
      return true; // Not an error if no analyses exist
    }
    
    console.log(`📊 Found ${analysisCards.length} analysis cards`);
    
    // Find and click load button on first card
    const firstCard = analysisCards[0];
    const loadButton = firstCard.querySelector('button:contains("Cargar"), button[class*="load"]');
    
    if (!loadButton) {
      console.log('❌ Load button not found');
      return false;
    }
    
    console.log('🔄 Clicking load button...');
    loadButton.click();
    
    // Wait for loading to complete
    await wait(3000);
    
    // Check if we switched to theory tab (not analyze tab)
    const theoryTab = document.querySelector('[value="theory"]');
    const analyzeTab = document.querySelector('[value="analyze"]');
    
    if (!theoryTab) {
      console.log('❌ Theory tab not found');
      return false;
    }
    
    // Check if theory tab is active
    const isTheoryActive = theoryTab.getAttribute('data-state') === 'active' || 
                          theoryTab.classList.contains('active') ||
                          theoryTab.getAttribute('aria-selected') === 'true';
    
    if (isTheoryActive) {
      console.log('✅ Correctly switched to theory tab (Resultados Detallados)');
      return true;
    } else {
      console.log('❌ Did not switch to theory tab as expected');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error testing tab switching:', error);
    return false;
  }
}

// Test 2: Verify image placeholder for analyses without images
async function testImagePlaceholder() {
  console.log('\n🖼️ Test 2: Image Placeholder for Analyses Without Images');
  
  try {
    // Make sure we're on theory tab
    const theoryTab = await findElement('[value="theory"]');
    if (theoryTab) {
      theoryTab.click();
      await wait(TEST_CONFIG.waitTime);
    }
    
    // Look for image placeholder
    const imagePlaceholder = document.querySelector('[class*="border-dashed"], .border-dashed');
    const placeholderText = document.querySelector('h3:contains("Imagen no disponible"), [class*="text-gray-700"]:contains("Imagen no disponible")');
    
    if (imagePlaceholder && placeholderText) {
      console.log('✅ Image placeholder found with appropriate message');
      
      // Check for informative text
      const informativeText = document.querySelector('p:contains("análisis fue guardado sin"), [class*="text-gray-500"]:contains("análisis fue guardado")');
      if (informativeText) {
        console.log('✅ Informative text about missing image found');
      }
      
      return true;
    } else {
      console.log('⚠️ Image placeholder not found - may indicate image is available or different UI state');
      return true; // Not necessarily an error
    }
    
  } catch (error) {
    console.error('❌ Error testing image placeholder:', error);
    return false;
  }
}

// Test 3: Verify enhanced error logging
async function testEnhancedErrorLogging() {
  console.log('\n🔍 Test 3: Enhanced Error Logging');
  
  try {
    // Capture console logs
    const originalConsoleLog = console.log;
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;
    
    const logs = [];
    const warnings = [];
    const errors = [];
    
    console.log = function(...args) {
      logs.push(args.join(' '));
      originalConsoleLog.apply(console, args);
    };
    
    console.warn = function(...args) {
      warnings.push(args.join(' '));
      originalConsoleWarn.apply(console, args);
    };
    
    console.error = function(...args) {
      errors.push(args.join(' '));
      originalConsoleError.apply(console, args);
    };
    
    // Try to load an analysis to trigger logging
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      historyTab.click();
      await wait(1000);
      
      const analysisCards = document.querySelectorAll('[class*="analysis-card"], .card');
      if (analysisCards.length > 0) {
        const loadButton = analysisCards[0].querySelector('button:contains("Cargar"), button[class*="load"]');
        if (loadButton) {
          loadButton.click();
          await wait(3000);
        }
      }
    }
    
    // Restore original console functions
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
    
    // Check for enhanced logging
    const hasImageLogging = logs.some(log => log.includes('🖼️') || log.includes('Loading specific image'));
    const hasDetailedLogging = logs.some(log => log.includes('getImageUrl called with') || log.includes('file_url'));
    
    console.log(`📊 Captured ${logs.length} logs, ${warnings.length} warnings, ${errors.length} errors`);
    
    if (hasImageLogging) {
      console.log('✅ Enhanced image loading logging detected');
    }
    
    if (hasDetailedLogging) {
      console.log('✅ Detailed debugging logging detected');
    }
    
    return hasImageLogging || hasDetailedLogging;
    
  } catch (error) {
    console.error('❌ Error testing enhanced logging:', error);
    return false;
  }
}

// Test 4: Verify agent message for missing images
async function testAgentMessageForMissingImages() {
  console.log('\n💬 Test 4: Agent Message for Missing Images');
  
  try {
    // Look for agent messages about missing images
    const agentMessages = document.querySelectorAll('[class*="agent-message"], [class*="message"]:not([class*="user"])');
    
    let foundMissingImageMessage = false;
    
    agentMessages.forEach(message => {
      const text = message.textContent || '';
      if (text.includes('imagen original no está disponible') || 
          text.includes('guardado antes de la implementación') ||
          text.includes('análisis fue guardado sin')) {
        foundMissingImageMessage = true;
        console.log('✅ Found agent message about missing image');
      }
    });
    
    if (foundMissingImageMessage) {
      return true;
    } else {
      console.log('⚠️ No agent message about missing images found - may indicate images are available');
      return true; // Not necessarily an error
    }
    
  } catch (error) {
    console.error('❌ Error testing agent messages:', error);
    return false;
  }
}

// Test 5: Verify UI state consistency
async function testUIStateConsistency() {
  console.log('\n🎯 Test 5: UI State Consistency');
  
  try {
    // Check if theory tab is enabled when analysis is loaded
    const theoryTab = document.querySelector('[value="theory"]');
    if (!theoryTab) {
      console.log('❌ Theory tab not found');
      return false;
    }
    
    const isDisabled = theoryTab.hasAttribute('disabled') || theoryTab.getAttribute('aria-disabled') === 'true';
    
    if (!isDisabled) {
      console.log('✅ Theory tab is enabled (analysis loaded)');
      
      // Check if we can see the results content
      theoryTab.click();
      await wait(1000);
      
      const resultsContent = document.querySelector('[value="theory"] + [role="tabpanel"], [class*="TabsContent"][data-state="active"]');
      if (resultsContent) {
        console.log('✅ Results content is visible');
        return true;
      } else {
        console.log('⚠️ Results content not visible');
        return false;
      }
    } else {
      console.log('⚠️ Theory tab is disabled - no analysis loaded');
      return true; // Expected if no analysis is loaded
    }
    
  } catch (error) {
    console.error('❌ Error testing UI state consistency:', error);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Running Image Loading Fixes Tests...\n');
  
  const tests = [
    { name: 'Tab Switching Behavior', fn: testTabSwitchingBehavior },
    { name: 'Image Placeholder', fn: testImagePlaceholder },
    { name: 'Enhanced Error Logging', fn: testEnhancedErrorLogging },
    { name: 'Agent Message for Missing Images', fn: testAgentMessageForMissingImages },
    { name: 'UI State Consistency', fn: testUIStateConsistency }
  ];
  
  const results = [];
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
      if (result) passedTests++;
      
      console.log(`${result ? '✅' : '❌'} ${test.name}: ${result ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      console.error(`❌ ${test.name}: ERROR - ${error.message}`);
      results.push({ name: test.name, passed: false, error: error.message });
    }
  }
  
  console.log(`\n🎯 Overall: ${passedTests}/${tests.length} tests passed`);
  
  if (passedTests === tests.length) {
    console.log('🎉 ALL TESTS PASSED! The image loading fixes are working correctly.');
  } else {
    console.log('⚠️ Some tests failed. The fixes may need additional work.');
  }
  
  return results;
}

// Auto-run if script is executed directly
if (typeof window !== 'undefined') {
  // Wait for the page to load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(runAllTests, 2000);
    });
  } else {
    setTimeout(runAllTests, 2000);
  }
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests };
}
