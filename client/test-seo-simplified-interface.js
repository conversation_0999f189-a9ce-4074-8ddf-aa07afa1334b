// Test script to verify the simplified SEO Analyzer interface
// Run this to test that only Analyzer and Dashboard tabs are working

console.log('🎯 Testing Simplified SEO Analyzer Interface');
console.log('============================================');

async function testSimplifiedInterface() {
  console.log('\n🔍 Step 1: Checking Interface Elements');
  
  try {
    // Check current URL
    const currentUrl = window.location.href;
    console.log('📍 Current URL:', currentUrl);
    
    // Look for SEO Analyzer component
    const seoAnalyzer = document.querySelector('[data-testid="seo-analyzer"]') ||
                       document.querySelector('.seo-analyzer') ||
                       document.querySelector('#seo-analyzer');
    
    if (seoAnalyzer) {
      console.log('✅ SEO Analyzer component found');
    } else {
      console.log('⚠️ SEO Analyzer component not found in current page');
      console.log('💡 Navigate to the SEO Analyzer tool first');
      return false;
    }
    
    // Check for tabs
    const tabs = Array.from(document.querySelectorAll('button[role="tab"], [data-value]'))
      .filter(el => el.textContent && (
        el.textContent.includes('Analizador') ||
        el.textContent.includes('Dashboard') ||
        el.textContent.includes('Historial') ||
        el.textContent.includes('Favoritos')
      ));
    
    console.log('📊 Found tabs:', tabs.map(tab => ({
      text: tab.textContent?.trim(),
      value: tab.getAttribute('data-value') || tab.getAttribute('value'),
      disabled: tab.disabled || tab.hasAttribute('disabled')
    })));
    
    // Check specifically for removed tabs
    const historyTab = tabs.find(tab => 
      tab.textContent?.includes('Historial') || tab.textContent?.includes('History')
    );
    const favoritesTab = tabs.find(tab => 
      tab.textContent?.includes('Favoritos') || tab.textContent?.includes('Favorites')
    );
    
    if (historyTab) {
      console.error('❌ History tab still found - removal incomplete');
      return false;
    } else {
      console.log('✅ History tab successfully removed');
    }
    
    if (favoritesTab) {
      console.error('❌ Favorites tab still found - removal incomplete');
      return false;
    } else {
      console.log('✅ Favorites tab successfully removed');
    }
    
    // Check for remaining tabs
    const analyzerTab = tabs.find(tab => 
      tab.textContent?.includes('Analizador') || tab.textContent?.includes('Analyzer')
    );
    const dashboardTab = tabs.find(tab => 
      tab.textContent?.includes('Dashboard')
    );
    
    if (analyzerTab) {
      console.log('✅ Analyzer tab found');
    } else {
      console.error('❌ Analyzer tab not found');
      return false;
    }
    
    if (dashboardTab) {
      console.log('✅ Dashboard tab found');
    } else {
      console.error('❌ Dashboard tab not found');
      return false;
    }
    
    return { analyzerTab, dashboardTab, tabs };
  } catch (error) {
    console.error('❌ Interface check failed:', error);
    return false;
  }
}

async function testTabNavigation(tabData) {
  console.log('\n🖱️ Step 2: Testing Tab Navigation');
  
  try {
    const { analyzerTab, dashboardTab } = tabData;
    
    // Test clicking Analyzer tab
    console.log('🔄 Testing Analyzer tab click...');
    analyzerTab.click();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Check if analyzer content is visible
    const analyzerContent = document.querySelector('[data-value="analyzer"], .analyzer-content') ||
                           document.querySelector('form') || // Analysis form
                           document.querySelector('input[placeholder*="URL"], input[placeholder*="url"]');
    
    if (analyzerContent) {
      console.log('✅ Analyzer tab content visible');
    } else {
      console.log('⚠️ Analyzer tab content not clearly visible');
    }
    
    // Test clicking Dashboard tab
    console.log('🔄 Testing Dashboard tab click...');
    dashboardTab.click();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Check if dashboard content is visible
    const dashboardContent = document.querySelector('[data-value="dashboard"], .dashboard-content') ||
                            document.querySelector('.dashboard') ||
                            document.querySelector('[class*="dashboard"]');
    
    if (dashboardContent) {
      console.log('✅ Dashboard tab content visible');
    } else {
      console.log('⚠️ Dashboard tab content not clearly visible');
    }
    
    // Switch back to Analyzer tab
    console.log('🔄 Switching back to Analyzer tab...');
    analyzerTab.click();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return true;
  } catch (error) {
    console.error('❌ Tab navigation test failed:', error);
    return false;
  }
}

async function testAnalyzerFunctionality() {
  console.log('\n🧪 Step 3: Testing Analyzer Functionality');
  
  try {
    // Look for URL input
    const urlInput = document.querySelector('input[placeholder*="URL"], input[placeholder*="url"]') ||
                    document.querySelector('input[type="url"]') ||
                    document.querySelector('input[name*="url"]');
    
    if (urlInput) {
      console.log('✅ URL input found');
      
      // Test entering a URL
      urlInput.value = 'https://example.com/test-simplified';
      urlInput.dispatchEvent(new Event('input', { bubbles: true }));
      urlInput.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log('✅ URL entered successfully');
    } else {
      console.log('⚠️ URL input not found');
    }
    
    // Look for analyze button
    const analyzeButton = Array.from(document.querySelectorAll('button'))
      .find(btn => btn.textContent?.includes('Analizar') || btn.textContent?.includes('Analyze'));
    
    if (analyzeButton) {
      console.log('✅ Analyze button found');
      console.log('🔧 Button state:', {
        disabled: analyzeButton.disabled,
        text: analyzeButton.textContent?.trim()
      });
    } else {
      console.log('⚠️ Analyze button not found');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Analyzer functionality test failed:', error);
    return false;
  }
}

async function testDashboardFunctionality() {
  console.log('\n📊 Step 4: Testing Dashboard Functionality');
  
  try {
    // Click on Dashboard tab first
    const dashboardTab = Array.from(document.querySelectorAll('button[role="tab"]'))
      .find(tab => tab.textContent?.includes('Dashboard'));
    
    if (dashboardTab) {
      dashboardTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Check for dashboard elements
    const dashboardElements = {
      charts: document.querySelectorAll('[class*="chart"], [class*="graph"]').length,
      cards: document.querySelectorAll('[class*="card"]').length,
      tables: document.querySelectorAll('table').length,
      lists: document.querySelectorAll('ul, ol').length
    };
    
    console.log('📈 Dashboard elements found:', dashboardElements);
    
    if (Object.values(dashboardElements).some(count => count > 0)) {
      console.log('✅ Dashboard content detected');
    } else {
      console.log('⚠️ Dashboard content not clearly detected');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Dashboard functionality test failed:', error);
    return false;
  }
}

async function runSimplifiedInterfaceTest() {
  console.log('🚀 Starting Simplified SEO Interface Test...\n');
  
  // Step 1: Check interface
  const interfaceResult = await testSimplifiedInterface();
  if (!interfaceResult) {
    console.log('\n❌ Interface check failed - stopping tests');
    return;
  }
  
  // Step 2: Test navigation
  const navigationResult = await testTabNavigation(interfaceResult);
  
  // Step 3: Test analyzer
  const analyzerResult = await testAnalyzerFunctionality();
  
  // Step 4: Test dashboard
  const dashboardResult = await testDashboardFunctionality();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 SIMPLIFIED INTERFACE TEST RESULTS');
  console.log('='.repeat(60));
  
  if (interfaceResult && navigationResult && analyzerResult && dashboardResult) {
    console.log('🎉 SUCCESS: Simplified interface working correctly!');
    console.log('✅ History and Favorites tabs removed');
    console.log('✅ Analyzer and Dashboard tabs functional');
    console.log('✅ Tab navigation working');
    console.log('✅ Core functionality preserved');
    console.log('\n💡 The SEO Analyzer now has a clean, focused interface!');
  } else {
    console.log('❌ ISSUES DETECTED');
    console.log('🔧 Check the errors above for specific problems');
  }
  
  console.log('\n📋 INTERFACE SUMMARY:');
  console.log('• Analizador tab: Main analysis functionality');
  console.log('• Dashboard tab: View saved analyses and statistics');
  console.log('• Removed tabs: Historial, Favoritos (non-functional)');
  console.log('• Result: Cleaner, more focused user experience');
}

// Auto-run the test
runSimplifiedInterfaceTest();

// Export for manual use
window.testSimplifiedSEO = {
  runSimplifiedInterfaceTest,
  testSimplifiedInterface,
  testTabNavigation,
  testAnalyzerFunctionality,
  testDashboardFunctionality
};

console.log('\n💡 Manual testing available:');
console.log('- window.testSimplifiedSEO.runSimplifiedInterfaceTest()');
console.log('- window.testSimplifiedSEO.testSimplifiedInterface()');
