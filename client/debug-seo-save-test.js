// SEO Analysis Save Test Script
// Run this in the browser console on the SEO Analyzer page

console.log('🧪 Testing SEO Analysis Save Functionality...');

async function testSEOAnalysisSave() {
  try {
    // Import required modules
    const { supabase } = await import('/src/lib/supabase.ts');
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('\n=== 🔐 AUTHENTICATION CHECK ===');
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email
    });
    
    // Check if user is not anonymous
    const isAuthenticated = !!user && user.id !== 'anonymous';
    console.log('🔍 Authentication state:', {
      userExists: !!user,
      userId: user.id,
      isAnonymous: user.id === 'anonymous',
      isAuthenticated
    });
    
    if (!isAuthenticated) {
      console.error('❌ User is not properly authenticated');
      return;
    }
    
    console.log('\n=== 💾 TESTING SAVE FUNCTIONALITY ===');
    
    // Create test analysis data
    const testAnalysisData = {
      user_id: user.id,
      url: 'https://test-debug-save.com',
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 75,
      basic_info: {
        title: 'Test Debug Save Page',
        description: 'Testing save functionality',
        h1: 'Test Page'
      },
      content_analysis: {
        word_count: 250,
        heading_structure: { h1: 1, h2: 2, h3: 1 }
      },
      seo_checks: {
        meta_description: true,
        title_tag: true,
        h1_tag: true
      },
      recommendations: [
        'Test recommendation 1',
        'Test recommendation 2'
      ],
      achievements: [
        'Test achievement 1'
      ],
      open_graph: {
        title: 'Test OG Title',
        description: 'Test OG Description'
      },
      twitter_card: {
        card: 'summary',
        title: 'Test Twitter Title'
      },
      preview_data: {
        title: 'Test Preview',
        description: 'Test Preview Description'
      },
      performance_metrics: {
        load_time: 1.5,
        page_size: 1024
      },
      analysis_duration_ms: 2500,
      status: 'completed',
      error_message: null,
      ai_enhanced: false,
      is_favorite: false,
      custom_name: null,
      tags: [],
      notes: null,
      view_count: 0,
      last_viewed_at: null,
      regeneration_count: 0
    };
    
    console.log('🔄 Attempting to save test analysis...');
    console.log('📊 Test data:', {
      user_id: testAnalysisData.user_id,
      url: testAnalysisData.url,
      overall_score: testAnalysisData.overall_score,
      status: testAnalysisData.status
    });
    
    try {
      const savedAnalysis = await seoAnalysisService.saveAnalysis(testAnalysisData);
      console.log('✅ Analysis saved successfully!', {
        id: savedAnalysis.id,
        url: savedAnalysis.url,
        created_at: savedAnalysis.created_at
      });
      
      // Verify it was saved by fetching it back
      console.log('\n=== 🔍 VERIFYING SAVE ===');
      
      const userAnalyses = await seoAnalysisService.getUserAnalyses(user.id, {
        limit: 10,
        orderBy: 'created_at',
        orderDirection: 'desc'
      });
      
      console.log('📊 User analyses after save:', {
        count: userAnalyses.length,
        analyses: userAnalyses.map(a => ({
          id: a.id,
          url: a.url,
          created_at: a.created_at,
          status: a.status
        }))
      });
      
      // Check if our test analysis is in the results
      const testAnalysis = userAnalyses.find(a => a.url === testAnalysisData.url);
      if (testAnalysis) {
        console.log('✅ Test analysis found in user analyses!');
      } else {
        console.warn('⚠️ Test analysis not found in user analyses');
      }
      
      // Clean up - delete the test analysis
      const shouldCleanup = confirm('Delete the test analysis? (Recommended)');
      if (shouldCleanup) {
        try {
          await seoAnalysisService.deleteAnalysis(savedAnalysis.id);
          console.log('🗑️ Test analysis cleaned up');
        } catch (cleanupError) {
          console.warn('⚠️ Could not clean up test analysis:', cleanupError);
        }
      }
      
    } catch (saveError) {
      console.error('❌ Save failed:', saveError);
      
      // Check if it's a database/RLS issue
      console.log('\n=== 🔍 TESTING DIRECT DATABASE ACCESS ===');
      
      try {
        const directInsert = await supabase
          .from('seo_analyses')
          .insert({
            user_id: user.id,
            url: 'https://test-direct-insert.com',
            analysis_mode: 'page',
            tool_type: 'seo_analyzer',
            analysis_version: '1.0',
            overall_score: 80,
            basic_info: { title: 'Direct Insert Test' },
            content_analysis: { word_count: 100 },
            seo_checks: { title_tag: true },
            recommendations: ['Direct test'],
            achievements: [],
            open_graph: {},
            twitter_card: {},
            preview_data: {},
            performance_metrics: {},
            status: 'completed',
            ai_enhanced: false,
            is_favorite: false
          })
          .select()
          .single();
        
        if (directInsert.error) {
          console.error('❌ Direct insert failed:', directInsert.error);
        } else {
          console.log('✅ Direct insert succeeded:', directInsert.data.id);
          
          // Clean up direct insert
          await supabase
            .from('seo_analyses')
            .delete()
            .eq('id', directInsert.data.id);
          console.log('🗑️ Direct insert cleaned up');
        }
        
      } catch (directError) {
        console.error('❌ Direct insert exception:', directError);
      }
    }
    
    console.log('\n=== 🏁 TEST COMPLETE ===');
    
  } catch (error) {
    console.error('💥 Test script error:', error);
  }
}

// Run the test
testSEOAnalysisSave();
