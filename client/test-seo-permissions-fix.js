// Test script to verify SEO Analyzer permission fix
// Run this in the browser console to test the permission resolution

console.log('🔐 Testing SEO Analyzer Permission Fix');
console.log('======================================');

async function testPermissionFix() {
  console.log('\n📡 Step 1: Testing Database Connection...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test basic connection to api.seo_analyses table
    const { data, error } = await supabase
      .from('seo_analyses')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error);
      console.log('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      
      if (error.code === '42501') {
        console.log('💡 Still getting permission denied - check grants and RLS policies');
      } else if (error.code === '42P01') {
        console.log('💡 Table not found - check if table exists in api schema');
      }
      
      return false;
    }
    
    console.log('✅ Database connection successful - no permission errors');
    return true;
  } catch (error) {
    console.error('❌ Failed to test database connection:', error);
    return false;
  }
}

async function testAuthentication() {
  console.log('\n🔐 Step 2: Testing Authentication...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.warn('⚠️ User not authenticated');
      console.log('💡 Please sign in to test authenticated operations');
      return null;
    }
    
    console.log('✅ User authenticated:', {
      userId: user.id,
      email: user.email
    });
    
    return user;
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return null;
  }
}

async function testSEOAnalysisService() {
  console.log('\n🔧 Step 3: Testing SEO Analysis Service...');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('✅ SEO Analysis Service imported successfully');
    
    // Test getting recent analyses (this was failing before)
    console.log('🔄 Testing getRecentAnalyses...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    console.log('✅ Recent analyses retrieved successfully:', {
      count: recentAnalyses.length
    });
    
    // Test getting favorite analyses
    console.log('🔄 Testing getFavoriteAnalyses...');
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    console.log('✅ Favorite analyses retrieved successfully:', {
      count: favoriteAnalyses.length
    });
    
    return true;
  } catch (error) {
    console.error('❌ SEO Analysis Service test failed:', error);
    console.log('Error details:', {
      message: error.message,
      code: error.code || 'No code',
      stack: error.stack
    });
    
    if (error.message.includes('permission denied')) {
      console.log('💡 Still getting permission errors - check database grants');
    }
    
    return false;
  }
}

async function testDirectQueries(userId) {
  console.log('\n📊 Step 4: Testing Direct Database Queries...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test SELECT query
    console.log('🔄 Testing SELECT query...');
    const { data: selectData, error: selectError } = await supabase
      .from('seo_analyses')
      .select('id, created_at, user_id, url, overall_score')
      .eq('user_id', userId)
      .limit(5);
    
    if (selectError) {
      console.error('❌ SELECT query failed:', selectError);
      return false;
    }
    
    console.log('✅ SELECT query successful:', {
      recordsFound: selectData?.length || 0
    });
    
    // Test INSERT query (if user is authenticated)
    if (userId) {
      console.log('🔄 Testing INSERT query...');
      const testData = {
        user_id: userId,
        url: 'https://example.com/permission-test',
        analysis_mode: 'page',
        tool_type: 'seo_analyzer',
        analysis_version: '1.0',
        overall_score: 75,
        basic_info: { title: 'Permission Test', title_length: 15 },
        content_analysis: { word_count: 300 },
        seo_checks: { has_title: true },
        recommendations: [],
        status: 'completed'
      };
      
      const { data: insertData, error: insertError } = await supabase
        .from('seo_analyses')
        .insert(testData)
        .select()
        .single();
      
      if (insertError) {
        console.error('❌ INSERT query failed:', insertError);
        return false;
      }
      
      console.log('✅ INSERT query successful:', {
        id: insertData.id,
        url: insertData.url
      });
      
      // Clean up test data
      console.log('🔄 Testing DELETE query...');
      const { error: deleteError } = await supabase
        .from('seo_analyses')
        .delete()
        .eq('id', insertData.id);
      
      if (deleteError) {
        console.error('❌ DELETE query failed:', deleteError);
        return false;
      }
      
      console.log('✅ DELETE query successful - test data cleaned up');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Direct query test failed:', error);
    return false;
  }
}

async function testRLSPolicies(userId) {
  console.log('\n🔒 Step 5: Testing RLS Policies...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test that we can only see our own data
    const { data: userAnalyses, error } = await supabase
      .from('seo_analyses')
      .select('user_id')
      .limit(10);
    
    if (error) {
      console.error('❌ RLS test failed:', error);
      return false;
    }
    
    // Check that all returned analyses belong to the current user
    const allBelongToUser = userAnalyses.every(analysis => analysis.user_id === userId);
    
    if (allBelongToUser) {
      console.log('✅ RLS Policy Working: All analyses belong to current user');
    } else {
      console.error('❌ RLS Policy Failed: Found analyses from other users');
      return false;
    }
    
    console.log(`✅ RLS test passed with ${userAnalyses.length} user analyses`);
    
    return true;
  } catch (error) {
    console.error('❌ RLS test error:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting SEO Analyzer Permission Fix Tests...\n');
  
  const dbTestResult = await testPermissionFix();
  if (!dbTestResult) {
    console.log('\n❌ Database connection still failing - check grants and policies');
    return;
  }
  
  const user = await testAuthentication();
  const serviceTestResult = await testSEOAnalysisService();
  
  let directQueryResult = true;
  let rlsTestResult = true;
  
  if (user) {
    directQueryResult = await testDirectQueries(user.id);
    if (directQueryResult) {
      rlsTestResult = await testRLSPolicies(user.id);
    }
  } else {
    console.log('\n⚠️ Skipping user-specific tests (not authenticated)');
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL RESULT');
  console.log('='.repeat(60));
  
  if (dbTestResult && serviceTestResult && directQueryResult && rlsTestResult) {
    console.log('🎉 SUCCESS: Permission issues resolved!');
    console.log('✅ Database connection working');
    console.log('✅ Service layer functional');
    console.log('✅ Direct queries working');
    console.log('✅ RLS policies enforced');
    console.log('\n💡 SEO Analyzer should now work without permission errors!');
  } else {
    console.log('❌ PARTIAL SUCCESS: Some tests failed');
    console.log('🔧 Please check the errors above');
  }
}

// Auto-run the tests
runAllTests();

// Export for manual testing
window.testSEOPermissions = {
  runAllTests,
  testPermissionFix,
  testAuthentication,
  testSEOAnalysisService,
  testDirectQueries,
  testRLSPolicies
};

console.log('\n💡 Manual testing available:');
console.log('- window.testSEOPermissions.runAllTests()');
console.log('- window.testSEOPermissions.testPermissionFix()');
console.log('- window.testSEOPermissions.testSEOAnalysisService()');
