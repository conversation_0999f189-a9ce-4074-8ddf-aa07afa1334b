/**
 * Servicio de integración Polotno para Mockups
 * Adaptado del servicio exitoso de ads y content generator
 */

export interface MockupPolotnoOptions {
  mockupId: string;
  imageUrl: string;
  mockupType: 'product' | 'portrait';
  platform?: string;
  metadata?: any;
}

export interface PolotnoIntegrationResult {
  success: boolean;
  editorUrl: string;
  tempImageId?: string;
  error?: string;
}

/**
 * Abre un mockup en el editor Polotno
 */
export async function openMockupInPolotno(options: MockupPolotnoOptions): Promise<PolotnoIntegrationResult> {
  try {
    console.log('🎨 Abriendo mockup en Polotno:', options);

    // Generar ID único para la imagen temporal
    const tempImageId = `mockup_${options.mockupType}_${options.mockupId}_${Date.now()}`;

    // Si la imagen es una URL externa (Ideogram), descargarla y almacenarla internamente
    let imageDataUrl = options.imageUrl;
    
    if (options.imageUrl.includes('ideogram.ai') || options.imageUrl.includes('cdn')) {
      console.log('📥 Descargando imagen externa para Polotno...');
      
      try {
        const downloadResponse = await fetch('/api/v1/temp-images/download-and-store', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: options.imageUrl,
            id: tempImageId
          })
        });

        if (downloadResponse.ok) {
          const downloadResult = await downloadResponse.json();
          if (downloadResult.success && downloadResult.internal_url) {
            console.log('✅ Imagen descargada y almacenada internamente');
            // Usar la URL interna
            imageDataUrl = downloadResult.internal_url;
          }
        }
      } catch (error) {
        console.warn('⚠️ Error descargando imagen, usando URL original:', error);
      }
    }

    // Si es una imagen base64 o muy grande, almacenarla temporalmente
    if (imageDataUrl.startsWith('data:') || imageDataUrl.length > 2000) {
      console.log('💾 Almacenando imagen temporalmente para Polotno...');
      
      const storeResponse = await fetch('/api/v1/temp-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: tempImageId,
          imageData: imageDataUrl
        })
      });

      if (storeResponse.ok) {
        const storeResult = await storeResponse.json();
        if (storeResult.success) {
          console.log('✅ Imagen almacenada temporalmente');
          // Construir URL del editor con parámetros específicos para mockups
          const editorUrl = buildPolotnoEditorUrl({
            tempImageId,
            mockupType: options.mockupType,
            platform: options.platform || 'mockup-generator',
            metadata: options.metadata
          });

          return {
            success: true,
            editorUrl,
            tempImageId
          };
        }
      }
    }

    // Fallback: usar URL directamente
    const editorUrl = buildPolotnoEditorUrl({
      imageUrl: encodeURIComponent(imageDataUrl),
      mockupType: options.mockupType,
      platform: options.platform || 'mockup-generator',
      metadata: options.metadata
    });

    return {
      success: true,
      editorUrl
    };

  } catch (error) {
    console.error('❌ Error abriendo mockup en Polotno:', error);
    return {
      success: false,
      editorUrl: '',
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}

/**
 * Construye la URL del editor Polotno con parámetros específicos
 */
function buildPolotnoEditorUrl(params: {
  tempImageId?: string;
  imageUrl?: string;
  mockupType: string;
  platform: string;
  metadata?: any;
}): string {
  const baseUrl = '/visual-editor';
  const urlParams = new URLSearchParams();

  if (params.tempImageId) {
    urlParams.set('tempImageId', params.tempImageId);
  } else if (params.imageUrl) {
    urlParams.set('imageUrl', params.imageUrl);
  }

  urlParams.set('platform', params.platform);
  urlParams.set('type', params.mockupType);

  // Agregar metadatos específicos para mockups
  if (params.metadata) {
    if (params.metadata.mockup_context) {
      urlParams.set('context', params.metadata.mockup_context);
    }
    if (params.metadata.portrait_style) {
      urlParams.set('style', params.metadata.portrait_style);
    }
    if (params.metadata.service_type) {
      urlParams.set('service', params.metadata.service_type);
    }
  }

  return `${baseUrl}?${urlParams.toString()}`;
}

/**
 * Abre múltiples mockups en Polotno (para variaciones)
 */
export async function openMultipleMockupsInPolotno(
  mockups: Array<{
    id: string;
    image_url: string;
    metadata?: any;
  }>,
  mockupType: 'product' | 'portrait'
): Promise<PolotnoIntegrationResult[]> {
  const results: PolotnoIntegrationResult[] = [];

  for (const mockup of mockups) {
    const result = await openMockupInPolotno({
      mockupId: mockup.id,
      imageUrl: mockup.image_url,
      mockupType,
      platform: 'mockup-generator-batch',
      metadata: mockup.metadata
    });

    results.push(result);

    // Pequeña pausa entre aperturas para evitar sobrecarga
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  return results;
}

/**
 * Crea un proyecto Polotno con múltiples mockups como páginas
 */
export async function createMockupProject(
  mockups: Array<{
    id: string;
    image_url: string;
    metadata?: any;
  }>,
  projectName: string,
  mockupType: 'product' | 'portrait'
): Promise<PolotnoIntegrationResult> {
  try {
    console.log('📁 Creando proyecto Polotno con múltiples mockups...');

    // Usar las URLs de las imágenes directamente (sin almacenamiento temporal)
    const imageUrls: string[] = [];

    for (let i = 0; i < mockups.length; i++) {
      const mockup = mockups[i];

      // Usar la URL de la imagen directamente
      if (mockup.image_url.startsWith('/api/v1/images/stored/')) {
        // Imagen local almacenada permanentemente - usar directamente
        imageUrls.push(mockup.image_url);
        console.log(`✅ [${i + 1}] Imagen local agregada:`, mockup.image_url);
      } else if (mockup.image_url.includes('ideogram.ai') || mockup.image_url.includes('cdn')) {
        // Imagen externa - almacenar temporalmente solo si es necesario
        const tempImageId = `project_${projectName}_${mockupType}_${i}_${Date.now()}`;

        try {
          const downloadResponse = await fetch('/api/v1/temp-images/download-and-store', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              url: mockup.image_url,
              id: tempImageId
            })
          });

          if (downloadResponse.ok) {
            const downloadResult = await downloadResponse.json();
            if (downloadResult.success) {
              imageUrls.push(`/api/v1/temp-images/${tempImageId}`);
              console.log(`✅ [${i + 1}] Imagen externa descargada:`, tempImageId);
              continue;
            }
          }
        } catch (error) {
          console.warn('⚠️ Error descargando imagen externa para proyecto:', error);
        }

        // Si falla la descarga, usar la URL original
        imageUrls.push(mockup.image_url);
        console.log(`⚠️ [${i + 1}] Usando URL original:`, mockup.image_url);
      } else {
        // Otras URLs - usar directamente
        imageUrls.push(mockup.image_url);
        console.log(`✅ [${i + 1}] URL directa agregada:`, mockup.image_url);
      }
    }

    if (imageUrls.length === 0) {
      throw new Error('No se pudieron procesar las imágenes del proyecto');
    }

    // Estrategia simple: abrir Polotno con múltiples imágenes usando URLs directas
    console.log('🚀 Abriendo Polotno con múltiples mockups:', imageUrls);

    // Construir URL del editor con múltiples imágenes
    const urlParams = new URLSearchParams();
    urlParams.set('platform', 'mockup-project');
    urlParams.set('type', mockupType);
    urlParams.set('project', projectName);

    // Pasar las URLs de imágenes como parámetros separados (IGUAL que para una sola imagen)
    imageUrls.forEach((imageUrl, index) => {
      urlParams.set(`mockupImageUrl${index + 1}`, encodeURIComponent(imageUrl));
    });

    // Indicar el número total de mockups
    urlParams.set('totalMockups', imageUrls.length.toString());

    const editorUrl = `/visual-editor?${urlParams.toString()}`;

    console.log('🔗 URL del editor:', editorUrl);
    console.log('✅ Proyecto Polotno creado con', imageUrls.length, 'mockups:', imageUrls);

    // Abrir el editor en una nueva ventana
    console.log('🚀 Intentando abrir ventana con URL:', editorUrl);

    try {
      const newWindow = window.open(editorUrl, '_blank');
      if (newWindow) {
        console.log('✅ Ventana abierta exitosamente');
      } else {
        console.error('❌ No se pudo abrir la ventana - posible bloqueo de popup');
      }
    } catch (error) {
      console.error('❌ Error abriendo ventana:', error);
    }

    return {
      success: true,
      editorUrl,
      tempImageId: tempImages[0] // Retornar el primer ID como referencia
    };

  } catch (error) {
    console.error('❌ Error creando proyecto Polotno:', error);
    return {
      success: false,
      editorUrl: '',
      error: error instanceof Error ? error.message : 'Error creando proyecto'
    };
  }
}

/**
 * Utilidad para abrir el editor en una nueva pestaña
 */
export function openPolotnoInNewTab(editorUrl: string): void {
  window.open(editorUrl, '_blank', 'noopener,noreferrer');
}

/**
 * Utilidad para verificar si Polotno está disponible
 */
export async function checkPolotnoAvailability(): Promise<boolean> {
  try {
    const response = await fetch('/visual-editor', { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}
