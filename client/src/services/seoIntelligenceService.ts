/**
 * SEO Intelligence Service for Emma Studio
 * Provides AI-powered SEO analysis, content generation, and optimization
 */

interface SEOAnalysis {
  score: number;
  keywords: {
    primary: string[];
    secondary: string[];
    density: Record<string, number>;
  };
  readability: {
    score: number;
    level: string;
    suggestions: string[];
  };
  structure: {
    headings: { level: number; text: string }[];
    paragraphs: number;
    wordCount: number;
    sentences: number;
  };
  saio: {
    score: number;
    qAndA: boolean;
    lists: boolean;
    freshness: boolean;
    multimedia: boolean;
    sources: boolean;
  };
  suggestions: string[];
}

interface ContentGenerationRequest {
  topic: string;
  keywords: string[];
  contentType: 'educational' | 'motivational' | 'commercial' | 'news';
  targetLength: number;
  tone: 'professional' | 'casual' | 'technical' | 'friendly';
  includeImages: boolean;
}

interface GeneratedContent {
  title: string;
  content: string;
  metaDescription: string;
  keywords: string[];
  images: Array<{
    prompt: string;
    alt: string;
    placement: number;
    url?: string;
  }>;
  seoScore: number;
}

class SEOIntelligenceService {
  private apiUrl: string;

  constructor() {
    this.apiUrl = '/api/v1/seo-gpt';
  }

  /**
   * Get or create a default project for the SEO GPT Optimizer
   */
  async getOrCreateDefaultProject(): Promise<string> {
    try {
      const response = await fetch(`${this.apiUrl}/projects/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'SEO GPT Optimizer Project',
          description: 'Default project for SEO content generation'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create project');
      }

      const result = await response.json();
      return result.project_id;
    } catch (error) {
      console.error('Project creation failed:', error);
      return 'default-project-id';
    }
  }

  /**
   * Analyze content for SEO optimization
   */
  async analyzeContent(content: string, targetKeywords?: string[], deepAnalysis: boolean = false, projectId?: string): Promise<SEOAnalysis> {
    try {
      const actualProjectId = projectId || await this.getOrCreateDefaultProject();

      const response = await fetch(`${this.apiUrl}/content/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_id: actualProjectId,
          content,
          target_keywords: targetKeywords || [],
          content_type: 'blog',
          deep_analysis: deepAnalysis
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze content');
      }

      const result = await response.json();
      console.log('🔍 Raw backend response:', result);

      // Map backend response to frontend interface
      const mappedResult = {
        score: Math.round(result.gpt_rank_score || 0),
        keywords: {
          primary: targetKeywords || ['SEO', 'contenido'],
          secondary: [],
          density: targetKeywords?.reduce((acc: Record<string, number>, keyword: string) => {
            acc[keyword] = 2.5;
            return acc;
          }, {}) || { 'SEO': 2.5, 'contenido': 2.0 }
        },
        readability: {
          score: Math.round((result.component_scores?.clarity_score || 50) * 2), // Convert to 0-100 scale
          level: result.score_grade || 'Mejorable',
          suggestions: Array.isArray(result.improvement_suggestions)
            ? result.improvement_suggestions.slice(0, 2).filter((s: any) => typeof s === 'string')
            : ['Mejora la claridad del texto', 'Usa oraciones más cortas']
        },
        structure: {
          headings: [],
          paragraphs: result.content_stats?.paragraph_count || 0,
          wordCount: result.content_stats?.word_count || 0,
          sentences: result.content_stats?.sentence_count || 0
        },
        saio: {
          score: Math.round((result.gpt_rank_score || 0) * 0.9), // SAIO score slightly lower
          qAndA: false,
          lists: false,
          freshness: true,
          multimedia: false,
          sources: false
        },
        suggestions: Array.isArray(result.improvement_suggestions)
          ? result.improvement_suggestions.filter((s: any) => typeof s === 'string' && s.length > 0)
          : ['Mejora la estructura del contenido', 'Agrega más detalles específicos']
      };

      console.log('📊 Mapped analysis result:', mappedResult);
      return mappedResult;
    } catch (error) {
      console.error('SEO analysis failed:', error);
      return this.getMockAnalysis(content);
    }
  }

  /**
   * Generate optimized SEO content
   */
  async generateContent(request: ContentGenerationRequest, projectId?: string): Promise<GeneratedContent> {
    try {
      const actualProjectId = projectId || await this.getOrCreateDefaultProject();

      const response = await fetch(`${this.apiUrl}/content/generate-blog`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_id: actualProjectId,
          topic: request.topic,
          content_type: request.contentType,
          target_length: this.mapTargetLength(request.targetLength),
          include_images: request.includeImages,
          num_images: request.includeImages ? 3 : 0
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate content');
      }

      const result = await response.json();
      const data = result.data;

      return {
        title: data.title || `Guía Completa sobre ${request.topic}`,
        content: data.content,
        metaDescription: data.meta_description || `Descubre todo sobre ${request.topic}. Guía completa con estrategias probadas.`,
        keywords: data.seo_analysis?.keywords || request.keywords,
        images: (data.images || []).map((url: string, index: number) => ({
          prompt: `Professional illustration for ${request.topic}`,
          alt: `Ilustración profesional de ${request.topic}`,
          placement: index + 1,
          url: url
        })),
        seoScore: data.seo_analysis?.seo_score || 85
      };
    } catch (error) {
      console.error('Content generation failed:', error);
      return this.getMockGeneratedContent(request);
    }
  }

  /**
   * Map frontend target length to backend format
   */
  private mapTargetLength(targetLength: number): string {
    if (targetLength < 1000) return 'short';
    if (targetLength < 2000) return 'medium';
    return 'long';
  }

  /**
   * Get SEO optimization suggestions
   */
  async getOptimizationSuggestions(content: string, analysis: SEOAnalysis): Promise<string[]> {
    const suggestions: string[] = [];

    // Keyword density suggestions
    if (analysis.keywords.density) {
      const densities = Object.values(analysis.keywords.density);
      const avgDensity = densities.reduce((a, b) => a + b, 0) / densities.length;
      
      if (avgDensity < 1) {
        suggestions.push('Aumenta la densidad de palabras clave (objetivo: 1-3%)');
      } else if (avgDensity > 3) {
        suggestions.push('Reduce la densidad de palabras clave para evitar keyword stuffing');
      }
    }

    // Structure suggestions
    if (analysis.structure.headings.length === 0) {
      suggestions.push('Añade encabezados (H1, H2, H3) para mejorar la estructura');
    }

    if (analysis.structure.wordCount < 300) {
      suggestions.push('Aumenta la longitud del contenido (mínimo 300 palabras)');
    }

    // SAIO optimization
    if (!analysis.saio.qAndA) {
      suggestions.push('Incluye preguntas y respuestas para optimización SAIO');
    }

    if (!analysis.saio.lists) {
      suggestions.push('Añade listas numeradas o con viñetas');
    }

    if (!analysis.saio.multimedia) {
      suggestions.push('Incluye imágenes, videos o elementos multimedia');
    }

    // Readability suggestions
    if (analysis.readability.score < 60) {
      suggestions.push('Mejora la legibilidad con oraciones más cortas y vocabulario simple');
    }

    return suggestions;
  }

  /**
   * Generate image prompts for content
   */
  async generateImagePrompts(content: string, count: number = 3, topic: string = ''): Promise<Array<{
    prompt: string;
    alt: string;
    placement: number;
  }>> {
    try {
      // Generate contextual prompts based on content analysis
      const prompts = [];

      for (let i = 0; i < count; i++) {
        const purposes = [
          'Hero image - main concept visualization',
          'Supporting concept illustration',
          'Data or process visualization',
          'Conclusion or call-to-action visual'
        ];

        const purpose = purposes[Math.min(i, purposes.length - 1)];

        prompts.push({
          prompt: `Professional illustration for ${topic || 'blog content'}. ${purpose}. Modern, clean, professional design suitable for blog content.`,
          alt: `Ilustración profesional de ${topic || 'contenido'}`,
          placement: i + 1
        });
      }

      return prompts;
    } catch (error) {
      console.error('Image prompt generation failed:', error);
      return this.getMockImagePrompts(content, count);
    }
  }

  /**
   * Mock analysis for development/fallback
   */
  private getMockAnalysis(content: string): SEOAnalysis {
    const wordCount = content.trim().split(/\s+/).length;
    const sentences = content.split(/[.!?]+/).length - 1;
    
    return {
      score: Math.floor(Math.random() * 40) + 60, // 60-100
      keywords: {
        primary: ['SEO', 'contenido', 'optimización'],
        secondary: ['marketing', 'digital', 'estrategia'],
        density: {
          'SEO': 2.1,
          'contenido': 1.8,
          'optimización': 1.2
        }
      },
      readability: {
        score: Math.floor(Math.random() * 30) + 60,
        level: 'Intermedio',
        suggestions: ['Usa oraciones más cortas', 'Simplifica el vocabulario técnico']
      },
      structure: {
        headings: [],
        paragraphs: Math.ceil(wordCount / 100),
        wordCount,
        sentences
      },
      saio: {
        score: Math.floor(Math.random() * 30) + 70,
        qAndA: content.includes('?'),
        lists: content.includes('•') || content.includes('1.'),
        freshness: true,
        multimedia: content.includes('img') || content.includes('imagen'),
        sources: content.includes('http') || content.includes('fuente')
      },
      suggestions: [
        'Añade más palabras clave relevantes',
        'Incluye preguntas frecuentes',
        'Mejora la estructura con subtítulos',
        'Añade elementos multimedia'
      ]
    };
  }

  /**
   * Mock content generation for development/fallback
   */
  private getMockGeneratedContent(request: ContentGenerationRequest): GeneratedContent {
    return {
      title: `Guía Completa sobre ${request.topic}`,
      content: `<h1>Guía Completa sobre ${request.topic}</h1>
      
<p>En el mundo digital actual, entender ${request.topic} es fundamental para el éxito de cualquier estrategia online.</p>

<h2>¿Qué es ${request.topic}?</h2>
<p>${request.topic} es una disciplina que combina técnicas avanzadas con estrategias probadas para obtener resultados excepcionales.</p>

<h2>Beneficios Principales</h2>
<ul>
<li>Mejora significativa en los resultados</li>
<li>Optimización de recursos y tiempo</li>
<li>Estrategias basadas en datos reales</li>
</ul>

<h2>Implementación Práctica</h2>
<p>Para implementar ${request.topic} de manera efectiva, es importante seguir estos pasos fundamentales...</p>`,
      metaDescription: `Descubre todo sobre ${request.topic}. Guía completa con estrategias probadas y consejos prácticos para obtener resultados excepcionales.`,
      keywords: request.keywords,
      images: [
        {
          prompt: `Professional illustration of ${request.topic} concept with modern design elements`,
          alt: `Ilustración profesional de ${request.topic}`,
          placement: 1
        },
        {
          prompt: `Infographic showing ${request.topic} benefits and statistics`,
          alt: `Infografía de beneficios de ${request.topic}`,
          placement: 2
        }
      ],
      seoScore: 85
    };
  }

  /**
   * Mock image prompts for development/fallback
   */
  private getMockImagePrompts(content: string, count: number): Array<{
    prompt: string;
    alt: string;
    placement: number;
  }> {
    const prompts = [
      {
        prompt: 'Professional office environment with modern technology and natural lighting',
        alt: 'Oficina profesional moderna',
        placement: 1
      },
      {
        prompt: 'Digital marketing infographic with charts and data visualization',
        alt: 'Infografía de marketing digital',
        placement: 2
      },
      {
        prompt: 'Team collaboration in modern workspace with laptops and documents',
        alt: 'Equipo colaborando en oficina moderna',
        placement: 3
      }
    ];

    return prompts.slice(0, count);
  }
}

// Create singleton instance
export const seoIntelligenceService = new SEOIntelligenceService();

// Export types
export type { 
  SEOAnalysis, 
  ContentGenerationRequest, 
  GeneratedContent 
};

export default SEOIntelligenceService;
