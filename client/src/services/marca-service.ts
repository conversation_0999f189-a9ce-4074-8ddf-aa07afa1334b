import type { <PERSON><PERSON>, CreateMarcaData, UpdateMarcaData } from '@/lib/supabase'

// Cache local para marcas
const MARCAS_CACHE_KEY = 'emma_marcas_cache'

// Función para generar IDs únicos
const generateId = () => {
  return 'marca_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * Servicio para gestionar marcas usando solo cache local
 * Todas las operaciones se realizan en localStorage
 */
export class MarcaService {

  /**
   * Obtener marcas del cache local
   */
  private static getMarcasFromCache(): Marca[] {
    try {
      const cached = localStorage.getItem(MARCAS_CACHE_KEY)
      return cached ? JSON.parse(cached) : []
    } catch (error) {
      console.error('Error reading marcas from cache:', error)
      return []
    }
  }

  /**
   * Guardar marcas en el cache local
   */
  private static saveMarcasToCache(marcas: Marca[]): void {
    try {
      localStorage.setItem(MARCAS_CACHE_KEY, JSON.stringify(marcas))
    } catch (error) {
      console.error('Error saving marcas to cache:', error)
    }
  }

  /**
   * Obtener todas las marcas del usuario
   */
  static async getMarcas(userId?: string): Promise<Marca[]> {
    try {
      const allMarcas = this.getMarcasFromCache()

      // Si se especifica userId, filtrar por usuario
      if (userId) {
        return allMarcas.filter(marca => marca.user_id === userId)
      }

      return allMarcas
    } catch (error) {
      console.error('Error in getMarcas:', error)
      return []
    }
  }

  /**
   * Obtener un marca por ID
   */
  static async getMarcaById(id: string): Promise<Marca | null> {
    try {
      const marcas = this.getMarcasFromCache()
      const marca = marcas.find(m => m.id === id)
      return marca || null
    } catch (error) {
      console.error('Error in getMarcaById:', error)
      return null
    }
  }

  /**
   * Crear un nuevo marca
   */
  static async createMarca(marcaData: CreateMarcaData): Promise<Marca> {
    try {
      const marcas = this.getMarcasFromCache()

      // Verificar si ya existe una marca con el mismo nombre
      const existingMarca = marcas.find(m => m.brand_name.toLowerCase() === marcaData.brand_name.toLowerCase())
      if (existingMarca) {
        throw new Error('Ya existe una marca con ese nombre')
      }

      const newMarca: Marca = {
        id: generateId(),
        ...marcaData,
        status: 'draft' as const,
        campaigns_count: 0,
        assets_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // Agregar la nueva marca al cache
      marcas.push(newMarca)
      this.saveMarcasToCache(marcas)

      console.log('Marca created successfully in cache:', newMarca.brand_name)
      return newMarca
    } catch (error) {
      console.error('Error in createMarca:', error)
      if (error instanceof Error) {
        throw error
      } else {
        throw new Error('Error inesperado al crear la marca')
      }
    }
  }

  /**
   * Actualizar un marca existente
   */
  static async updateMarca(marcaData: UpdateMarcaData): Promise<Marca> {
    try {
      const { id, ...updateData } = marcaData
      const marcas = this.getMarcasFromCache()

      const marcaIndex = marcas.findIndex(m => m.id === id)
      if (marcaIndex === -1) {
        throw new Error('Marca no encontrada')
      }

      // Actualizar la marca
      marcas[marcaIndex] = {
        ...marcas[marcaIndex],
        ...updateData,
        updated_at: new Date().toISOString()
      }

      this.saveMarcasToCache(marcas)
      return marcas[marcaIndex]
    } catch (error) {
      console.error('Error in updateMarca:', error)
      throw error
    }
  }

  /**
   * Eliminar un marca
   */
  static async deleteMarca(id: string): Promise<void> {
    try {
      const marcas = this.getMarcasFromCache()
      const filteredMarcas = marcas.filter(m => m.id !== id)

      if (filteredMarcas.length === marcas.length) {
        throw new Error('Marca no encontrada')
      }

      this.saveMarcasToCache(filteredMarcas)
    } catch (error) {
      console.error('Error in deleteMarca:', error)
      throw error
    }
  }

  /**
   * Duplicar un marca
   */
  static async duplicateMarca(id: string): Promise<Marca> {
    try {
      // Obtener el marca original
      const original = await this.getMarcaById(id)
      if (!original) {
        throw new Error('Marca no encontrado')
      }

      // Crear una copia con nuevo nombre
      const duplicateData: CreateMarcaData = {
        brand_name: `${original.brand_name} (Copia)`,
        website: original.website,
        industry: original.industry,
        logo_url: original.logo_url,
        primary_color: original.primary_color,
        secondary_color: original.secondary_color,
        target_audience: original.target_audience,
        tone: original.tone,
        personality: original.personality,
        description: original.description,
        unique_value: original.unique_value,
        competitors: original.competitors,
        documents: original.documents,
        examples: original.examples,
        user_id: original.user_id
      }

      return await this.createMarca(duplicateData)
    } catch (error) {
      console.error('Error in duplicateMarca:', error)
      throw error
    }
  }

  /**
   * Cambiar el estado de un marca
   */
  static async updateMarcaStatus(id: string, status: 'draft' | 'active' | 'archived'): Promise<Marca> {
    return await this.updateMarca({ id, status })
  }

  /**
   * Buscar marcas por texto
   */
  static async searchMarcas(searchTerm: string, userId?: string): Promise<Marca[]> {
    try {
      const marcas = await this.getMarcas(userId)
      const searchLower = searchTerm.toLowerCase()

      return marcas.filter(marca =>
        marca.brand_name.toLowerCase().includes(searchLower) ||
        marca.description.toLowerCase().includes(searchLower) ||
        marca.industry.toLowerCase().includes(searchLower)
      )
    } catch (error) {
      console.error('Error in searchMarcas:', error)
      return []
    }
  }

  /**
   * Obtener estadísticas de marcas
   */
  static async getMarcasStats(userId?: string): Promise<{
    total: number
    active: number
    draft: number
    archived: number
    totalCampaigns: number
    totalAssets: number
  }> {
    try {
      const marcas = await this.getMarcas(userId)

      const stats = {
        total: marcas.length,
        active: marcas.filter(m => m.status === 'active').length,
        draft: marcas.filter(m => m.status === 'draft').length,
        archived: marcas.filter(m => m.status === 'archived').length,
        totalCampaigns: marcas.reduce((sum, m) => sum + (m.campaigns_count || 0), 0),
        totalAssets: marcas.reduce((sum, m) => sum + (m.assets_count || 0), 0)
      }

      return stats
    } catch (error) {
      console.error('Error in getMarcasStats:', error)
      throw error
    }
  }
}
