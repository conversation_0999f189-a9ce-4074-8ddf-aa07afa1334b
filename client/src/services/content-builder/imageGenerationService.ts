/**
 * Image Generation Service for Content Builder
 * Handles AI-powered image generation and smart prompt creation
 * 
 * Features:
 * - Smart prompt generation based on content context
 * - Ideogram AI integration with optimal settings
 * - Image optimization for content insertion
 * - SEO-friendly alt text generation
 */

import { generateImage, type ImageGeneratorOptions } from '../image-generator-service';

// Types
export interface ContentImageRequest {
  content: string;
  targetKeywords?: string[];
  imageType?: 'hero' | 'section' | 'illustration' | 'diagram' | 'decorative';
  placement?: 'top' | 'middle' | 'bottom' | 'inline';
  aspectRatio?: '1:1' | '16:9' | '4:3' | '3:4' | '9:16';
  style?: 'realistic' | 'illustration' | 'minimalist' | 'professional' | 'creative';
}

export interface GeneratedContentImage {
  url: string;
  prompt: string;
  altText: string;
  caption?: string;
  placement: number; // Character position in content
  aspectRatio: string;
  seoScore: number;
}

export interface ImageSuggestion {
  prompt: string;
  altText: string;
  placement: number;
  reasoning: string;
  imageType: string;
}

// Smart prompt generation based on content analysis
class ContentImageGenerator {
  private readonly IDEOGRAM_SETTINGS = {
    rendering_speed: 'QUALITY' as const,
    magic_prompt: 'AUTO' as const,
    style_type: 'AUTO' as const,
    num_images: 1,
  };

  /**
   * Analyze content and generate smart image suggestions
   */
  async analyzeContentForImages(content: string, targetKeywords: string[] = []): Promise<ImageSuggestion[]> {
    const suggestions: ImageSuggestion[] = [];
    
    // Extract headings and sections
    const sections = this.extractContentSections(content);
    
    // Generate suggestions for each major section
    for (const section of sections) {
      if (section.wordCount > 100) { // Only suggest images for substantial sections
        const suggestion = await this.generateImageSuggestion(section, targetKeywords);
        if (suggestion) {
          suggestions.push(suggestion);
        }
      }
    }

    // Always suggest a hero image if content is substantial
    if (content.length > 500) {
      const heroSuggestion = await this.generateHeroImageSuggestion(content, targetKeywords);
      if (heroSuggestion) {
        suggestions.unshift(heroSuggestion); // Add at beginning
      }
    }

    return suggestions.slice(0, 5); // Limit to 5 suggestions
  }

  /**
   * Generate image based on content context
   */
  async generateContentImage(request: ContentImageRequest): Promise<GeneratedContentImage> {
    const prompt = await this.createSmartPrompt(request);
    const altText = this.generateSEOAltText(prompt, request.targetKeywords);

    const imageOptions: ImageGeneratorOptions = {
      prompt,
      aspect_ratio: request.aspectRatio || '16:9',
      ...this.IDEOGRAM_SETTINGS,
      negative_prompt: 'blurry, low quality, watermark, text overlay, distorted, pixelated'
    };

    const result = await generateImage(imageOptions);

    if (!result.success || !result.image_url) {
      throw new Error(result.error || 'Failed to generate image');
    }

    // Download and store the image locally for reliable access
    let localImageUrl = result.image_url;
    try {
      const downloadResponse = await fetch('/api/v1/temp-images/download-and-store', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: result.image_url,
          id: `content_${Date.now()}`
        })
      });

      if (downloadResponse.ok) {
        const downloadResult = await downloadResponse.json();
        if (downloadResult.success && downloadResult.internal_url) {
          localImageUrl = downloadResult.internal_url;
          console.log('✅ Image downloaded and stored locally:', localImageUrl);
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to download image locally, using original URL:', error);
      // Continue with original URL as fallback
    }

    return {
      url: localImageUrl,
      prompt,
      altText,
      caption: this.generateCaption(prompt, request.imageType),
      placement: this.calculateOptimalPlacement(request.content, request.placement),
      aspectRatio: request.aspectRatio || '16:9',
      seoScore: this.calculateSEOScore(altText, request.targetKeywords)
    };
  }

  /**
   * Generate multiple images for content
   */
  async generateMultipleImages(
    requests: ContentImageRequest[]
  ): Promise<GeneratedContentImage[]> {
    const results: GeneratedContentImage[] = [];
    
    // Generate images sequentially to avoid rate limiting
    for (const request of requests) {
      try {
        const image = await this.generateContentImage(request);
        results.push(image);
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error('Failed to generate image:', error);
      }
    }
    
    return results;
  }

  /**
   * Create smart prompt based on content context
   */
  private async createSmartPrompt(request: ContentImageRequest): Promise<string> {
    const { content, imageType = 'illustration', style = 'professional' } = request;
    
    // Extract key concepts from content
    const keyConcepts = this.extractKeyConcepts(content);
    const mainTopic = this.extractMainTopic(content);
    
    // Build prompt based on image type
    const basePrompts = {
      hero: `Professional hero image representing ${mainTopic}`,
      section: `Illustrative image showing ${keyConcepts.slice(0, 2).join(' and ')}`,
      illustration: `Clean illustration of ${mainTopic} concept`,
      diagram: `Simple diagram explaining ${mainTopic}`,
      decorative: `Elegant visual element related to ${mainTopic}`
    };

    const basePrompt = basePrompts[imageType] || basePrompts.illustration;
    
    // Add style modifiers
    const styleModifiers = {
      realistic: 'photorealistic, high quality photography',
      illustration: 'modern flat design illustration, clean vector style',
      minimalist: 'minimalist design, clean composition, simple',
      professional: 'professional business style, corporate aesthetic',
      creative: 'creative artistic style, vibrant colors, engaging'
    };

    const styleModifier = styleModifiers[style] || styleModifiers.professional;
    
    return `${basePrompt}, ${styleModifier}, high quality, well-composed`;
  }

  /**
   * Extract content sections for analysis
   */
  private extractContentSections(content: string) {
    const sections: Array<{
      title: string;
      content: string;
      position: number;
      wordCount: number;
    }> = [];

    // Simple heading detection
    const headingRegex = /<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi;
    let match;
    let lastPosition = 0;

    while ((match = headingRegex.exec(content)) !== null) {
      const title = match[1].replace(/<[^>]*>/g, ''); // Remove any HTML tags
      const position = match.index;
      
      // Add previous section if exists
      if (lastPosition < position) {
        const sectionContent = content.slice(lastPosition, position);
        const wordCount = sectionContent.split(/\s+/).length;
        
        if (wordCount > 50) {
          sections.push({
            title: 'Content Section',
            content: sectionContent,
            position: lastPosition,
            wordCount
          });
        }
      }
      
      lastPosition = position;
    }

    // Add final section
    if (lastPosition < content.length) {
      const sectionContent = content.slice(lastPosition);
      const wordCount = sectionContent.split(/\s+/).length;
      
      if (wordCount > 50) {
        sections.push({
          title: 'Final Section',
          content: sectionContent,
          position: lastPosition,
          wordCount
        });
      }
    }

    return sections;
  }

  /**
   * Generate image suggestion for a content section
   */
  private async generateImageSuggestion(
    section: { title: string; content: string; position: number; wordCount: number },
    targetKeywords: string[]
  ): Promise<ImageSuggestion | null> {
    const keyConcepts = this.extractKeyConcepts(section.content);
    
    if (keyConcepts.length === 0) return null;

    const prompt = `Professional illustration showing ${keyConcepts.slice(0, 2).join(' and ')}, modern clean style`;
    const altText = this.generateSEOAltText(prompt, targetKeywords);
    
    return {
      prompt,
      altText,
      placement: section.position,
      reasoning: `Visual support for section about ${keyConcepts[0]}`,
      imageType: 'section'
    };
  }

  /**
   * Generate hero image suggestion
   */
  private async generateHeroImageSuggestion(
    content: string,
    targetKeywords: string[]
  ): Promise<ImageSuggestion> {
    const mainTopic = this.extractMainTopic(content);
    const prompt = `Professional hero image representing ${mainTopic}, high quality, engaging composition`;
    const altText = this.generateSEOAltText(prompt, targetKeywords);
    
    return {
      prompt,
      altText,
      placement: 0,
      reasoning: 'Hero image to capture attention and represent main topic',
      imageType: 'hero'
    };
  }

  /**
   * Extract key concepts from text
   */
  private extractKeyConcepts(text: string): string[] {
    // Remove HTML tags
    const cleanText = text.replace(/<[^>]*>/g, ' ');
    
    // Simple keyword extraction (in a real implementation, you'd use NLP)
    const words = cleanText.toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 4)
      .filter(word => !['that', 'this', 'with', 'from', 'they', 'have', 'will', 'been', 'were'].includes(word));
    
    // Get most frequent words
    const frequency: Record<string, number> = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([word]) => word);
  }

  /**
   * Extract main topic from content
   */
  private extractMainTopic(content: string): string {
    // Try to get from first heading
    const headingMatch = content.match(/<h[1-3][^>]*>(.*?)<\/h[1-3]>/i);
    if (headingMatch) {
      return headingMatch[1].replace(/<[^>]*>/g, '').trim();
    }
    
    // Fallback to key concepts
    const concepts = this.extractKeyConcepts(content);
    return concepts[0] || 'content topic';
  }

  /**
   * Generate SEO-friendly alt text
   */
  private generateSEOAltText(prompt: string, targetKeywords: string[] = []): string {
    const baseAlt = prompt
      .replace(/professional|high quality|modern|clean/gi, '')
      .trim();
    
    // Include target keyword if relevant
    const relevantKeyword = targetKeywords.find(keyword => 
      baseAlt.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (relevantKeyword && !baseAlt.toLowerCase().includes(relevantKeyword.toLowerCase())) {
      return `${baseAlt} related to ${relevantKeyword}`;
    }
    
    return baseAlt;
  }

  /**
   * Generate caption for image
   */
  private generateCaption(prompt: string, imageType?: string): string {
    const type = imageType || 'illustration';
    return `${type.charAt(0).toUpperCase() + type.slice(1)} showing ${prompt.split(',')[0]}`;
  }

  /**
   * Calculate optimal placement in content
   */
  private calculateOptimalPlacement(content: string, placement?: string): number {
    switch (placement) {
      case 'top':
        return 0;
      case 'bottom':
        return content.length;
      case 'middle':
        return Math.floor(content.length / 2);
      default:
        // Find a good break point (after a paragraph)
        const paragraphBreaks = [...content.matchAll(/<\/p>/g)];
        if (paragraphBreaks.length > 1) {
          const midIndex = Math.floor(paragraphBreaks.length / 2);
          return paragraphBreaks[midIndex].index || Math.floor(content.length / 2);
        }
        return Math.floor(content.length / 2);
    }
  }

  /**
   * Calculate SEO score for image
   */
  private calculateSEOScore(altText: string, targetKeywords: string[] = []): number {
    let score = 50; // Base score
    
    // Alt text length (optimal: 50-125 characters)
    if (altText.length >= 50 && altText.length <= 125) {
      score += 20;
    } else if (altText.length > 10) {
      score += 10;
    }
    
    // Keyword inclusion
    const keywordMatches = targetKeywords.filter(keyword =>
      altText.toLowerCase().includes(keyword.toLowerCase())
    );
    score += keywordMatches.length * 15;
    
    // Descriptive quality (simple heuristic)
    if (altText.split(' ').length >= 5) {
      score += 15;
    }
    
    return Math.min(score, 100);
  }
}

// Export singleton instance
export const contentImageGenerator = new ContentImageGenerator();

// Export main functions
export const {
  analyzeContentForImages,
  generateContentImage,
  generateMultipleImages
} = contentImageGenerator;
