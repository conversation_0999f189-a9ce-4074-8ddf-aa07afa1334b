/**
 * WebSocket Service
 * Provides real-time communication with the backend agent system
 */

import { v4 as uuidv4 } from 'uuid';

// Event types
export type WebSocketEventType =
  | 'open'
  | 'close'
  | 'error'
  | 'message'
  | 'reconnecting'
  | 'agent_thinking'
  | 'agent_partial_response'
  | 'agent_response_complete'
  | 'agent_reasoning'
  | 'agent_error';

// Event handler type
export type WebSocketEventHandler = (event: any) => void;

// Reasoning step interface
export interface ReasoningStep {
  timestamp: number;
  type: string;
  agent?: string;
  agent_name?: string;
  content: string;
  details?: any;
}

// WebSocket message
export interface WebSocketMessage {
  type: string;
  content: any;
  session_id?: string;
  agent_id?: string;
  request_id?: string;
  reasoning_trace?: ReasoningStep[];
}

// WebSocket service options
export interface WebSocketServiceOptions {
  url?: string;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  debug?: boolean;
}

/**
 * WebSocket Service for real-time communication with the backend
 */
export class WebSocketService {
  private socket: WebSocket | null = null;
  private eventHandlers: Map<WebSocketEventType, WebSocketEventHandler[]> = new Map();
  private reconnectAttempts: number = 0;
  private reconnectTimeout: number | null = null;
  private sessionId: string | null = null;
  private messageQueue: WebSocketMessage[] = [];
  private options: WebSocketServiceOptions;
  private isConnected: boolean = false;

  /**
   * Create a new WebSocket service
   * @param options Service options
   */
  constructor(options: WebSocketServiceOptions = {}) {
    this.options = {
      url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:8001/api/v1/ws',
      autoReconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      debug: false,
      ...options
    };

    // Initialize event handlers map
    this.eventHandlers = new Map();

    // Generate a session ID
    this.sessionId = uuidv4();
  }

  /**
   * Connect to the WebSocket server
   * @returns Promise that resolves when connected
   */
  public connect(): Promise<void> {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      try {
        this.log(`Connecting to WebSocket server at ${this.options.url}`);
        this.socket = new WebSocket(this.options.url!);

        this.socket.onopen = (event) => {
          this.log('WebSocket connection established');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.triggerEvent('open', event);

          // Register the session
          this.registerSession();

          // Process any queued messages
          this.processQueue();

          resolve();
        };

        this.socket.onclose = (event) => {
          this.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
          this.isConnected = false;
          this.triggerEvent('close', event);

          if (this.options.autoReconnect) {
            this.scheduleReconnect();
          }
        };

        this.socket.onerror = (event) => {
          this.log('WebSocket error', event);
          this.triggerEvent('error', event);
          reject(event);
        };

        this.socket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.log('Received message', message);

            // Trigger general message event
            this.triggerEvent('message', message);

            // Trigger specific event based on message type
            if (message.type) {
              this.triggerEvent(message.type as WebSocketEventType, message.content);
            }
          } catch (error) {
            this.log('Error parsing message', error);
          }
        };
      } catch (error) {
        this.log('Error connecting to WebSocket server', error);
        reject(error);

        if (this.options.autoReconnect) {
          this.scheduleReconnect();
        }
      }
    });
  }

  /**
   * Register the current session with the server
   */
  private registerSession(): void {
    if (!this.isConnected || !this.socket) {
      this.queueMessage({
        type: 'register_session',
        content: {
          session_id: this.sessionId
        }
      });
      return;
    }

    this.send({
      type: 'register_session',
      content: {
        session_id: this.sessionId
      }
    });
  }

  /**
   * Send a message to the agent
   * @param agentId Agent ID
   * @param message Message to send
   * @returns Promise that resolves with the request ID
   */
  public sendAgentMessage(agentId: string, message: string): string {
    const requestId = uuidv4();

    this.send({
      type: 'agent_request',
      content: {
        message
      },
      session_id: this.sessionId!,
      agent_id: agentId,
      request_id: requestId
    });

    return requestId;
  }

  /**
   * Send a message to the WebSocket server
   * @param message Message to send
   */
  public send(message: WebSocketMessage): void {
    if (!this.isConnected || !this.socket || this.socket.readyState !== WebSocket.OPEN) {
      this.queueMessage(message);

      if (!this.isConnected) {
        this.connect().catch(error => {
          this.log('Error connecting to send message', error);
        });
      }

      return;
    }

    try {
      this.log('Sending message', message);
      this.socket.send(JSON.stringify(message));
    } catch (error) {
      this.log('Error sending message', error);
      this.queueMessage(message);
    }
  }

  /**
   * Queue a message to be sent when the connection is established
   * @param message Message to queue
   */
  private queueMessage(message: WebSocketMessage): void {
    this.log('Queueing message', message);
    this.messageQueue.push(message);
  }

  /**
   * Process the message queue
   */
  private processQueue(): void {
    if (!this.isConnected || !this.socket || this.socket.readyState !== WebSocket.OPEN) {
      return;
    }

    this.log(`Processing ${this.messageQueue.length} queued messages`);

    const queue = [...this.messageQueue];
    this.messageQueue = [];

    for (const message of queue) {
      this.send(message);
    }
  }

  /**
   * Schedule a reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimeout !== null) {
      return;
    }

    if (this.options.maxReconnectAttempts && this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.log(`Maximum reconnect attempts (${this.options.maxReconnectAttempts}) reached`);
      return;
    }

    this.reconnectAttempts++;

    const delay = this.options.reconnectInterval! * Math.pow(1.5, this.reconnectAttempts - 1);
    this.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

    this.triggerEvent('reconnecting', { attempt: this.reconnectAttempts, delay });

    this.reconnectTimeout = window.setTimeout(() => {
      this.reconnectTimeout = null;
      this.connect().catch(error => {
        this.log('Error reconnecting', error);
      });
    }, delay);
  }

  /**
   * Register an event handler
   * @param event Event type
   * @param handler Event handler
   */
  public on(event: WebSocketEventType, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }

    this.eventHandlers.get(event)!.push(handler);
  }

  /**
   * Remove an event handler
   * @param event Event type
   * @param handler Event handler
   */
  public off(event: WebSocketEventType, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(event)) {
      return;
    }

    const handlers = this.eventHandlers.get(event)!;
    const index = handlers.indexOf(handler);

    if (index !== -1) {
      handlers.splice(index, 1);
    }
  }

  /**
   * Trigger an event
   * @param event Event type
   * @param data Event data
   */
  private triggerEvent(event: WebSocketEventType, data: any): void {
    if (!this.eventHandlers.has(event)) {
      return;
    }

    for (const handler of this.eventHandlers.get(event)!) {
      try {
        handler(data);
      } catch (error) {
        this.log(`Error in event handler for ${event}`, error);
      }
    }
  }

  /**
   * Close the WebSocket connection
   */
  public disconnect(): void {
    if (this.reconnectTimeout !== null) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    this.isConnected = false;
  }

  /**
   * Log a message if debug is enabled
   * @param message Message to log
   * @param data Additional data
   */
  private log(message: string, data?: any): void {
    if (this.options.debug) {
      if (data) {
        console.log(`[WebSocketService] ${message}`, data);
      } else {
        console.log(`[WebSocketService] ${message}`);
      }
    }
  }
}
