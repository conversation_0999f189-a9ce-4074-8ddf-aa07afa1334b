/**
 * Sistema de Mejora de Prompts para Mockups
 * Usa IA real (mismo endpoint que ads/content) para mejorar prompts
 */

export interface PromptAnalysis {
  originalPrompt: string;
  enhancedPrompt: string;
  suggestions: string[];
  improvements: PromptImprovement[];
  confidence: number;
  category: 'product' | 'portrait' | 'mixed';
}

export interface PromptImprovement {
  type: 'photography' | 'lighting' | 'composition' | 'style' | 'context' | 'quality';
  description: string;
  before: string;
  after: string;
  impact: 'high' | 'medium' | 'low';
}

export interface PromptSuggestion {
  category: string;
  suggestions: string[];
  examples: string[];
}

class MockupPromptEnhancer {
  /**
   * Analiza y mejora un prompt para mockups usando IA real
   */
  async analyzeAndEnhancePrompt(prompt: string, type: 'product' | 'portrait' = 'product'): Promise<PromptAnalysis> {
    const analysis = this.analyzePrompt(prompt);
    
    // Usar el mismo endpoint que ads y content para mejora real con IA
    let enhancedPrompt = prompt;
    try {
      enhancedPrompt = await this.enhancePromptWithAI(prompt, type);
      console.log('✅ AI enhancement successful');
    } catch (error) {
      console.warn('⚠️ AI enhancement failed, using original prompt:', error);
      enhancedPrompt = prompt;
    }
    
    const improvements = this.generateImprovements(prompt, enhancedPrompt);
    const suggestions = this.generateSuggestions(type);

    return {
      originalPrompt: prompt,
      enhancedPrompt,
      suggestions,
      improvements,
      confidence: this.calculateConfidence(prompt, enhancedPrompt),
      category: analysis.category
    };
  }

  /**
   * Mejora el prompt usando IA real (mismo endpoint que ads/content)
   */
  private async enhancePromptWithAI(prompt: string, type: 'product' | 'portrait'): Promise<string> {
    const response = await fetch('/api/v1/content/enhance-prompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'dev-api-key-for-testing'
      },
      body: JSON.stringify({
        prompt: prompt,
        platform: type === 'product' ? 'mockup-product' : 'mockup-portrait',
        type: 'mockup',
        enhancement_type: 'optimization',
        context: type === 'product' 
          ? 'Professional product photography and mockup generation'
          : 'Professional portrait photography and realistic human generation'
      })
    });

    if (!response.ok) {
      throw new Error(`Enhancement API failed: ${response.status}`);
    }

    const result = await response.json();
    const enhanced = result.enhanced_prompt || result.content || result.text;
    
    if (!enhanced || enhanced.trim() === prompt.trim()) {
      throw new Error('No enhancement received from AI');
    }

    return enhanced.trim();
  }

  /**
   * Analiza el prompt original
   */
  private analyzePrompt(prompt: string): { category: 'product' | 'portrait' | 'mixed' } {
    const lowerPrompt = prompt.toLowerCase();
    
    const productKeywords = ['producto', 'product', 'botella', 'bottle', 'smartphone', 'auriculares', 'headphones'];
    const portraitKeywords = ['retrato', 'portrait', 'persona', 'person', 'hombre', 'man', 'mujer', 'woman'];
    
    const hasProduct = productKeywords.some(keyword => lowerPrompt.includes(keyword));
    const hasPortrait = portraitKeywords.some(keyword => lowerPrompt.includes(keyword));
    
    if (hasProduct && hasPortrait) return { category: 'mixed' };
    if (hasPortrait) return { category: 'portrait' };
    return { category: 'product' };
  }

  /**
   * Genera mejoras detectadas comparando original vs mejorado
   */
  private generateImprovements(original: string, enhanced: string): PromptImprovement[] {
    const improvements: PromptImprovement[] = [];
    
    if (enhanced.length > original.length * 1.2) {
      improvements.push({
        type: 'quality',
        description: 'IA agregó detalles profesionales',
        before: original,
        after: enhanced,
        impact: 'high'
      });
    }

    const qualityTerms = ['professional', 'high quality', 'detailed', 'realistic'];
    const hasNewQualityTerms = qualityTerms.some(term => 
      enhanced.toLowerCase().includes(term) && !original.toLowerCase().includes(term)
    );
    
    if (hasNewQualityTerms) {
      improvements.push({
        type: 'photography',
        description: 'IA mejoró términos fotográficos',
        before: original,
        after: enhanced,
        impact: 'high'
      });
    }

    return improvements;
  }

  /**
   * Genera sugerencias contextuales
   */
  private generateSuggestions(type: 'product' | 'portrait'): string[] {
    if (type === 'product') {
      return [
        'Especifica el contexto de uso (en manos, sobre mesa, en uso)',
        'Agrega detalles sobre el ambiente (oficina, hogar, exterior)',
        'Menciona el estilo de fotografía deseado (minimalista, comercial)',
        'Incluye detalles sobre la iluminación'
      ];
    } else {
      return [
        'Define la expresión facial deseada (confiada, amigable, seria)',
        'Especifica el estilo de vestimenta (profesional, casual)',
        'Agrega detalles sobre el fondo o ambiente',
        'Considera la edad y características demográficas'
      ];
    }
  }

  /**
   * Calcula la confianza basada en la mejora del prompt
   */
  private calculateConfidence(originalPrompt: string, enhancedPrompt: string): number {
    const originalLength = originalPrompt.trim().length;
    const enhancedLength = enhancedPrompt.trim().length;
    
    // Base confidence
    let confidence = 0.5;
    
    // Length bonus (longer prompts tend to be more detailed)
    if (originalLength > 50) confidence += 0.1;
    if (originalLength > 100) confidence += 0.1;
    
    // Enhancement bonus (if AI enhanced it significantly)
    if (enhancedLength > originalLength * 1.2) confidence += 0.2;
    
    // Quality terms bonus
    const qualityTerms = ['professional', 'high quality', 'detailed', 'realistic'];
    const hasQualityTerms = qualityTerms.some(term => 
      enhancedPrompt.toLowerCase().includes(term)
    );
    if (hasQualityTerms) confidence += 0.1;
    
    // Specific terms bonus
    const specificTerms = ['lighting', 'composition', 'photography', 'studio'];
    const hasSpecificTerms = specificTerms.some(term => 
      enhancedPrompt.toLowerCase().includes(term)
    );
    if (hasSpecificTerms) confidence += 0.1;
    
    return Math.max(0.3, Math.min(1.0, confidence));
  }
}

// Instancia global del servicio
export const mockupPromptEnhancer = new MockupPromptEnhancer();

/**
 * Función de conveniencia para mejorar prompts
 */
export async function enhanceMockupPrompt(
  prompt: string, 
  type: 'product' | 'portrait' = 'product'
): Promise<PromptAnalysis> {
  return mockupPromptEnhancer.analyzeAndEnhancePrompt(prompt, type);
}

/**
 * Obtiene sugerencias rápidas para un tipo específico
 */
export function getQuickSuggestions(type: 'product' | 'portrait'): PromptSuggestion[] {
  if (type === 'product') {
    return [
      {
        category: 'Contextos de Uso',
        suggestions: ['en manos', 'sobre escritorio', 'en uso real', 'ambiente lifestyle'],
        examples: ['smartphone en manos de una persona joven', 'botella de agua en un gimnasio moderno']
      },
      {
        category: 'Estilos Fotográficos',
        suggestions: ['minimalista', 'comercial', 'lifestyle', 'estudio profesional'],
        examples: ['fotografía comercial minimalista', 'estilo lifestyle natural']
      }
    ];
  } else {
    return [
      {
        category: 'Estilos de Retrato',
        suggestions: ['profesional', 'casual', 'artístico', 'comercial'],
        examples: ['retrato profesional de negocios', 'retrato casual y amigable']
      },
      {
        category: 'Expresiones y Poses',
        suggestions: ['confiada', 'amigable', 'seria', 'natural'],
        examples: ['expresión confiada y profesional', 'sonrisa natural y cálida']
      }
    ];
  }
}
