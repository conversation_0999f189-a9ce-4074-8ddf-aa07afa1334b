/**
 * Polotno Integration Service
 * Handles proper integration between Emma Studio and Polotno editor
 * Focuses on native text layers instead of HTML overlays
 */

export interface PolotnoTextLayer {
  type: 'text';
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  fontSize: number;
  fontFamily: string;
  fill: string;
  align: 'left' | 'center' | 'right';
  fontWeight?: 'normal' | 'bold';
  fontStyle?: 'normal' | 'italic';
}

export interface PolotnoImageLayer {
  type: 'image';
  x: number;
  y: number;
  width: number;
  height: number;
  src: string;
}

export interface PolotnoPageData {
  width: number;
  height: number;
  backgroundImage?: string;
  elements: (PolotnoTextLayer | PolotnoImageLayer)[];
}

export class PolotnoIntegrationService {
  /**
   * Create Polotno page data from post content
   */
  static createPageFromPost(
    imageUrl: string,
    textContent: string,
    dimensions: { width: number; height: number } = { width: 1080, height: 1080 },
    platform: string = 'Instagram'
  ): PolotnoPageData {
    const { width, height } = dimensions;
    
    // Create base page structure
    const pageData: PolotnoPageData = {
      width,
      height,
      elements: []
    };

    // Add background image as an image element (not background)
    if (imageUrl) {
      pageData.elements.push({
        type: 'image',
        x: 0,
        y: 0,
        width,
        height,
        src: imageUrl
      });
    }

    // Add text as native text layers
    if (textContent && textContent.trim()) {
      const textLayers = this.parseTextToLayers(textContent, width, height, platform);
      pageData.elements.push(...textLayers);
    }

    return pageData;
  }

  /**
   * Parse text content into multiple text layers
   * Handles bold text, different sizes, etc.
   */
  private static parseTextToLayers(
    text: string,
    canvasWidth: number,
    canvasHeight: number,
    platform: string
  ): PolotnoTextLayer[] {
    const layers: PolotnoTextLayer[] = [];
    
    // Split text into lines and process each
    const lines = text.split('\n').filter(line => line.trim());
    
    if (lines.length === 0) return layers;

    // Calculate positioning
    const totalLines = lines.length;
    const lineHeight = this.calculateOptimalFontSize(text, canvasWidth, canvasHeight, platform);
    const totalTextHeight = totalLines * lineHeight * 1.2; // 1.2 for line spacing
    
    // Center vertically
    let startY = (canvasHeight - totalTextHeight) / 2;
    
    lines.forEach((line, index) => {
      const layer = this.createTextLayer(
        line,
        canvasWidth,
        startY + (index * lineHeight * 1.2),
        lineHeight,
        platform
      );
      layers.push(layer);
    });

    return layers;
  }

  /**
   * Create a single text layer
   */
  private static createTextLayer(
    text: string,
    canvasWidth: number,
    y: number,
    fontSize: number,
    platform: string
  ): PolotnoTextLayer {
    // Detect if text should be bold (simple heuristic)
    const isBold = text.includes('**') || text.toUpperCase() === text;
    const cleanText = text.replace(/\*\*/g, ''); // Remove markdown bold

    // Platform-specific styling
    const platformStyles = this.getPlatformTextStyles(platform);

    return {
      type: 'text',
      x: canvasWidth * 0.1, // 10% margin
      y,
      width: canvasWidth * 0.8, // 80% width
      height: fontSize * 1.5,
      text: cleanText,
      fontSize,
      fontFamily: platformStyles.fontFamily,
      fill: platformStyles.color,
      align: 'center',
      fontWeight: isBold ? 'bold' : 'normal'
    };
  }

  /**
   * Calculate optimal font size based on content and canvas
   */
  private static calculateOptimalFontSize(
    text: string,
    canvasWidth: number,
    canvasHeight: number,
    platform: string
  ): number {
    const baseSize = Math.min(canvasWidth, canvasHeight) / 20; // Base calculation
    
    // Platform adjustments
    const platformMultipliers = {
      'Instagram': 1.0,
      'Facebook': 0.9,
      'LinkedIn': 0.8,
      'X': 1.1
    };

    const multiplier = platformMultipliers[platform as keyof typeof platformMultipliers] || 1.0;
    
    // Text length adjustments
    const textLength = text.length;
    let lengthMultiplier = 1.0;
    
    if (textLength > 100) lengthMultiplier = 0.8;
    else if (textLength > 50) lengthMultiplier = 0.9;
    else if (textLength < 20) lengthMultiplier = 1.2;

    return Math.max(16, Math.min(72, baseSize * multiplier * lengthMultiplier));
  }

  /**
   * Get platform-specific text styling
   */
  private static getPlatformTextStyles(platform: string) {
    const styles = {
      'Instagram': {
        fontFamily: 'Inter, Arial, sans-serif',
        color: '#FFFFFF'
      },
      'Facebook': {
        fontFamily: 'Helvetica, Arial, sans-serif',
        color: '#FFFFFF'
      },
      'LinkedIn': {
        fontFamily: 'Arial, sans-serif',
        color: '#000000'
      },
      'X': {
        fontFamily: 'Arial, sans-serif',
        color: '#FFFFFF'
      }
    };

    return styles[platform as keyof typeof styles] || styles.Instagram;
  }

  /**
   * Generate URL for Polotno editor with pre-loaded content
   */
  static generatePolotnoUrl(
    imageUrl: string,
    textContent: string = '',
    platform: string = 'Instagram',
    dimensions: { width: number; height: number } = { width: 1080, height: 1080 }
  ): string {
    // Create page data
    const pageData = this.createPageFromPost(imageUrl, textContent, dimensions, platform);
    
    // Encode page data for URL
    const encodedData = encodeURIComponent(JSON.stringify(pageData));
    
    // Generate URL with proper parameters
    const baseUrl = '/visual-editor';
    const params = new URLSearchParams({
      pageData: encodedData,
      platform: platform.toLowerCase(),
      width: dimensions.width.toString(),
      height: dimensions.height.toString()
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Save image temporarily for Polotno editor
   */
  static async saveImageForEditor(imageDataUrl: string): Promise<string> {
    try {
      const response = await fetch('/api/v1/temp-images/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: imageDataUrl,
          expiresIn: 3600 // 1 hour
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save image');
      }

      const result = await response.json();
      return result.tempImageId;
    } catch (error) {
      console.error('Error saving image for editor:', error);
      throw error;
    }
  }
}

export default PolotnoIntegrationService;
