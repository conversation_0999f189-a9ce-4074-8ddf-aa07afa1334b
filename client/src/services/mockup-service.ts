/**
 * Service for product mockup generation using <PERSON>'s exclusive AI technology.
 */

export interface MockupContext {
  name: string;
  description: string;
}

export interface MockupGenerationOptions {
  productImage?: File;
  context: string;
  productDescription: string;
  size?: "1024x1024" | "1024x1792" | "1792x1024";
  variations?: number;
}

export interface MockupVariation {
  success: boolean;
  image_url?: string;
  metadata?: {
    mockup_context: string;
    context_name: string;
    context_description: string;
    product_description: string;
    service_type: string;
    model: string;
    variation_number: number;
    total_variations: number;
    variation_style: string;
    [key: string]: any;
  };
  error?: string;
}

export interface MockupGenerationResult {
  success: boolean;
  variations?: MockupVariation[];
  total_generated?: number;
  context?: string;
  context_name?: string;
  product_description?: string;
  metadata?: {
    service_type: string;
    model: string;
    requested_variations: number;
    successful_variations: number;
    [key: string]: any;
  };
  message?: string;
  error?: string;
}

export interface MultiMockupGenerationOptions {
  productImage: File;
  contexts: string[];
  productDescription?: string;
  size?: "1024x1024" | "1024x1792" | "1792x1024";
}

export interface MultiMockupGenerationResult {
  success: boolean;
  results?: Record<string, MockupGenerationResult>;
  total_generated?: number;
  contexts_processed?: string[];
  product_description?: string;
  message?: string;
  error?: string;
}

export interface ContextsResponse {
  success: boolean;
  contexts?: Record<string, MockupContext>;
  total_contexts?: number;
  error?: string;
}

export interface EnhancedMockupContext extends MockupContext {
  confidence_score: number;
  recommended: boolean;
}

export interface ContextSuggestionsResponse {
  success: boolean;
  product_description?: string;
  suggested_contexts?: Record<string, EnhancedMockupContext>;
  all_contexts?: Record<string, MockupContext>;
  total_suggested?: number;
  reasoning?: string;
  confidence_scores?: Record<string, number>;
  analysis?: {
    product_type_detected: boolean;
    top_recommendation: string | null;
    high_confidence_count: number;
  };
  error?: string;
}

const API_BASE_URL = "/api/mockups";

/**
 * Get all available mockup contexts
 */
export async function getMockupContexts(): Promise<ContextsResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/contexts`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error getting mockup contexts:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al obtener contextos"
    };
  }
}

/**
 * Generate a single product mockup
 */
export async function generateMockup(options: MockupGenerationOptions): Promise<MockupGenerationResult> {
  try {
    const formData = new FormData();

    // Only append image if provided
    if (options.productImage) {
      formData.append("product_image", options.productImage);
    }

    formData.append("context", options.context);
    formData.append("product_description", options.productDescription || "");
    formData.append("size", options.size || "1024x1024");
    formData.append("variations", String(options.variations || 4));

    const response = await fetch(`${API_BASE_URL}/generate`, {
      method: "POST",
      headers: {
        "X-API-Key": "dev-api-key-for-testing"
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error generating mockup:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al generar mockup"
    };
  }
}

/**
 * Generate multiple mockups with different contexts
 */
export async function generateMultipleMockups(options: MultiMockupGenerationOptions): Promise<MultiMockupGenerationResult> {
  try {
    const formData = new FormData();
    formData.append("product_image", options.productImage);
    formData.append("contexts", JSON.stringify(options.contexts));
    formData.append("product_description", options.productDescription || "");
    formData.append("size", options.size || "1024x1024");

    const response = await fetch(`${API_BASE_URL}/generate-multiple`, {
      method: "POST",
      headers: {
        "X-API-Key": "dev-api-key-for-testing"
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error generating multiple mockups:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al generar múltiples mockups"
    };
  }
}

/**
 * Get context suggestions based on product description
 */
export async function getContextSuggestions(productDescription: string): Promise<ContextSuggestionsResponse> {
  try {
    const formData = new FormData();
    formData.append("product_description", productDescription);

    const response = await fetch(`${API_BASE_URL}/preview-contexts`, {
      method: "POST",
      headers: {
        "X-API-Key": "dev-api-key-for-testing"
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error getting context suggestions:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al obtener sugerencias"
    };
  }
}

/**
 * Download mockup image
 */
export function downloadMockup(imageUrl: string, filename?: string): void {
  try {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename || `mockup-${Date.now()}.png`;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Error downloading mockup:", error);
  }
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: 'El archivo debe ser una imagen' };
  }

  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return { valid: false, error: 'La imagen debe ser menor a 10MB' };
  }

  // Check supported formats
  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!supportedFormats.includes(file.type)) {
    return { valid: false, error: 'Formato no soportado. Usa JPG, PNG o WEBP' };
  }

  return { valid: true };
}

/**
 * Create image preview URL
 */
export function createImagePreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Cleanup image preview URL
 */
export function cleanupImagePreview(url: string): void {
  URL.revokeObjectURL(url);
}

// Portrait Generation Interfaces
export interface PortraitGenerationOptions {
  prompt: string;
  gender?: "any" | "male" | "female";
  age?: "any" | "young" | "adult" | "senior";
  ethnicity?: "any" | "caucasian" | "asian" | "african" | "hispanic" | "mixed";
  style?: "professional" | "casual" | "artistic" | "commercial";
  lighting?: "studio" | "natural" | "dramatic" | "soft";
  size?: "1024x1024" | "1024x1792" | "1792x1024";
  variations?: number;
}

export interface PortraitGenerationResult {
  success: boolean;
  variations: MockupVariation[];
  total_generated: number;
  portrait_style: string;
  lighting: string;
  enhanced_prompt: string;
  error?: string;
}

/**
 * Generate realistic human portraits
 */
export async function generatePortrait(options: PortraitGenerationOptions): Promise<PortraitGenerationResult> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("gender", options.gender || "any");
    formData.append("age", options.age || "adult");
    formData.append("ethnicity", options.ethnicity || "any");
    formData.append("style", options.style || "professional");
    formData.append("lighting", options.lighting || "studio");
    formData.append("size", options.size || "1024x1024");
    formData.append("variations", String(options.variations || 4));

    const response = await fetch(`${API_BASE_URL}/generate-portrait`, {
      method: "POST",
      headers: {
        "X-API-Key": "dev-api-key-for-testing"
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error generating portrait:", error);
    return {
      success: false,
      variations: [],
      total_generated: 0,
      portrait_style: options.style || "professional",
      lighting: options.lighting || "studio",
      enhanced_prompt: options.prompt,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}
