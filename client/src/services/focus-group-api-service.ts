import api from "@/lib/api";

// API base URL - using consistent path format
const API_BASE = '/api';

// Define a type for the expected error response structure
interface ErrorResponse {
  error?: {
    code?: string;
    message?: string;
    request_id?: string;
    trace_snippet?: string[];
  };
}

// Types for focus group operations
export interface FocusGroupSimulationRequest {
  content: string;
  product_category?: string;
  context?: string;
  questions?: string[];
  num_participants?: number;
  discussion_rounds?: number;
}

export interface FocusGroupParticipant {
  id: string;
  name: string;
  age: number;
  gender: string;
  occupation: string;
  personality: string;
  background: string;
  interests: string[];
}

export interface FocusGroupConversation {
  participant_id: string;
  participant_name: string;
  message: string;
  sentiment: string;
  timestamp: string;
}

export interface FocusGroupDiscussion {
  round: number;
  topic: string;
  conversation: FocusGroupConversation[];
}

export interface FocusGroupSummary {
  overall_sentiment: string;
  key_insights: string[];
  recommendations: string[];
  demographic_patterns: Record<string, any>;
  sentiment_analysis: Record<string, any>;
}

export interface FocusGroupSimulation {
  discussions: FocusGroupDiscussion[];
  summary: FocusGroupSummary;
}

export interface FocusGroupResponse {
  status: string;
  error_message?: string;
  focus_group_simulation?: FocusGroupSimulation;
  timestamp: string;
}

export interface TextAssistRequest {
  content: string;
  context?: string;
}

export interface TextAssistResponse {
  status: string;
  suggestions?: string[];
  error_message?: string;
}

/**
 * Service for interacting with the Focus Group API endpoints
 */
class FocusGroupApiService {
  /**
   * Simulate a focus group session
   * @param request Focus group simulation request
   * @returns Focus group simulation response
   */
  async simulateFocusGroup(request: FocusGroupSimulationRequest): Promise<FocusGroupResponse> {
    try {
      console.log('Simulating focus group with request:', request);

      const { data } = await api.post(`${API_BASE}/simulate-focus-group`, request);
      
      console.log('Focus group simulation response:', data);
      return data;
      
    } catch (error: any) {
      console.error("Error simulating focus group:", error);

      // Extract error message from response
      const errorData = error?.response?.data as ErrorResponse;
      const errorMessage = errorData?.error?.message || 
                          errorData?.detail || 
                          error.message || 
                          "Unknown error occurred during focus group simulation";

      throw new Error(errorMessage);
    }
  }

  /**
   * Get text improvement suggestions
   * @param request Text assist request
   * @returns Text improvement suggestions
   */
  async getTextSuggestions(request: TextAssistRequest): Promise<TextAssistResponse> {
    try {
      console.log('Getting text suggestions for:', request);

      const { data } = await api.post(`${API_BASE}/text-autocorrect`, request);
      
      console.log('Text suggestions response:', data);
      return data;
      
    } catch (error: any) {
      console.error("Error getting text suggestions:", error);

      // Extract error message from response
      const errorData = error?.response?.data as ErrorResponse;
      const errorMessage = errorData?.error?.message || 
                          errorData?.detail || 
                          error.message || 
                          "Unknown error occurred during text assistance";

      throw new Error(errorMessage);
    }
  }

  /**
   * Get recent focus group simulations
   * @param limit Maximum number of simulations to return
   * @returns List of recent simulations
   */
  async getRecentSimulations(limit: number = 5): Promise<any[]> {
    try {
      console.log('Getting recent focus group simulations...');

      const { data } = await api.get(`${API_BASE}/focus-group/recent?limit=${limit}`);
      
      console.log('Recent simulations response:', data);
      return data.simulations || [];
      
    } catch (error: any) {
      console.error("Error getting recent simulations:", error);

      // Extract error message from response
      const errorData = error?.response?.data as ErrorResponse;
      const errorMessage = errorData?.error?.message || 
                          errorData?.detail || 
                          error.message || 
                          "Unknown error occurred while fetching recent simulations";

      throw new Error(errorMessage);
    }
  }

  /**
   * Get favorite focus group simulations
   * @param limit Maximum number of simulations to return
   * @returns List of favorite simulations
   */
  async getFavoriteSimulations(limit: number = 50): Promise<any[]> {
    try {
      console.log('Getting favorite focus group simulations...');

      const { data } = await api.get(`${API_BASE}/focus-group/favorites?limit=${limit}`);
      
      console.log('Favorite simulations response:', data);
      return data.simulations || [];
      
    } catch (error: any) {
      console.error("Error getting favorite simulations:", error);

      // Extract error message from response
      const errorData = error?.response?.data as ErrorResponse;
      const errorMessage = errorData?.error?.message || 
                          errorData?.detail || 
                          error.message || 
                          "Unknown error occurred while fetching favorite simulations";

      throw new Error(errorMessage);
    }
  }

  /**
   * Get a specific focus group simulation by ID
   * @param simulationId The simulation ID
   * @returns The simulation data
   */
  async getSimulationById(simulationId: string): Promise<any> {
    try {
      console.log('Getting focus group simulation by ID:', simulationId);

      const { data } = await api.get(`${API_BASE}/focus-group/${simulationId}`);
      
      console.log('Simulation by ID response:', data);
      return data.simulation;
      
    } catch (error: any) {
      console.error("Error getting simulation by ID:", error);

      // Extract error message from response
      const errorData = error?.response?.data as ErrorResponse;
      const errorMessage = errorData?.error?.message || 
                          errorData?.detail || 
                          error.message || 
                          "Unknown error occurred while fetching simulation";

      throw new Error(errorMessage);
    }
  }

  /**
   * Toggle favorite status of a simulation
   * @param simulationId The simulation ID
   * @returns Updated simulation data
   */
  async toggleFavorite(simulationId: string): Promise<any> {
    try {
      console.log('Toggling favorite status for simulation:', simulationId);

      const { data } = await api.post(`${API_BASE}/focus-group/${simulationId}/toggle-favorite`);
      
      console.log('Toggle favorite response:', data);
      return data.simulation;
      
    } catch (error: any) {
      console.error("Error toggling favorite status:", error);

      // Extract error message from response
      const errorData = error?.response?.data as ErrorResponse;
      const errorMessage = errorData?.error?.message || 
                          errorData?.detail || 
                          error.message || 
                          "Unknown error occurred while toggling favorite status";

      throw new Error(errorMessage);
    }
  }

  /**
   * Delete a focus group simulation
   * @param simulationId The simulation ID
   * @returns Success status
   */
  async deleteSimulation(simulationId: string): Promise<boolean> {
    try {
      console.log('Deleting focus group simulation:', simulationId);

      await api.delete(`${API_BASE}/focus-group/${simulationId}`);
      
      console.log('Simulation deleted successfully');
      return true;
      
    } catch (error: any) {
      console.error("Error deleting simulation:", error);

      // Extract error message from response
      const errorData = error?.response?.data as ErrorResponse;
      const errorMessage = errorData?.error?.message || 
                          errorData?.detail || 
                          error.message || 
                          "Unknown error occurred while deleting simulation";

      throw new Error(errorMessage);
    }
  }
}

// Export a singleton instance
export const focusGroupApiService = new FocusGroupApiService();
export default focusGroupApiService;
