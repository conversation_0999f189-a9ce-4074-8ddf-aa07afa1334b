/**
 * Auto-save Manager Service for Content Builder
 * Handles debounced auto-saving with project context and localStorage backup
 */

// Simple debounce utility
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export interface AutoSaveConfig {
  projectId: string;
  debounceMs?: number;
  enableLocalStorage?: boolean;
  onSaveSuccess?: (timestamp: Date) => void;
  onSaveError?: (error: Error) => void;
}

export interface ContentData {
  content: string;
  topic?: string;
  wordCount: number;
  charCount: number;
  lastModified: Date;
}

export class AutoSaveManager {
  private config: AutoSaveConfig;
  private debouncedSave: (...args: any[]) => void;
  private localStorageKey: string;

  constructor(config: AutoSaveConfig) {
    this.config = {
      debounceMs: 5000, // 5 seconds - mucho más tiempo
      enableLocalStorage: true,
      ...config
    };

    this.localStorageKey = `content-builder-${this.config.projectId}`;
    this.debouncedSave = debounce(this.performSave.bind(this), this.config.debounceMs);
  }

  /**
   * Save content with debouncing
   */
  saveContent(contentData: ContentData): void {
    // Immediate localStorage backup if enabled
    if (this.config.enableLocalStorage) {
      this.saveToLocalStorage(contentData);
    }
    
    // Debounced server save
    this.debouncedSave(contentData);
  }

  /**
   * Force immediate save (bypass debouncing)
   */
  async forceSave(contentData: ContentData): Promise<void> {
    await this.performSave(contentData);
  }

  /**
   * Restore content from localStorage
   */
  restoreFromLocalStorage(): ContentData | null {
    if (!this.config.enableLocalStorage) return null;
    
    try {
      const stored = localStorage.getItem(this.localStorageKey);
      if (stored) {
        const data = JSON.parse(stored);
        return {
          ...data,
          lastModified: new Date(data.lastModified)
        };
      }
    } catch (error) {
      console.error('Error restoring from localStorage:', error);
    }
    
    return null;
  }

  /**
   * Clear localStorage backup
   */
  clearLocalStorage(): void {
    if (this.config.enableLocalStorage) {
      localStorage.removeItem(this.localStorageKey);
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AutoSaveConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Recreate debounced function if debounce time changed
    if (newConfig.debounceMs) {
      this.debouncedSave = debounce(this.performSave.bind(this), this.config.debounceMs);
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    // Note: Our simple debounce doesn't have cancel method
    // The timeout will be cleared automatically when the function is called again
  }

  private saveToLocalStorage(contentData: ContentData): void {
    try {
      localStorage.setItem(this.localStorageKey, JSON.stringify(contentData));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  private async performSave(contentData: ContentData): Promise<void> {
    try {
      const API_BASE = import.meta.env.VITE_API_BASE || "http://localhost:8001";
      
      const response = await fetch(`${API_BASE}/api/v1/seo-gpt/projects/${this.config.projectId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content_text: contentData.content,
          content_length: contentData.content.length,
          word_count: contentData.wordCount,
          updated_at: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error(`Save failed: ${response.statusText}`);
      }

      // Clear localStorage backup after successful server save
      if (this.config.enableLocalStorage) {
        this.clearLocalStorage();
      }

      this.config.onSaveSuccess?.(new Date());
      
    } catch (error) {
      const saveError = error instanceof Error ? error : new Error('Unknown save error');
      console.error('Auto-save error:', saveError);
      this.config.onSaveError?.(saveError);
    }
  }
}

export default AutoSaveManager;
