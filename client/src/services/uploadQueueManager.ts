/**
 * Upload Queue Manager
 * Manages simultaneous image uploads with configurable concurrency limits
 * Prevents database save failures by controlling upload batch sizes
 */

import { TLAsset } from '@tldraw/tldraw'

export interface QueuedUpload {
  id: string
  asset: TLAsset
  file: File
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  result?: { src: string }
  error?: string
  timestamp: number
}

export interface UploadQueueConfig {
  maxConcurrent: number
  retryAttempts: number
  retryDelay: number
}

export interface UploadQueueEvents {
  onUploadStart?: (upload: QueuedUpload) => void
  onUploadProgress?: (upload: QueuedUpload) => void
  onUploadComplete?: (upload: QueuedUpload) => void
  onUploadError?: (upload: QueuedUpload, error: string) => void
  onQueueUpdate?: (queue: QueuedUpload[]) => void
}

export class UploadQueueManager {
  private queue: QueuedUpload[] = []
  private processing: Set<string> = new Set()
  private config: UploadQueueConfig
  private events: UploadQueueEvents
  private uploadFunction: (asset: TLAsset, file: File) => Promise<{ src: string }>

  constructor(
    uploadFunction: (asset: TLAsset, file: File) => Promise<{ src: string }>,
    config: Partial<UploadQueueConfig> = {},
    events: UploadQueueEvents = {}
  ) {
    this.uploadFunction = uploadFunction
    this.config = {
      maxConcurrent: 3, // Safe default based on testing
      retryAttempts: 2,
      retryDelay: 1000,
      ...config
    }
    this.events = events

    console.log('🚀 Upload Queue Manager initialized', {
      maxConcurrent: this.config.maxConcurrent,
      retryAttempts: this.config.retryAttempts
    })
  }

  /**
   * Add uploads to the queue
   */
  async addUploads(uploads: Array<{ asset: TLAsset; file: File }>): Promise<string[]> {
    const uploadIds: string[] = []

    for (const { asset, file } of uploads) {
      const queuedUpload: QueuedUpload = {
        id: this.generateUploadId(),
        asset,
        file,
        status: 'pending',
        progress: 0,
        timestamp: Date.now()
      }

      this.queue.push(queuedUpload)
      uploadIds.push(queuedUpload.id)

      console.log('📥 Added upload to queue', {
        id: queuedUpload.id,
        fileName: file.name,
        queueLength: this.queue.length
      })
    }

    this.events.onQueueUpdate?.(this.queue)
    this.processQueue()

    return uploadIds
  }

  /**
   * Add a single upload to the queue
   */
  async addUpload(asset: TLAsset, file: File): Promise<string> {
    const [uploadId] = await this.addUploads([{ asset, file }])
    return uploadId
  }

  /**
   * Get upload status by ID
   */
  getUpload(id: string): QueuedUpload | undefined {
    return this.queue.find(upload => upload.id === id)
  }

  /**
   * Get all uploads with optional status filter
   */
  getUploads(status?: QueuedUpload['status']): QueuedUpload[] {
    if (status) {
      return this.queue.filter(upload => upload.status === status)
    }
    return [...this.queue]
  }

  /**
   * Get queue statistics
   */
  getQueueStats() {
    const stats = {
      total: this.queue.length,
      pending: this.queue.filter(u => u.status === 'pending').length,
      processing: this.queue.filter(u => u.status === 'processing').length,
      completed: this.queue.filter(u => u.status === 'completed').length,
      failed: this.queue.filter(u => u.status === 'failed').length,
      activeSlots: this.processing.size,
      maxConcurrent: this.config.maxConcurrent
    }

    return stats
  }

  /**
   * Clear completed and failed uploads from queue
   */
  clearCompleted(): void {
    const beforeLength = this.queue.length
    this.queue = this.queue.filter(upload => 
      upload.status === 'pending' || upload.status === 'processing'
    )
    
    const cleared = beforeLength - this.queue.length
    if (cleared > 0) {
      console.log(`🧹 Cleared ${cleared} completed/failed uploads from queue`)
      this.events.onQueueUpdate?.(this.queue)
    }
  }

  /**
   * Process the upload queue
   */
  private async processQueue(): Promise<void> {
    // Check if we can start more uploads
    const availableSlots = this.config.maxConcurrent - this.processing.size
    if (availableSlots <= 0) {
      return
    }

    // Get pending uploads
    const pendingUploads = this.queue.filter(upload => upload.status === 'pending')
    const uploadsToStart = pendingUploads.slice(0, availableSlots)

    if (uploadsToStart.length === 0) {
      return
    }

    console.log(`🔄 Processing ${uploadsToStart.length} uploads (${this.processing.size}/${this.config.maxConcurrent} slots used)`)

    // Start uploads
    for (const upload of uploadsToStart) {
      this.processUpload(upload)
    }
  }

  /**
   * Process a single upload
   */
  private async processUpload(upload: QueuedUpload): Promise<void> {
    upload.status = 'processing'
    upload.progress = 0
    this.processing.add(upload.id)

    console.log(`📤 Starting upload: ${upload.file.name} (${upload.id})`)
    
    this.events.onUploadStart?.(upload)
    this.events.onQueueUpdate?.(this.queue)

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        if (upload.status === 'processing' && upload.progress < 90) {
          upload.progress += Math.random() * 20
          upload.progress = Math.min(upload.progress, 90)
          this.events.onUploadProgress?.(upload)
        }
      }, 200)

      // Perform the actual upload
      const result = await this.uploadFunction(upload.asset, upload.file)

      clearInterval(progressInterval)

      // Complete the upload
      upload.status = 'completed'
      upload.progress = 100
      upload.result = result

      console.log(`✅ Upload completed: ${upload.file.name} (${upload.id})`)
      
      this.events.onUploadComplete?.(upload)

    } catch (error) {
      upload.status = 'failed'
      upload.error = error instanceof Error ? error.message : 'Upload failed'

      console.error(`❌ Upload failed: ${upload.file.name} (${upload.id})`, error)
      
      this.events.onUploadError?.(upload, upload.error)

      // TODO: Implement retry logic if needed
    } finally {
      this.processing.delete(upload.id)
      this.events.onQueueUpdate?.(this.queue)

      // Continue processing queue
      setTimeout(() => this.processQueue(), 100)
    }
  }

  /**
   * Generate unique upload ID
   */
  private generateUploadId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Wait for all uploads to complete
   */
  async waitForCompletion(): Promise<void> {
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const hasActiveUploads = this.queue.some(upload => 
          upload.status === 'pending' || upload.status === 'processing'
        )

        if (!hasActiveUploads) {
          resolve()
        } else {
          setTimeout(checkCompletion, 100)
        }
      }

      checkCompletion()
    })
  }

  /**
   * Cancel all pending uploads
   */
  cancelPending(): void {
    const pendingUploads = this.queue.filter(upload => upload.status === 'pending')
    
    for (const upload of pendingUploads) {
      upload.status = 'failed'
      upload.error = 'Cancelled by user'
    }

    if (pendingUploads.length > 0) {
      console.log(`🚫 Cancelled ${pendingUploads.length} pending uploads`)
      this.events.onQueueUpdate?.(this.queue)
    }
  }

  /**
   * Get upload results for completed uploads
   */
  getResults(): Array<{ id: string; result?: { src: string }; error?: string }> {
    return this.queue
      .filter(upload => upload.status === 'completed' || upload.status === 'failed')
      .map(upload => ({
        id: upload.id,
        result: upload.result,
        error: upload.error
      }))
  }
}
