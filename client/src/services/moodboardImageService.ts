/**
 * MoodBoard Image Service
 * Handles image storage and retrieval for MoodBoard with Supabase integration
 * Based on the successful Visual Complexity Analyzer image service
 */

import { supabase } from '@/lib/supabase'
import { api } from '@/lib/api'
import { TLAssetStore, TLAsset, uniqueId } from '@tldraw/tldraw'
import { UploadQueueManager, QueuedUpload } from './uploadQueueManager'

export interface ImageUploadResult {
  success: boolean
  filePath?: string
  url?: string
  error?: string
}

export interface ImageRetrievalResult {
  success: boolean
  url?: string
  error?: string
}

export class MoodboardImageService {
  private readonly bucketName = 'design-analysis-images'

  /**
   * Upload an image file to Supabase Storage for MoodBoard
   * Creates a unique file path using user ID and timestamp
   */
  async uploadImage(file: File, userId: string): Promise<ImageUploadResult> {
    try {
      console.log('🖼️ MoodBoard: Starting image upload:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        userId
      })

      // Validate file
      if (!file || file.size === 0) {
        return { success: false, error: 'Invalid file provided' }
      }

      // Check file size (max 10MB)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        return { success: false, error: 'File size exceeds 10MB limit' }
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        return { success: false, error: 'File type not supported. Use JPEG, PNG, WebP, or GIF.' }
      }

      // Generate unique file path
      const timestamp = Date.now()
      const randomId = Math.random().toString(36).substring(2, 10)
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'png'
      const fileName = `${timestamp}_${randomId}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`
      const filePath = `${userId}/${fileName}`

      console.log('📁 Generated file path:', filePath)

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        console.error('❌ Upload failed:', error)
        return { success: false, error: `Upload failed: ${error.message}` }
      }

      console.log('✅ Upload successful:', data)

      // Get the image URL using our new backend endpoint
      const imageUrl = await this.getImageUrl(filePath)
      
      return {
        success: true,
        filePath,
        url: imageUrl || undefined
      }

    } catch (error) {
      console.error('💥 Upload error:', error)
      return { success: false, error: `Upload error: ${error instanceof Error ? error.message : 'Unknown error'}` }
    }
  }

  /**
   * Get an image URL from Supabase Storage using the backend endpoint
   * Uses the new authenticated image retrieval service
   */
  async getImageUrl(filePath: string): Promise<string | null> {
    try {
      console.log('🔍 MoodBoard: Getting image URL for:', filePath)

      if (!filePath) {
        console.warn('⚠️ No file path provided')
        return null
      }

      // Use the new backend image retrieval endpoint
      const response = await api.get(`/api/image/${filePath}`, {
        responseType: 'blob'
      })

      if (response.status === 200 && response.data) {
        // Create a blob URL for the image
        const blob = response.data
        const url = URL.createObjectURL(blob)
        
        console.log('✅ MoodBoard: Image URL created successfully')
        return url
      } else {
        console.warn('⚠️ MoodBoard: Failed to get image from backend')
        return null
      }

    } catch (error) {
      console.error('❌ MoodBoard: Error getting image URL:', error)
      
      // Fallback: try signed URL method
      try {
        console.log('🔄 MoodBoard: Trying signed URL fallback...')
        return await this.getSignedUrl(filePath)
      } catch (fallbackError) {
        console.error('❌ MoodBoard: Fallback also failed:', fallbackError)
        return null
      }
    }
  }

  /**
   * Get a signed URL for temporary image access
   * Fallback method when direct retrieval fails
   */
  async getSignedUrl(filePath: string): Promise<string | null> {
    try {
      console.log('🔐 MoodBoard: Creating signed URL for:', filePath)

      const response = await api.get(`/api/image-url/${filePath}`)
      
      if (response.data?.success && response.data?.signed_url) {
        console.log('✅ MoodBoard: Signed URL created successfully')
        return response.data.signed_url
      } else {
        console.warn('⚠️ MoodBoard: Failed to create signed URL')
        return null
      }

    } catch (error) {
      console.error('❌ MoodBoard: Error creating signed URL:', error)
      return null
    }
  }

  /**
   * Convert a File to a data URL for immediate display
   * Used for instant preview while uploading to storage
   */
  async fileToDataUrl(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  /**
   * Process an image for MoodBoard use
   * Uploads to storage and returns both immediate data URL and storage URL
   */
  async processImageForMoodboard(file: File, userId: string): Promise<{
    dataUrl: string
    storageUrl?: string
    filePath?: string
    success: boolean
    error?: string
  }> {
    try {
      console.log('🎨 MoodBoard: Processing image for moodboard use')

      // Get immediate data URL for instant display
      const dataUrl = await this.fileToDataUrl(file)

      // Upload to storage for persistence
      const uploadResult = await this.uploadImage(file, userId)

      if (uploadResult.success) {
        return {
          dataUrl,
          storageUrl: uploadResult.url,
          filePath: uploadResult.filePath,
          success: true
        }
      } else {
        // Return data URL even if upload fails, for immediate use
        console.warn('⚠️ Storage upload failed, using data URL only:', uploadResult.error)
        return {
          dataUrl,
          success: true,
          error: `Storage upload failed: ${uploadResult.error}`
        }
      }

    } catch (error) {
      console.error('💥 Error processing image for moodboard:', error)
      return {
        dataUrl: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Resolve image URLs in tldraw data
   * Converts file paths to accessible URLs for display
   */
  async resolveImageUrlsInTldrawData(tldrawData: any): Promise<any> {
    try {
      console.log('🔄 MoodBoard: Resolving image URLs in tldraw data')

      if (!tldrawData?.store) {
        return tldrawData
      }

      const resolvedData = { ...tldrawData }
      let resolvedCount = 0

      // Process each shape in the store
      for (const [shapeId, shape] of Object.entries(resolvedData.store)) {
        if ((shape as any).type === 'image' && (shape as any).props?.src) {
          const src = (shape as any).props.src

          // Skip if already a data URL or blob URL
          if (src.startsWith('data:') || src.startsWith('blob:') || src.startsWith('http')) {
            continue
          }

          // Try to resolve the file path to a URL
          const resolvedUrl = await this.getImageUrl(src)
          if (resolvedUrl) {
            (resolvedData.store as any)[shapeId].props.src = resolvedUrl
            resolvedCount++
            console.log(`✅ Resolved image ${shapeId}: ${src} -> URL`)
          } else {
            console.warn(`⚠️ Failed to resolve image ${shapeId}: ${src}`)
          }
        }
      }

      console.log(`🎯 MoodBoard: Resolved ${resolvedCount} image URLs`)
      return resolvedData

    } catch (error) {
      console.error('💥 Error resolving image URLs in tldraw data:', error)
      return tldrawData // Return original data if resolution fails
    }
  }

  /**
   * Test image access functionality
   * Useful for debugging image display issues
   */
  async testImageAccess(): Promise<{
    success: boolean
    results: any
    errors: string[]
  }> {
    try {
      console.log('🧪 MoodBoard: Testing image access functionality')

      const response = await api.get('/api/test-image-access')
      
      return {
        success: true,
        results: response.data,
        errors: []
      }

    } catch (error) {
      console.error('❌ MoodBoard: Image access test failed:', error)
      return {
        success: false,
        results: null,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }
}

/**
 * Custom TLAssetStore implementation for MoodBoard
 * Handles image uploads and retrieval for tldraw integration
 * Now includes upload queue management to prevent simultaneous upload failures
 */
export class MoodboardAssetStore implements TLAssetStore {
  private imageService: MoodboardImageService
  private userId: string
  private uploadQueue: UploadQueueManager

  constructor(userId: string, onQueueUpdate?: (queue: QueuedUpload[]) => void) {
    this.imageService = new MoodboardImageService()
    this.userId = userId

    // Initialize upload queue with safe concurrency limit
    this.uploadQueue = new UploadQueueManager(
      this.performUpload.bind(this),
      {
        maxConcurrent: 3, // Safe limit to prevent database save failures
        retryAttempts: 2,
        retryDelay: 1000
      },
      {
        onQueueUpdate,
        onUploadStart: (upload) => {
          console.log('🚀 Upload started:', upload.file.name)
        },
        onUploadComplete: (upload) => {
          console.log('✅ Upload completed:', upload.file.name)
        },
        onUploadError: (upload, error) => {
          console.error('❌ Upload failed:', upload.file.name, error)
        }
      }
    )
  }

  /**
   * Upload asset to storage using queue management
   * This method is called when the user creates a file (drag/drop, paste, etc.)
   */
  async upload(asset: TLAsset, file: File): Promise<{ src: string }> {
    try {
      console.log('🎨 MoodBoard Asset Store: Queuing upload', {
        assetId: asset.id,
        fileName: file.name,
        fileSize: file.size,
        queueStats: this.uploadQueue.getQueueStats()
      })

      // Add upload to queue and wait for completion
      const uploadId = await this.uploadQueue.addUpload(asset, file)

      // Wait for this specific upload to complete
      await this.waitForUploadCompletion(uploadId)

      // Get the result
      const upload = this.uploadQueue.getUpload(uploadId)

      if (upload?.status === 'completed' && upload.result) {
        console.log('✅ MoodBoard Asset Store: Queued upload successful', {
          assetId: asset.id,
          uploadId,
          src: upload.result.src.substring(0, 100) + '...'
        })

        return upload.result
      } else {
        throw new Error(upload?.error || 'Upload failed in queue')
      }

    } catch (error) {
      console.error('❌ MoodBoard Asset Store: Queued upload failed', error)

      // Fallback to data URL for immediate display
      const dataUrl = await this.fileToDataUrl(file)
      console.log('🔄 MoodBoard Asset Store: Using data URL fallback')

      return { src: dataUrl }
    }
  }

  /**
   * Wait for a specific upload to complete
   */
  private async waitForUploadCompletion(uploadId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const checkStatus = () => {
        const upload = this.uploadQueue.getUpload(uploadId)

        if (!upload) {
          reject(new Error('Upload not found in queue'))
          return
        }

        if (upload.status === 'completed') {
          resolve()
        } else if (upload.status === 'failed') {
          reject(new Error(upload.error || 'Upload failed'))
        } else {
          // Still processing, check again
          setTimeout(checkStatus, 100)
        }
      }

      checkStatus()
    })
  }

  /**
   * Perform the actual upload (used by queue manager)
   */
  private async performUpload(asset: TLAsset, file: File): Promise<{ src: string }> {
    // Upload image using the existing service
    const uploadResult = await this.imageService.uploadImage(file, this.userId)

    if (!uploadResult.success || !uploadResult.filePath) {
      throw new Error(uploadResult.error || 'Upload failed')
    }

    // Get the URL for the uploaded image
    const imageUrl = await this.imageService.getImageUrl(uploadResult.filePath)

    if (!imageUrl) {
      throw new Error('Failed to get image URL after upload')
    }

    return { src: imageUrl }
  }

  /**
   * Resolve asset URL for display
   * This method is called whenever the editor needs to display an asset
   */
  resolve(asset: TLAsset): string | null {
    try {
      const src = asset.props.src as string

      if (!src) {
        console.warn('⚠️ MoodBoard Asset Store: No src found in asset', asset.id)
        return null
      }

      // Return the src directly (this is the default behavior)
      // If you wanted to add authentication tokens or serve optimized images,
      // you could modify the URL here
      console.log('🔍 MoodBoard Asset Store: Resolving asset', { assetId: asset.id, src: src.substring(0, 50) + '...' })

      return src

    } catch (error) {
      console.error('❌ MoodBoard Asset Store: Error resolving asset', error)
      return null
    }
  }

  /**
   * Get upload queue statistics for UI feedback
   */
  getQueueStats() {
    return this.uploadQueue.getQueueStats()
  }

  /**
   * Get all uploads for UI display
   */
  getUploads() {
    return this.uploadQueue.getUploads()
  }

  /**
   * Clear completed uploads from queue
   */
  clearCompleted() {
    this.uploadQueue.clearCompleted()
  }

  /**
   * Convert file to data URL for immediate display
   */
  private fileToDataUrl(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }
}

/**
 * Fallback asset store for cases where user ID is not available
 * This ensures Tldraw always has an asset store, even if uploads won't work
 */
export class FallbackAssetStore implements TLAssetStore {
  async upload(asset: TLAsset, file: File): Promise<{ src: string }> {
    console.warn('⚠️ Fallback Asset Store: Upload attempted without user authentication')

    // Return data URL as fallback
    const dataUrl = await this.fileToDataUrl(file)
    console.log('🔄 Fallback Asset Store: Using data URL fallback')

    return { src: dataUrl }
  }

  resolve(asset: TLAsset): string | null {
    const src = asset.props.src as string
    console.log('🔍 Fallback Asset Store: Resolving asset', { assetId: asset.id })
    return src || null
  }

  private fileToDataUrl(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }
}

export const moodboardImageService = new MoodboardImageService()
export const fallbackAssetStore = new FallbackAssetStore()
export default moodboardImageService
