/**
 * Image Quality Service
 * Handles image optimization, quality control, and formatting for the content builder
 */

export interface ImageQualityOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number; // 0-1
  format?: 'jpeg' | 'png' | 'webp';
  maintainAspectRatio?: boolean;
}

export interface OptimizedImageResult {
  url: string;
  width: number;
  height: number;
  format: string;
  size: number;
  quality: number;
}

class ImageQualityService {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
  }

  /**
   * Optimize image for editor insertion
   */
  async optimizeImageForEditor(
    imageUrl: string,
    options: ImageQualityOptions = {}
  ): Promise<OptimizedImageResult> {
    const {
      maxWidth = 800,
      maxHeight = 600,
      quality = 0.9,
      format = 'jpeg',
      maintainAspectRatio = true
    } = options;

    try {
      console.log('🖼️ Optimizing image for editor:', imageUrl);

      // Load the image
      const img = await this.loadImage(imageUrl);
      
      // Calculate optimal dimensions
      const dimensions = this.calculateOptimalDimensions(
        img.width,
        img.height,
        maxWidth,
        maxHeight,
        maintainAspectRatio
      );

      // Resize and optimize
      const optimizedDataUrl = this.resizeAndOptimize(
        img,
        dimensions.width,
        dimensions.height,
        quality,
        format
      );

      return {
        url: optimizedDataUrl,
        width: dimensions.width,
        height: dimensions.height,
        format,
        size: this.estimateDataUrlSize(optimizedDataUrl),
        quality
      };

    } catch (error) {
      console.error('❌ Error optimizing image:', error);
      // Return original image data as fallback
      return {
        url: imageUrl,
        width: maxWidth,
        height: maxHeight,
        format: 'unknown',
        size: 0,
        quality: 1
      };
    }
  }

  /**
   * Load image from URL
   */
  private loadImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      
      img.src = url;
    });
  }

  /**
   * Calculate optimal dimensions maintaining aspect ratio
   */
  private calculateOptimalDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number,
    maintainAspectRatio: boolean
  ): { width: number; height: number } {
    if (!maintainAspectRatio) {
      return { width: maxWidth, height: maxHeight };
    }

    const aspectRatio = originalWidth / originalHeight;
    
    let width = originalWidth;
    let height = originalHeight;

    // Scale down if too large
    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }

    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return {
      width: Math.round(width),
      height: Math.round(height)
    };
  }

  /**
   * Resize and optimize image
   */
  private resizeAndOptimize(
    img: HTMLImageElement,
    width: number,
    height: number,
    quality: number,
    format: string
  ): string {
    // Set canvas dimensions
    this.canvas.width = width;
    this.canvas.height = height;

    // Clear canvas
    this.ctx.clearRect(0, 0, width, height);

    // Enable image smoothing for better quality
    this.ctx.imageSmoothingEnabled = true;
    this.ctx.imageSmoothingQuality = 'high';

    // Draw resized image
    this.ctx.drawImage(img, 0, 0, width, height);

    // Convert to data URL with specified quality
    const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
    return this.canvas.toDataURL(mimeType, quality);
  }

  /**
   * Estimate data URL size in bytes
   */
  private estimateDataUrlSize(dataUrl: string): number {
    // Remove data URL prefix and calculate base64 size
    const base64 = dataUrl.split(',')[1] || '';
    return Math.round((base64.length * 3) / 4);
  }

  /**
   * Validate image URL and check if it's accessible
   */
  async validateImageUrl(url: string): Promise<boolean> {
    try {
      await this.loadImage(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get image metadata
   */
  async getImageMetadata(url: string): Promise<{
    width: number;
    height: number;
    aspectRatio: number;
    isValid: boolean;
  }> {
    try {
      const img = await this.loadImage(url);
      return {
        width: img.width,
        height: img.height,
        aspectRatio: img.width / img.height,
        isValid: true
      };
    } catch {
      return {
        width: 0,
        height: 0,
        aspectRatio: 1,
        isValid: false
      };
    }
  }

  /**
   * Create thumbnail from image
   */
  async createThumbnail(
    imageUrl: string,
    thumbnailSize: number = 150
  ): Promise<string> {
    try {
      const optimized = await this.optimizeImageForEditor(imageUrl, {
        maxWidth: thumbnailSize,
        maxHeight: thumbnailSize,
        quality: 0.8,
        format: 'jpeg'
      });
      return optimized.url;
    } catch (error) {
      console.error('❌ Error creating thumbnail:', error);
      return imageUrl; // Return original as fallback
    }
  }
}

// Export singleton instance
export const imageQualityService = new ImageQualityService();
