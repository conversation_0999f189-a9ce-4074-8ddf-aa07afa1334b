/**
 * Emma Studio - Sistema de Healthcheck Robusto
 * Previene errores de conexión y detecta problemas del backend
 */

import { useState, useEffect } from 'react';

export interface HealthStatus {
  isHealthy: boolean;
  status: 'healthy' | 'unhealthy' | 'unknown';
  timestamp: string;
  responseTime?: number;
  error?: string;
}

/**
 * Verifica el estado del backend
 */
export const checkBackendHealth = async (): Promise<HealthStatus> => {
  const startTime = Date.now();
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 segundos timeout
    
    const response = await fetch('/api/v1/ad-creator-agent/health', {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
      }
    });
    
    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      return {
        isHealthy: true,
        status: 'healthy',
        timestamp: new Date().toISOString(),
        responseTime
      };
    } else {
      return {
        isHealthy: false,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        responseTime,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }
    
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    
    return {
      isHealthy: false,
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      responseTime,
      error: error.name === 'AbortError' ? 'Timeout' : error.message
    };
  }
};

/**
 * Hook para monitoreo continuo del backend
 */
export const useBackendHealthMonitor = (intervalMs: number = 30000) => {
  const [healthStatus, setHealthStatus] = useState<HealthStatus>({
    isHealthy: true,
    status: 'unknown',
    timestamp: new Date().toISOString()
  });
  
  const [isMonitoring, setIsMonitoring] = useState(false);
  
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    const startMonitoring = async () => {
      setIsMonitoring(true);
      
      // Check inicial
      const initialStatus = await checkBackendHealth();
      setHealthStatus(initialStatus);
      
      // Monitoreo continuo
      interval = setInterval(async () => {
        const status = await checkBackendHealth();
        setHealthStatus(status);
        
        // Log para debugging
        if (!status.isHealthy) {
          console.error('❌ Backend health check failed:', status);
        } else {
          console.log('✅ Backend healthy:', status.responseTime + 'ms');
        }
      }, intervalMs);
    };
    
    startMonitoring();
    
    return () => {
      if (interval) {
        clearInterval(interval);
      }
      setIsMonitoring(false);
    };
  }, [intervalMs]);
  
  return { healthStatus, isMonitoring };
};

/**
 * Función para hacer requests con retry automático
 */
export const robustApiCall = async <T>(
  apiCall: () => Promise<Response>,
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 API call attempt ${attempt}/${maxRetries}`);
      
      const response = await apiCall();
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ API call successful on attempt ${attempt}`);
        return data;
      }
      
      // Si es error 500 y tenemos más intentos, reintentar
      if (response.status === 500 && attempt < maxRetries) {
        console.warn(`⚠️ Error 500 on attempt ${attempt}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        continue;
      }
      
      // Para otros errores, lanzar inmediatamente
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      
    } catch (error: any) {
      lastError = error;
      
      // Si es el último intento, lanzar el error
      if (attempt === maxRetries) {
        console.error(`❌ API call failed after ${maxRetries} attempts:`, error);
        throw error;
      }
      
      // Si es error de red, reintentar
      if (error.name === 'TypeError' || error.message.includes('fetch')) {
        console.warn(`⚠️ Network error on attempt ${attempt}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        continue;
      }
      
      // Para otros errores, lanzar inmediatamente
      throw error;
    }
  }
  
  throw lastError!;
};

/**
 * Wrapper para requests de free generation con manejo robusto
 */
export const robustFreeGeneration = async (formData: FormData) => {
  return robustApiCall<any>(
    () => fetch('/api/v1/ad-creator-agent/free-generation', {
      method: 'POST',
      body: formData
    }),
    3, // 3 intentos
    2000 // 2 segundos entre intentos
  );
};

/**
 * Detectar si el backend está disponible antes de hacer requests
 */
export const ensureBackendAvailable = async (): Promise<void> => {
  const health = await checkBackendHealth();
  
  if (!health.isHealthy) {
    throw new Error(
      `Backend no disponible: ${health.error || 'Estado desconocido'}. ` +
      'Verifica que el backend esté corriendo en puerto 8000.'
    );
  }
};
