import React, { useState } from "react";
import { motion } from "framer-motion";
import { 
  Edit, 
  Save, 
  X, 
  Building, 
  Globe, 
  Palette, 
  Users, 
  MessageSquare, 
  Target, 
  FileText, 
  Sparkles,
  CheckCircle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Marca } from "@/lib/supabase";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface BrandQualificationAnswersProps {
  marca: Marca;
  onUpdate?: (updatedMarca: Marca) => void;
}

const BrandQualificationAnswers: React.FC<BrandQualificationAnswersProps> = ({ 
  marca, 
  onUpdate 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedMarca, setEditedMarca] = useState<Marca>(marca);

  const handleSave = () => {
    if (onUpdate) {
      onUpdate(editedMarca);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedMarca(marca);
    setIsEditing(false);
  };

  const personalityTraits = [
    "Confiable", "Innovadora", "Accesible", "Premium",
    "Sostenible", "Global", "Local", "Disruptiva",
    "Tradicional", "Moderna", "Experta", "Inclusiva",
    "Amigable", "Profesional", "Creativa", "Juvenil"
  ];

  const togglePersonalityTrait = (trait: string) => {
    const currentPersonality = Array.isArray(editedMarca.personality) 
      ? editedMarca.personality 
      : editedMarca.personality ? editedMarca.personality.split(',').map(p => p.trim()) : [];
    
    const updatedPersonality = currentPersonality.includes(trait)
      ? currentPersonality.filter(p => p !== trait)
      : [...currentPersonality, trait];
    
    setEditedMarca(prev => ({
      ...prev,
      personality: updatedPersonality
    }));
  };

  const getPersonalityArray = (personality: string | string[] | undefined): string[] => {
    if (Array.isArray(personality)) return personality;
    if (typeof personality === 'string') return personality.split(',').map(p => p.trim()).filter(Boolean);
    return [];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Respuestas de Calificación de Marca</h2>
          <p className="text-gray-600">Revisa y edita las respuestas de tu marca</p>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancelar
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Guardar
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Editar Respuestas
            </Button>
          )}
        </div>
      </div>

      {/* Step 1: Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Paso 1: Información Básica
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-gray-600">Nombre de la Marca</Label>
              {isEditing ? (
                <Input
                  value={editedMarca.brand_name}
                  onChange={(e) => setEditedMarca(prev => ({ ...prev, brand_name: e.target.value }))}
                  className="mt-1"
                />
              ) : (
                <p className="text-gray-900 font-medium">{marca.brand_name}</p>
              )}
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-600">Sitio Web</Label>
              {isEditing ? (
                <Input
                  value={editedMarca.website || ''}
                  onChange={(e) => setEditedMarca(prev => ({ ...prev, website: e.target.value }))}
                  className="mt-1"
                />
              ) : (
                <p className="text-gray-900">{marca.website || 'No especificado'}</p>
              )}
            </div>
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-600">Industria</Label>
            {isEditing ? (
              <Select 
                value={editedMarca.industry} 
                onValueChange={(value) => setEditedMarca(prev => ({ ...prev, industry: value }))}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="saas">SaaS & Tecnología</SelectItem>
                  <SelectItem value="ecommerce">E-commerce & Retail</SelectItem>
                  <SelectItem value="servicios">Servicios Profesionales</SelectItem>
                  <SelectItem value="salud">Salud & Bienestar</SelectItem>
                  <SelectItem value="educacion">Educación & Formación</SelectItem>
                  <SelectItem value="finanzas">Finanzas & Seguros</SelectItem>
                  <SelectItem value="inmobiliaria">Inmobiliaria</SelectItem>
                  <SelectItem value="alimentacion">Alimentación & Bebidas</SelectItem>
                  <SelectItem value="entretenimiento">Entretenimiento & Media</SelectItem>
                  <SelectItem value="otro">Otro</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <p className="text-gray-900">{marca.industry}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Step 2: Visual Identity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Paso 2: Identidad Visual
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium text-gray-600">Logo</Label>
            <p className="text-gray-900">{marca.logo_url || 'No especificado'}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-gray-600">Color Primario</Label>
              <div className="flex items-center gap-3 mt-1">
                <div
                  className="w-8 h-8 rounded-lg border-2 border-gray-200"
                  style={{ backgroundColor: isEditing ? editedMarca.primary_color : marca.primary_color }}
                ></div>
                {isEditing ? (
                  <Input
                    type="color"
                    value={editedMarca.primary_color}
                    onChange={(e) => setEditedMarca(prev => ({ ...prev, primary_color: e.target.value }))}
                    className="w-20"
                  />
                ) : (
                  <span className="text-gray-900 font-mono">{marca.primary_color}</span>
                )}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-600">Color Secundario</Label>
              <div className="flex items-center gap-3 mt-1">
                <div
                  className="w-8 h-8 rounded-lg border-2 border-gray-200"
                  style={{ backgroundColor: isEditing ? editedMarca.secondary_color : marca.secondary_color }}
                ></div>
                {isEditing ? (
                  <Input
                    type="color"
                    value={editedMarca.secondary_color}
                    onChange={(e) => setEditedMarca(prev => ({ ...prev, secondary_color: e.target.value }))}
                    className="w-20"
                  />
                ) : (
                  <span className="text-gray-900 font-mono">{marca.secondary_color}</span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 3: Audience and Personality */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Paso 3: Audiencia y Personalidad
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium text-gray-600">Público Objetivo</Label>
            {isEditing ? (
              <Textarea
                value={editedMarca.target_audience}
                onChange={(e) => setEditedMarca(prev => ({ ...prev, target_audience: e.target.value }))}
                rows={3}
                className="mt-1"
              />
            ) : (
              <p className="text-gray-900">{marca.target_audience}</p>
            )}
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-600">Tono de Voz</Label>
            {isEditing ? (
              <Select 
                value={editedMarca.tone} 
                onValueChange={(value) => setEditedMarca(prev => ({ ...prev, tone: value }))}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="profesional">Profesional y Autoritativo</SelectItem>
                  <SelectItem value="amigable">Amigable y Cercano</SelectItem>
                  <SelectItem value="innovador">Innovador y Visionario</SelectItem>
                  <SelectItem value="casual">Casual y Relajado</SelectItem>
                  <SelectItem value="elegante">Elegante y Sofisticado</SelectItem>
                  <SelectItem value="divertido">Divertido y Energético</SelectItem>
                  <SelectItem value="tecnico">Técnico y Preciso</SelectItem>
                  <SelectItem value="inspiracional">Inspiracional y Motivador</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <p className="text-gray-900">{marca.tone}</p>
            )}
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-600">Personalidad de la Marca</Label>
            {isEditing ? (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                {personalityTraits.map((trait) => {
                  const currentPersonality = getPersonalityArray(editedMarca.personality);
                  const isSelected = currentPersonality.includes(trait);
                  return (
                    <Button
                      key={trait}
                      type="button"
                      variant={isSelected ? "default" : "outline"}
                      onClick={() => togglePersonalityTrait(trait)}
                      className="h-8 text-xs justify-start"
                    >
                      {trait}
                    </Button>
                  );
                })}
              </div>
            ) : (
              <div className="flex flex-wrap gap-2 mt-1">
                {getPersonalityArray(marca.personality).map((trait, index) => (
                  <Badge key={index} variant="secondary">
                    {trait}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Step 4: Positioning and Differentiation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Paso 4: Posicionamiento y Diferenciación
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium text-gray-600">Descripción de la Marca</Label>
            {isEditing ? (
              <Textarea
                value={editedMarca.description}
                onChange={(e) => setEditedMarca(prev => ({ ...prev, description: e.target.value }))}
                rows={4}
                className="mt-1"
              />
            ) : (
              <p className="text-gray-900">{marca.description}</p>
            )}
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-600">Propuesta de Valor Única</Label>
            {isEditing ? (
              <Textarea
                value={editedMarca.unique_value}
                onChange={(e) => setEditedMarca(prev => ({ ...prev, unique_value: e.target.value }))}
                rows={3}
                className="mt-1"
              />
            ) : (
              <p className="text-gray-900">{marca.unique_value}</p>
            )}
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-600">Principales Competidores</Label>
            {isEditing ? (
              <Input
                value={editedMarca.competitors || ''}
                onChange={(e) => setEditedMarca(prev => ({ ...prev, competitors: e.target.value }))}
                className="mt-1"
              />
            ) : (
              <p className="text-gray-900">{marca.competitors || 'No especificado'}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Step 5: Content Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Paso 5: Lineamientos de Contenido
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium text-gray-600">Lineamientos de Contenido y Referencias</Label>
            {isEditing ? (
              <Textarea
                value={editedMarca.examples || ''}
                onChange={(e) => setEditedMarca(prev => ({ ...prev, examples: e.target.value }))}
                rows={5}
                className="mt-1"
              />
            ) : (
              <p className="text-gray-900 whitespace-pre-wrap">{marca.examples || 'No especificado'}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Success Summary */}
      <Card className="bg-green-50 border-green-200">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 mb-3">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold text-green-900">
              Marca Completada con Éxito
            </h3>
          </div>
          <p className="text-green-800 mb-4">
            Tu marca ha sido configurada correctamente con toda la información necesaria para que Emma pueda crear contenido personalizado.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Identidad definida</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Audiencia clara</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Tono establecido</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Posicionamiento único</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BrandQualificationAnswers;
