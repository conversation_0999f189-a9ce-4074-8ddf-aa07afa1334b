"use client"

import React, { useEffect, useState } from "react"
import { <PERSON> } from "wouter"
import { LucideIcon, Users, Briefcase, CreditCard, Calculator, BookOpen } from "lucide-react"
import { cn } from "@/lib/utils"
import { emmaAiLogo } from "@/assets"
import LanguageSelector from "@/components/common/LanguageSelector"
import { useLanguage } from "@/contexts/LanguageContext"

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
}

interface NavBarProps {
  items: NavItem[]
  className?: string
}

export function EmmaNavBar({ items, className }: NavBarProps) {
  const { t, currentLanguage } = useLanguage()
  const translatedItems = items || getEmmaNavItems(t)
  const [activeTab, setActiveTab] = useState(translatedItems[0].name)
  const [isMobile, setIsMobile] = useState(false)

  // Update activeTab when language changes
  useEffect(() => {
    setActiveTab(translatedItems[0].name)
  }, [currentLanguage, translatedItems])

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Mobile navbar positioning fix
  useEffect(() => {
    const fixMobileNavbar = () => {
      const navbar = document.querySelector('.emma-navbar-mobile-fix');
      if (navbar && window.innerWidth < 768) {
        // Force navbar to stay at top on mobile
        (navbar as HTMLElement).style.position = 'fixed';
        (navbar as HTMLElement).style.top = '0';
        (navbar as HTMLElement).style.left = '50%';
        (navbar as HTMLElement).style.bottom = 'auto';
        (navbar as HTMLElement).style.transform = 'translateX(-50%)';
        (navbar as HTMLElement).style.WebkitTransform = 'translateX(-50%)';
        (navbar as HTMLElement).style.zIndex = '50';
      }
    };

    // Fix on mount and resize
    fixMobileNavbar();
    window.addEventListener('resize', fixMobileNavbar);
    window.addEventListener('orientationchange', fixMobileNavbar);

    // Also fix on load to ensure it works
    window.addEventListener('load', fixMobileNavbar);

    return () => {
      window.removeEventListener('resize', fixMobileNavbar);
      window.removeEventListener('orientationchange', fixMobileNavbar);
      window.removeEventListener('load', fixMobileNavbar);
    };
  }, [])

  return (
    <div
      className={cn(
        "fixed top-0 left-1/2 -translate-x-1/2 z-50 pt-6 sm:pt-12 pointer-events-none overflow-hidden emma-navbar-mobile-fix",
        className,
      )}
      style={{
        // Ensure proper mobile positioning
        position: 'fixed',
        top: 0,
        left: '50%',
        zIndex: 50,
        WebkitTransform: 'translateX(-50%)',
        transform: 'translateX(-50%)',
      }}
    >
      <div className="flex items-center gap-1 sm:gap-3 bg-white/10 border border-white/20 backdrop-blur-lg py-2 sm:py-3 px-2 sm:px-3 rounded-full shadow-lg pointer-events-auto overflow-visible">
        {translatedItems.map((item) => {
          const Icon = item.icon
          const isActive = activeTab === item.name

          return (
            <Link
              key={item.name}
              href={item.url}
              onClick={() => setActiveTab(item.name)}
              className={cn(
                "relative cursor-pointer text-sm font-semibold rounded-full transition-all duration-300 ease-out",
                "text-gray-700 hover:text-[#3018ef]",
                isActive && item.name !== "Emma" && "bg-white/20 text-[#3018ef] shadow-md",
                // Mobile-first responsive padding
                item.name === "Emma"
                  ? "px-2 py-2 sm:px-4 sm:py-3" // Logo gets smaller padding
                  : "px-3 py-2 sm:px-6 sm:py-3" // Other items get responsive padding
              )}
            >
              {item.name === "Emma" ? (
                <div className="flex items-center justify-center">
                  <img
                    src={emmaAiLogo}
                    alt="Emma Logo"
                    className="w-12 h-12 sm:w-16 sm:h-16 object-contain"
                  />
                </div>
              ) : (
                <>
                  <span className="hidden md:inline">{item.name}</span>
                  <span className="md:hidden">
                    <Icon size={16} strokeWidth={2.5} className="sm:w-[18px] sm:h-[18px]" />
                  </span>
                </>
              )}
              {isActive && item.name !== "Emma" && (
                <div
                  className="absolute inset-0 w-full bg-[#3018ef]/10 rounded-full -z-10 transition-all duration-300"
                />
              )}
            </Link>
          )
        })}

        {/* Language Selector - Enhanced mobile visibility */}
        <div className="ml-2 sm:ml-3 pl-2 sm:pl-3 border-l border-white/30 flex-shrink-0">
          <LanguageSelector variant="toggle" />
        </div>
      </div>
    </div>
  )
}

// Emma Navigation Items function that uses translations
export const getEmmaNavItems = (t: (key: string) => string): NavItem[] => [
  { name: "Emma", url: "/", icon: Users }, // Logo doesn't need translation
  { name: t('navigation.professionals'), url: "/profesionales-ia", icon: Users },
  { name: t('navigation.solutions'), url: "/soluciones-negocio", icon: Briefcase },
  { name: t('navigation.blog'), url: "/blog", icon: BookOpen },
  { name: t('navigation.plans'), url: "/login", icon: CreditCard },
  { name: t('navigation.calculator'), url: "/calculadora", icon: Calculator },
]

// Default items for backward compatibility
export const emmaNavItems: NavItem[] = [
  { name: "Emma", url: "/", icon: Users },
  { name: "Profesionales IA", url: "/profesionales-ia", icon: Users },
  { name: "Soluciones", url: "/soluciones-negocio", icon: Briefcase },
  { name: "Blog", url: "/blog", icon: BookOpen },
  { name: "Planes", url: "/login", icon: CreditCard },
  { name: "Calculadora", url: "/calculadora", icon: Calculator },
]
