"use client";

import * as React from "react";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { CheckCircle2, XCircle } from "lucide-react";

export interface AutocorrectTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  autoCorrectEnabled?: boolean;
  showCorrectionIndicator?: boolean;
  debounceMs?: number;
}

const AutocorrectTextarea = React.forwardRef<
  HTMLTextAreaElement,
  AutocorrectTextareaProps
>(
  (
    {
      className,
      autoCorrectEnabled = true,
      showCorrectionIndicator = true,
      debounceMs = 800,
      onChange,
      ...props
    },
    ref,
  ) => {
    const [value, setValue] = React.useState(
      props.value || props.defaultValue || "",
    );
    const [correctedText, setCorrectedText] = React.useState("");
    const [isCorrecting, setIsCorrecting] = React.useState(false);
    const [corrections, setCorrections] = React.useState(0);
    const debounceTimerRef = React.useRef<NodeJS.Timeout | null>(null);
    const { toast } = useToast();

    // Common typos and corrections in Spanish
    const commonTypos: Record<string, string> = {
      // Abreviaturas comunes
      xq: "porque",
      pq: "porque",
      kiero: "quiero",
      xfa: "por favor",
      toi: "estoy",
      toy: "estoy",
      k: "que",
      q: "que",
      tb: "también",
      xa: "para",
      d: "de",
      x: "por",

      // Palabras sin tilde comunes
      analisis: "análisis",
      informacion: "información",
      comunicacion: "comunicación",
      tambien: "también",
      musica: "música",
      tecnologia: "tecnología",
      diseno: "diseño",
      codigo: "código",
      exito: "éxito",
      pagina: "página",

      // Errores comunes
      aver: "haber",
      haci: "así",
      ai: "hay",
      valla: "vaya",
    };

    const applyAutocorrect = async (text: string): Promise<string> => {
      if (!autoCorrectEnabled) return text;

      // First apply fast local corrections
      let corrected = text;
      let localCorrectionCount = 0;

      // Solo aplicar correcciones locales si el texto tiene al menos 3 caracteres
      if (text.length > 3) {
        // Dividir el texto en palabras para procesarlas individualmente
        const words = corrected.split(/\s+/);
        const correctedWords = words.map((word) => {
          // Palabra original para comparación
          const originalWord = word;

          // Verificar si la palabra (sin signos de puntuación) está en nuestro diccionario
          const wordWithoutPunctuation = word.replace(/[.,;:!?]$/, "");
          const punctuation = word.substring(wordWithoutPunctuation.length);

          // Buscar en el diccionario de correcciones
          const correction = commonTypos[wordWithoutPunctuation.toLowerCase()];

          if (correction) {
            // Mantenemos la capitalización original
            let correctedWord = "";
            if (
              wordWithoutPunctuation[0] &&
              wordWithoutPunctuation[0].toUpperCase() ===
                wordWithoutPunctuation[0]
            ) {
              correctedWord =
                correction.charAt(0).toUpperCase() + correction.slice(1);
            } else {
              correctedWord = correction;
            }

            // Agregar puntuación si la había
            correctedWord += punctuation;

            // Contar corrección
            if (originalWord.toLowerCase() !== correctedWord.toLowerCase()) {
              localCorrectionCount++;
            }

            return correctedWord;
          }

          return word;
        });

        // Reconstruir el texto corregido
        corrected = correctedWords.join(" ");
      }

      let totalCorrections = localCorrectionCount;

      // For longer texts (>30 chars), also try server-side correction
      if (text.length > 30) {
        try {
          const response = await fetch("/api/text-autocorrect", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ content: corrected, context: "es" }),
          });

          if (response.ok) {
            const data = await response.json();
            if (data.status === "success" && data.has_corrections) {
              // Use the server's corrected version
              corrected = data.corrected_text;
              // Update the correction count
              if (data.corrections && data.corrections.length > 0) {
                totalCorrections += data.corrections.length;
              }
            }
          }
        } catch (error) {
          console.error("Error with autocorrection API:", error);
          // Continue with the local correction if the API fails
        }
      }

      // Set the total corrections found
      setCorrections(totalCorrections);

      // Return the corrected text
      return corrected;
    };

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setValue(newValue);

      // Always call onChange with the original input
      if (onChange) {
        onChange(e);
      }

      // Apply autocorrect with debounce
      if (autoCorrectEnabled) {
        setIsCorrecting(true);

        if (debounceTimerRef.current) {
          clearTimeout(debounceTimerRef.current);
        }

        debounceTimerRef.current = setTimeout(async () => {
          try {
            const corrected = await applyAutocorrect(newValue);
            setCorrectedText(corrected);

            // Update the input value if there were corrections
            if (corrected !== newValue) {
              setValue(corrected);

              // Create a new event to propagate the corrected value
              const newEvent = {
                ...e,
                target: {
                  ...e.target,
                  value: corrected,
                },
              } as React.ChangeEvent<HTMLTextAreaElement>;

              if (onChange) {
                onChange(newEvent);
              }

              // Show toast for significant corrections
              if (corrections > 2) {
                toast({
                  title: "Corrección automática aplicada",
                  description: `Se corrigieron ${corrections} errores ortográficos.`,
                  variant: "default",
                });
              }
            }
          } catch (error) {
            console.error("Error applying autocorrect:", error);
          } finally {
            setIsCorrecting(false);
          }
        }, debounceMs);
      }
    };

    // Clean up the timer on unmount
    React.useEffect(() => {
      return () => {
        if (debounceTimerRef.current) {
          clearTimeout(debounceTimerRef.current);
        }
      };
    }, []);

    return (
      <div className="relative">
        <Textarea
          ref={ref}
          className={cn("pr-8", className)}
          value={value as string}
          onChange={handleChange}
          {...props}
        />

        {showCorrectionIndicator && autoCorrectEnabled && (
          <div className="absolute right-2 top-2">
            {isCorrecting ? (
              <span className="text-yellow-500 animate-pulse">•</span>
            ) : corrections > 0 ? (
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-muted-foreground opacity-30" />
            )}
          </div>
        )}
      </div>
    );
  },
);

AutocorrectTextarea.displayName = "AutocorrectTextarea";

export { AutocorrectTextarea };
