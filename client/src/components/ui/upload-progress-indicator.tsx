/**
 * Upload Progress Indicator
 * Provides visual feedback for batch image uploads in mood board editor
 */

import React from 'react'
import { QueuedUpload } from '@/services/uploadQueueManager'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { X, Upload, CheckCircle, AlertCircle, Clock } from 'lucide-react'

interface UploadProgressIndicatorProps {
  uploads: QueuedUpload[]
  onClearCompleted?: () => void
  onCancel?: () => void
  className?: string
}

export function UploadProgressIndicator({ 
  uploads, 
  onClearCompleted, 
  onCancel,
  className = '' 
}: UploadProgressIndicatorProps) {
  const stats = {
    total: uploads.length,
    pending: uploads.filter(u => u.status === 'pending').length,
    processing: uploads.filter(u => u.status === 'processing').length,
    completed: uploads.filter(u => u.status === 'completed').length,
    failed: uploads.filter(u => u.status === 'failed').length
  }

  const hasActiveUploads = stats.pending > 0 || stats.processing > 0
  const hasCompletedUploads = stats.completed > 0 || stats.failed > 0

  if (uploads.length === 0) {
    return null
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Upload className="w-4 h-4 text-blue-600" />
          <span className="font-medium text-gray-900">
            Subiendo imágenes
          </span>
          <Badge variant="secondary" className="text-xs">
            {stats.total}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {hasCompletedUploads && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearCompleted}
              className="text-xs"
            >
              Limpiar
            </Button>
          )}
          {hasActiveUploads && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="text-xs text-red-600 hover:text-red-700"
            >
              <X className="w-3 h-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Overall Progress */}
      {hasActiveUploads && (
        <div className="mb-3">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Progreso general</span>
            <span>{stats.completed + stats.failed}/{stats.total}</span>
          </div>
          <Progress 
            value={(stats.completed + stats.failed) / stats.total * 100} 
            className="h-2"
          />
        </div>
      )}

      {/* Status Summary */}
      <div className="flex items-center gap-4 text-sm mb-3">
        {stats.processing > 0 && (
          <div className="flex items-center gap-1 text-blue-600">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse" />
            <span>{stats.processing} procesando</span>
          </div>
        )}
        
        {stats.pending > 0 && (
          <div className="flex items-center gap-1 text-gray-500">
            <Clock className="w-3 h-3" />
            <span>{stats.pending} en cola</span>
          </div>
        )}
        
        {stats.completed > 0 && (
          <div className="flex items-center gap-1 text-green-600">
            <CheckCircle className="w-3 h-3" />
            <span>{stats.completed} completadas</span>
          </div>
        )}
        
        {stats.failed > 0 && (
          <div className="flex items-center gap-1 text-red-600">
            <AlertCircle className="w-3 h-3" />
            <span>{stats.failed} fallidas</span>
          </div>
        )}
      </div>

      {/* Individual Upload Items (show only active or recent) */}
      <div className="space-y-2 max-h-32 overflow-y-auto">
        {uploads
          .filter(upload => 
            upload.status === 'processing' || 
            upload.status === 'pending' ||
            (upload.status === 'failed' && Date.now() - upload.timestamp < 10000) // Show failed for 10 seconds
          )
          .slice(0, 5) // Limit to 5 items to prevent UI overflow
          .map(upload => (
            <UploadItem key={upload.id} upload={upload} />
          ))}
      </div>

      {/* Show count if there are more items */}
      {uploads.length > 5 && (
        <div className="text-xs text-gray-500 mt-2 text-center">
          y {uploads.length - 5} más...
        </div>
      )}
    </div>
  )
}

interface UploadItemProps {
  upload: QueuedUpload
}

function UploadItem({ upload }: UploadItemProps) {
  const getStatusIcon = () => {
    switch (upload.status) {
      case 'pending':
        return <Clock className="w-3 h-3 text-gray-400" />
      case 'processing':
        return <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
      case 'completed':
        return <CheckCircle className="w-3 h-3 text-green-600" />
      case 'failed':
        return <AlertCircle className="w-3 h-3 text-red-600" />
      default:
        return null
    }
  }

  const getStatusColor = () => {
    switch (upload.status) {
      case 'pending':
        return 'text-gray-600'
      case 'processing':
        return 'text-blue-600'
      case 'completed':
        return 'text-green-600'
      case 'failed':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="flex items-center gap-2 p-2 bg-gray-50 rounded text-sm">
      {getStatusIcon()}
      
      <div className="flex-1 min-w-0">
        <div className="truncate font-medium text-gray-900">
          {upload.file.name}
        </div>
        
        {upload.status === 'processing' && (
          <div className="mt-1">
            <Progress value={upload.progress} className="h-1" />
          </div>
        )}
        
        {upload.status === 'failed' && upload.error && (
          <div className="text-xs text-red-600 mt-1">
            {upload.error}
          </div>
        )}
      </div>
      
      <div className={`text-xs ${getStatusColor()}`}>
        {upload.status === 'processing' && `${Math.round(upload.progress)}%`}
        {upload.status === 'pending' && 'En cola'}
        {upload.status === 'completed' && 'Listo'}
        {upload.status === 'failed' && 'Error'}
      </div>
    </div>
  )
}
