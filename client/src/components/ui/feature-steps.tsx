"use client"

import React, { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { useLanguage } from "@/contexts/LanguageContext"

interface Feature {
  step: string
  title?: string
  content: string
  image: string
  tools?: string[]
}

interface FeatureStepsProps {
  features: Feature[]
  className?: string
  title?: string | React.ReactNode
  autoPlayInterval?: number
  imageHeight?: string
}

export function FeatureSteps({
  features,
  className,
  title,
  autoPlayInterval = 3000,
  imageHeight = "h-[400px]",
}: FeatureStepsProps) {
  const { currentLanguage } = useLanguage()
  const [currentFeature, setCurrentFeature] = useState(0)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      if (progress < 100) {
        setProgress((prev) => prev + 100 / (autoPlayInterval / 100))
      } else {
        setCurrentFeature((prev) => (prev + 1) % features.length)
        setProgress(0)
      }
    }, 100)

    return () => clearInterval(timer)
  }, [progress, features.length, autoPlayInterval])

  const handleStepClick = (index: number) => {
    setCurrentFeature(index)
    setProgress(0)
  }

  return (
    <div className={cn("p-8 md:p-16 lg:p-20", className)}>
      <div className="max-w-7xl mx-auto w-full">
        {title && (
          <h2 className="text-3xl md:text-4xl lg:text-6xl font-bold mb-16 text-center">
            {title}
          </h2>
        )}

        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-12 lg:gap-16">
          <div className="order-2 lg:order-1">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentFeature}
                className="bg-white/80 backdrop-blur-sm shadow-xl border border-gray-100 rounded-3xl p-8 lg:p-12"
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 50 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex items-center gap-6 mb-8">
                  <div className="w-16 h-16 lg:w-20 lg:h-20 rounded-2xl bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] shadow-lg flex items-center justify-center text-white flex-shrink-0">
                    <span className="text-2xl lg:text-3xl font-bold">{currentFeature + 1}</span>
                  </div>
                  <h3 className="text-2xl lg:text-4xl font-bold text-gray-900">
                    {features[currentFeature].title || features[currentFeature].step}
                  </h3>
                </div>

                <div className="space-y-4">
                  {features[currentFeature].tools?.map((tool, index) => {
                    const [toolName, toolDescription] = tool.split(' - ');
                    return (
                      <motion.div
                        key={index}
                        className="flex items-start gap-4 p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <div className="w-4 h-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex-shrink-0 mt-2 shadow-sm" />
                        <div className="flex-1">
                          <h4 className="text-lg font-black text-gray-900 mb-2">{toolName}</h4>
                          {toolDescription && (
                            <p className="text-gray-600 leading-relaxed">{toolDescription}</p>
                          )}
                        </div>
                      </motion.div>
                    );
                  }) || (
                    <p className="text-lg text-gray-600 leading-relaxed">
                      {features[currentFeature].content}
                    </p>
                  )}
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          <div
            className={cn(
              "order-1 lg:order-2 relative h-[300px] lg:h-[500px] xl:h-[600px] overflow-hidden rounded-3xl shadow-2xl border border-gray-200"
            )}
          >
            <AnimatePresence mode="wait">
              {features.map(
                (feature, index) =>
                  index === currentFeature && (
                    <motion.div
                      key={index}
                      className="absolute inset-0 rounded-lg overflow-hidden"
                      initial={{ y: 100, opacity: 0, rotateX: -20 }}
                      animate={{ y: 0, opacity: 1, rotateX: 0 }}
                      exit={{ y: -100, opacity: 0, rotateX: 20 }}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                    >
                      <img
                        src={feature.image}
                        alt={feature.step}
                        className="w-full h-full object-cover transition-transform transform"
                      />
                      <div className="absolute bottom-0 left-0 right-0 h-2/3 bg-gradient-to-t from-background via-background/50 to-transparent" />
                    </motion.div>
                  ),
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Navigation Dots */}
        <div className="flex justify-center gap-4 mt-12">
          {features.map((_, index) => (
            <button
              key={index}
              className={cn(
                "w-4 h-4 rounded-full transition-all duration-300 shadow-lg",
                index === currentFeature
                  ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] scale-125"
                  : "bg-gray-300 hover:bg-gray-400 hover:scale-110"
              )}
              onClick={() => handleStepClick(index)}
              aria-label={`Go to step ${index + 1}`}
            />
          ))}
        </div>

        {/* Progress Bar */}
        <div className="mt-8 max-w-md mx-auto">
          <div className="w-full bg-gray-200 rounded-full h-3 shadow-lg">
            <div
              className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] h-full rounded-full transition-all duration-100"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-center text-sm font-bold text-gray-600 mt-3">
            {currentLanguage === 'en'
              ? `Auto-advance in ${Math.ceil((autoPlayInterval - (progress * autoPlayInterval / 100)) / 1000)}s`
              : `Auto-avanza en ${Math.ceil((autoPlayInterval - (progress * autoPlayInterval / 100)) / 1000)}s`
            }
          </p>
        </div>
      </div>
    </div>
  )
}
