import React from "react";
import { useLocation, Redirect } from "wouter";
import { useAuth } from "@/hooks/use-auth";

interface ProtectedRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export function ProtectedRoute({
  children,
  adminOnly = false,
}: ProtectedRouteProps) {
  const { user, isLoading } = useAuth();
  const [, navigate] = useLocation();

  // Si todavía está cargando el estado de autenticación, mostrar un loader
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
      </div>
    );
  }

  // Si no hay usuario autenticado, redirigir al login
  if (!user) {
    return <Redirect to="/login" />;
  }

  // Si se requiere rol de admin, aquí agregarías la verificación
  // Esto requeriría obtener el rol del usuario de Firestore

  // Si pasa todas las verificaciones, mostrar el contenido protegido
  return <>{children}</>;
}
