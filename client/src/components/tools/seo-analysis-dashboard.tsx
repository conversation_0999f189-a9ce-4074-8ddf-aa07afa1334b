"use client";

import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { seoAnalysisService } from "@/services/seoAnalysisService";
import {
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Trash2,
  ExternalLink,
  RefreshCw,
  Activity,
  Globe,
  FileText,
  Calendar,
  Timer
} from "lucide-react";

interface SEOAnalysis {
  id: number;
  analysis_id: string;
  user_id?: string;
  url: string;
  mode: string;
  status: string;
  current_page: number;
  total_pages: number;
  phase?: string;
  status_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  estimated_completion?: string;
  processing_time?: number;
  pages_analyzed: number;
  total_pages_found: number;
  failed_urls_count: number;
  ai_enhanced: boolean;
}

interface SEOAnalysisDashboardProps {
  onLoadAnalysis?: (analysis: any) => void;
}

export default function SEOAnalysisDashboard({ onLoadAnalysis }: SEOAnalysisDashboardProps) {
  const [activeTab, setActiveTab] = useState("active");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user, isLoading: authLoading } = useAuth();

  // Derive authentication state from user presence
  const isAuthenticated = !!user && user.id !== 'anonymous';

  // Debug authentication state
  console.log('🔐 SEO Dashboard Auth Debug:', {
    authLoading,
    userExists: !!user,
    userId: user?.id,
    userEmail: user?.email,
    isAuthenticated,
    queryEnabled: !!user?.id && user.id !== 'anonymous'
  });

  // Fetch user analyses from Supabase using seoAnalysisService
  const { data: analyses = [], isLoading, error: queryError, refetch } = useQuery({
    queryKey: ["seo-analyses", user?.id],
    queryFn: async () => {
      console.log('🔍 Dashboard: Starting query function');
      console.log('🔍 Dashboard: User details:', {
        userId: user?.id,
        userEmail: user?.email,
        isAuthenticated
      });

      if (!user?.id || user.id === 'anonymous') {
        console.log('⚠️ Dashboard: No valid user ID, returning empty array');
        return [];
      }

      try {
        console.log('🔄 Dashboard: Calling seoAnalysisService.getUserAnalyses...');
        const startTime = Date.now();

        const analyses = await seoAnalysisService.getUserAnalyses(user.id, {
          limit: 100,
          orderBy: 'created_at',
          orderDirection: 'desc'
        });

        const endTime = Date.now();
        console.log(`✅ Dashboard: Query completed in ${endTime - startTime}ms`);
        console.log('✅ Dashboard: Fetched analyses from Supabase:', analyses.length);

        if (analyses.length > 0) {
          console.log('📊 Dashboard: Sample analysis:', {
            id: analyses[0].id,
            url: analyses[0].url,
            status: analyses[0].status,
            created_at: analyses[0].created_at
          });
        } else {
          console.log('📊 Dashboard: No analyses found for user');
        }

        return analyses;
      } catch (error) {
        console.error('❌ Dashboard: Error fetching analyses:', error);
        console.error('❌ Dashboard: Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
        throw error;
      }
    },
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
    refetchInterval: 10000, // Refetch every 10 seconds for real-time updates
  });

  // Delete analysis mutation (using seoAnalysisService)
  const deleteMutation = useMutation({
    mutationFn: async (analysisId: string) => {
      console.log('🗑️ Deleting analysis:', analysisId);
      await seoAnalysisService.deleteAnalysis(analysisId);
    },
    onSuccess: () => {
      toast({
        title: "Análisis eliminado",
        description: "El análisis ha sido eliminado exitosamente",
      });
      queryClient.invalidateQueries({ queryKey: ["seo-analyses", user?.id] });
    },
    onError: (error: Error) => {
      console.error('❌ Error deleting analysis:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar el análisis",
        variant: "destructive",
      });
    },
  });

  // Filter analyses by status (comprehensive status mapping for all systems)
  const activeAnalyses = analyses.filter((a: any) =>
    ["pending", "in_progress", "processing", "running", "researching", "writing", "optimizing"].includes(a.status)
  );
  const completedAnalyses = analyses.filter((a: any) =>
    ["complete", "completed", "success", "published"].includes(a.status)
  );
  const failedAnalyses = analyses.filter((a: any) =>
    ["error", "cancelled", "failed", "draft"].includes(a.status)
  );

  // Debug logging to see what analyses we have
  console.log('🔍 SEO Dashboard Debug:', {
    totalAnalyses: analyses.length,
    activeCount: activeAnalyses.length,
    completedCount: completedAnalyses.length,
    failedCount: failedAnalyses.length,
    sampleAnalyses: analyses.slice(0, 3).map(a => ({ id: a.id, status: a.status, url: a.url }))
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
      case "processing":
      case "researching":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "in_progress":
      case "running":
      case "writing":
      case "optimizing":
        return <Activity className="h-4 w-4 text-blue-500 animate-spin" />;
      case "complete":
      case "completed":
      case "success":
      case "published":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "error":
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "cancelled":
      case "draft":
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      pending: "outline",
      processing: "outline",
      researching: "outline",
      in_progress: "default",
      running: "default",
      writing: "default",
      optimizing: "default",
      complete: "secondary",
      completed: "secondary",
      success: "secondary",
      published: "secondary",
      error: "destructive",
      failed: "destructive",
      cancelled: "outline",
      draft: "outline",
    };

    const labels: Record<string, string> = {
      pending: "Pendiente",
      processing: "Procesando",
      researching: "Investigando",
      in_progress: "En progreso",
      running: "Ejecutando",
      writing: "Escribiendo",
      optimizing: "Optimizando",
      complete: "Completado",
      completed: "Completado",
      success: "Completado",
      published: "Publicado",
      error: "Error",
      failed: "Error",
      cancelled: "Cancelado",
      draft: "Borrador",
    };

    return (
      <Badge variant={variants[status] || "outline"}>
        {labels[status] || status}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("es-ES", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)}m`;
    } else {
      return `${Math.round(seconds / 3600)}h`;
    }
  };

  const getProgress = (analysis: any) => {
    if (["complete", "completed", "success", "published"].includes(analysis.status)) return 100;
    if (["error", "failed", "cancelled", "draft"].includes(analysis.status)) return 0;
    if (["processing", "running", "in_progress"].includes(analysis.status)) return 50;
    if (["researching", "writing"].includes(analysis.status)) return 25;
    if (["optimizing"].includes(analysis.status)) return 75;
    if (["pending"].includes(analysis.status)) return 10;
    // Default for unknown status
    return 0;
  };

  const AnalysisCard = ({ analysis }: { analysis: any }) => (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {getStatusIcon(analysis.status)}
              <CardTitle className="text-lg truncate">{analysis.url}</CardTitle>
              {getStatusBadge(analysis.status)}
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                {analysis.analysis_mode === "website" ? (
                  <Globe className="h-3 w-3" />
                ) : (
                  <FileText className="h-3 w-3" />
                )}
                {analysis.analysis_mode === "website" ? "Sitio completo" : "Página específica"}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {formatDate(analysis.created_at)}
              </div>
              {analysis.analysis_duration_ms && (
                <div className="flex items-center gap-1">
                  <Timer className="h-3 w-3" />
                  {formatDuration(analysis.analysis_duration_ms / 1000)}
                </div>
              )}
              {analysis.overall_score && (
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium">
                    Puntuación: {analysis.overall_score}/100
                  </span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {["complete", "completed", "success"].includes(analysis.status) && (
              <Button
                variant="outline"
                size="sm"
                onClick={async () => {
                  if (onLoadAnalysis) {
                    try {
                      console.log('📊 Loading analysis from Dashboard:', analysis.id);
                      // Since we're now fetching from Supabase directly, we already have the full analysis data
                      onLoadAnalysis(analysis);
                    } catch (error) {
                      console.error("Error loading analysis:", error);
                      toast({
                        title: "Error",
                        description: "No se pudo cargar el análisis",
                        variant: "destructive"
                      });
                    }
                  } else {
                    // Fallback to opening in new tab if onLoadAnalysis is not provided
                    window.open(`/dashboard/herramientas/seo-analyzer?analysis=${analysis.id}`, "_blank");
                  }
                }}
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Ver resultados
              </Button>
            )}
            {["pending", "in_progress", "processing"].includes(analysis.status) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => deleteMutation.mutate(analysis.id)}
                disabled={deleteMutation.isPending}
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Cancelar
              </Button>
            )}
            {["complete", "completed", "success", "error", "failed"].includes(analysis.status) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => deleteMutation.mutate(analysis.id)}
                disabled={deleteMutation.isPending}
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Eliminar
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {["pending", "in_progress", "processing"].includes(analysis.status) && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Procesando análisis SEO...</span>
            </div>
            <Progress value={50} className="h-2" />
          </div>
        )}

        {["complete", "completed", "success"].includes(analysis.status) && analysis.overall_score && (
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <div className="font-medium">Puntuación SEO</div>
              <div className="text-muted-foreground">{analysis.overall_score}/100</div>
            </div>
            <div>
              <div className="font-medium">Recomendaciones</div>
              <div className="text-muted-foreground">
                {analysis.recommendations?.length || 0}
              </div>
            </div>
            <div>
              <div className="font-medium">IA mejorada</div>
              <div className="text-muted-foreground">
                {analysis.ai_enhanced ? "Sí" : "No"}
              </div>
            </div>
          </div>
        )}

        {["error", "failed"].includes(analysis.status) && (
          <div className="text-sm text-red-600">
            Error durante el análisis
          </div>
        )}
      </CardContent>
    </Card>
  );

  // Debug render state
  console.log('🎨 Dashboard Render State:', {
    authLoading,
    isLoading,
    hasError: !!queryError,
    userExists: !!user,
    isAuthenticated,
    analysesCount: analyses.length,
    queryEnabled: !!user?.id && user.id !== 'anonymous'
  });

  // Show error state if query failed
  if (queryError) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <h3 className="text-lg font-medium mb-2 text-red-600">Error al cargar análisis</h3>
          <p className="text-muted-foreground mb-4">
            {queryError.message || 'Error desconocido'}
          </p>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Reintentar
          </Button>
        </div>
      </div>
    );
  }

  // Show loading state while auth is loading or query is loading
  if (authLoading || (isLoading && !!user?.id)) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        {authLoading ? 'Cargando autenticación...' : 'Cargando análisis...'}
      </div>
    );
  }

  // Show authentication required message if user is not authenticated
  if (!user || user.id === 'anonymous') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <h3 className="text-lg font-medium mb-2">Autenticación requerida</h3>
          <p className="text-muted-foreground">
            Debes iniciar sesión para ver tus análisis SEO.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Mis Análisis SEO</h2>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Actualizar
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="active">
            Activos ({activeAnalyses.length})
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completados ({completedAnalyses.length})
          </TabsTrigger>
          <TabsTrigger value="failed">
            Fallidos ({failedAnalyses.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          <ScrollArea className="h-[600px]">
            {activeAnalyses.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No hay análisis activos
              </div>
            ) : (
              activeAnalyses.map((analysis: SEOAnalysis) => (
                <AnalysisCard key={analysis.id} analysis={analysis} />
              ))
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <ScrollArea className="h-[600px]">
            {completedAnalyses.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No hay análisis completados
              </div>
            ) : (
              completedAnalyses.map((analysis: SEOAnalysis) => (
                <AnalysisCard key={analysis.id} analysis={analysis} />
              ))
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="failed" className="space-y-4">
          <ScrollArea className="h-[600px]">
            {failedAnalyses.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No hay análisis fallidos
              </div>
            ) : (
              failedAnalyses.map((analysis: SEOAnalysis) => (
                <AnalysisCard key={analysis.id} analysis={analysis} />
              ))
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
}
