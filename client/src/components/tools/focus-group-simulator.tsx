"use client";

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { focusGroupService, type FocusGroupSimulation } from "@/services/focusGroupService";
import { focusGroupApiService } from "@/services/focus-group-api-service";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AutocorrectTextarea } from "@/components/ui/autocorrect-textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  AlertCircle,
  Clock,
  MessageCircle,
  Brain,
  Lightbulb,
  Sparkles,
  MoveDown,
  Download,
  UserCircle,
  MessageSquare,
  MessageSquarePlus,
  FileText,
  ArrowLeft,
  Wand2,
  ChevronRight,
  History,
  Heart,
  Star,
  Edit3,
  Trash2,
  RefreshCw,
  Calendar,
  Check,
  X,
} from "lucide-react";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

// Interfaces
interface Participant {
  id: number;
  name: string;
  age_range: string;
  gender: string;
  education: string;
  income_level: string;
  digital_usage: string;
  interests: string[];
  personality_traits: string[];
  buying_behavior: {
    price_sensitivity: string;
    brand_loyalty: string;
    research_level: string;
    decision_speed: string;
    influencer_impact: string;
  };
}

interface Comment {
  participant_id: number;
  participant_name: string;
  comment: string;
  sentiment: "positivo" | "neutral" | "negativo";
}

interface Discussion {
  question: string;
  conversation: Comment[];
}

interface DemographicPattern {
  pattern: string;
  affected_demographics: string[];
}

interface FocusGroupResult {
  status: string;
  error_message?: string;
  focus_group_simulation?: {
    discussions: Discussion[];
    summary: {
      key_insights: string[];
      sentiment_analysis: {
        overall: string;
        breakdown: {
          positive_aspects: string[];
          negative_aspects: string[];
          neutral_observations: string[];
        };
      };
      demographic_patterns: DemographicPattern[];
      recommendations: string[];
    };
  };
  timestamp: string;
}

// Componente para mostrar una tarjeta de focus group guardado
interface FocusGroupCardProps {
  focusGroup: FocusGroupSimulation;
  onLoad: () => void;
  onToggleFavorite: () => void;
  onRename: (newName: string) => void;
  onDelete: () => void;
  onRegenerate: () => void;
  isRenaming: boolean;
  onStartRename: () => void;
  onCancelRename: () => void;
  renameValue: string;
  onRenameValueChange: (value: string) => void;
}

function FocusGroupCard({
  focusGroup,
  onLoad,
  onToggleFavorite,
  onRename,
  onDelete,
  onRegenerate,
  isRenaming,
  onStartRename,
  onCancelRename,
  renameValue,
  onRenameValueChange,
}: FocusGroupCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDisplayName = () => {
    return focusGroup.custom_name || `Focus Group ${formatDate(focusGroup.created_at)}`;
  };

  const handleRenameSubmit = () => {
    if (renameValue.trim()) {
      onRename(renameValue.trim());
    } else {
      onCancelRename();
    }
  };

  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {isRenaming ? (
            <div className="flex items-center gap-2 mb-2">
              <Input
                value={renameValue}
                onChange={(e) => onRenameValueChange(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleRenameSubmit();
                  if (e.key === 'Escape') onCancelRename();
                }}
                className="text-lg font-semibold"
                autoFocus
              />
              <Button size="sm" onClick={handleRenameSubmit}>
                <Check className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={onCancelRename}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">
              {getDisplayName()}
            </h3>
          )}

          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {focusGroup.content}
          </p>

          <div className="flex items-center gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {formatDate(focusGroup.created_at)}
            </div>

            {focusGroup.regeneration_count > 0 && (
              <div className="flex items-center gap-1">
                <RefreshCw className="h-3 w-3" />
                {focusGroup.regeneration_count} regeneraciones
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2 ml-4">
          <Button
            size="sm"
            variant="outline"
            onClick={onToggleFavorite}
            className={focusGroup.is_favorite ? "text-red-600 border-red-200" : ""}
          >
            {focusGroup.is_favorite ? (
              <Heart className="h-4 w-4 fill-current" />
            ) : (
              <Heart className="h-4 w-4" />
            )}
          </Button>

          <Button size="sm" variant="outline" onClick={onStartRename}>
            <Edit3 className="h-4 w-4" />
          </Button>

          <Button size="sm" variant="outline" onClick={onRegenerate} className="text-blue-600 hover:text-blue-700">
            <RefreshCw className="h-4 w-4" />
          </Button>

          <Button size="sm" variant="outline" onClick={onDelete} className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4" />
          </Button>

          <Button size="sm" onClick={onLoad}>
            Cargar
          </Button>
        </div>
      </div>
    </Card>
  );
}

// Componente principal
export default function FocusGroupSimulator() {
  // Authentication
  const { user } = useAuth();

  // Estado del formulario
  const [contentToAnalyze, setContentToAnalyze] = useState("");
  const [productCategory, setProductCategory] = useState("");
  const [additionalContext, setAdditionalContext] = useState("");
  const [customQuestions, setCustomQuestions] = useState<string[]>([""]);
  const [useAutocorrect, setUseAutocorrect] = useState(true);

  // Estado para las pestañas de la interfaz
  const [activeTab, setActiveTab] = useState<"form" | "recent" | "favorites">("form");

  // Estado para los focus groups guardados
  const [recentFocusGroups, setRecentFocusGroups] = useState<FocusGroupSimulation[]>([]);
  const [favoriteFocusGroups, setFavoriteFocusGroups] = useState<FocusGroupSimulation[]>([]);
  const [isLoadingFocusGroups, setIsLoadingFocusGroups] = useState(false);

  // Estado para la gestión de focus groups
  const [selectedFocusGroup, setSelectedFocusGroup] = useState<FocusGroupSimulation | null>(null);
  const [isRenaming, setIsRenaming] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState("");

  // Estado para el formulario intuitivo
  const [useIntuitiveForm, setUseIntuitiveForm] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [intuitiveFormData, setIntuitiveFormData] = useState({
    ideaType: "",
    customIdea: "",
    targetAge: [] as string[],
    customAge: "",
    priceRange: "",
    customPrice: "",
    competition: "",
    problemSolved: "",
    customProblem: "",
    usageLocation: "",
    customLocation: "",
    uniqueFeature: "",
    customFeature: "",
    timeToUse: "",
    customTime: "",
    mainBenefit: "",
    customBenefit: "",
    briefDescription: ""
  });

  // Estados para la simulación y resultados
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<FocusGroupResult | null>(null);
  const [viewMode, setViewMode] = useState<"form" | "live-simulation">("form");
  const [currentSimulationId, setCurrentSimulationId] = useState<string | null>(null);
  const [isTogglingFavorite, setIsTogglingFavorite] = useState(false);

  // Estado para el timer de carga
  const [loadingTime, setLoadingTime] = useState(0);
  const [loadingStage, setLoadingStage] = useState(0);
  const MAX_LOADING_TIME = 180; // segundos máximos de carga (3 minutos)
  const LOADING_STAGES = 15; // número de etapas de carga

  // Estados para la asistencia de texto
  const [showSuggestionDialog, setShowSuggestionDialog] = useState(false);
  const [textSuggestions, setTextSuggestions] = useState<string[]>([]);
  const [isTextAssistLoading, setIsTextAssistLoading] = useState(false);
  const [textFieldToAssist, setTextFieldToAssist] = useState<
    "content" | "context" | "question" | null
  >(null);
  const [questionIndexToAssist, setQuestionIndexToAssist] = useState<
    number | null
  >(null);

  // Ref para el divResultados
  const resultsRef = useRef<HTMLDivElement>(null);

  // Toast para notificaciones
  const { toast } = useToast();

  // Cargar focus groups guardados cuando el usuario esté autenticado
  useEffect(() => {
    if (user && user.id !== "anonymous") {
      loadSavedFocusGroups();
    }
  }, [user]);

  // Debounced background sync to reduce API calls
  const backgroundSyncTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const scheduleBackgroundSync = useCallback(() => {
    if (backgroundSyncTimeoutRef.current) {
      clearTimeout(backgroundSyncTimeoutRef.current);
    }
    backgroundSyncTimeoutRef.current = setTimeout(() => {
      loadSavedFocusGroups(true);
    }, 2000); // Wait 2 seconds before syncing
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (backgroundSyncTimeoutRef.current) {
        clearTimeout(backgroundSyncTimeoutRef.current);
      }
    };
  }, []);

  const loadSavedFocusGroups = async (skipLoading = false) => {
    if (!user || user.id === "anonymous") return;

    if (!skipLoading) {
      setIsLoadingFocusGroups(true);
    }

    try {
      const [recent, favorites] = await Promise.all([
        focusGroupService.getRecentSimulations(),
        focusGroupService.getFavoriteSimulations()
      ]);

      setRecentFocusGroups(recent);
      setFavoriteFocusGroups(favorites);
    } catch (error) {
      console.error("Error loading saved focus groups:", error);
      if (!skipLoading) {
        toast({
          title: "Error",
          description: "No se pudieron cargar los focus groups guardados",
          variant: "destructive",
        });
      }
    } finally {
      if (!skipLoading) {
        setIsLoadingFocusGroups(false);
      }
    }
  };

  // Optimistic update helper functions
  const addOptimisticSimulation = useCallback((simulation: FocusGroupSimulation) => {
    setRecentFocusGroups(prev => [simulation, ...prev]);
  }, []);

  const updateOptimisticFavorite = useCallback((simulationId: string, isFavorite: boolean) => {
    // Update the simulation in both lists
    const updateSimulation = (sim: FocusGroupSimulation) =>
      sim.id === simulationId ? { ...sim, is_favorite: isFavorite } : sim;

    setRecentFocusGroups(prev => prev.map(updateSimulation));
    setFavoriteFocusGroups(prev => {
      const updated = prev.map(updateSimulation);
      if (isFavorite) {
        // Add to favorites if not already there
        const existingIndex = updated.findIndex(sim => sim.id === simulationId);
        if (existingIndex === -1) {
          const recentSim = recentFocusGroups.find(sim => sim.id === simulationId);
          if (recentSim) {
            return [{ ...recentSim, is_favorite: true }, ...updated];
          }
        }
        return updated;
      } else {
        // Remove from favorites
        return updated.filter(sim => sim.id !== simulationId);
      }
    });
  }, [recentFocusGroups]);

  const removeOptimisticSimulation = useCallback((simulationId: string) => {
    setRecentFocusGroups(prev => prev.filter(sim => sim.id !== simulationId));
    setFavoriteFocusGroups(prev => prev.filter(sim => sim.id !== simulationId));
  }, []);

  // Memoized counts for better performance
  const recentCount = useMemo(() => recentFocusGroups.length, [recentFocusGroups.length]);
  const favoritesCount = useMemo(() => favoriteFocusGroups.length, [favoriteFocusGroups.length]);



  const handleRenameFocusGroup = async (focusGroupId: string, newName: string) => {
    if (!user || user.id === "anonymous") return;

    // Optimistic update - update UI immediately
    const updateName = (sim: FocusGroupSimulation) =>
      sim.id === focusGroupId ? { ...sim, custom_name: newName } : sim;

    setRecentFocusGroups(prev => prev.map(updateName));
    setFavoriteFocusGroups(prev => prev.map(updateName));

    try {
      await focusGroupService.renameSimulation(focusGroupId, newName);
      setIsRenaming(null);
      setRenameValue("");

      // Schedule background sync to ensure consistency
      scheduleBackgroundSync();

      toast({
        title: "Renombrado",
        description: "Focus group renombrado correctamente",
      });
    } catch (error) {
      console.error("Error renaming focus group:", error);

      // Revert optimistic update on error
      loadSavedFocusGroups(true);

      toast({
        title: "Error",
        description: "No se pudo renombrar el focus group",
        variant: "destructive",
      });
    }
  };

  const handleDeleteFocusGroup = async (focusGroupId: string) => {
    if (!user || user.id === "anonymous") return;

    // Store the simulation for potential rollback
    const deletedSim = recentFocusGroups.find(sim => sim.id === focusGroupId) ||
                      favoriteFocusGroups.find(sim => sim.id === focusGroupId);

    // Optimistic update - remove from UI immediately
    removeOptimisticSimulation(focusGroupId);

    try {
      await focusGroupService.deleteSimulation(focusGroupId);

      toast({
        title: "Eliminado",
        description: "Focus group eliminado correctamente",
      });
    } catch (error) {
      console.error("Error deleting focus group:", error);

      // Revert optimistic update on error
      if (deletedSim) {
        setRecentFocusGroups(prev => [deletedSim, ...prev]);
        if (deletedSim.is_favorite) {
          setFavoriteFocusGroups(prev => [deletedSim, ...prev]);
        }
      }

      toast({
        title: "Error",
        description: "No se pudo eliminar el focus group",
        variant: "destructive",
      });
    }
  };

  const handleToggleFavorite = async (focusGroupId?: string) => {
    const targetId = focusGroupId || currentSimulationId;
    if (!targetId || !user || user.id === "anonymous") {
      toast({
        title: "Autenticación requerida",
        description: "Debes iniciar sesión para marcar favoritos",
        variant: "destructive",
      });
      return;
    }

    // Find the current simulation to determine its current favorite status
    const currentSim = recentFocusGroups.find(sim => sim.id === targetId) ||
                      favoriteFocusGroups.find(sim => sim.id === targetId);

    if (!currentSim) return;

    const newFavoriteStatus = !currentSim.is_favorite;

    // Optimistic update - update UI immediately
    updateOptimisticFavorite(targetId, newFavoriteStatus);

    setIsTogglingFavorite(true);
    try {
      await focusGroupService.toggleFavorite(targetId);

      // Schedule background sync to ensure consistency
      scheduleBackgroundSync();

      toast({
        title: "Favorito actualizado",
        description: "El estado de favorito se ha actualizado correctamente",
      });
    } catch (error) {
      console.error("Error toggling favorite:", error);

      // Revert optimistic update on error
      updateOptimisticFavorite(targetId, currentSim.is_favorite);

      toast({
        title: "Error",
        description: "No se pudo actualizar el favorito",
        variant: "destructive",
      });
    } finally {
      setIsTogglingFavorite(false);
    }
  };

  const handleLoadFocusGroup = (focusGroup: FocusGroupSimulation) => {
    // Load the focus group parameters into the form
    setContentToAnalyze(focusGroup.content);
    setProductCategory(focusGroup.product_category || "");
    setAdditionalContext(focusGroup.context || "");
    setCustomQuestions(focusGroup.custom_questions.length > 0 ? focusGroup.custom_questions : [""]);

    // Load the results
    if (focusGroup.simulation_results) {
      setResult(focusGroup.simulation_results);
    }

    // Switch to form tab to show the loaded data
    setActiveTab("form");

    // Record the view
    if (user && user.id !== "anonymous") {
      focusGroupService.recordView(focusGroup.id).catch(console.error);
    }

    toast({
      title: "Focus group cargado",
      description: "Los parámetros y resultados han sido cargados",
    });
  };

  const handleRegenerateFocusGroup = async (focusGroup: FocusGroupSimulation) => {
    if (!user || user.id === "anonymous") return;

    // Load the parameters
    setContentToAnalyze(focusGroup.content);
    setProductCategory(focusGroup.product_category || "");
    setAdditionalContext(focusGroup.context || "");
    setCustomQuestions(focusGroup.custom_questions.length > 0 ? focusGroup.custom_questions : [""]);

    // Clear current results
    setResult(null);

    // Switch to form tab
    setActiveTab("form");

    // Record the regeneration
    try {
      await focusGroupService.recordRegeneration(focusGroup.id);
    } catch (error) {
      console.error("Error recording regeneration:", error);
    }

    toast({
      title: "Parámetros cargados",
      description: "Los parámetros han sido cargados. Haz clic en 'Simular Focus Group' para regenerar",
    });
  };

  // Opciones para el formulario intuitivo
  const intuitiveFormOptions = {
    ideaType: [
      { value: "una app móvil", label: "📱 App Móvil", icon: "📱" },
      { value: "una plataforma web", label: "💻 Plataforma Web", icon: "💻" },
      { value: "un producto físico", label: "📦 Producto Físico", icon: "📦" },
      { value: "un servicio", label: "🛎️ Servicio", icon: "🛎️" },
      { value: "un restaurante/comida", label: "🍔 Comida/Restaurante", icon: "🍔" },
      { value: "una campaña de marketing", label: "📢 Campaña Marketing", icon: "📢" },
      { value: "custom_idea", label: "✏️ Otro (escribir)", icon: "✏️" }
    ],
    targetAge: [
      { value: "18-25", label: "👨‍🎓 18-25 años", icon: "👨‍🎓" },
      { value: "26-35", label: "👨‍💼 26-35 años", icon: "👨‍💼" },
      { value: "36-45", label: "👨‍👩‍👧‍👦 36-45 años", icon: "👨‍👩‍👧‍👦" },
      { value: "46-55", label: "👨‍🦳 46-55 años", icon: "👨‍🦳" },
      { value: "55+", label: "👴 55+ años", icon: "👴" },
      { value: "todas las edades", label: "👥 Todas las edades", icon: "👥" },
      { value: "custom_age", label: "✏️ Otro (escribir)", icon: "✏️" }
    ],
    priceRange: [
      { value: "gratis", label: "🆓 Gratis", icon: "🆓" },
      { value: "$1-50", label: "💵 $1-50", icon: "💵" },
      { value: "$51-200", label: "💰 $51-200", icon: "💰" },
      { value: "$201-500", label: "💎 $201-500", icon: "💎" },
      { value: "$500+", label: "👑 $500+", icon: "👑" },
      { value: "por definir", label: "❓ Por definir", icon: "❓" },
      { value: "custom_price", label: "✏️ Otro (escribir)", icon: "✏️" }
    ],
    problemSolved: [
      { value: "falta de tiempo", label: "⏰ Falta de tiempo", icon: "⏰" },
      { value: "es muy caro", label: "💸 Es muy caro", icon: "💸" },
      { value: "es muy complicado", label: "😰 Es muy complicado", icon: "😰" },
      { value: "no hay opciones cerca", label: "📍 No hay opciones cerca", icon: "📍" },
      { value: "no saben cómo hacerlo", label: "🤔 No saben cómo hacerlo", icon: "🤔" },
      { value: "mala calidad disponible", label: "👎 Mala calidad disponible", icon: "👎" },
      { value: "problemas de salud", label: "💪 Problemas de salud", icon: "💪" },
      { value: "problemas de relaciones", label: "💕 Problemas de relaciones", icon: "💕" },
      { value: "falta de motivación", label: "🔥 Falta de motivación", icon: "🔥" },
      { value: "estrés y ansiedad", label: "😌 Estrés y ansiedad", icon: "😌" },
      { value: "custom", label: "✏️ Otro (escribir)", icon: "✏️" }
    ],
    usageLocation: [
      { value: "en casa", label: "🏠 En casa", icon: "🏠" },
      { value: "en el trabajo", label: "🏢 En el trabajo", icon: "🏢" },
      { value: "en movimiento", label: "🚶 En movimiento", icon: "🚶" },
      { value: "en tienda física", label: "🏪 En tienda física", icon: "🏪" },
      { value: "online", label: "🌐 Online", icon: "🌐" },
      { value: "en cualquier lugar", label: "📍 En cualquier lugar", icon: "📍" },
      { value: "custom_location", label: "✏️ Otro (escribir)", icon: "✏️" }
    ],
    uniqueFeature: [
      { value: "es más barato", label: "💰 Es más barato", icon: "💰" },
      { value: "es más rápido", label: "⚡ Es más rápido", icon: "⚡" },
      { value: "es más fácil de usar", label: "😊 Es más fácil de usar", icon: "😊" },
      { value: "tiene mejor calidad", label: "⭐ Tiene mejor calidad", icon: "⭐" },
      { value: "es más personalizado", label: "🎯 Es más personalizado", icon: "🎯" },
      { value: "es más conveniente", label: "👌 Es más conveniente", icon: "👌" },
      { value: "custom_feature", label: "✏️ Otro (escribir)", icon: "✏️" }
    ],
    timeToUse: [
      { value: "segundos", label: "⚡ Segundos", icon: "⚡" },
      { value: "minutos", label: "⏱️ Minutos", icon: "⏱️" },
      { value: "horas", label: "🕐 Horas", icon: "🕐" },
      { value: "días", label: "📅 Días", icon: "📅" },
      { value: "uso continuo", label: "🔄 Uso continuo", icon: "🔄" },
      { value: "depende del usuario", label: "👤 Depende del usuario", icon: "👤" },
      { value: "custom_time", label: "✏️ Otro (escribir)", icon: "✏️" }
    ],
    mainBenefit: [
      { value: "ahorra tiempo", label: "⏰ Ahorra tiempo", icon: "⏰" },
      { value: "ahorra dinero", label: "💰 Ahorra dinero", icon: "💰" },
      { value: "mejora la salud", label: "💪 Mejora la salud", icon: "💪" },
      { value: "aumenta la productividad", label: "📈 Aumenta productividad", icon: "📈" },
      { value: "mejora la calidad de vida", label: "😊 Mejora calidad de vida", icon: "😊" },
      { value: "facilita las tareas", label: "✅ Facilita las tareas", icon: "✅" },
      { value: "custom_benefit", label: "✏️ Otro (escribir)", icon: "✏️" }
    ]
  };

  // Mensajes para mostrar durante la carga
  const loadingMessages = [
    "Iniciando simulación de focus group...",
    "Configurando entorno virtual...",
    "Generando perfiles demográficos de participantes...",
    "Creando personalidades diversas para la simulación...",
    "Analizando contenido a evaluar...",
    "Preparando preguntas para la discusión...",
    "Creando escenarios de interacción grupal...",
    "Simulando respuestas iniciales de participantes...",
    "Procesando interacciones entre participantes...",
    "Analizando patrones de comportamiento...",
    "Identificando tendencias demográficas en respuestas...",
    "Extrayendo insights clave de las conversaciones...",
    "Evaluando impacto por segmentos...",
    "Elaborando recomendaciones personalizadas...",
    "Finalizando reporte de simulación...",
  ];

  // Función para obtener el color del sentimiento
  const getSentimentColor = (
    sentiment: "positivo" | "neutral" | "negativo",
  ) => {
    switch (sentiment) {
      case "positivo":
        return "bg-green-50 border-green-200";
      case "negativo":
        return "bg-red-50 border-red-200";
      case "neutral":
      default:
        return "bg-blue-50 border-blue-200";
    }
  };

  // Agregar y eliminar preguntas personalizadas
  const addCustomQuestion = () => {
    setCustomQuestions([...customQuestions, ""]);
  };

  const removeCustomQuestion = (index: number) => {
    const newQuestions = [...customQuestions];
    newQuestions.splice(index, 1);
    setCustomQuestions(newQuestions);
  };

  const updateCustomQuestion = (index: number, value: string) => {
    const newQuestions = [...customQuestions];
    newQuestions[index] = value;
    setCustomQuestions(newQuestions);
  };

  // Funciones para el formulario intuitivo
  const updateIntuitiveFormField = useCallback((field: string, value: string) => {
    setIntuitiveFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const toggleAgeSelection = useCallback((age: string) => {
    setIntuitiveFormData(prev => ({
      ...prev,
      targetAge: prev.targetAge.includes(age)
        ? prev.targetAge.filter(a => a !== age)
        : [...prev.targetAge, age]
    }));
  }, []);

  // Handlers específicos para los campos problemáticos
  const competitionHandler = useMemo(() => (e: React.ChangeEvent<HTMLInputElement>) => {
    updateIntuitiveFormField("competition", e.target.value);
  }, [updateIntuitiveFormField]);

  const descriptionHandler = useMemo(() => (e: React.ChangeEvent<HTMLInputElement>) => {
    updateIntuitiveFormField("briefDescription", e.target.value);
  }, [updateIntuitiveFormField]);

  const generateContentFromIntuitiveForm = () => {
    const data = intuitiveFormData;

    // Generar descripción completa basada en las respuestas
    let generatedContent = `Estamos desarrollando `;

    // Manejar tipo de idea personalizado
    if (data.ideaType === "custom_idea" && data.customIdea) {
      generatedContent += `${data.customIdea.toLowerCase()} `;
    } else if (data.ideaType && data.ideaType !== "custom_idea") {
      generatedContent += `${data.ideaType.toLowerCase()} `;
    }

    if (data.briefDescription) {
      generatedContent += `"${data.briefDescription}" `;
    }

    // Manejar múltiples edades incluyendo personalizada
    if (data.targetAge.length > 0) {
      const ages = data.targetAge.map(age => {
        if (age === "custom_age" && data.customAge) {
          return data.customAge;
        }
        return age;
      }).filter(age => age !== "custom_age");

      if (ages.length === 1) {
        generatedContent += `dirigido principalmente a personas de ${ages[0]} años. `;
      } else if (ages.length > 1) {
        generatedContent += `dirigido a personas de ${ages.join(', ')} años. `;
      }
    }

    // Manejar problema personalizado o predefinido
    if (data.problemSolved) {
      if (data.problemSolved === "custom" && data.customProblem) {
        generatedContent += `El problema principal que resuelve es ${data.customProblem.toLowerCase()}. `;
      } else if (data.problemSolved !== "custom") {
        generatedContent += `El problema principal que resuelve es ${data.problemSolved.toLowerCase()}. `;
      }
    }

    // Manejar característica única personalizada
    if (data.uniqueFeature) {
      if (data.uniqueFeature === "custom_feature" && data.customFeature) {
        generatedContent += `Lo que lo hace único es que ${data.customFeature.toLowerCase()}. `;
      } else if (data.uniqueFeature !== "custom_feature") {
        generatedContent += `Lo que lo hace único es que ${data.uniqueFeature.toLowerCase()}. `;
      }
    }

    // Manejar ubicación personalizada
    if (data.usageLocation) {
      if (data.usageLocation === "custom_location" && data.customLocation) {
        generatedContent += `Los usuarios lo utilizarían principalmente ${data.customLocation.toLowerCase()}. `;
      } else if (data.usageLocation !== "custom_location") {
        generatedContent += `Los usuarios lo utilizarían principalmente ${data.usageLocation.toLowerCase()}. `;
      }
    }

    // Manejar tiempo personalizado
    if (data.timeToUse) {
      if (data.timeToUse === "custom_time" && data.customTime) {
        generatedContent += `El tiempo estimado de uso sería ${data.customTime.toLowerCase()}. `;
      } else if (data.timeToUse !== "custom_time") {
        generatedContent += `El tiempo estimado de uso sería ${data.timeToUse.toLowerCase()}. `;
      }
    }

    // Manejar precio personalizado
    if (data.priceRange) {
      if (data.priceRange === "custom_price" && data.customPrice) {
        generatedContent += `El rango de precio estaría en ${data.customPrice}. `;
      } else if (data.priceRange !== "custom_price") {
        generatedContent += `El rango de precio estaría en ${data.priceRange}. `;
      }
    }

    if (data.competition) {
      generatedContent += `En cuanto a competencia, se compararía con ${data.competition}. `;
    }

    // Manejar beneficio personalizado
    if (data.mainBenefit) {
      if (data.mainBenefit === "custom_benefit" && data.customBenefit) {
        generatedContent += `El beneficio principal que ofrece es ${data.customBenefit.toLowerCase()}.`;
      } else if (data.mainBenefit !== "custom_benefit") {
        generatedContent += `El beneficio principal que ofrece es ${data.mainBenefit.toLowerCase()}.`;
      }
    }

    return generatedContent.trim();
  };

  const useIntuitiveFormContent = () => {
    const generatedContent = generateContentFromIntuitiveForm();
    setContentToAnalyze(generatedContent);
    setUseIntuitiveForm(false);
    setCurrentStep(0);

    toast({
      title: "Contenido generado",
      description: "Se ha creado la descripción basada en tus respuestas.",
    });
  };

  const resetIntuitiveForm = () => {
    setIntuitiveFormData({
      ideaType: "",
      customIdea: "",
      targetAge: [],
      customAge: "",
      priceRange: "",
      customPrice: "",
      competition: "",
      problemSolved: "",
      customProblem: "",
      usageLocation: "",
      customLocation: "",
      uniqueFeature: "",
      customFeature: "",
      timeToUse: "",
      customTime: "",
      mainBenefit: "",
      customBenefit: "",
      briefDescription: ""
    });
    setCurrentStep(0);
  };

  // Solicitar mejoras de texto usando IA
  const requestTextAssistance = (
    fieldType: "content" | "context" | "question",
    index?: number,
  ) => {
    setTextFieldToAssist(fieldType);
    setQuestionIndexToAssist(index !== undefined ? index : null);
    setIsTextAssistLoading(true);

    let textToImprove = "";
    switch (fieldType) {
      case "content":
        textToImprove = contentToAnalyze;
        break;
      case "context":
        textToImprove = additionalContext;
        break;
      case "question":
        if (
          index !== undefined &&
          index >= 0 &&
          index < customQuestions.length
        ) {
          textToImprove = customQuestions[index];
        }
        break;
    }

    if (!textToImprove || textToImprove.trim().length < 5) {
      toast({
        title: "Texto insuficiente",
        description:
          "Por favor, escribe algo más de texto antes de solicitar mejoras.",
        variant: "destructive",
      });
      setIsTextAssistLoading(false);
      return;
    }

    // Llamada a la API para mejorar el texto
    fetch("/api/text-assist", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        content: textToImprove,
        context: `Mejora de texto para ${
          fieldType === "content"
            ? "contenido principal"
            : fieldType === "context"
              ? "contexto adicional"
              : "pregunta personalizada"
        }`,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.status === "success" && data.suggestions) {
          setTextSuggestions(data.suggestions);
          setShowSuggestionDialog(true);
        } else {
          throw new Error(data.error || "No se pudieron generar sugerencias");
        }
      })
      .catch((error) => {
        toast({
          title: "Error al generar sugerencias",
          description: error.message,
          variant: "destructive",
        });
      })
      .finally(() => {
        setIsTextAssistLoading(false);
      });
  };

  // Aplicar una sugerencia seleccionada
  const applySuggestion = (suggestion: string) => {
    switch (textFieldToAssist) {
      case "content":
        setContentToAnalyze(suggestion);
        break;
      case "context":
        setAdditionalContext(suggestion);
        break;
      case "question":
        if (questionIndexToAssist !== null) {
          updateCustomQuestion(questionIndexToAssist, suggestion);
        }
        break;
    }

    setShowSuggestionDialog(false);
    toast({
      title: "Texto actualizado",
      description: "Se ha aplicado la sugerencia al texto.",
    });
  };

  // Validar contenido
  const isValidContent = (content: string): boolean => {
    const cleanContent = content.trim().toLowerCase();

    // Check minimum length
    if (cleanContent.length < 5) return false;

    // Check for excessive repetition
    if (/(.)\1{4,}/.test(cleanContent)) return false;

    // Check for keyboard mashing
    const randomPatterns = [
      /[qwertyuiop]{5,}/,
      /[asdfghjkl]{5,}/,
      /[zxcvbnm]{5,}/,
      /^[bcdfghjklmnpqrstvwxyz]{8,}$/
    ];

    if (randomPatterns.some(pattern => pattern.test(cleanContent))) return false;

    // Check vowel ratio
    const vowels = (cleanContent.match(/[aeiouáéíóú]/g) || []).length;
    const consonants = (cleanContent.match(/[bcdfghjklmnpqrstvwxyzñ]/g) || []).length;

    if (consonants > 0 && vowels / (vowels + consonants) < 0.15) return false;

    // Check for meaningful patterns
    const meaningfulPatterns = [
      /\b(app|aplicacion|producto|servicio|idea|negocio|empresa|startup)\b/,
      /\b(para|que|con|sin|como|donde|cuando|porque)\b/,
      /\b(gente|personas|usuarios|clientes|mercado)\b/,
      /\b(hacer|crear|desarrollar|vender|comprar|usar)\b/
    ];

    if (meaningfulPatterns.some(pattern => pattern.test(cleanContent))) return true;

    // Check for basic sentence structure
    return cleanContent.split(' ').length >= 2;
  };

  // Ejecutar simulación
  const runFocusGroupSimulation = async () => {
    if (!contentToAnalyze) {
      setError("Por favor, ingresa el contenido que quieres analizar");
      return;
    }

    if (!isValidContent(contentToAnalyze)) {
      setError("Por favor describe una idea real de producto o servicio. Evita texto sin sentido o caracteres aleatorios.");
      return;
    }

    setIsLoading(true);
    setError(null);
    setViewMode("live-simulation");
    setLoadingTime(0);
    setLoadingStage(0);

    // Temporizador para mostrar progreso simulado
    const loadingInterval = setInterval(() => {
      setLoadingTime((prev) => {
        const newTime = prev + 1;
        // Actualizar etapa de carga basado en el tiempo
        const newStage = Math.min(
          Math.floor((newTime / MAX_LOADING_TIME) * LOADING_STAGES),
          LOADING_STAGES - 1,
        );
        if (newStage > loadingStage) {
          setLoadingStage(newStage);
        }
        return newTime;
      });
    }, 1000);

    try {
      // Filtrar preguntas vacías
      const filteredQuestions = customQuestions.filter((q) => q.trim() !== "");

      // Llamada a la API usando el servicio
      const data = await focusGroupApiService.simulateFocusGroup({
        content: contentToAnalyze,
        product_category: productCategory,
        context: additionalContext,
        questions: filteredQuestions.length > 0 ? filteredQuestions : undefined,
      });

      clearInterval(loadingInterval);

      if (data.status === "success") {
        setResult(data);
        setViewMode("form"); // Cambiado de 'results' a 'form' para usar un solo modo de visualización

        // Check if the backend returned a simulation ID
        const simulationId = data.simulation_id;
        if (simulationId) {
          setCurrentSimulationId(simulationId);
          console.log("Simulation saved with ID:", simulationId);
        } else {
          // Fallback to simulated ID if backend doesn't return one
          const simulatedId = `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          setCurrentSimulationId(simulatedId);
          console.log("Using simulated ID:", simulatedId);
        }

        // If user is authenticated and simulation was saved, add optimistic update
        if (user && user.id !== "anonymous" && simulationId) {
          try {
            // Create optimistic simulation object
            const optimisticSimulation: FocusGroupSimulation = {
              id: simulationId,
              user_id: user.id,
              content: contentToAnalyze,
              product_category: productCategory || null,
              context: additionalContext || null,
              custom_questions: customQuestions.filter(q => q.trim() !== ""),
              participants: data.participants || [],
              insights: data.insights || [],
              recommendations: data.recommendations || [],
              custom_name: null,
              is_favorite: false,
              tags: [],
              notes: null,
              last_viewed_at: new Date().toISOString(),
              regeneration_count: 0,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            // Add to recent list immediately
            addOptimisticSimulation(optimisticSimulation);

            // Schedule background sync to ensure consistency
            scheduleBackgroundSync();

            toast({
              title: "Focus group guardado",
              description: "La simulación se ha guardado correctamente",
            });
          } catch (loadError) {
            console.error("Error with optimistic update:", loadError);
            // Fallback to regular reload
            loadSavedFocusGroups();
          }
        }
      } else {
        throw new Error(
          data.error_message || "Hubo un problema con la simulación",
        );
      }
    } catch (err: any) {
      clearInterval(loadingInterval);
      setError(err.message || "Error al procesar la solicitud");
      setViewMode("form");
    } finally {
      setIsLoading(false);
    }
  };

  // Generar PDF con los resultados
  const generatePDF = async () => {
    if (!resultsRef.current || !result) return;

    toast({
      title: "Generando PDF",
      description: "Espera un momento mientras se crea el documento...",
    });

    try {
      const content = resultsRef.current;
      const canvas = await html2canvas(content, {
        scale: 1,
        useCORS: true,
        logging: false,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
      });

      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const imgX = (pdfWidth - imgWidth * ratio) / 2;
      const imgY = 10;

      pdf.addImage(
        imgData,
        "PNG",
        imgX,
        imgY,
        imgWidth * ratio,
        imgHeight * ratio,
      );
      pdf.save(`focus-group-${new Date().toISOString().slice(0, 10)}.pdf`);

      toast({
        title: "PDF generado con éxito",
        description: "El documento se ha descargado correctamente.",
      });
    } catch (err) {
      toast({
        title: "Error al generar PDF",
        description: "No se pudo crear el documento PDF.",
        variant: "destructive",
      });
    }
  };

  // Componente para el formulario intuitivo con slides
  const IntuitiveFormView = () => {
    const totalSteps = 10;
    const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

    const isStepValid = (step: number) => {
      switch (step) {
        case 0: return intuitiveFormData.ideaType !== "" &&
                       (intuitiveFormData.ideaType !== "custom_idea" || intuitiveFormData.customIdea.trim() !== "");
        case 1: return intuitiveFormData.targetAge.length > 0 &&
                       (!intuitiveFormData.targetAge.includes("custom_age") || intuitiveFormData.customAge.trim() !== "");
        case 2: return true; // Precio es opcional
        case 3: return intuitiveFormData.problemSolved !== "" &&
                       (intuitiveFormData.problemSolved !== "custom" || intuitiveFormData.customProblem.trim() !== "");
        case 4: return true; // Ubicación es opcional
        case 5: return true; // Característica única es opcional
        case 6: return true; // Tiempo es opcional
        case 7: return true; // Beneficio es opcional
        case 8: return true; // Competencia es opcional
        case 9: return intuitiveFormData.briefDescription.trim().length > 0;
        default: return false;
      }
    };

    const nextStep = () => {
      if (currentStep < totalSteps - 1) {
        setCurrentStep(currentStep + 1);
      }
    };

    const prevStep = () => {
      if (currentStep > 0) {
        setCurrentStep(currentStep - 1);
      }
    };

    const renderStep = () => {
      switch (currentStep) {
        case 0:
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Qué tipo de idea tienes?</h2>
                <p className="text-gray-600">Selecciona la categoría que mejor describe tu idea</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {intuitiveFormOptions.ideaType.map((option) => (
                  <Button
                    key={option.value}
                    variant={intuitiveFormData.ideaType === option.value ? "default" : "outline"}
                    className={`h-20 text-left justify-start flex-col gap-2 ${
                      intuitiveFormData.ideaType === option.value
                        ? "bg-blue-600 text-white border-blue-600"
                        : "hover:bg-blue-50 hover:border-blue-300"
                    }`}
                    onClick={() => updateIntuitiveFormField("ideaType", option.value)}
                  >
                    <span className="text-2xl">{option.icon}</span>
                    <span className="text-sm font-medium">{option.label.replace(/^[^\s]+ /, '')}</span>
                  </Button>
                ))}
              </div>
              {intuitiveFormData.ideaType === "custom_idea" && (
                <div className="mt-4 max-w-md mx-auto">
                  <Input
                    placeholder="Describe tu tipo de idea..."
                    value={intuitiveFormData.customIdea}
                    onChange={(e) => updateIntuitiveFormField("customIdea", e.target.value)}
                    className="border-gray-300 text-center"
                    autoFocus
                  />
                </div>
              )}
            </div>
          );

        case 1:
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Para qué edad está dirigido?</h2>
                <p className="text-gray-600">Puedes seleccionar múltiples rangos de edad</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {intuitiveFormOptions.targetAge.map((option) => (
                  <Button
                    key={option.value}
                    variant={intuitiveFormData.targetAge.includes(option.value) ? "default" : "outline"}
                    className={`h-20 text-left justify-start flex-col gap-2 ${
                      intuitiveFormData.targetAge.includes(option.value)
                        ? "bg-blue-600 text-white border-blue-600"
                        : "hover:bg-blue-50 hover:border-blue-300"
                    }`}
                    onClick={() => toggleAgeSelection(option.value)}
                  >
                    <span className="text-2xl">{option.icon}</span>
                    <span className="text-sm font-medium">{option.label.replace(/^[^\s]+ /, '')}</span>
                  </Button>
                ))}
              </div>
              {intuitiveFormData.targetAge.includes("custom_age") && (
                <div className="mt-4 max-w-md mx-auto">
                  <Input
                    placeholder="Especifica el rango de edad..."
                    value={intuitiveFormData.customAge}
                    onChange={(e) => updateIntuitiveFormField("customAge", e.target.value)}
                    className="border-gray-300 text-center"
                    autoFocus
                  />
                </div>
              )}
            </div>
          );

        case 2:
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Cuál sería el precio aproximado?</h2>
                <p className="text-gray-600">Opcional - Selecciona el rango de precio</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {intuitiveFormOptions.priceRange.map((option) => (
                  <Button
                    key={option.value}
                    variant={intuitiveFormData.priceRange === option.value ? "default" : "outline"}
                    className={`h-20 text-left justify-start flex-col gap-2 ${
                      intuitiveFormData.priceRange === option.value
                        ? "bg-blue-600 text-white border-blue-600"
                        : "hover:bg-blue-50 hover:border-blue-300"
                    }`}
                    onClick={() => updateIntuitiveFormField("priceRange", option.value)}
                  >
                    <span className="text-2xl">{option.icon}</span>
                    <span className="text-sm font-medium">{option.label.replace(/^[^\s]+ /, '')}</span>
                  </Button>
                ))}
              </div>
              {intuitiveFormData.priceRange === "custom_price" && (
                <div className="mt-4 max-w-md mx-auto">
                  <Input
                    placeholder="Especifica el precio..."
                    value={intuitiveFormData.customPrice}
                    onChange={(e) => updateIntuitiveFormField("customPrice", e.target.value)}
                    className="border-gray-300 text-center"
                    autoFocus
                  />
                </div>
              )}
            </div>
          );

        case 3:
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Qué problema principal resuelve?</h2>
                <p className="text-gray-600">Selecciona el problema que tu idea soluciona</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {intuitiveFormOptions.problemSolved.map((option) => (
                  <Button
                    key={option.value}
                    variant={intuitiveFormData.problemSolved === option.value ? "default" : "outline"}
                    className={`h-20 text-left justify-start flex-col gap-2 ${
                      intuitiveFormData.problemSolved === option.value
                        ? "bg-blue-600 text-white border-blue-600"
                        : "hover:bg-blue-50 hover:border-blue-300"
                    }`}
                    onClick={() => updateIntuitiveFormField("problemSolved", option.value)}
                  >
                    <span className="text-2xl">{option.icon}</span>
                    <span className="text-sm font-medium">{option.label.replace(/^[^\s]+ /, '')}</span>
                  </Button>
                ))}
              </div>
              {intuitiveFormData.problemSolved === "custom" && (
                <div className="mt-4">
                  <Input
                    placeholder="Describe el problema que resuelve tu idea..."
                    value={intuitiveFormData.customProblem}
                    onChange={(e) => updateIntuitiveFormField("customProblem", e.target.value)}
                    className="border-gray-300 text-center"
                    autoFocus
                  />
                </div>
              )}
            </div>
          );

        case 4:
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Dónde lo usarían/comprarían?</h2>
                <p className="text-gray-600">Opcional - Selecciona el lugar de uso</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {intuitiveFormOptions.usageLocation.map((option) => (
                  <Button
                    key={option.value}
                    variant={intuitiveFormData.usageLocation === option.value ? "default" : "outline"}
                    className={`h-20 text-left justify-start flex-col gap-2 ${
                      intuitiveFormData.usageLocation === option.value
                        ? "bg-blue-600 text-white border-blue-600"
                        : "hover:bg-blue-50 hover:border-blue-300"
                    }`}
                    onClick={() => updateIntuitiveFormField("usageLocation", option.value)}
                  >
                    <span className="text-2xl">{option.icon}</span>
                    <span className="text-sm font-medium">{option.label.replace(/^[^\s]+ /, '')}</span>
                  </Button>
                ))}
              </div>
              {intuitiveFormData.usageLocation === "custom_location" && (
                <div className="mt-4 max-w-md mx-auto">
                  <Input
                    placeholder="Especifica dónde se usaría..."
                    value={intuitiveFormData.customLocation}
                    onChange={(e) => updateIntuitiveFormField("customLocation", e.target.value)}
                    className="border-gray-300 text-center"
                    autoFocus
                  />
                </div>
              )}
            </div>
          );

        case 5:
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Qué lo hace diferente?</h2>
                <p className="text-gray-600">Opcional - Selecciona su ventaja competitiva</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {intuitiveFormOptions.uniqueFeature.map((option) => (
                  <Button
                    key={option.value}
                    variant={intuitiveFormData.uniqueFeature === option.value ? "default" : "outline"}
                    className={`h-20 text-left justify-start flex-col gap-2 ${
                      intuitiveFormData.uniqueFeature === option.value
                        ? "bg-blue-600 text-white border-blue-600"
                        : "hover:bg-blue-50 hover:border-blue-300"
                    }`}
                    onClick={() => updateIntuitiveFormField("uniqueFeature", option.value)}
                  >
                    <span className="text-2xl">{option.icon}</span>
                    <span className="text-sm font-medium">{option.label.replace(/^[^\s]+ /, '')}</span>
                  </Button>
                ))}
              </div>
              {intuitiveFormData.uniqueFeature === "custom_feature" && (
                <div className="mt-4 max-w-md mx-auto">
                  <Input
                    placeholder="Describe qué lo hace único..."
                    value={intuitiveFormData.customFeature}
                    onChange={(e) => updateIntuitiveFormField("customFeature", e.target.value)}
                    className="border-gray-300 text-center"
                    autoFocus
                  />
                </div>
              )}
            </div>
          );

        case 6:
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Cuánto tiempo tomaría usarlo?</h2>
                <p className="text-gray-600">Opcional - Selecciona la duración de uso</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {intuitiveFormOptions.timeToUse.map((option) => (
                  <Button
                    key={option.value}
                    variant={intuitiveFormData.timeToUse === option.value ? "default" : "outline"}
                    className={`h-20 text-left justify-start flex-col gap-2 ${
                      intuitiveFormData.timeToUse === option.value
                        ? "bg-blue-600 text-white border-blue-600"
                        : "hover:bg-blue-50 hover:border-blue-300"
                    }`}
                    onClick={() => updateIntuitiveFormField("timeToUse", option.value)}
                  >
                    <span className="text-2xl">{option.icon}</span>
                    <span className="text-sm font-medium">{option.label.replace(/^[^\s]+ /, '')}</span>
                  </Button>
                ))}
              </div>
              {intuitiveFormData.timeToUse === "custom_time" && (
                <div className="mt-4 max-w-md mx-auto">
                  <Input
                    placeholder="Especifica el tiempo de uso..."
                    value={intuitiveFormData.customTime}
                    onChange={(e) => updateIntuitiveFormField("customTime", e.target.value)}
                    className="border-gray-300 text-center"
                    autoFocus
                  />
                </div>
              )}
            </div>
          );

        case 7:
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Cuál es el beneficio principal?</h2>
                <p className="text-gray-600">Opcional - Selecciona el beneficio clave</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {intuitiveFormOptions.mainBenefit.map((option) => (
                  <Button
                    key={option.value}
                    variant={intuitiveFormData.mainBenefit === option.value ? "default" : "outline"}
                    className={`h-20 text-left justify-start flex-col gap-2 ${
                      intuitiveFormData.mainBenefit === option.value
                        ? "bg-blue-600 text-white border-blue-600"
                        : "hover:bg-blue-50 hover:border-blue-300"
                    }`}
                    onClick={() => updateIntuitiveFormField("mainBenefit", option.value)}
                  >
                    <span className="text-2xl">{option.icon}</span>
                    <span className="text-sm font-medium">{option.label.replace(/^[^\s]+ /, '')}</span>
                  </Button>
                ))}
              </div>
              {intuitiveFormData.mainBenefit === "custom_benefit" && (
                <div className="mt-4 max-w-md mx-auto">
                  <Input
                    placeholder="Describe el beneficio principal..."
                    value={intuitiveFormData.customBenefit}
                    onChange={(e) => updateIntuitiveFormField("customBenefit", e.target.value)}
                    className="border-gray-300 text-center"
                    autoFocus
                  />
                </div>
              )}
            </div>
          );

        case 8:
          return (
            <div className="space-y-6" key="competition-step">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">¿Quién es tu competencia principal?</h2>
                <p className="text-gray-600">Opcional - Menciona empresas o productos similares</p>
              </div>
              <div className="max-w-md mx-auto">
                <Input
                  key="competition-input"
                  placeholder="Ej: Netflix, Uber, McDonald's, etc."
                  value={intuitiveFormData.competition}
                  onChange={competitionHandler}
                  className="border-gray-300 text-center text-lg h-12"
                  autoFocus
                />
              </div>
            </div>
          );

        case 9:
          return (
            <div className="space-y-6" key="description-step">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">Describe tu idea en pocas palabras</h2>
                <p className="text-gray-600">Escribe 2-3 palabras clave que resuman tu idea</p>
              </div>
              <div className="max-w-md mx-auto space-y-4">
                <Input
                  key="description-input"
                  placeholder="Ej: app ejercicio local, comida saludable rápida..."
                  value={intuitiveFormData.briefDescription}
                  onChange={descriptionHandler}
                  className="border-gray-300 text-center text-lg h-12"
                  autoFocus
                />
                {intuitiveFormData.briefDescription && (
                  <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm font-medium text-green-700 mb-2">Vista previa del contenido:</p>
                    <p className="text-sm text-green-600 italic">
                      {generateContentFromIntuitiveForm()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          );

        default:
          return <div>Paso {currentStep + 1}</div>;
      }
    };

    return (
      <Card className="w-full border-2 border-blue-200 shadow-lg min-h-[600px]">
        {/* Barra de progreso */}
        <div className="p-4 border-b border-blue-100 bg-blue-50">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-700">
              Paso {currentStep + 1} de {totalSteps}
            </span>
            <span className="text-sm text-blue-600">{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        <CardContent className="p-8 flex-1 flex flex-col justify-center">
          {renderStep()}
        </CardContent>

        {/* Botones de navegación */}
        <div className="p-6 border-t border-gray-100 flex justify-between">
          <Button
            variant="outline"
            onClick={currentStep === 0 ? () => setUseIntuitiveForm(false) : prevStep}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {currentStep === 0 ? "Volver" : "Anterior"}
          </Button>

          {currentStep === totalSteps - 1 ? (
            <Button
              onClick={useIntuitiveFormContent}
              disabled={!isStepValid(currentStep)}
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
            >
              <Sparkles className="h-4 w-4" />
              Generar contenido
            </Button>
          ) : (
            <Button
              onClick={nextStep}
              disabled={!isStepValid(currentStep)}
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              Siguiente
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </div>
      </Card>
    );
  };

  // Componente para la simulación en vivo con burbujas
  const LiveSimulationView = () => {
    // Estado para burbujas animadas
    const [bubbles, setBubbles] = useState<
      Array<{
        id: number;
        x: number;
        y: number;
        size: number;
        color: string;
        speedX: number;
        speedY: number;
        content: string;
      }>
    >([]);

    // Inicializar burbujas
    useEffect(() => {
      if (!isLoading) return;

      // Generar burbujas aleatorias
      const newBubbles = Array.from({ length: 15 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: 20 + Math.random() * 40,
        color: [
          "bg-blue-100 border-blue-300",
          "bg-green-100 border-green-300",
          "bg-purple-100 border-purple-300",
          "bg-amber-100 border-amber-300",
          "bg-pink-100 border-pink-300",
        ][Math.floor(Math.random() * 5)],
        speedX: (Math.random() - 0.5) * 1.5,
        speedY: (Math.random() - 0.5) * 1.5,
        content: Math.random() > 0.7 ? "💬" : "",
      }));

      setBubbles(newBubbles);
    }, [isLoading]);

    // Animar burbujas
    useEffect(() => {
      if (!isLoading || bubbles.length === 0) return;

      const interval = setInterval(() => {
        setBubbles((prevBubbles) =>
          prevBubbles.map((bubble) => {
            let newX = bubble.x + bubble.speedX;
            let newY = bubble.y + bubble.speedY;

            if (newX <= 0 || newX >= 100) {
              bubble.speedX = -bubble.speedX;
              newX = bubble.x + bubble.speedX;
            }

            if (newY <= 0 || newY >= 100) {
              bubble.speedY = -bubble.speedY;
              newY = bubble.y + bubble.speedY;
            }

            return { ...bubble, x: newX, y: newY };
          }),
        );
      }, 50);

      return () => clearInterval(interval);
    }, [isLoading, bubbles]);

    // Calcular progreso
    const progressPercentage = Math.min(
      Math.round((loadingTime / MAX_LOADING_TIME) * 100),
      100,
    );
    const currentProgressMessage =
      loadingMessages[Math.min(loadingStage, loadingMessages.length - 1)];

    return (
      <div className="relative w-full h-[650px] overflow-hidden bg-gray-50 border-2 border-blue-200 rounded-lg shadow-lg">
        {/* Burbujas */}
        <div className="absolute inset-0 z-0">
          {bubbles.map((bubble) => (
            <motion.div
              key={bubble.id}
              className={`absolute rounded-full border-2 ${bubble.color} flex items-center justify-center`}
              style={{
                left: `${bubble.x}%`,
                top: `${bubble.y}%`,
                width: `${bubble.size}px`,
                height: `${bubble.size}px`,
              }}
              animate={{
                scale: [1, 1.05, 0.95, 1],
                rotate: [0, 5, -5, 0],
              }}
              transition={{
                duration: 2 + Math.random() * 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              <span className="text-xl">{bubble.content}</span>
            </motion.div>
          ))}
        </div>

        {/* Contenido central */}
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-center p-6">
          <motion.div
            className="bg-white/90 backdrop-blur-sm p-8 rounded-xl shadow-xl border-2 border-blue-300 text-center max-w-lg"
            animate={{ scale: [1, 1.02, 1] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          >
            <motion.div
              className="mx-auto mb-6 text-5xl"
              animate={{ scale: [1, 1.2, 1], rotate: [0, 10, -10, 0] }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              💬
            </motion.div>

            <h3 className="text-xl font-bold text-blue-700 mb-3">
              Simulando Focus Group
            </h3>

            <p className="text-gray-700 mb-4">{currentProgressMessage}</p>

            <div className="space-y-2 mb-6">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Progreso</span>
                <span className="text-sm text-blue-600 font-bold">
                  {progressPercentage}%
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2.5" />
            </div>

            <p className="text-sm text-gray-500">
              Estamos procesando tu solicitud utilizando IA para generar
              insights valiosos.
              <br />
              Este proceso puede tardar hasta 3 minutos dependiendo de la
              complejidad del contenido.
            </p>
          </motion.div>
        </div>
      </div>
    );
  };

  // Componente para mostrar un comentario individual
  const CommentCard = ({ comment }: { comment: Comment }) => (
    <div
      className={`p-3 rounded-lg border mb-3 ${getSentimentColor(comment.sentiment)}`}
    >
      <div className="flex items-center gap-2 mb-2">
        <UserCircle className="h-5 w-5" />
        <span className="font-medium">{comment.participant_name}</span>
        <Badge
          variant="outline"
          className={`ml-auto ${
            comment.sentiment === "positivo"
              ? "border-green-300 text-green-700"
              : comment.sentiment === "negativo"
                ? "border-red-300 text-red-700"
                : "border-blue-300 text-blue-700"
          }`}
        >
          {comment.sentiment}
        </Badge>
      </div>
      <p className="text-sm">{comment.comment}</p>
    </div>
  );

  // Componente para mostrar una discusión completa
  const DiscussionBlock = ({
    discussion,
    index,
  }: {
    discussion: Discussion;
    index: number;
  }) => (
    <Card className="mb-6 border-2 border-gray-200 shadow-sm">
      <CardHeader className="bg-gray-50 border-b pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <MessageSquarePlus className="h-5 w-5 text-blue-600" />
          <span>
            Pregunta {index + 1}: {discussion.question}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <ScrollArea className="h-[400px] pr-4">
          {discussion.conversation.map((comment, i) => (
            <CommentCard key={i} comment={comment} />
          ))}
        </ScrollArea>
      </CardContent>
    </Card>
  );

  // Renderizar la interfaz principal
  return (
    <div className="container mx-auto p-6 space-y-8">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "form" | "recent" | "favorites")} className="w-full">
        {/* Tabs Navigation - positioned at the top */}
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="form" className="flex items-center gap-2">
            <MessageSquarePlus className="h-4 w-4" />
            Nuevo Focus Group
          </TabsTrigger>
          <TabsTrigger value="recent" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            Recientes ({recentCount})
          </TabsTrigger>
          <TabsTrigger value="favorites" className="flex items-center gap-2">
            <Heart className="h-4 w-4" />
            Favoritos ({favoritesCount})
          </TabsTrigger>
        </TabsList>

        {/* Form Tab Content */}
        <TabsContent value="form" className="space-y-8">
      {/* Dialog para mostrar sugerencias de texto */}
      <Dialog
        open={showSuggestionDialog}
        onOpenChange={setShowSuggestionDialog}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              Mejoras de texto sugeridas por IA
            </DialogTitle>
            <DialogDescription>
              Selecciona una de las siguientes versiones mejoradas para tu
              texto.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {isTextAssistLoading ? (
              <div className="flex flex-col items-center justify-center py-8 space-y-4">
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 180, 360],
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Sparkles className="h-12 w-12 text-blue-500" />
                </motion.div>
                <p className="text-center text-sm text-muted-foreground">
                  Generando mejoras para tu texto usando IA...
                </p>
              </div>
            ) : (
              <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                {textSuggestions.map((suggestion, index) => (
                  <Card
                    key={index}
                    className="cursor-pointer hover:shadow-md transition-all border-2 hover:border-blue-200"
                    onClick={() => applySuggestion(suggestion)}
                  >
                    <CardContent className="p-4">
                      <p className="text-sm">{suggestion}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowSuggestionDialog(false)}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col gap-6">
        {/* Selector de modo de entrada */}
        {viewMode !== "live-simulation" && !useIntuitiveForm && (
          <Card className="w-full border border-gray-200 shadow-sm">
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                <div className="text-center">
                  <h3 className="font-medium text-gray-700 mb-2">¿Cómo prefieres crear tu idea?</h3>
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        resetIntuitiveForm();
                        setUseIntuitiveForm(true);
                      }}
                      className="flex-1 h-auto p-4 flex-col gap-2 hover:bg-blue-50 hover:border-blue-300"
                    >
                      <Sparkles className="h-6 w-6 text-blue-600" />
                      <span className="font-medium">Formulario Intuitivo</span>
                      <span className="text-xs text-gray-500">10 preguntas con botones</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="flex-1 h-auto p-4 flex-col gap-2 bg-gray-50 border-gray-300"
                      disabled
                    >
                      <FileText className="h-6 w-6 text-gray-600" />
                      <span className="font-medium">Escritura Libre</span>
                      <span className="text-xs text-gray-500">Escribe tu idea directamente</span>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}



        {/* Formulario intuitivo */}
        {useIntuitiveForm && <IntuitiveFormView />}

        {/* Panel de configuración */}
        {viewMode !== "live-simulation" && !useIntuitiveForm && (
          <Card className="w-full border-2 border-gray-200 shadow-md hover:shadow-lg transition-all">
            <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
              <CardTitle className="text-blue-600 flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                Simulador de Focus Group
              </CardTitle>
              <CardDescription>
                Simula un focus group virtual para evaluar contenido y obtener
                insights cualitativos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="content" className="text-base font-medium">
                    Contenido a evaluar
                  </Label>
                  {contentToAnalyze.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      onClick={() => requestTextAssistance("content")}
                    >
                      <Wand2 className="h-3.5 w-3.5 mr-1" />
                      <span className="text-xs">Mejorar texto</span>
                    </Button>
                  )}
                </div>
                {useAutocorrect ? (
                  <AutocorrectTextarea
                    placeholder="Escribe o pega aquí el texto, anuncio, titular, o descripción de producto que quieres evaluar con el focus group virtual..."
                    className="min-h-[120px] border-gray-300"
                    value={contentToAnalyze}
                    onChange={(e) => setContentToAnalyze(e.target.value)}
                  />
                ) : (
                  <Textarea
                    id="content"
                    placeholder="Escribe o pega aquí el texto, anuncio, titular, o descripción de producto que quieres evaluar con el focus group virtual..."
                    className="min-h-[120px]"
                    value={contentToAnalyze}
                    onChange={(e) => setContentToAnalyze(e.target.value)}
                  />
                )}
                <div className="flex items-center space-x-2 mt-1">
                  <Switch
                    id="autocorrect"
                    checked={useAutocorrect}
                    onCheckedChange={setUseAutocorrect}
                  />
                  <Label
                    htmlFor="autocorrect"
                    className="text-xs text-gray-500"
                  >
                    Corrección automática
                  </Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category" className="text-base font-medium">
                  Categoría del producto (opcional)
                </Label>
                <Input
                  id="category"
                  placeholder="Ej: Software, Moda, Tecnología, Alimentos, etc."
                  value={productCategory}
                  onChange={(e) => setProductCategory(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="context" className="text-base font-medium">
                    Contexto adicional (opcional)
                  </Label>
                  {additionalContext.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      onClick={() => requestTextAssistance("context")}
                    >
                      <Wand2 className="h-3.5 w-3.5 mr-1" />
                      <span className="text-xs">Mejorar texto</span>
                    </Button>
                  )}
                </div>
                <Textarea
                  id="context"
                  placeholder="Información sobre la empresa, marca, audiencia objetivo, etc."
                  className="min-h-[80px]"
                  value={additionalContext}
                  onChange={(e) => setAdditionalContext(e.target.value)}
                />
              </div>

              <div className="space-y-4 pt-2">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    Preguntas personalizadas (opcional)
                  </Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCustomQuestion}
                    className="text-xs h-8"
                  >
                    Agregar pregunta
                  </Button>
                </div>

                <div className="space-y-3">
                  {customQuestions.map((question, index) => (
                    <div key={index} className="flex gap-2">
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center">
                          <Label
                            htmlFor={`question-${index}`}
                            className="text-xs text-gray-500"
                          >
                            Pregunta {index + 1}
                          </Label>
                          {question.length > 0 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 px-2 ml-auto text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              onClick={() =>
                                requestTextAssistance("question", index)
                              }
                            >
                              <Wand2 className="h-3 w-3 mr-1" />
                              <span className="text-[10px]">Mejorar</span>
                            </Button>
                          )}
                        </div>
                        <Input
                          id={`question-${index}`}
                          placeholder="Ej: ¿Qué impresión te da este producto?"
                          value={question}
                          onChange={(e) =>
                            updateCustomQuestion(index, e.target.value)
                          }
                          className="text-sm"
                        />
                      </div>
                      {customQuestions.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCustomQuestion(index)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 px-2"
                        >
                          ✕
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={runFocusGroupSimulation}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                disabled={isLoading || contentToAnalyze.trim() === ""}
              >
                {isLoading ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "linear",
                      }}
                      className="mr-2"
                    >
                      <Clock className="h-4 w-4" />
                    </motion.div>
                    Simulando...
                  </>
                ) : (
                  <>
                    <Users className="h-4 w-4 mr-2" />
                    Iniciar simulación de Focus Group
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Panel de resultados */}
        {viewMode === "form" && result && (
          <Card className="w-full border-2 border-gray-200 shadow-md hover:shadow-lg transition-all overflow-hidden">
            <CardHeader className="border-b border-gray-100 bg-blue-50 rounded-t-lg">
              <CardTitle className="text-blue-600 flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                Resultados del Focus Group
              </CardTitle>
              <CardDescription>
                Insights cualitativos y patrones identificados por nuestro
                simulador de IA
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="p-4 flex justify-between items-center border-b">
                <p className="text-sm text-gray-600">
                  <Clock className="h-4 w-4 inline mr-1 text-gray-400" />
                  Generado: {new Date(result.timestamp).toLocaleString()}
                </p>
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => handleToggleFavorite()}
                    variant="outline"
                    size="sm"
                    className="h-8 text-xs"
                    disabled={isTogglingFavorite || !user || user.id === "anonymous"}
                  >
                    {isTogglingFavorite ? (
                      <div className="animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-current mr-1" />
                    ) : (
                      <Heart className={`h-3.5 w-3.5 mr-1 ${
                        currentSimulationId &&
                        [...recentFocusGroups, ...favoriteFocusGroups]
                          .find(fg => fg.id === currentSimulationId)?.is_favorite
                          ? "fill-current text-red-600"
                          : ""
                      }`} />
                    )}
                    {isTogglingFavorite ? "Guardando..." : "Favorito"}
                  </Button>
                  <Button
                    onClick={generatePDF}
                    variant="outline"
                    size="sm"
                    className="h-8 text-xs"
                  >
                    <Download className="h-3.5 w-3.5 mr-1" />
                    Exportar PDF
                  </Button>
                </div>
              </div>

              <div className="p-6" ref={resultsRef}>
                {/* Contenido que quieres incluir en el PDF */}
                {result.focus_group_simulation && (
                  <div className="space-y-8">
                    {/* Resumen y key insights */}
                    <Card className="border-2 border-blue-200 shadow-sm">
                      <CardHeader className="bg-blue-50 pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <Brain className="h-5 w-5 text-blue-600" />
                          Principales insights
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-4">
                        <ul className="space-y-2">
                          {result.focus_group_simulation.summary.key_insights.map(
                            (insight, i) => (
                              <li key={i} className="flex items-start gap-2">
                                <Lightbulb className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                                <p className="text-sm">{insight}</p>
                              </li>
                            ),
                          )}
                        </ul>
                      </CardContent>
                    </Card>

                    {/* Análisis de sentimiento */}
                    <Card className="border-2 border-gray-200 shadow-sm">
                      <CardHeader className="bg-gray-50 pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <MessageCircle className="h-5 w-5 text-blue-600" />
                          Análisis de sentimiento
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-4 space-y-4">
                        <p className="text-sm font-medium">
                          Sentimiento general:{" "}
                          {
                            result.focus_group_simulation.summary
                              .sentiment_analysis.overall
                          }
                        </p>

                        <div className="space-y-3">
                          <div>
                            <p className="text-sm font-medium text-green-700 mb-1 flex items-center">
                              <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                              Aspectos positivos:
                            </p>
                            <ul className="pl-5 space-y-1">
                              {result.focus_group_simulation.summary.sentiment_analysis.breakdown.positive_aspects.map(
                                (aspect, i) => (
                                  <li key={i} className="text-sm list-disc">
                                    {aspect}
                                  </li>
                                ),
                              )}
                            </ul>
                          </div>

                          <div>
                            <p className="text-sm font-medium text-red-700 mb-1 flex items-center">
                              <span className="inline-block w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                              Aspectos negativos:
                            </p>
                            <ul className="pl-5 space-y-1">
                              {result.focus_group_simulation.summary.sentiment_analysis.breakdown.negative_aspects.map(
                                (aspect, i) => (
                                  <li key={i} className="text-sm list-disc">
                                    {aspect}
                                  </li>
                                ),
                              )}
                            </ul>
                          </div>

                          <div>
                            <p className="text-sm font-medium text-blue-700 mb-1 flex items-center">
                              <span className="inline-block w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                              Observaciones neutrales:
                            </p>
                            <ul className="pl-5 space-y-1">
                              {result.focus_group_simulation.summary.sentiment_analysis.breakdown.neutral_observations.map(
                                (observation, i) => (
                                  <li key={i} className="text-sm list-disc">
                                    {observation}
                                  </li>
                                ),
                              )}
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Patrones demográficos */}
                    {result.focus_group_simulation.summary.demographic_patterns
                      .length > 0 && (
                      <Card className="border-2 border-gray-200 shadow-sm">
                        <CardHeader className="bg-gray-50 pb-3">
                          <CardTitle className="text-base flex items-center gap-2">
                            <Users className="h-5 w-5 text-blue-600" />
                            Patrones demográficos
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          <div className="space-y-4">
                            {result.focus_group_simulation.summary.demographic_patterns.map(
                              (pattern, i) => (
                                <div key={i} className="space-y-2">
                                  <p className="text-sm font-medium">
                                    {pattern.pattern}
                                  </p>
                                  <div className="flex flex-wrap gap-2">
                                    {pattern.affected_demographics.map(
                                      (demo, j) => (
                                        <Badge
                                          key={j}
                                          variant="outline"
                                          className="bg-gray-50"
                                        >
                                          {demo}
                                        </Badge>
                                      ),
                                    )}
                                  </div>
                                </div>
                              ),
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Recomendaciones */}
                    <Card className="border-2 border-blue-200 shadow-sm">
                      <CardHeader className="bg-blue-50 pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <FileText className="h-5 w-5 text-blue-600" />
                          Recomendaciones
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-4">
                        <ul className="space-y-2">
                          {result.focus_group_simulation.summary.recommendations.map(
                            (recommendation, i) => (
                              <li key={i} className="flex items-start gap-2">
                                <ChevronRight className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                                <p className="text-sm">{recommendation}</p>
                              </li>
                            ),
                          )}
                        </ul>
                      </CardContent>
                    </Card>

                    {/* Conversaciones */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                        <MessageSquare className="h-5 w-5 text-blue-600" />
                        Transcripción de las conversaciones
                      </h3>

                      {result.focus_group_simulation.discussions.map(
                        (discussion, i) => (
                          <DiscussionBlock
                            key={i}
                            discussion={discussion}
                            index={i}
                          />
                        ),
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Vista de simulación en vivo */}
        {viewMode === "live-simulation" && (
          <div className="w-full">
            <LiveSimulationView />
          </div>
        )}
      </div>

      {/* Botón para volver desde simulación en vivo */}
      {viewMode === "live-simulation" && (
        <div className="flex justify-center mt-4">
          <Button
            variant="outline"
            onClick={() => {
              setViewMode("form");
              setIsLoading(false);
            }}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Cancelar y volver
          </Button>
        </div>
      )}

      {/* Mostrar errores */}
      {error && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
        </TabsContent>

        {/* Recent Focus Groups Tab */}
        <TabsContent value="recent" className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Focus Groups Recientes</h2>
            <p className="text-gray-600">Últimos 5 focus groups generados</p>
          </div>

          {isLoadingFocusGroups ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : recentFocusGroups.length === 0 ? (
            <Card className="p-8 text-center">
              <div className="flex flex-col items-center gap-4">
                <History className="h-12 w-12 text-gray-400" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900">No hay focus groups recientes</h3>
                  <p className="text-gray-500">Crea tu primer focus group para verlo aquí</p>
                </div>
                <Button onClick={() => setActiveTab("form")} className="mt-2">
                  <MessageSquarePlus className="h-4 w-4 mr-2" />
                  Crear Focus Group
                </Button>
              </div>
            </Card>
          ) : (
            <div className="grid gap-4">
              {recentFocusGroups.map((focusGroup) => (
                <FocusGroupCard
                  key={focusGroup.id}
                  focusGroup={focusGroup}
                  onLoad={() => handleLoadFocusGroup(focusGroup)}
                  onToggleFavorite={() => handleToggleFavorite(focusGroup.id)}
                  onRename={(newName) => handleRenameFocusGroup(focusGroup.id, newName)}
                  onDelete={() => handleDeleteFocusGroup(focusGroup.id)}
                  onRegenerate={() => handleRegenerateFocusGroup(focusGroup)}
                  isRenaming={isRenaming === focusGroup.id}
                  onStartRename={() => {
                    setIsRenaming(focusGroup.id);
                    setRenameValue(focusGroup.custom_name || `Focus Group ${new Date(focusGroup.created_at).toLocaleDateString()}`);
                  }}
                  onCancelRename={() => {
                    setIsRenaming(null);
                    setRenameValue("");
                  }}
                  renameValue={renameValue}
                  onRenameValueChange={setRenameValue}
                />
              ))}
            </div>
          )}
        </TabsContent>

        {/* Favorite Focus Groups Tab */}
        <TabsContent value="favorites" className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Focus Groups Favoritos</h2>
            <p className="text-gray-600">Tus focus groups marcados como favoritos</p>
          </div>

          {isLoadingFocusGroups ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : favoriteFocusGroups.length === 0 ? (
            <Card className="p-8 text-center">
              <div className="flex flex-col items-center gap-4">
                <Heart className="h-12 w-12 text-gray-400" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900">No hay focus groups favoritos</h3>
                  <p className="text-gray-500">Marca focus groups como favoritos para verlos aquí</p>
                </div>
                <Button onClick={() => setActiveTab("recent")} variant="outline" className="mt-2">
                  <History className="h-4 w-4 mr-2" />
                  Ver Recientes
                </Button>
              </div>
            </Card>
          ) : (
            <div className="grid gap-4">
              {favoriteFocusGroups.map((focusGroup) => (
                <FocusGroupCard
                  key={focusGroup.id}
                  focusGroup={focusGroup}
                  onLoad={() => handleLoadFocusGroup(focusGroup)}
                  onToggleFavorite={() => handleToggleFavorite(focusGroup.id)}
                  onRename={(newName) => handleRenameFocusGroup(focusGroup.id, newName)}
                  onDelete={() => handleDeleteFocusGroup(focusGroup.id)}
                  onRegenerate={() => handleRegenerateFocusGroup(focusGroup)}
                  isRenaming={isRenaming === focusGroup.id}
                  onStartRename={() => {
                    setIsRenaming(focusGroup.id);
                    setRenameValue(focusGroup.custom_name || `Focus Group ${new Date(focusGroup.created_at).toLocaleDateString()}`);
                  }}
                  onCancelRename={() => {
                    setIsRenaming(null);
                    setRenameValue("");
                  }}
                  renameValue={renameValue}
                  onRenameValueChange={setRenameValue}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
