/**
 * AdMiniEditor - Mini editor de imágenes para anuncios generados
 * Permite borrar partes de la imagen y enviar al Editor Profesional para edición avanzada
 */

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Eraser, Undo, Download, Edit3, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/hooks/use-toast';
import { GeneratedAd } from '@/types/ad-creator-types';

interface AdMiniEditorProps {
  ad: GeneratedAd;
  isOpen: boolean;
  onClose: () => void;
  onSave: (editedAd: GeneratedAd) => void;
}

export function AdMiniEditor({ ad, isOpen, onClose, onSave }: AdMiniEditorProps) {
  const { toast } = useToast();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(20);
  const [hasMask, setHasMask] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [originalImageData, setOriginalImageData] = useState<ImageData | null>(null);
  // Estados del Editor Profesional removidos - ahora se abre en nueva pestaña

  // Parámetros adicionales para Stability AI (como en el editor exitoso)
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("webp");
  const [growMask, setGrowMask] = useState(5);
  const [originalImageFile, setOriginalImageFile] = useState<File | null>(null);

  // Estado persistente del editor por imagen
  const editorStateKey = `editor_state_${ad.id}`;
  const [savedCanvasState, setSavedCanvasState] = useState<string | null>(null);

  // Funciones para persistencia del estado
  const saveCanvasState = () => {
    const canvas = canvasRef.current;
    if (canvas && imageLoaded) {
      try {
        const canvasData = canvas.toDataURL('image/png');
        const state = {
          canvasData,
          hasMask,
          brushSize,
          timestamp: Date.now()
        };

        try {
          localStorage.setItem(editorStateKey, JSON.stringify(state));
          setSavedCanvasState(canvasData);
          console.log('💾 Canvas state saved for ad:', ad.id);
        } catch (quotaError) {
          if (quotaError instanceof Error && quotaError.name === 'QuotaExceededError') {
            console.warn('⚠️ LocalStorage lleno, limpiando estados antiguos...');
            // Limpiar estados de canvas antiguos
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && (key.startsWith('canvas_state_') || key.startsWith('editor_state_'))) {
                keysToRemove.push(key);
              }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            console.log('🧹 Limpiados', keysToRemove.length, 'estados antiguos');

            // Intentar guardar de nuevo
            localStorage.setItem(editorStateKey, JSON.stringify(state));
            setSavedCanvasState(canvasData);
            console.log('💾 Canvas state saved after cleanup for ad:', ad.id);
          } else {
            throw quotaError;
          }
        }
      } catch (error) {
        console.error('❌ Error saving canvas state:', error);
      }
    }
  };

  const loadCanvasState = () => {
    try {
      const savedState = localStorage.getItem(editorStateKey);
      if (savedState) {
        const state = JSON.parse(savedState);
        console.log('📂 Loading saved canvas state for ad:', ad.id);
        return state;
      }
    } catch (error) {
      console.error('❌ Error loading canvas state:', error);
    }
    return null;
  };

  const clearCanvasState = () => {
    localStorage.removeItem(editorStateKey);
    setSavedCanvasState(null);
    console.log('🗑️ Canvas state cleared for ad:', ad.id);
  };

  // Convertir imagen URL a File (necesario para Stability AI)
  const convertImageUrlToFile = async (imageUrl: string): Promise<File> => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      return new File([blob], `ad-image-${ad.id}.png`, { type: 'image/png' });
    } catch (error) {
      console.error('Error converting image URL to File:', error);
      throw new Error('No se pudo convertir la imagen');
    }
  };

  // Debug: Log when component receives new props
  useEffect(() => {
    console.log('🎨 AdMiniEditor props changed:', {
      isOpen,
      adId: ad.id,
      imageUrl: ad.image_url?.substring(0, 100) + '...',
      isBase64: ad.image_url?.startsWith('data:'),
      urlLength: ad.image_url?.length
    });
  }, [isOpen, ad.image_url, ad.id]);

  // Debug: Log when component receives new props
  useEffect(() => {
    console.log('🎨 AdMiniEditor props changed:', {
      isOpen,
      adId: ad.id,
      imageUrl: ad.image_url?.substring(0, 100) + '...',
      isBase64: ad.image_url?.startsWith('data:'),
      urlLength: ad.image_url?.length
    });
  }, [isOpen, ad.image_url, ad.id]);

  // Cargar imagen en el canvas cuando se abre el modal
  useEffect(() => {
    console.log('🎨 useEffect for loading image triggered:', { isOpen, hasImageUrl: !!ad.image_url });
    if (isOpen && ad.image_url) {
      // Reset state before loading
      setImageLoaded(false);
      setHasMask(false);
      setOriginalImageData(null);

      // Small delay to ensure canvas is rendered
      setTimeout(() => {
        loadImageToCanvas();
      }, 100);
    }
  }, [isOpen, ad.image_url]);

  // Guardar estado cuando el modal se cierra
  useEffect(() => {
    if (!isOpen && imageLoaded) {
      saveCanvasState();
    }
  }, [isOpen, imageLoaded]);

  const loadImageToCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('❌ Canvas ref not available');
      return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('❌ Canvas context not available');
      return;
    }

    console.log('🎨 Loading image to canvas:', {
      imageUrl: ad.image_url?.substring(0, 100) + '...',
      isBase64: ad.image_url?.startsWith('data:'),
      urlLength: ad.image_url?.length
    });

    const img = new Image();
    // Don't set crossOrigin for base64 data URLs
    if (!ad.image_url.startsWith('data:')) {
      img.crossOrigin = 'anonymous';
    }
    img.src = ad.image_url;

    img.onload = () => {
      console.log('✅ Image loaded successfully:', {
        width: img.width,
        height: img.height,
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight
      });

      // Hacer el canvas cuadrado respetando las dimensiones de la imagen
      const maxSize = 500; // Tamaño máximo del canvas
      const aspectRatio = img.width / img.height;

      if (aspectRatio > 1) {
        // Imagen más ancha que alta
        canvas.width = maxSize;
        canvas.height = maxSize / aspectRatio;
      } else {
        // Imagen más alta que ancha o cuadrada
        canvas.height = maxSize;
        canvas.width = maxSize * aspectRatio;
      }

      console.log('🎨 Canvas dimensions set:', {
        canvasWidth: canvas.width,
        canvasHeight: canvas.height,
        aspectRatio
      });

      // Dibujar la imagen respetando sus proporciones
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // Guardar los datos originales de la imagen
      setOriginalImageData(ctx.getImageData(0, 0, canvas.width, canvas.height));
      setImageLoaded(true);
      setHasMask(false);

      console.log('✅ Image successfully loaded to canvas');

      // Crear archivo de imagen para Stability AI
      convertImageUrlToFile(ad.image_url)
        .then(file => {
          setOriginalImageFile(file);
          console.log('✅ Image file created for Stability AI');
        })
        .catch(error => {
          console.error('❌ Error creating image file:', error);
        });

      // Intentar restaurar estado guardado después de cargar la imagen original
      setTimeout(() => {
        const savedState = loadCanvasState();
        if (savedState && savedState.canvasData) {
          console.log('🔄 Restoring saved canvas state...');
          const savedImg = new Image();
          savedImg.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(savedImg, 0, 0, canvas.width, canvas.height);
            setHasMask(savedState.hasMask || false);
            setBrushSize(savedState.brushSize || 20);
            console.log('✅ Canvas state restored successfully');
          };
          savedImg.src = savedState.canvasData;
        }
      }, 100);
    };

    img.onerror = (error) => {
      console.error('❌ Image load error:', error);
      console.error('❌ Failed image URL:', ad.image_url?.substring(0, 200));

      toast({
        title: "Error",
        description: "No se pudo cargar la imagen",
        variant: "destructive",
      });
    };

    // Add timeout to detect if image is taking too long
    setTimeout(() => {
      if (!imageLoaded) {
        console.warn('⚠️ Image taking longer than 10 seconds to load');
      }
    }, 10000);
  };

  // Funciones de dibujo para la máscara
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    draw(e);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const x = ((e.clientX - rect.left) * canvas.width) / rect.width;
    const y = ((e.clientY - rect.top) * canvas.height) / rect.height;

    // Configurar el pincel para la máscara (rojo semi-transparente)
    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
    ctx.beginPath();
    ctx.arc(x, y, brushSize / 2, 0, 2 * Math.PI);
    ctx.fill();

    setHasMask(true);
  };

  const endDrawing = () => {
    setIsDrawing(false);
    // Guardar estado automáticamente después de dibujar
    setTimeout(() => {
      saveCanvasState();
    }, 100);
  };

  // Limpiar la máscara
  const clearMask = () => {
    if (!originalImageData) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Restaurar la imagen original
    ctx.putImageData(originalImageData, 0, 0);
    setHasMask(false);
  };

  // Reiniciar editor (limpiar estado guardado y empezar de nuevo)
  const resetEditor = () => {
    clearCanvasState();
    clearMask();
    toast({
      title: "Editor reiniciado",
      description: "Se ha limpiado el estado guardado",
    });
  };

  // Procesar la imagen con la máscara (borrar objetos)
  const handleEraseObjects = async () => {
    console.log('🎯 INICIANDO BORRADO DE OBJETOS:', {
      hasMask,
      hasOriginalImageFile: !!originalImageFile,
      originalImageFileSize: originalImageFile?.size,
      imageLoaded
    });

    if (!hasMask || !originalImageFile) {
      console.error('❌ Faltan requisitos para borrar:', { hasMask, hasOriginalImageFile: !!originalImageFile });
      toast({
        title: "Error",
        description: "Debes dibujar sobre las áreas que deseas borrar y esperar a que la imagen se cargue",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const canvas = canvasRef.current;
      if (!canvas) throw new Error("Canvas no disponible");

      // Crear canvas para la imagen original
      const originalCanvas = document.createElement('canvas');
      originalCanvas.width = canvas.width;
      originalCanvas.height = canvas.height;
      const originalCtx = originalCanvas.getContext('2d');
      if (!originalCtx) throw new Error("No se pudo crear el contexto del canvas original");

      // Restaurar la imagen original
      if (!originalImageData) throw new Error("No hay datos de imagen original");
      originalCtx.putImageData(originalImageData, 0, 0);

      // Crear canvas para la máscara
      const maskCanvas = document.createElement('canvas');
      maskCanvas.width = canvas.width;
      maskCanvas.height = canvas.height;
      const maskCtx = maskCanvas.getContext('2d');
      if (!maskCtx) throw new Error("No se pudo crear el contexto del canvas de máscara");

      // Crear máscara en blanco y negro
      maskCtx.fillStyle = 'black';
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);

      // Obtener los datos del canvas actual (con las marcas rojas)
      const currentImageData = canvas.getContext('2d')?.getImageData(0, 0, canvas.width, canvas.height);
      if (!currentImageData) throw new Error("No se pudieron obtener los datos de la imagen actual");

      // Convertir las áreas rojas a blanco en la máscara
      const maskImageData = maskCtx.createImageData(maskCanvas.width, maskCanvas.height);
      let redPixelsFound = 0;
      let totalPixels = 0;

      for (let i = 0; i < currentImageData.data.length; i += 4) {
        const r = currentImageData.data[i];
        const g = currentImageData.data[i + 1];
        const b = currentImageData.data[i + 2];
        totalPixels++;

        // Si es rojo (área marcada para borrar), hacer blanco en la máscara
        if (r > 200 && g < 100 && b < 100) {
          maskImageData.data[i] = 255;     // R
          maskImageData.data[i + 1] = 255; // G
          maskImageData.data[i + 2] = 255; // B
          maskImageData.data[i + 3] = 255; // A
          redPixelsFound++;
        } else {
          maskImageData.data[i] = 0;       // R
          maskImageData.data[i + 1] = 0;   // G
          maskImageData.data[i + 2] = 0;   // B
          maskImageData.data[i + 3] = 255; // A
        }
      }

      console.log('🎯 MÁSCARA CREADA:', {
        totalPixels,
        redPixelsFound,
        maskPercentage: ((redPixelsFound / totalPixels) * 100).toFixed(2) + '%',
        canvasSize: `${canvas.width}x${canvas.height}`
      });
      maskCtx.putImageData(maskImageData, 0, 0);

      // Convertir ambos canvas a blobs
      const [originalBlob, maskBlob] = await Promise.all([
        new Promise<Blob>((resolve) => {
          originalCanvas.toBlob((blob) => {
            if (blob) resolve(blob);
          }, 'image/png');
        }),
        new Promise<Blob>((resolve) => {
          maskCanvas.toBlob((blob) => {
            if (blob) resolve(blob);
          }, 'image/png');
        })
      ]);

      if (!originalBlob || !maskBlob) throw new Error("Error al crear las imágenes");

      // Crear FormData para enviar a la API (mejorado con parámetros adicionales)
      const formData = new FormData();
      formData.append('image', originalImageFile); // Usar archivo original en lugar de blob del canvas
      formData.append('mask', maskBlob, 'mask.png');
      formData.append('output_format', outputFormat);
      formData.append('grow_mask', growMask.toString());

      console.log('📡 Enviando a Stability AI:', {
        imageSize: originalImageFile.size,
        maskSize: maskBlob.size,
        outputFormat,
        growMask
      });

      // Llamar a la API de borrado de objetos usando Stability AI
      console.log('📡 Enviando request a Stability AI...');
      const response = await fetch('/api/v1/ai-editor/erase', {
        method: 'POST',
        body: formData,
      });

      console.log('📡 Response recibida:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      if (!response.ok) {
        console.error('❌ Response no OK, obteniendo error...');
        const errorData = await response.json();
        console.error('❌ Error data:', errorData);
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      console.log('📡 Parseando JSON response...');
      const result = await response.json();
      console.log('✅ JSON parseado exitosamente:', {
        success: result.success,
        hasImage: !!result.image,
        hasImageUrl: !!result.image_url,
        error: result.error
      });

      if (result.success && (result.image || result.image_url)) {
        console.log('✅ Procesando respuesta exitosa de Stability AI...');

        // Manejar ambos formatos de respuesta (mejorado)
        const imageDataUrl = result.image
          ? `data:image/${outputFormat};base64,${result.image}`
          : result.image_url;

        console.log('🎨 Image data URL creada:', {
          length: imageDataUrl.length,
          isBase64: imageDataUrl.startsWith('data:'),
          format: outputFormat
        });

        // Crear un nuevo ad con la imagen editada
        const editedAd: GeneratedAd = {
          ...ad,
          id: `${ad.id}_edited_${Date.now()}`,
          image_url: imageDataUrl,
          timestamp: Date.now(),
          metadata: {
            ...ad.metadata,
            edited: true,
            originalId: ad.id,
            stabilityAI: true,
            editedWith: 'AdMiniEditor'
          }
        };

        console.log('💾 Guardando ad editado:', {
          id: editedAd.id,
          originalId: ad.id,
          hasImageUrl: !!editedAd.image_url,
          imageUrlLength: editedAd.image_url?.length
        });

        console.log('📞 Llamando onSave con ad editado...');
        onSave(editedAd);
        console.log('✅ onSave completado');

        console.log('🧹 Limpiando estado del canvas...');
        clearCanvasState();
        console.log('✅ Estado del canvas limpiado');

        console.log('🎉 Mostrando toast de éxito...');
        toast({
          title: "🎉 ¡Objetos borrados!",
          description: "La imagen ha sido procesada exitosamente con Stability AI",
        });
        console.log('✅ Toast mostrado');

        console.log('✅ Proceso completado exitosamente - finalizando función');
      } else {
        console.error('❌ Respuesta no exitosa:', result);
        throw new Error(result.error || 'Error desconocido en la respuesta de Stability AI');
      }
    } catch (error) {
      console.error('Error processing image:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Enviar a Editor Profesional para edición avanzada
  const sendToPolotno = () => {
    console.log('🎨 Enviando imagen al Editor Profesional (Polotno)');

    // Limpiar localStorage de imágenes viejas para hacer espacio
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('editor-professional-image-') || key.startsWith('editor_state_'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log('🧹 Limpiado localStorage:', keysToRemove.length, 'items removidos');

    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('❌ Canvas not available for Editor Profesional');
      toast({
        title: "Error",
        description: "Canvas no disponible",
        variant: "destructive",
      });
      return;
    }

    if (!imageLoaded) {
      console.error('❌ Image not loaded yet for Editor Profesional');
      toast({
        title: "Error",
        description: "La imagen aún no se ha cargado",
        variant: "destructive",
      });
      return;
    }

    try {
      // Obtener la imagen actual del canvas en base64 (con o sin ediciones)
      const imageDataUrl = canvas.toDataURL('image/png', 0.9);

      console.log('🎨 Abriendo Editor Profesional con imagen:', {
        imageSize: imageDataUrl.length,
        adId: ad.id,
        hasEdits: hasMask,
        canvasSize: `${canvas.width}x${canvas.height}`,
        imagePreview: imageDataUrl.substring(0, 100) + '...'
      });

      // MÉTODO QUE SIEMPRE FUNCIONA: Guardar en backend temporalmente
      const imageId = `temp-${Date.now()}`;

      console.log('🎨 Guardando imagen en backend temporalmente...');

      // Guardar imagen en backend
      fetch('/api/v1/temp-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: imageId,
          imageData: imageDataUrl
        })
      })
      .then(response => response.json())
      .then(result => {
        if (result.success) {
          console.log('✅ Imagen guardada en backend, abriendo Polotno...');
          // Abrir visual-editor con el ID
          window.open(`/visual-editor?tempImageId=${imageId}&platform=custom`, '_blank', 'noopener,noreferrer');
        } else {
          throw new Error('Error guardando imagen');
        }
      })
      .catch(error => {
        console.error('❌ Error guardando imagen:', error);
        // Fallback: usar base64 directamente (puede causar 431 pero es el último recurso)
        window.open(`/visual-editor?imageUrl=${encodeURIComponent(imageDataUrl)}&platform=custom`, '_blank', 'noopener,noreferrer');
      });

      console.log('✅ Editor Profesional opened in new tab successfully');

      toast({
        title: "🎨 Editor Profesional",
        description: "Se ha abierto el Editor Profesional en una nueva pestaña",
      });

    } catch (error) {
      console.error('❌ Error opening Editor Profesional:', error);

      toast({
        title: "Error",
        description: "No se pudo abrir el Editor Profesional",
        variant: "destructive",
      });
    }
  };

  // Descargar imagen actual
  const downloadCurrentImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = `${ad.id}_edited.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();

    toast({
      title: "Descarga iniciada",
      description: "La imagen editada se está descargando",
    });
  };

  return (
    <>
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Editor de Imagen - Anuncio {ad.id}
          </DialogTitle>
          <DialogDescription>
            Dibuja sobre los objetos que quieres borrar y usa las herramientas de edición avanzada.
            {savedCanvasState && (
              <div className="mt-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
                💾 Se ha restaurado tu trabajo anterior en esta imagen
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Canvas para edición */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Dibuja sobre los objetos a borrar:</h4>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm">Tamaño del pincel:</span>
                  <Slider
                    value={[brushSize]}
                    onValueChange={(value) => setBrushSize(value[0])}
                    max={50}
                    min={5}
                    step={5}
                    className="w-24"
                  />
                  <span className="text-sm w-8">{brushSize}</span>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm">Expandir máscara:</span>
                  <Slider
                    value={[growMask]}
                    onValueChange={(value) => setGrowMask(value[0])}
                    max={20}
                    min={0}
                    step={1}
                    className="w-20"
                  />
                  <span className="text-sm w-8">{growMask}</span>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm">Formato:</span>
                  <select
                    value={outputFormat}
                    onChange={(e) => setOutputFormat(e.target.value as "png" | "webp" | "jpeg")}
                    className="text-sm border rounded px-2 py-1 bg-white"
                  >
                    <option value="webp">WebP</option>
                    <option value="png">PNG</option>
                    <option value="jpeg">JPEG</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="border rounded-lg overflow-hidden bg-gray-50 flex justify-center items-center min-h-[400px]">
              <canvas
                ref={canvasRef}
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={endDrawing}
                onMouseLeave={endDrawing}
                className="cursor-crosshair border border-gray-300 rounded shadow-sm"
                style={{
                  maxWidth: "500px",
                  maxHeight: "500px",
                  display: imageLoaded ? 'block' : 'none',
                  backgroundColor: 'white'
                }}
              />

              {/* Debug: Show image as regular img element */}
              {!imageLoaded && ad.image_url && (
                <div className="absolute top-2 right-2 w-32 h-32 border border-red-500">
                  <img
                    src={ad.image_url}
                    alt="Debug preview"
                    className="w-full h-full object-cover"
                    onLoad={() => console.log('✅ Debug img loaded successfully')}
                    onError={(e) => console.error('❌ Debug img failed to load:', e)}
                  />
                </div>
              )}
              {!imageLoaded && (
                <div className="h-64 flex items-center justify-center">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Cargando imagen...</p>
                    <div className="mt-4 text-xs text-gray-500 max-w-md">
                      <p>Debug Info:</p>
                      <p>Image URL: {ad.image_url?.substring(0, 50)}...</p>
                      <p>Is Base64: {ad.image_url?.startsWith('data:') ? 'Yes' : 'No'}</p>
                      <p>URL Length: {ad.image_url?.length}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Controles */}
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={clearMask}
              variant="outline"
              disabled={!hasMask}
            >
              <Undo className="w-4 h-4 mr-2" />
              Limpiar Máscara
            </Button>

            <Button
              onClick={resetEditor}
              variant="outline"
              className="border-orange-500 text-orange-600 hover:bg-orange-50"
            >
              <X className="w-4 h-4 mr-2" />
              Reiniciar Editor
            </Button>

            <Button
              onClick={handleEraseObjects}
              disabled={!hasMask || isProcessing}
              className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]"
            >
              {isProcessing ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Eraser className="w-4 h-4 mr-2" />
              )}
              {isProcessing ? 'Procesando...' : 'Borrar Objetos'}
            </Button>

            <Button
              onClick={sendToPolotno}
              variant="outline"
              className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef]/5"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Editor Profesional
            </Button>

            <Button
              onClick={downloadCurrentImage}
              variant="outline"
            >
              <Download className="w-4 h-4 mr-2" />
              Descargar
            </Button>
          </div>

          {/* Instrucciones */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-medium text-blue-900 mb-2">Instrucciones:</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Dibuja con el mouse sobre las áreas que deseas borrar</li>
              <li>• Ajusta el tamaño del pincel según necesites</li>
              <li>• Haz clic en "Borrar Objetos" para procesar la imagen</li>
              <li>• Usa "Editor Profesional" para edición avanzada con texto y elementos</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Editor Profesional ahora se abre en nueva pestaña - modal removido */}
    </>
  );
}
