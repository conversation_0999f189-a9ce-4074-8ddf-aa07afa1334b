/**
 * AdResultsGrid - Componente separado para mostrar los resultados de anuncios generados
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, Heart, Share2, Eye, Edit3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { GeneratedAd } from '@/types/ad-creator-types';
import { AdMiniEditor } from './AdMiniEditor';

interface AdResultsGridProps {
  generatedAds: GeneratedAd[];
  isGenerating: boolean;
  onSave: (ad: GeneratedAd) => void;
  onDownload?: (ad: GeneratedAd) => void;
  onShare?: (ad: GeneratedAd) => void;
  onPreview?: (ad: GeneratedAd) => void;
  onEdit?: (ad: GeneratedAd) => void;
}

export function AdResultsGrid({
  generatedAds,
  isGenerating,
  onSave,
  onDownload,
  onShare,
  onPreview,
  onEdit
}: AdResultsGridProps) {
  const [editingAdId, setEditingAdId] = useState<string | null>(null);
  console.log(`🖼️ AdResultsGrid: Rendering ${generatedAds.length} ads, isGenerating: ${isGenerating}`);

  if (isGenerating) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="text-center py-12">
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-white/70 backdrop-blur-xl rounded-full border border-white/20">
            <div className="w-5 h-5 border-2 border-[#3018ef] border-t-transparent rounded-full animate-spin"></div>
            <span className="text-slate-700 font-medium">Generando anuncios...</span>
          </div>
        </div>
      </div>
    );
  }

  if (generatedAds.length === 0) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="text-center py-12">
          <div className="text-slate-500">
            No hay anuncios generados aún
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6 text-center">
        <p className="text-sm text-gray-600">
          Mostrando {generatedAds.length} anuncios generados
        </p>
      </div>
      
      <AnimatePresence>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {generatedAds.map((ad, index) => {
            console.log(`🖼️ Rendering ad ${index + 1}:`, ad);
            return (
              <motion.div
                key={ad.id}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden hover:shadow-2xl hover:shadow-[#3018ef]/10 transition-all duration-500"
              >
                {/* Image container */}
                <div className="relative overflow-hidden">
                  <img
                    src={ad.image_url}
                    alt="Generated ad"
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-500"
                    onError={(e) => {
                      console.error(`❌ Error loading image for ad ${ad.id}:`, ad.image_url);
                      e.currentTarget.style.display = 'none';
                    }}
                    onLoad={() => {
                      console.log(`✅ Image loaded successfully for ad ${ad.id}`);
                    }}
                  />
                  
                  {/* Overlay with actions */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex gap-2">
                      {onPreview && (
                        <Button
                          size="sm"
                          variant="secondary"
                          className="bg-white/90 hover:bg-white text-slate-700"
                          onClick={() => onPreview(ad)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="secondary"
                        className="bg-white/90 hover:bg-white text-slate-700"
                        onClick={() => setEditingAdId(ad.id)}
                        title="Editar imagen"
                      >
                        <Edit3 className="w-4 h-4" />
                      </Button>
                      {onShare && (
                        <Button
                          size="sm"
                          variant="secondary"
                          className="bg-white/90 hover:bg-white text-slate-700"
                          onClick={() => onShare(ad)}
                        >
                          <Share2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6 space-y-4">
                  {/* Ad metadata */}
                  <div className="space-y-2">
                    {ad.metadata?.headline && (
                      <h3 className="font-bold text-slate-900 text-lg leading-tight">
                        {ad.metadata.headline}
                      </h3>
                    )}
                    {ad.metadata?.punchline && (
                      <p className="text-slate-600 text-sm">
                        {ad.metadata.punchline}
                      </p>
                    )}
                    {ad.metadata?.cta && (
                      <div className="inline-block px-3 py-1 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white text-xs font-medium rounded-full">
                        {ad.metadata.cta}
                      </div>
                    )}
                  </div>

                  {/* Prompt preview */}
                  <div className="text-xs text-slate-500 bg-slate-50 rounded-lg p-3">
                    <p className="line-clamp-2">
                      {ad.prompt.length > 100 ? `${ad.prompt.substring(0, 100)}...` : ad.prompt}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      onClick={() => onSave(ad)}
                      size="sm"
                      variant="outline"
                      className="flex-1 border-[#3018ef]/20 hover:bg-[#3018ef]/5"
                    >
                      <Heart className="w-4 h-4 mr-2" />
                      Guardar
                    </Button>
                    {onDownload && (
                      <Button
                        onClick={() => onDownload(ad)}
                        size="sm"
                        className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#3018ef]/90 hover:to-[#dd3a5a]/90"
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    )}
                  </div>

                  {/* Generation info */}
                  <div className="text-xs text-slate-400 pt-2 border-t border-slate-100">
                    <div className="flex justify-between">
                      <span>Variación {ad.metadata?.variation || index + 1}</span>
                      <span>
                        {new Date(ad.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </AnimatePresence>

      {/* Mini Editor Modal */}
      {editingAdId && (
        <AdMiniEditor
          ad={generatedAds.find(ad => ad.id === editingAdId)!}
          isOpen={!!editingAdId}
          onClose={() => setEditingAdId(null)}
          onSave={(editedAd) => {
            // Update the ad in the list if needed
            if (onEdit) {
              onEdit(editedAd);
            }
            setEditingAdId(null);
          }}
        />
      )}
    </div>
  );
}

export default AdResultsGrid;
