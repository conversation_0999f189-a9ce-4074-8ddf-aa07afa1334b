/**
 * Results Step Component
 * Third step of the simplified ad creator - showing generated results
 */

import { motion } from "framer-motion";
import { GeneratedAd } from "@/types/ad-creator-types";
import { GenerationProgress } from "../../shared/GenerationProgress";
import { AdResultsGrid } from "../../shared/AdResultsGrid";
import { AdResultsActions } from "../../shared/AdResultsActions";
import { GenerationProgress as GenerationProgressType } from "../types/simplified-ad-types";

interface ResultsStepProps {
  generatedAds: GeneratedAd[];
  isGenerating: boolean;
  generationProgress: GenerationProgressType;
  estimatedTime: number;
  onSave: (ad: GeneratedAd) => void;
  onDownload: (imageUrl: string, adId: string) => void;
  onGenerateMore: () => void;
  onBackToEdit: () => void;
  onEdit?: (ad: GeneratedAd) => void;
}

export function ResultsStep({
  generatedAds,
  isGenerating,
  generationProgress,
  estimatedTime,
  onSave,
  onDownload,
  onGenerateMore,
  onBackToEdit,
  onEdit
}: ResultsStepProps) {
  return (
    <div className="space-y-12">
      <div className="text-center">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-4xl font-bold mb-4"
        >
          <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
            🎉 Tus Anuncios Generados
          </span>
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed mb-6"
        >
          Emma ha creado 6 variaciones únicas con IA avanzada
        </motion.p>
      </div>

      {/* Generation Progress Component */}
      <GenerationProgress
        isGenerating={isGenerating}
        completed={generationProgress.completed}
        total={generationProgress.total}
        variations={generatedAds}
        errors={generationProgress.errors}
        estimatedTimeRemaining={estimatedTime}
      />

      <AdResultsGrid
        generatedAds={generatedAds}
        isGenerating={isGenerating}
        onSave={onSave}
        onDownload={(ad) => onDownload(ad.image_url, ad.id)}
        onShare={(ad) => {
          // TODO: Implement share functionality
          console.log('Share ad:', ad.id);
        }}
        onPreview={(ad) => {
          // TODO: Implement preview functionality
          console.log('Preview ad:', ad.id);
        }}
        onEdit={onEdit}
      />

      <AdResultsActions
        generatedAdsCount={generatedAds.length}
        onGenerateMore={onGenerateMore}
        onBackToEdit={onBackToEdit}
        isGenerating={isGenerating}
      />
    </div>
  );
}
