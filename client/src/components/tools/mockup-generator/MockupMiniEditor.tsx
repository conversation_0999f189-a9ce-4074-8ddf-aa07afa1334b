/**
 * MockupMiniEditor - Mini editor de imágenes para mockups generados
 * Copiado completo del AdMiniEditor exitoso para mantener toda la funcionalidad
 */

import React, { useState, useRef, useEffect } from 'react';
import { X, Eraser, Undo, Download, Edit3, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/hooks/use-toast';

interface MockupMiniEditorProps {
  mockup: {
    id: string;
    image_url: string;
    prompt?: string;
    metadata?: any;
  };
  isOpen: boolean;
  onClose: () => void;
  onSave: (editedMockup: any) => void;
}

export function MockupMiniEditor({ mockup, isOpen, onClose, onSave }: MockupMiniEditorProps) {
  const { toast } = useToast();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(20);
  const [hasMask, setHasMask] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [originalImageData, setOriginalImageData] = useState<ImageData | null>(null);

  // Parámetros adicionales para Stability AI
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("webp");
  const [growMask, setGrowMask] = useState(5);
  const [originalImageFile, setOriginalImageFile] = useState<File | null>(null);

  // Convertir imagen URL a File (necesario para Stability AI)
  const convertImageUrlToFile = async (imageUrl: string): Promise<File> => {
    try {
      // Images are now served locally, no CORS issues
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      return new File([blob], `mockup-image-${mockup.id}.png`, { type: 'image/png' });
    } catch (error) {
      console.error('Error converting image URL to File:', error);
      throw new Error('No se pudo convertir la imagen');
    }
  };

  // Cargar imagen en el canvas cuando se abre el modal
  useEffect(() => {
    console.log('🎨 useEffect for loading image triggered:', { isOpen, hasImageUrl: !!mockup.image_url });
    if (isOpen && mockup.image_url) {
      setImageLoaded(false);
      setHasMask(false);
      setOriginalImageData(null);

      setTimeout(() => {
        loadImageToCanvas();
      }, 100);
    }
  }, [isOpen, mockup.image_url]);

  const loadImageToCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('❌ Canvas ref not available');
      return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('❌ Canvas context not available');
      return;
    }

    console.log('🎨 Loading image to canvas:', mockup.image_url?.substring(0, 100) + '...');

    const img = new Image();

    // Images are now served locally, no CORS issues
    img.src = mockup.image_url;
    console.log('🎨 Loading local image:', mockup.image_url);

    img.onload = () => {
      console.log('✅ Image loaded successfully');

      const maxSize = 500;
      const aspectRatio = img.width / img.height;

      if (aspectRatio > 1) {
        canvas.width = maxSize;
        canvas.height = maxSize / aspectRatio;
      } else {
        canvas.height = maxSize;
        canvas.width = maxSize * aspectRatio;
      }

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      setOriginalImageData(ctx.getImageData(0, 0, canvas.width, canvas.height));
      setImageLoaded(true);
      setHasMask(false);

      convertImageUrlToFile(mockup.image_url)
        .then(file => {
          setOriginalImageFile(file);
          console.log('✅ Image file created for Stability AI');
        })
        .catch(error => {
          console.error('❌ Error creating image file:', error);
        });
    };

    img.onerror = (error) => {
      console.error('❌ Image load error:', error);
      toast({
        title: "Error",
        description: "No se pudo cargar la imagen",
        variant: "destructive",
      });
    };
  };

  // Funciones de dibujo para la máscara
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    draw(e);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const x = ((e.clientX - rect.left) * canvas.width) / rect.width;
    const y = ((e.clientY - rect.top) * canvas.height) / rect.height;

    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
    ctx.beginPath();
    ctx.arc(x, y, brushSize / 2, 0, 2 * Math.PI);
    ctx.fill();

    setHasMask(true);
  };

  const endDrawing = () => {
    setIsDrawing(false);
  };

  // Limpiar la máscara
  const clearMask = () => {
    if (!originalImageData) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.putImageData(originalImageData, 0, 0);
    setHasMask(false);
  };

  // Procesar la imagen con Stability AI
  const handleEraseObjects = async () => {
    if (!hasMask || !originalImageFile) {
      toast({
        title: "Error",
        description: "Debes dibujar sobre las áreas que deseas borrar",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const canvas = canvasRef.current;
      if (!canvas) throw new Error("Canvas no disponible");

      // Crear canvas para la máscara
      const maskCanvas = document.createElement('canvas');
      maskCanvas.width = canvas.width;
      maskCanvas.height = canvas.height;
      const maskCtx = maskCanvas.getContext('2d');
      if (!maskCtx) throw new Error("No se pudo crear el contexto del canvas de máscara");

      // Crear máscara en blanco y negro
      maskCtx.fillStyle = 'black';
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);

      // Obtener los datos del canvas actual (con las marcas rojas)
      const currentImageData = canvas.getContext('2d')?.getImageData(0, 0, canvas.width, canvas.height);
      if (!currentImageData) throw new Error("No se pudieron obtener los datos de la imagen actual");

      // Convertir las áreas rojas a blanco en la máscara
      const maskImageData = maskCtx.createImageData(maskCanvas.width, maskCanvas.height);

      for (let i = 0; i < currentImageData.data.length; i += 4) {
        const r = currentImageData.data[i];
        const g = currentImageData.data[i + 1];
        const b = currentImageData.data[i + 2];

        // Si es rojo (área marcada para borrar), hacer blanco en la máscara
        if (r > 200 && g < 100 && b < 100) {
          maskImageData.data[i] = 255;     // R
          maskImageData.data[i + 1] = 255; // G
          maskImageData.data[i + 2] = 255; // B
          maskImageData.data[i + 3] = 255; // A
        } else {
          maskImageData.data[i] = 0;       // R
          maskImageData.data[i + 1] = 0;   // G
          maskImageData.data[i + 2] = 0;   // B
          maskImageData.data[i + 3] = 255; // A
        }
      }

      maskCtx.putImageData(maskImageData, 0, 0);

      // Convertir máscara a blob
      const maskBlob = await new Promise<Blob>((resolve) => {
        maskCanvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, 'image/png');
      });

      if (!maskBlob) throw new Error("Error al crear la máscara");

      // Crear FormData para enviar a la API
      const formData = new FormData();
      formData.append('image', originalImageFile);
      formData.append('mask', maskBlob, 'mask.png');
      formData.append('output_format', outputFormat);
      formData.append('grow_mask', growMask.toString());

      console.log('📡 Enviando a Stability AI...');

      // Llamar a la API de borrado de objetos
      const response = await fetch('/api/v1/ai-editor/erase', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();

      if (result.success && (result.image || result.image_url)) {
        const imageDataUrl = result.image
          ? `data:image/${outputFormat};base64,${result.image}`
          : result.image_url;

        // Crear un nuevo mockup con la imagen editada
        const editedMockup = {
          ...mockup,
          id: `${mockup.id}_edited_${Date.now()}`,
          image_url: imageDataUrl,
          timestamp: Date.now(),
          metadata: {
            ...mockup.metadata,
            edited: true,
            originalId: mockup.id,
            stabilityAI: true,
            editedWith: 'MockupMiniEditor'
          }
        };

        onSave(editedMockup);

        toast({
          title: "🎉 ¡Objetos borrados!",
          description: "La imagen ha sido procesada exitosamente",
        });
      } else {
        throw new Error(result.error || 'Error desconocido en la respuesta de Stability AI');
      }
    } catch (error) {
      console.error('Error processing image:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Enviar a Editor Profesional para edición avanzada
  const sendToPolotno = () => {
    console.log('🎨 Enviando imagen al Editor Profesional (Polotno)');

    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('❌ Canvas not available for Editor Profesional');
      toast({
        title: "Error",
        description: "Canvas no disponible",
        variant: "destructive",
      });
      return;
    }

    if (!imageLoaded) {
      console.error('❌ Image not loaded yet for Editor Profesional');
      toast({
        title: "Error",
        description: "La imagen aún no se ha cargado",
        variant: "destructive",
      });
      return;
    }

    try {
      // Obtener la imagen actual del canvas en base64 (con o sin ediciones)
      const imageDataUrl = canvas.toDataURL('image/png', 0.9);

      console.log('🎨 Abriendo Editor Profesional con imagen:', {
        imageSize: imageDataUrl.length,
        mockupId: mockup.id,
        hasEdits: hasMask,
        canvasSize: `${canvas.width}x${canvas.height}`
      });

      // Guardar imagen en backend temporalmente
      const imageId = `temp-${Date.now()}`;

      console.log('🎨 Guardando imagen en backend temporalmente...');

      // Guardar imagen en backend
      fetch('/api/v1/temp-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: imageId,
          imageData: imageDataUrl
        })
      })
      .then(response => response.json())
      .then(result => {
        if (result.success) {
          console.log('✅ Imagen guardada en backend, abriendo Polotno...');
          // Abrir visual-editor con el ID
          window.open(`/visual-editor?tempImageId=${imageId}&platform=mockup`, '_blank', 'noopener,noreferrer');
        } else {
          throw new Error('Error guardando imagen');
        }
      })
      .catch(error => {
        console.error('❌ Error guardando imagen:', error);
        // Fallback: usar base64 directamente
        window.open(`/visual-editor?imageUrl=${encodeURIComponent(imageDataUrl)}&platform=mockup`, '_blank', 'noopener,noreferrer');
      });

      console.log('✅ Editor Profesional opened in new tab successfully');

      toast({
        title: "🎨 Editor Profesional",
        description: "Se ha abierto el Editor Profesional en una nueva pestaña",
      });

    } catch (error) {
      console.error('❌ Error opening Editor Profesional:', error);

      toast({
        title: "Error",
        description: "No se pudo abrir el Editor Profesional",
        variant: "destructive",
      });
    }
  };

  // Descargar imagen actual
  const downloadCurrentImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = `${mockup.id}_edited.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();

    toast({
      title: "Descarga iniciada",
      description: "La imagen editada se está descargando",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Editor de Imagen - Mockup {mockup.id}
          </DialogTitle>
          <DialogDescription>
            Dibuja sobre los objetos que quieres borrar y usa las herramientas de edición avanzada.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Canvas para edición */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Dibuja sobre los objetos a borrar:</h4>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm">Tamaño del pincel:</span>
                  <Slider
                    value={[brushSize]}
                    onValueChange={(value) => setBrushSize(value[0])}
                    max={50}
                    min={5}
                    step={5}
                    className="w-24"
                  />
                  <span className="text-sm w-8">{brushSize}</span>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm">Expandir máscara:</span>
                  <Slider
                    value={[growMask]}
                    onValueChange={(value) => setGrowMask(value[0])}
                    max={20}
                    min={0}
                    step={1}
                    className="w-20"
                  />
                  <span className="text-sm w-8">{growMask}</span>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm">Formato:</span>
                  <select
                    value={outputFormat}
                    onChange={(e) => setOutputFormat(e.target.value as "png" | "webp" | "jpeg")}
                    className="text-sm border rounded px-2 py-1 bg-white"
                  >
                    <option value="webp">WebP</option>
                    <option value="png">PNG</option>
                    <option value="jpeg">JPEG</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="border rounded-lg overflow-hidden bg-gray-50 flex justify-center items-center min-h-[400px]">
              <canvas
                ref={canvasRef}
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={endDrawing}
                onMouseLeave={endDrawing}
                className="cursor-crosshair border border-gray-300 rounded shadow-sm"
                style={{
                  maxWidth: "500px",
                  maxHeight: "500px",
                  display: imageLoaded ? 'block' : 'none',
                  backgroundColor: 'white'
                }}
              />

              {!imageLoaded && (
                <div className="h-64 flex items-center justify-center">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Cargando imagen...</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Controles */}
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={clearMask}
              variant="outline"
              disabled={!hasMask}
            >
              <Undo className="w-4 h-4 mr-2" />
              Limpiar Máscara
            </Button>

            <Button
              onClick={handleEraseObjects}
              disabled={!hasMask || isProcessing}
              className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]"
            >
              {isProcessing ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Eraser className="w-4 h-4 mr-2" />
              )}
              {isProcessing ? 'Procesando...' : 'Borrar Objetos'}
            </Button>

            <Button
              onClick={sendToPolotno}
              variant="outline"
              className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef]/5"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Editor Profesional
            </Button>

            <Button
              onClick={downloadCurrentImage}
              variant="outline"
            >
              <Download className="w-4 h-4 mr-2" />
              Descargar
            </Button>
          </div>

          {/* Instrucciones */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-medium text-blue-900 mb-2">Instrucciones:</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Dibuja con el mouse sobre las áreas que deseas borrar</li>
              <li>• Ajusta el tamaño del pincel según necesites</li>
              <li>• Haz clic en "Borrar Objetos" para procesar la imagen</li>
              <li>• Usa "Editor Profesional" para edición avanzada con texto y elementos</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}