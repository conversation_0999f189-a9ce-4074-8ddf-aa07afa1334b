/**
 * Componente Simple de Mejora de Prompts para Mockups
 * Funciona exactamente como el ads creator - simple y directo
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Wand2, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PromptEnhancerProps {
  initialPrompt: string;
  type: 'product' | 'portrait';
  onPromptChange: (prompt: string) => void;
  className?: string;
}

export function PromptEnhancer({ initialPrompt, type, onPromptChange, className }: PromptEnhancerProps) {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState(initialPrompt);
  const [isEnhancing, setIsEnhancing] = useState(false);

  // Update prompt when initial changes
  useEffect(() => {
    setPrompt(initialPrompt);
  }, [initialPrompt]);

  const handleEnhancePrompt = async () => {
    if (!prompt.trim()) return;

    setIsEnhancing(true);

    try {
      const response = await fetch('/api/v1/content/improve-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'dev-api-key-for-testing'
        },
        body: JSON.stringify({
          prompt: prompt,
          context: type === 'product'
            ? 'Improve this prompt for professional product photography and mockup generation. IMPORTANT: Respond in the SAME LANGUAGE as the input prompt. If input is in Spanish, respond in Spanish. If input is in English, respond in English. If input is in Chinese, respond in Chinese, etc.'
            : 'Improve this prompt for professional portrait photography and realistic human generation. IMPORTANT: Respond in the SAME LANGUAGE as the input prompt. If input is in Spanish, respond in Spanish. If input is in English, respond in English. If input is in Chinese, respond in Chinese, etc.'
        })
      });

      if (response.ok) {
        const result = await response.json();
        const enhancedPrompt = result.improved_prompt;
        if (enhancedPrompt && enhancedPrompt !== prompt) {
          handlePromptChange(enhancedPrompt);
          setIsEnhancing(false);
          toast({
            title: "¡Prompt mejorado!",
            description: "La IA ha mejorado tu descripción",
          });
          return;
        }
      }
    } catch (error) {
      console.warn('Failed to enhance prompt with AI:', error);
    }

    // Fallback if AI fails
    const fallbackEnhancement = type === 'product'
      ? ", fotografía profesional de producto, alta calidad, iluminación profesional"
      : ", retrato profesional, alta calidad, iluminación de estudio";

    handlePromptChange(prompt + fallbackEnhancement);
    setIsEnhancing(false);

    toast({
      title: "Prompt mejorado",
      description: "Se agregaron mejoras básicas al prompt",
    });
  };

  const handlePromptChange = (value: string) => {
    setPrompt(value);
    onPromptChange(value);
  };



  return (
    <div className={`space-y-4 ${className}`}>
      {/* Área de texto principal */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Wand2 className="h-4 w-4" />
            Descripción {type === 'product' ? 'del Producto' : 'del Retrato'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Textarea
            value={prompt}
            onChange={(e) => handlePromptChange(e.target.value)}
            placeholder={type === 'product' 
              ? "ej., Botella de agua premium en un gimnasio moderno, iluminación natural..."
              : "ej., Retrato profesional de una mujer de negocios confiada, iluminación de estudio..."
            }
            className="min-h-[100px] resize-none"
          />
          
          {/* Simple enhance button */}
          <Button
            onClick={handleEnhancePrompt}
            disabled={!prompt.trim() || isEnhancing}
            size="sm"
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
          >
            {isEnhancing ? (
              <>
                <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                Mejorando con IA...
              </>
            ) : (
              <>
                <Wand2 className="h-3 w-3 mr-1" />
                Mejorar Prompt con IA
              </>
            )}
          </Button>

        </CardContent>
      </Card>
    </div>
  );
}
