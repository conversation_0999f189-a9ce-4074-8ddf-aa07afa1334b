import { useState, useEffect, useCallback } from 'react';

export interface ProgressStage {
  name: string;
  description: string;
  duration: number;
  completed: boolean;
}

export interface PostGenerationProgress {
  currentStage: number;
  progress: number;
  elapsedTime: number;
  estimatedTimeRemaining: number;
  stages: ProgressStage[];
  isComplete: boolean;
}

export const usePostGenerationProgress = (isGenerating: boolean, estimatedTotalTime: number = 45) => {
  const [progressData, setProgressData] = useState<PostGenerationProgress>({
    currentStage: 0,
    progress: 0,
    elapsedTime: 0,
    estimatedTimeRemaining: estimatedTotalTime,
    stages: [
      { name: "Analizando tu marca", description: "Procesando información de la marca y audiencia objetivo...", duration: 5, completed: false },
      { name: "Generando contenido", description: "Creando textos personalizados con IA avanzada...", duration: 8, completed: false },
      { name: "Diseñando imágenes", description: "🚀 Generación batch con Ideogram 3.0 (optimizado)...", duration: 25, completed: false },
      { name: "Optimizando posts", description: "🎨 Aplicando sistema de dos capas y finalizando...", duration: 7, completed: false }
    ],
    isComplete: false
  });

  const [startTime, setStartTime] = useState<number | null>(null);

  // Reset progress when generation starts
  useEffect(() => {
    if (isGenerating && !startTime) {
      setStartTime(Date.now());
      setProgressData(prev => ({
        ...prev,
        currentStage: 0,
        progress: 0,
        elapsedTime: 0,
        estimatedTimeRemaining: estimatedTotalTime,
        stages: prev.stages.map(stage => ({ ...stage, completed: false })),
        isComplete: false
      }));
    } else if (!isGenerating && startTime) {
      // Generation completed
      setProgressData(prev => ({
        ...prev,
        progress: 100,
        isComplete: true,
        stages: prev.stages.map(stage => ({ ...stage, completed: true }))
      }));
      setStartTime(null);
    }
  }, [isGenerating, startTime, estimatedTotalTime]);

  // Update progress based on elapsed time
  useEffect(() => {
    if (!isGenerating || !startTime) return;

    const interval = setInterval(() => {
      const elapsed = (Date.now() - startTime) / 1000;
      const progressPercent = Math.min((elapsed / estimatedTotalTime) * 100, 98); // Allow up to 98% during generation
      const remaining = Math.max(0, estimatedTotalTime - elapsed);

      // Determine current stage based on elapsed time
      let currentStageIndex = 0;
      let cumulativeDuration = 0;
      const updatedStages = progressData.stages.map((stage, index) => {
        cumulativeDuration += stage.duration;
        const completed = elapsed > cumulativeDuration;
        if (!completed && currentStageIndex === 0) {
          currentStageIndex = index;
        }
        return { ...stage, completed };
      });

      // If we're past all stages, set to last stage
      if (elapsed > cumulativeDuration) {
        currentStageIndex = progressData.stages.length - 1;
      }

      setProgressData(prev => ({
        ...prev,
        currentStage: currentStageIndex,
        progress: progressPercent,
        elapsedTime: elapsed,
        estimatedTimeRemaining: remaining,
        stages: updatedStages
      }));
    }, 500);

    return () => clearInterval(interval);
  }, [isGenerating, startTime, estimatedTotalTime, progressData.stages]);

  // Manual stage advancement (for when we know specific stages are complete)
  const advanceToStage = useCallback((stageIndex: number) => {
    setProgressData(prev => ({
      ...prev,
      currentStage: Math.min(stageIndex, prev.stages.length - 1),
      stages: prev.stages.map((stage, index) => ({
        ...stage,
        completed: index < stageIndex
      }))
    }));
  }, []);

  // Mark specific stage as complete
  const completeStage = useCallback((stageIndex: number) => {
    setProgressData(prev => ({
      ...prev,
      stages: prev.stages.map((stage, index) => ({
        ...stage,
        completed: index <= stageIndex ? true : stage.completed
      }))
    }));
  }, []);

  // Complete progress immediately (for when backend finishes early)
  const completeProgress = useCallback(() => {
    setProgressData(prev => ({
      ...prev,
      progress: 100,
      isComplete: true,
      estimatedTimeRemaining: 0,
      stages: prev.stages.map(stage => ({ ...stage, completed: true }))
    }));
  }, []);

  // Format time helper
  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return {
    ...progressData,
    advanceToStage,
    completeStage,
    completeProgress, // 🚀 New function to complete progress immediately
    formatTime: (seconds: number) => formatTime(seconds),
    formattedElapsedTime: formatTime(progressData.elapsedTime),
    formattedRemainingTime: formatTime(progressData.estimatedTimeRemaining)
  };
};

export default usePostGenerationProgress;
