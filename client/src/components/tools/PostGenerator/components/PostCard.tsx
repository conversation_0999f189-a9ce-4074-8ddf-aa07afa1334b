import React, { useState, useMemo, useCallback, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { Edit3, Download, Share2, Instagram, Linkedin, Facebook, Twitter, Hash, MessageCircle, Copy, Palette, ChevronDown, Image, FileText, Package } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { GeneratedPost } from "../PostGenerator";
import { AdMiniEditor } from "../../ad-creator/shared/AdMiniEditor";
import { GeneratedAd } from "@/types/ad-creator-types";


interface PostCardProps {
  post: GeneratedPost;
  onEdit: () => void;
  onExport: () => void;
  onShare: () => void;
  onGenerateMore?: () => void;
  // New export functions
  onExportBackgroundOnly?: () => void;
  onExportWithText?: () => void;
  onExportTextContent?: () => void;
  onExportComplete?: () => void;
}

// Static animation objects to prevent re-creation on each render
const CARD_HOVER_ANIMATION = { y: -2 };
const CARD_INITIAL_ANIMATION = { opacity: 0, y: 20 };
const CARD_ANIMATE_ANIMATION = { opacity: 1, y: 0 };

const PostCard: React.FC<PostCardProps> = ({
  post,
  onEdit,
  onExport,
  onShare,
  onGenerateMore,
  onExportBackgroundOnly,
  onExportWithText,
  onExportTextContent,
  onExportComplete,
}) => {
  // Debug logging
  console.log('🎨 PostCard rendering with post:', {
    id: post.id,
    imageUrl: post.imageUrl,
    hasImage: !!post.imageUrl,
    imageLength: post.imageUrl?.length,
    platform: post.platform
  });

  // State for image loading error handling
  const [imageError, setImageError] = useState(false);

  // State for mini editor
  const [isEditorOpen, setIsEditorOpen] = useState(false);

  // State for current image URL (can be updated after editing)
  const [currentImageUrl, setCurrentImageUrl] = useState(post.imageUrl);

  // Canvas ref and states (like AdMiniEditor)
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [canvasImageData, setCanvasImageData] = useState<string | null>(null);

  // Sync currentImageUrl when post.imageUrl changes (e.g., new post generated)
  useEffect(() => {
    setCurrentImageUrl(post.imageUrl);
    setImageLoaded(false); // Reset image loaded state
  }, [post.imageUrl]);

  // Load image to canvas (EXACTLY like AdMiniEditor)
  const loadImageToCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !currentImageUrl) {
      console.error('❌ Canvas ref or image URL not available');
      return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('❌ Canvas context not available');
      return;
    }

    console.log('🎨 Loading image to canvas (like AdMiniEditor):', {
      imageUrl: currentImageUrl?.substring(0, 100) + '...',
      isBase64: currentImageUrl?.startsWith('data:'),
      urlLength: currentImageUrl?.length
    });

    const img = new Image();
    // Don't set crossOrigin for base64 data URLs (like AdMiniEditor)
    if (!currentImageUrl.startsWith('data:')) {
      img.crossOrigin = 'anonymous';
    }

    // ARREGLAR CORS: Usar proxy si es imagen de Ideogram
    let imageUrlToLoad = currentImageUrl;
    if (currentImageUrl.includes('ideogram.ai') && !currentImageUrl.startsWith('/api/v1/ad-creator-agent/proxy-image')) {
      imageUrlToLoad = `/api/v1/ad-creator-agent/proxy-image?url=${encodeURIComponent(currentImageUrl)}`;
      console.log('🔄 USANDO PROXY PARA CORS EN CANVAS (loadImageToCanvas):', {
        originalUrl: currentImageUrl.substring(0, 100) + '...',
        proxyUrl: imageUrlToLoad
      });
    }

    img.src = imageUrlToLoad;

    img.onload = () => {
      console.log('✅ Image loaded successfully to canvas:', {
        width: img.width,
        height: img.height,
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight
      });

      // Set canvas dimensions (square like Instagram)
      const maxSize = 500;
      canvas.width = maxSize;
      canvas.height = maxSize;

      console.log('🎨 Canvas dimensions set:', {
        canvasWidth: canvas.width,
        canvasHeight: canvas.height
      });

      // Draw image to canvas (like AdMiniEditor)
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // Get base64 from canvas for Polotno
      const imageDataUrl = canvas.toDataURL('image/png', 0.9);
      setCanvasImageData(imageDataUrl);
      setImageLoaded(true);

      console.log('✅ Image successfully loaded to canvas (like AdMiniEditor)');
    };

    img.onerror = (error) => {
      console.error('❌ Image load error:', error);
      console.error('❌ Failed image URL:', currentImageUrl?.substring(0, 200));
      setImageLoaded(false);
    };
  }, [currentImageUrl]);

  // Load image to canvas when currentImageUrl changes
  useEffect(() => {
    if (currentImageUrl) {
      // Small delay to ensure canvas is ready
      setTimeout(() => {
        loadImageToCanvas();
      }, 100);
    }
  }, [currentImageUrl, loadImageToCanvas]);

  // State for inline text editing
  const [isEditingText, setIsEditingText] = useState(false);
  const [editedContent, setEditedContent] = useState(post.content);
  const [currentContent, setCurrentContent] = useState(post.content); // Para forzar re-render del overlay

  // State for description editing
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [editedDescription, setEditedDescription] = useState(post.description || '');
  const [currentDescription, setCurrentDescription] = useState(post.description || '');

  // Memoized dimension calculations to prevent recalculation on every render
  const imageDimensions = useMemo(() => {
    const platformDimensions = {
      instagram: { width: 480, height: 480, aspectRatio: 1 },
      facebook: { width: 520, height: 273, aspectRatio: 1.9 },
      linkedin: { width: 520, height: 272, aspectRatio: 1.91 },
      x: { width: 520, height: 292, aspectRatio: 1.78 },
      twitter: { width: 520, height: 292, aspectRatio: 1.78 },
      "instagram stories": { width: 320, height: 568, aspectRatio: 0.56 }
    };

    const platformKey = post.platform.toLowerCase() as keyof typeof platformDimensions;
    let result = platformDimensions[platformKey] || platformDimensions.instagram;

    // Calculate based on metadata dimensions if available
    const dimensions = post.metadata?.dimensions;
    if (dimensions && dimensions.includes('x')) {
      try {
        const [width, height] = dimensions.split('x').map(Number);
        if (width && height) {
          const aspectRatio = width / height;
          let displayWidth, displayHeight;

          if (aspectRatio > 1.5) {
            displayWidth = 520;
            displayHeight = Math.round(displayWidth / aspectRatio);
          } else if (aspectRatio < 0.8) {
            displayHeight = 580;
            displayWidth = Math.round(displayHeight * aspectRatio);
          } else {
            displayWidth = 480;
            displayHeight = Math.round(displayWidth / aspectRatio);
          }

          result = { width: displayWidth, height: displayHeight, aspectRatio };
        }
      } catch (error) {
        // Silent fallback to default dimensions
      }
    }

    return result;
  }, [post.platform, post.metadata?.dimensions]);

  // Memoized image error handler
  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  // Reset image error when imageUrl changes
  React.useEffect(() => {
    setImageError(false);
  }, [post.imageUrl]);

  // Convert GeneratedPost to GeneratedAd for compatibility with AdMiniEditor
  const convertPostToAd = useCallback((): GeneratedAd => {
    // Use current image URL (which may be edited)
    const imageUrlToUse = currentImageUrl || post.imageUrl || '';

    console.log('🔄 Converting post to ad (EXACTLY like ads creator):', {
      postId: post.id,
      originalImageUrl: post.imageUrl,
      currentImageUrl: currentImageUrl,
      imageUrlToUse: imageUrlToUse,
      imageUrlLength: imageUrlToUse?.length,
      isIdeogram: imageUrlToUse?.includes('ideogram.ai'),
      isBase64: imageUrlToUse?.startsWith('data:')
    });

    // Use proxy for Ideogram URLs like ads creator does
    let finalImageUrl = imageUrlToUse;
    if (finalImageUrl && finalImageUrl.includes('ideogram.ai') && !finalImageUrl.startsWith('/api/v1/ad-creator-agent/proxy-image')) {
      finalImageUrl = `/api/v1/ad-creator-agent/proxy-image?url=${encodeURIComponent(finalImageUrl)}`;
      console.log('🔄 USANDO PROXY PARA CORS:', {
        originalUrl: imageUrlToUse,
        proxyUrl: finalImageUrl,
        isIdeogram: finalImageUrl.includes('ideogram.ai')
      });
    } else {
      console.log('🔄 NO USANDO PROXY:', {
        originalUrl: imageUrlToUse,
        hasIdeogram: finalImageUrl.includes('ideogram.ai'),
        alreadyProxy: finalImageUrl.startsWith('/api/v1/ad-creator-agent/proxy-image')
      });
    }

    return {
      id: post.id,
      image_url: finalImageUrl,  // Use proxy URL like ads creator
      prompt: post.content,
      timestamp: Date.now(),
      metadata: {
        platform: post.platform,
        originalType: 'post'
      }
    };
  }, [post.id, post.imageUrl, currentImageUrl, post.content, post.platform]);

  // Handler for opening image editor - EXACTLY like ads creator
  const handleOpenImageEditor = useCallback(() => {
    console.log('🎨 Opening image editor for post (EXACTLY like ads creator):', post.id);

    if (!post.imageUrl) {
      console.error('❌ No image URL available for editor');
      return;
    }

    console.log('🎨 Post image URL:', post.imageUrl);
    setIsEditorOpen(true);
  }, [post.id, post.imageUrl]);

  // Handler for opening Polotno editor - USANDO CANVAS EXISTENTE COMO ADS CREATOR
  const handleOpenPolotnoEditor = useCallback(async () => {
    console.log('🎨 Opening Polotno using EXISTING canvas (like ads creator):', post.id);

    const canvas = canvasRef.current;

    // Si canvas no está listo, intentar cargarlo y esperar
    if (!canvas || !imageLoaded || !canvasImageData) {
      console.log('⏳ Canvas not ready, attempting to load image first...', {
        hasCanvas: !!canvas,
        imageLoaded,
        hasCanvasImageData: !!canvasImageData,
        currentImageUrl: !!currentImageUrl
      });

      // Si no hay imagen, no podemos continuar
      if (!currentImageUrl) {
        console.error('❌ No image URL available');
        return;
      }

      // Intentar cargar imagen en canvas
      if (canvas && !imageLoaded) {
        console.log('🔄 Loading image to canvas before opening Polotno...');
        loadImageToCanvas();

        // Esperar hasta que la imagen se cargue (máximo 5 segundos)
        let attempts = 0;
        const maxAttempts = 50; // 5 segundos (50 * 100ms)

        while (attempts < maxAttempts && (!imageLoaded || !canvasImageData)) {
          await new Promise(resolve => setTimeout(resolve, 100));
          attempts++;

          if (attempts % 10 === 0) {
            console.log(`⏳ Waiting for canvas to load... (${attempts/10}s)`);
          }
        }

        // Verificar si finalmente se cargó
        if (!imageLoaded || !canvasImageData) {
          console.error('❌ Canvas failed to load after waiting:', {
            imageLoaded,
            hasCanvasImageData: !!canvasImageData,
            attempts
          });
          return;
        }

        console.log('✅ Canvas loaded successfully after waiting');
      } else {
        console.error('❌ Canvas ref not available');
        return;
      }
    }

    try {
      console.log('🎨 Using EXISTING canvas method (EXACTLY like ads creator)...');

      // STEP 1: Get base64 from existing canvas (EXACTLY like ads creator)
      const imageDataUrl = canvas.toDataURL('image/png', 0.9);

      console.log('🎨 Image from existing canvas (like ads creator):', {
        imageSize: imageDataUrl.length,
        postId: post.id,
        canvasSize: `${canvas.width}x${canvas.height}`,
        isBase64: imageDataUrl.startsWith('data:image/'),
        imagePreview: imageDataUrl.substring(0, 100) + '...'
      });

      // STEP 2: Save to backend temporarily (EXACTLY like ads creator)
      const imageId = `temp-${Date.now()}`;

      console.log('🎨 Saving canvas image to backend (like ads creator)...');

      const response = await fetch('/api/v1/temp-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: imageId,
          imageData: imageDataUrl // ← BASE64 from existing canvas (like ads creator)
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Canvas image saved to backend, opening Polotno (like ads creator)...');

        // STEP 3: Open visual-editor with tempImageId AND content (better than ads creator)
        const encodedContent = encodeURIComponent(post.content || '');
        const polotnoUrl = `/visual-editor?tempImageId=${imageId}&platform=${post.platform.toLowerCase()}&content=${encodedContent}`;

        console.log('🔗 Opening Polotno with canvas image AND text content:', {
          imageId,
          platform: post.platform.toLowerCase(),
          contentLength: post.content?.length || 0,
          contentPreview: post.content?.substring(0, 100) + '...',
          fullContent: post.content, // ← DEBUGGING: Ver contenido completo
          encodedContent: encodedContent.substring(0, 100) + '...',
          polotnoUrl: polotnoUrl.substring(0, 200) + '...'
        });
        window.open(polotnoUrl, '_blank', 'noopener,noreferrer');

        console.log('✅ Polotno opened with existing canvas method (like ads creator)');
      } else {
        throw new Error(`Error saving canvas image to backend: ${result.message || 'Unknown error'}`);
      }

    } catch (error) {
      console.error('❌ Error opening Polotno with canvas:', error);

      // Fallback: use direct URL method with content
      let fallbackUrl = currentImageUrl || '';
      if (fallbackUrl && fallbackUrl.includes('ideogram.ai') && !fallbackUrl.startsWith('/api/v1/ad-creator-agent/proxy-image')) {
        fallbackUrl = `/api/v1/ad-creator-agent/proxy-image?url=${encodeURIComponent(fallbackUrl)}`;
      }
      if (fallbackUrl) {
        const encodedContent = encodeURIComponent(post.content || '');
        window.open(`/visual-editor?imageUrl=${encodeURIComponent(fallbackUrl)}&platform=${post.platform.toLowerCase()}&content=${encodedContent}`, '_blank', 'noopener,noreferrer');
      }
    }
  }, [post.id, post.platform, imageLoaded, canvasImageData]);



  // Handler for inline text editing
  const handleTextClick = useCallback(() => {
    setIsEditingText(true);
  }, []);

  const handleTextSave = useCallback(() => {
    // Update the post content locally
    console.log('💾 Saving text changes:', editedContent);
    post.content = editedContent; // Update the post object directly
    setCurrentContent(editedContent); // Force re-render of overlay
    setIsEditingText(false);
  }, [editedContent, post]);

  const handleTextCancel = useCallback(() => {
    setEditedContent(currentContent);
    setIsEditingText(false);
  }, [currentContent]);

  // Handlers for description editing
  const handleDescriptionClick = useCallback(() => {
    setIsEditingDescription(true);
  }, []);

  const handleDescriptionSave = useCallback(() => {
    console.log('💾 Saving description changes:', editedDescription);
    post.description = editedDescription; // Update the post object directly
    setCurrentDescription(editedDescription); // Force re-render
    setIsEditingDescription(false);
  }, [editedDescription, post]);

  const handleDescriptionCancel = useCallback(() => {
    setEditedDescription(currentDescription);
    setIsEditingDescription(false);
  }, [currentDescription]);
  // Memoized platform icon to prevent re-creation
  const platformIcon = useMemo(() => {
    switch (post.platform.toLowerCase()) {
      case "instagram":
        return <Instagram className="w-4 h-4" />;
      case "linkedin":
        return <Linkedin className="w-4 h-4" />;
      case "facebook":
        return <Facebook className="w-4 h-4" />;
      case "twitter":
      case "x":
        return <Twitter className="w-4 h-4" />;
      default:
        return <Instagram className="w-4 h-4" />;
    }
  }, [post.platform]);

  // Memoized platform color
  const platformColor = useMemo(() => {
    switch (post.platform.toLowerCase()) {
      case "instagram":
        return "from-pink-500 to-purple-600";
      case "linkedin":
        return "from-blue-600 to-blue-700";
      case "facebook":
        return "from-blue-500 to-blue-600";
      case "twitter":
      case "x":
        return "from-gray-800 to-black";
      default:
        return "from-pink-500 to-purple-600";
    }
  }, [post.platform]);

  // Memoized template color
  const templateColor = useMemo(() => {
    switch (post.template.toLowerCase()) {
      case "informativo":
        return "bg-blue-100 text-blue-800";
      case "motivacional":
        return "bg-green-100 text-green-800";
      case "educativo":
        return "bg-purple-100 text-purple-800";
      case "entretenimiento":
        return "bg-orange-100 text-orange-800";
      case "promocional":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }, [post.template]);

  return (
    <motion.div
      className="w-full max-w-4xl mx-auto bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300"
      whileHover={CARD_HOVER_ANIMATION}
      initial={CARD_INITIAL_ANIMATION}
      animate={CARD_ANIMATE_ANIMATION}
      data-post-id={post.id}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg bg-gradient-to-r ${platformColor} text-white`}>
              {platformIcon}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 capitalize">
                {post.platform}
              </h3>
              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${templateColor}`}>
                {post.template}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 flex-wrap">
            <Button
              onClick={handleOpenImageEditor}
              variant="outline"
              size="sm"
              className="flex items-center hover:bg-[#3018ef] hover:text-white transition-colors"
              title="Editar imagen con Stability AI"
            >
              <Edit3 className="w-4 h-4 mr-1" />
              Editar
            </Button>
            <Button
              onClick={handleOpenPolotnoEditor}
              variant="outline"
              size="sm"
              className="flex items-center hover:bg-[#dd3a5a] hover:text-white transition-colors"
              title="Editor Profesional (Polotno)"
            >
              <Palette className="w-4 h-4 mr-1" />
              Editor Profesional
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center hover:bg-green-600 hover:text-white transition-colors"
                >
                  <Download className="w-4 h-4 mr-1" />
                  Exportar
                  <ChevronDown className="w-3 h-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem onClick={onExportBackgroundOnly || onExport}>
                  <Image className="w-4 h-4 mr-2" />
                  Imagen sin texto
                  <span className="text-xs text-gray-500 ml-auto">PNG</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onExportWithText}>
                  <Palette className="w-4 h-4 mr-2" />
                  Imagen con texto
                  <span className="text-xs text-gray-500 ml-auto">PNG</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onExportTextContent}>
                  <FileText className="w-4 h-4 mr-2" />
                  Texto para Instagram
                  <span className="text-xs text-gray-500 ml-auto">TXT</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onExportComplete}>
                  <Package className="w-4 h-4 mr-2" />
                  Paquete completo
                  <span className="text-xs text-gray-500 ml-auto">TODO</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button
              onClick={onShare}
              variant="outline"
              size="sm"
              className="flex items-center hover:bg-blue-600 hover:text-white transition-colors"
            >
              <Share2 className="w-4 h-4 mr-1" />
              Compartir
            </Button>

            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log("🚀 PostCard: 'Más como este' button clicked!");
                console.log("🔍 onGenerateMore function:", onGenerateMore);
                console.log("🔍 onGenerateMore exists:", !!onGenerateMore);
                if (onGenerateMore) {
                  try {
                    onGenerateMore();
                    console.log("✅ onGenerateMore executed successfully");
                  } catch (error) {
                    console.error("❌ Error executing onGenerateMore:", error);
                  }
                } else {
                  console.error("❌ onGenerateMore function is not defined!");
                  alert("Error: La función 'más como este' no está disponible");
                }
              }}
              variant="outline"
              size="sm"
              className="flex items-center hover:bg-purple-600 hover:text-white transition-colors border-purple-300 text-purple-700"
            >
              <Copy className="w-4 h-4 mr-1" />
              Más como este
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Text Content */}
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <MessageCircle className="w-4 h-4 mr-2" />
                Contenido del Post
                <span className="text-xs text-gray-500 ml-2">(Click para editar)</span>
              </h4>
              <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-[#3018ef]">
                {isEditingText ? (
                  <div className="space-y-2">
                    <textarea
                      value={editedContent}
                      onChange={(e) => setEditedContent(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded resize-none text-gray-800"
                      rows={4}
                      autoFocus
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={handleTextSave}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        Guardar
                      </Button>
                      <Button
                        onClick={handleTextCancel}
                        size="sm"
                        variant="outline"
                      >
                        Cancelar
                      </Button>
                    </div>
                  </div>
                ) : (
                  <p
                    className="text-gray-800 leading-relaxed whitespace-pre-wrap cursor-pointer hover:bg-gray-100 p-2 rounded transition-colors"
                    onClick={handleTextClick}
                  >
                    {currentContent}
                  </p>
                )}
              </div>
            </div>

            {/* Descripción/Caption */}
            {post.description && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Descripción
                  <span className="text-xs text-gray-500 ml-2">(Click para editar)</span>
                </h4>
                <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-[#dd3a5a]">
                  {isEditingDescription ? (
                    <div className="space-y-2">
                      <textarea
                        value={editedDescription}
                        onChange={(e) => setEditedDescription(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded resize-none text-gray-800"
                        rows={3}
                        autoFocus
                      />
                      <div className="flex gap-2">
                        <Button
                          onClick={handleDescriptionSave}
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          Guardar
                        </Button>
                        <Button
                          onClick={handleDescriptionCancel}
                          size="sm"
                          variant="outline"
                        >
                          Cancelar
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <p
                      className="text-gray-800 leading-relaxed whitespace-pre-wrap cursor-pointer hover:bg-gray-100 p-2 rounded transition-colors"
                      onClick={handleDescriptionClick}
                    >
                      {currentDescription}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Hashtags */}
            {post.hashtags && post.hashtags.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <Hash className="w-4 h-4 mr-2" />
                  Hashtags
                </h4>
                <div className="flex flex-wrap gap-2">
                  {post.hashtags.map((hashtag, index) => (
                    <span
                      key={index}
                      className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                    >
                      #{hashtag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* CTA */}
            {post.cta && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Call to Action
                </h4>
                <div 
                  className="inline-block px-4 py-2 rounded-lg text-white font-medium"
                  style={{ backgroundColor: post.metadata.brandColor }}
                >
                  {post.cta}
                </div>
              </div>
            )}
          </div>

          {/* Visual Preview */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Vista Previa Visual
            </h4>
            
            {/* Platform indicator */}
            <div className="mb-4 text-center">
              <span className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                {post.platform} • {post.metadata?.dimensions || `${imageDimensions.width}x${imageDimensions.height}`}
              </span>
            </div>

            {/* Post preview with optimized image handling and text overlay */}
            <div className="flex justify-center">
              <div
                className="rounded-lg overflow-hidden border border-gray-200 shadow-sm relative"
                style={{
                  width: `${imageDimensions.width}px`,
                  height: `${imageDimensions.height}px`,
                }}
                data-preview-container={post.id}
              >
                {currentImageUrl && currentImageUrl.trim() !== "" && !imageError ? (
                  <>
                    <img
                      src={currentImageUrl}
                      alt={`${post.platform} post image`}
                      className="w-full h-full object-cover block"
                      onError={(e) => {
                        console.error('❌ Error loading image:', currentImageUrl);
                        console.error('❌ Image error event:', e);
                        handleImageError();
                      }}
                      onLoad={() => {
                        console.log('✅ Image loaded successfully:', post.imageUrl);
                      }}
                      loading="lazy"
                    />
                    {/* Text overlay preview - SIN caja gris horrible */}
                    <div className="absolute inset-0 flex flex-col justify-center items-center p-4 text-center">
                      <div className="max-w-full">
                        <p className="text-white text-sm font-bold leading-tight line-clamp-4 drop-shadow-lg" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}>
                          {currentContent.length > 120 ? `${currentContent.substring(0, 120)}...` : currentContent}
                        </p>
                        {post.hashtags && post.hashtags.length > 0 && (
                          <div className="mt-2">
                            <p className="text-white text-xs font-medium drop-shadow-lg" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}>
                              {post.hashtags.slice(0, 3).map(tag => `#${tag}`).join(' ')}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                  </>
                ) : (
                  <div
                    className="w-full h-full flex items-center justify-center text-white font-bold text-lg"
                    style={{
                      background: `linear-gradient(135deg, ${post.metadata.brandColor} 0%, ${post.metadata.brandColor}dd 100%)`
                    }}
                  >
                    {post.metadata.businessName}
                  </div>
                )}
              </div>
            </div>

              {/* Social media preview */}
              <div className="space-y-2 mt-4">
                <div className="flex items-center space-x-2">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold"
                    style={{ backgroundColor: post.metadata.brandColor }}
                  >
                    {post.metadata.businessName.charAt(0)}
                  </div>
                  <span className="font-semibold text-gray-800 text-sm">
                    {post.metadata.businessName}
                  </span>
                </div>

                {/* Descripción/Caption debajo de la marca como en Instagram/LinkedIn */}
                {currentDescription && (
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {currentDescription}
                  </p>
                )}

                {post.hashtags && post.hashtags.length > 0 && (
                  <p className="text-blue-600 text-xs">
                    {post.hashtags.slice(0, 3).map(tag => `#${tag}`).join(' ')}
                  </p>
                )}
              </div>

            {/* Metadata */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Plataforma:</span>
                <span className="font-medium text-gray-800 capitalize">{post.platform}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Dimensiones originales:</span>
                <span className="font-medium text-gray-800">
                  {post.metadata?.dimensions || 'No especificadas'} px
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Dimensiones de vista:</span>
                <span className="font-medium text-gray-800">
                  {imageDimensions.width}x{imageDimensions.height} px
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Proporción:</span>
                <span className="font-medium text-gray-800">
                  {imageDimensions.aspectRatio.toFixed(2)}:1
                  {imageDimensions.aspectRatio === 1 ? ' (Cuadrada)' :
                   imageDimensions.aspectRatio > 1 ? ' (Horizontal)' : ' (Vertical)'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tema:</span>
                <span className="font-medium text-gray-800">{post.metadata.theme}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Color de marca:</span>
                <div className="flex items-center space-x-2">
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: post.metadata.brandColor }}
                  />
                  <span className="font-medium text-gray-800">{post.metadata.brandColor}</span>
                </div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tipo de contenido:</span>
                <span className={`font-medium ${post.metadata.imageGenerated ? 'text-blue-600' : 'text-purple-600'}`}>
                  {post.metadata.imageGenerated ? '🖼️ Visual' : '📝 Texto'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hidden canvas for image processing (like AdMiniEditor) */}
      <canvas
        ref={canvasRef}
        style={{ display: 'none' }}
        className="hidden"
      />

      {/* Mini Editor Modal - EXACTLY like ads creator */}
      {isEditorOpen && (
        <AdMiniEditor
          ad={convertPostToAd()}
          isOpen={isEditorOpen}
          onClose={() => setIsEditorOpen(false)}
          onSave={(editedAd) => {
            console.log('💾 ACTUALIZANDO IMAGEN DEL POST:', {
              originalImageUrl: currentImageUrl,
              newImageUrl: editedAd.image_url,
              adId: editedAd.id
            });

            // Update the current image URL with the edited image
            setCurrentImageUrl(editedAd.image_url);
            setIsEditorOpen(false);

            console.log('✅ IMAGEN DEL POST ACTUALIZADA EXITOSAMENTE');
          }}
        />
      )}
    </motion.div>
  );
};

export default PostCard;
