/**
 * Database-aware hook for buyer persona generator
 * Replaces localStorage with Supabase database operations
 */

import { useState, useEffect, useCallback } from 'react'
import { buyerPersonaService } from '@/services/buyerPersonaService'
import type { 
  BuyerPersonaProject, 
  BuyerPersona, 
  BuyerPersonaConversation,
  CreateBuyerPersonaProjectData,
  CreateBuyerPersonaData,
  CreateBuyerPersonaConversationData
} from '@/services/buyerPersonaService'

interface UsePersonaDatabaseReturn {
  // Projects
  projects: BuyerPersonaProject[]
  currentProject: BuyerPersonaProject | null
  projectsLoading: boolean
  projectsError: string | null
  
  // Personas
  personas: BuyerPersona[]
  currentPersona: BuyerPersona | null
  personasLoading: boolean
  personasError: string | null
  
  // Conversations
  conversations: BuyerPersonaConversation[]
  currentConversation: BuyerPersonaConversation | null
  conversationsLoading: boolean
  conversationsError: string | null
  
  // Statistics
  userStats: {
    totalProjects: number
    totalPersonas: number
    totalConversations: number
    favoriteProjects: number
    favoritePersonas: number
    favoriteConversations: number
  } | null
  
  // Actions
  loadProjects: () => Promise<void>
  loadProject: (id: string) => Promise<void>
  createProject: (data: CreateBuyerPersonaProjectData) => Promise<BuyerPersonaProject>
  updateProject: (id: string, updates: Partial<BuyerPersonaProject>) => Promise<void>
  deleteProject: (id: string) => Promise<void>
  toggleProjectFavorite: (id: string, isFavorite: boolean) => Promise<void>
  setCurrentProject: (project: BuyerPersonaProject | null) => void
  
  loadPersonasByProject: (projectId: string) => Promise<void>
  loadPersona: (id: string) => Promise<void>
  createPersona: (data: CreateBuyerPersonaData) => Promise<BuyerPersona>
  updatePersona: (id: string, updates: Partial<BuyerPersona>) => Promise<void>
  deletePersona: (id: string) => Promise<void>
  togglePersonaFavorite: (id: string, isFavorite: boolean) => Promise<void>
  
  loadConversationsByPersona: (personaId: string) => Promise<void>
  loadConversationsByProject: (projectId: string) => Promise<void>
  loadConversation: (id: string) => Promise<void>
  createConversation: (data: CreateBuyerPersonaConversationData) => Promise<BuyerPersonaConversation>
  updateConversation: (id: string, updates: Partial<BuyerPersonaConversation>) => Promise<void>
  deleteConversation: (id: string) => Promise<void>
  addMessageToConversation: (conversationId: string, message: any) => Promise<void>
  
  loadUserStats: () => Promise<void>
  refreshAll: () => Promise<void>
}

export function usePersonaDatabase(): UsePersonaDatabaseReturn {
  // State
  const [projects, setProjects] = useState<BuyerPersonaProject[]>([])
  const [currentProject, setCurrentProject] = useState<BuyerPersonaProject | null>(null)
  const [projectsLoading, setProjectsLoading] = useState(false)
  const [projectsError, setProjectsError] = useState<string | null>(null)
  
  const [personas, setPersonas] = useState<BuyerPersona[]>([])
  const [currentPersona, setCurrentPersona] = useState<BuyerPersona | null>(null)
  const [personasLoading, setPersonasLoading] = useState(false)
  const [personasError, setPersonasError] = useState<string | null>(null)
  
  const [conversations, setConversations] = useState<BuyerPersonaConversation[]>([])
  const [currentConversation, setCurrentConversation] = useState<BuyerPersonaConversation | null>(null)
  const [conversationsLoading, setConversationsLoading] = useState(false)
  const [conversationsError, setConversationsError] = useState<string | null>(null)
  
  const [userStats, setUserStats] = useState<any>(null)

  // Project operations
  const loadProjects = useCallback(async () => {
    try {
      setProjectsLoading(true)
      setProjectsError(null)
      console.log('🔄 Loading projects...')
      const data = await buyerPersonaService.getProjects()
      console.log('✅ Projects loaded:', data.length, 'projects')
      console.log('📊 Projects data:', data)
      setProjects(data)
    } catch (error) {
      setProjectsError(error instanceof Error ? error.message : 'Failed to load projects')
      console.error('❌ Error loading projects:', error)
    } finally {
      setProjectsLoading(false)
    }
  }, [])

  const loadProject = useCallback(async (id: string) => {
    try {
      const project = await buyerPersonaService.getProjectById(id)
      setCurrentProject(project)
      if (project) {
        await buyerPersonaService.updateProjectAccess(id)
      }
    } catch (error) {
      console.error('Error loading project:', error)
    }
  }, [])

  const createProject = useCallback(async (data: CreateBuyerPersonaProjectData): Promise<BuyerPersonaProject> => {
    try {
      console.log('🚀 Creating project with data:', data)
      const project = await buyerPersonaService.createProject(data)
      console.log('✅ Project created successfully:', project)
      console.log('📝 Adding project to state...')
      setProjects(prev => {
        const newProjects = [project, ...prev]
        console.log('📊 Updated projects state:', newProjects.length, 'projects')
        return newProjects
      })
      return project
    } catch (error) {
      console.error('❌ Error creating project:', error)
      setProjectsError(error instanceof Error ? error.message : 'Failed to create project')
      throw error
    }
  }, [])

  const updateProject = useCallback(async (id: string, updates: Partial<BuyerPersonaProject>) => {
    try {
      const updatedProject = await buyerPersonaService.updateProject({ id, ...updates })
      setProjects(prev => prev.map(p => p.id === id ? updatedProject : p))
      if (currentProject?.id === id) {
        setCurrentProject(updatedProject)
      }
    } catch (error) {
      setProjectsError(error instanceof Error ? error.message : 'Failed to update project')
      throw error
    }
  }, [currentProject])

  const deleteProject = useCallback(async (id: string) => {
    try {
      await buyerPersonaService.deleteProject(id)
      setProjects(prev => prev.filter(p => p.id !== id))
      if (currentProject?.id === id) {
        setCurrentProject(null)
      }
    } catch (error) {
      setProjectsError(error instanceof Error ? error.message : 'Failed to delete project')
      throw error
    }
  }, [currentProject])

  const toggleProjectFavorite = useCallback(async (id: string, isFavorite: boolean) => {
    try {
      const updatedProject = await buyerPersonaService.toggleProjectFavorite(id, isFavorite)
      setProjects(prev => prev.map(p => p.id === id ? updatedProject : p))
      if (currentProject?.id === id) {
        setCurrentProject(updatedProject)
      }
    } catch (error) {
      console.error('Error toggling project favorite:', error)
    }
  }, [currentProject])

  // Persona operations
  const loadPersonasByProject = useCallback(async (projectId: string) => {
    try {
      setPersonasLoading(true)
      setPersonasError(null)
      const data = await buyerPersonaService.getPersonasByProject(projectId)
      setPersonas(data)
    } catch (error) {
      setPersonasError(error instanceof Error ? error.message : 'Failed to load personas')
      console.error('Error loading personas:', error)
    } finally {
      setPersonasLoading(false)
    }
  }, [])

  const loadPersona = useCallback(async (id: string) => {
    try {
      const persona = await buyerPersonaService.getPersonaById(id)
      setCurrentPersona(persona)
      if (persona) {
        await buyerPersonaService.updatePersonaView(id)
      }
    } catch (error) {
      console.error('Error loading persona:', error)
    }
  }, [])

  const createPersona = useCallback(async (data: CreateBuyerPersonaData): Promise<BuyerPersona> => {
    try {
      const persona = await buyerPersonaService.createPersona(data)
      setPersonas(prev => [persona, ...prev])
      return persona
    } catch (error) {
      setPersonasError(error instanceof Error ? error.message : 'Failed to create persona')
      throw error
    }
  }, [])

  const updatePersona = useCallback(async (id: string, updates: Partial<BuyerPersona>) => {
    try {
      const updatedPersona = await buyerPersonaService.updatePersona({ id, ...updates })
      setPersonas(prev => prev.map(p => p.id === id ? updatedPersona : p))
      if (currentPersona?.id === id) {
        setCurrentPersona(updatedPersona)
      }
    } catch (error) {
      setPersonasError(error instanceof Error ? error.message : 'Failed to update persona')
      throw error
    }
  }, [currentPersona])

  const deletePersona = useCallback(async (id: string) => {
    try {
      await buyerPersonaService.deletePersona(id)
      setPersonas(prev => prev.filter(p => p.id !== id))
      if (currentPersona?.id === id) {
        setCurrentPersona(null)
      }
    } catch (error) {
      setPersonasError(error instanceof Error ? error.message : 'Failed to delete persona')
      throw error
    }
  }, [currentPersona])

  const togglePersonaFavorite = useCallback(async (id: string, isFavorite: boolean) => {
    try {
      const updatedPersona = await buyerPersonaService.togglePersonaFavorite(id, isFavorite)
      setPersonas(prev => prev.map(p => p.id === id ? updatedPersona : p))
      if (currentPersona?.id === id) {
        setCurrentPersona(updatedPersona)
      }
    } catch (error) {
      console.error('Error toggling persona favorite:', error)
    }
  }, [currentPersona])

  // Conversation operations
  const loadConversationsByPersona = useCallback(async (personaId: string) => {
    try {
      setConversationsLoading(true)
      setConversationsError(null)
      const data = await buyerPersonaService.getConversationsByPersona(personaId)
      setConversations(data)
    } catch (error) {
      setConversationsError(error instanceof Error ? error.message : 'Failed to load conversations')
      console.error('Error loading conversations:', error)
    } finally {
      setConversationsLoading(false)
    }
  }, [])

  const loadConversationsByProject = useCallback(async (projectId: string) => {
    try {
      setConversationsLoading(true)
      setConversationsError(null)
      const data = await buyerPersonaService.getConversationsByProject(projectId)
      setConversations(data)
    } catch (error) {
      setConversationsError(error instanceof Error ? error.message : 'Failed to load conversations')
      console.error('Error loading conversations:', error)
    } finally {
      setConversationsLoading(false)
    }
  }, [])

  const loadConversation = useCallback(async (id: string) => {
    try {
      const conversation = await buyerPersonaService.getConversationById(id)
      setCurrentConversation(conversation)
    } catch (error) {
      console.error('Error loading conversation:', error)
    }
  }, [])

  const createConversation = useCallback(async (data: CreateBuyerPersonaConversationData): Promise<BuyerPersonaConversation> => {
    try {
      const conversation = await buyerPersonaService.createConversation(data)
      setConversations(prev => [conversation, ...prev])
      return conversation
    } catch (error) {
      setConversationsError(error instanceof Error ? error.message : 'Failed to create conversation')
      throw error
    }
  }, [])

  const updateConversation = useCallback(async (id: string, updates: Partial<BuyerPersonaConversation>) => {
    try {
      const updatedConversation = await buyerPersonaService.updateConversation({ id, ...updates })
      setConversations(prev => prev.map(c => c.id === id ? updatedConversation : c))
      if (currentConversation?.id === id) {
        setCurrentConversation(updatedConversation)
      }
    } catch (error) {
      setConversationsError(error instanceof Error ? error.message : 'Failed to update conversation')
      throw error
    }
  }, [currentConversation])

  const deleteConversation = useCallback(async (id: string) => {
    try {
      await buyerPersonaService.deleteConversation(id)
      setConversations(prev => prev.filter(c => c.id !== id))
      if (currentConversation?.id === id) {
        setCurrentConversation(null)
      }
    } catch (error) {
      setConversationsError(error instanceof Error ? error.message : 'Failed to delete conversation')
      throw error
    }
  }, [currentConversation])

  const addMessageToConversation = useCallback(async (conversationId: string, message: any) => {
    try {
      const updatedConversation = await buyerPersonaService.addMessageToConversation(conversationId, message)
      setConversations(prev => prev.map(c => c.id === conversationId ? updatedConversation : c))
      if (currentConversation?.id === conversationId) {
        setCurrentConversation(updatedConversation)
      }
    } catch (error) {
      setConversationsError(error instanceof Error ? error.message : 'Failed to add message')
      throw error
    }
  }, [currentConversation])

  // Statistics
  const loadUserStats = useCallback(async () => {
    try {
      const stats = await buyerPersonaService.getUserStats()
      setUserStats(stats)
    } catch (error) {
      console.error('Error loading user stats:', error)
    }
  }, [])

  // Refresh all data
  const refreshAll = useCallback(async () => {
    console.log('🔄 Refreshing all data...')
    await Promise.all([
      loadProjects(),
      loadUserStats()
    ])
    console.log('✅ All data refreshed')
  }, [loadProjects, loadUserStats])

  // Load initial data
  useEffect(() => {
    refreshAll()
  }, [refreshAll])

  return {
    // Projects
    projects,
    currentProject,
    projectsLoading,
    projectsError,
    
    // Personas
    personas,
    currentPersona,
    personasLoading,
    personasError,
    
    // Conversations
    conversations,
    currentConversation,
    conversationsLoading,
    conversationsError,
    
    // Statistics
    userStats,
    
    // Actions
    loadProjects,
    loadProject,
    createProject,
    updateProject,
    deleteProject,
    toggleProjectFavorite,
    setCurrentProject,
    
    loadPersonasByProject,
    loadPersona,
    createPersona,
    updatePersona,
    deletePersona,
    togglePersonaFavorite,
    
    loadConversationsByPersona,
    loadConversationsByProject,
    loadConversation,
    createConversation,
    updateConversation,
    deleteConversation,
    addMessageToConversation,
    
    loadUserStats,
    refreshAll
  }
}
