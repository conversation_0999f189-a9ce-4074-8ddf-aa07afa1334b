/**
 * Migration Manager for Buyer Persona Generator
 * Handles migration from localStorage to Supabase database
 */

import { buyerPersonaService } from '@/services/buyerPersonaService'
import { PersonaDataManager } from './data-manager'
import { PersonaHistoryManager } from './history-manager'
import type { GenerationResult, BuyerPersona, PremiumFeatureData } from '../types'

interface MigrationResult {
  success: boolean
  projectsCreated: number
  personasCreated: number
  conversationsCreated: number
  errors: string[]
  warnings: string[]
}

interface LocalStorageData {
  currentResult?: GenerationResult | null
  productDescription?: string | null
  premiumData?: PremiumFeatureData | null
  history: any[]
}

export class PersonaMigrationManager {
  private static readonly MIGRATION_FLAG_KEY = 'buyer_personas_migrated'
  private static readonly BACKUP_KEY = 'buyer_personas_backup'

  /**
   * Check if migration has already been completed
   */
  static isMigrationCompleted(): boolean {
    try {
      return localStorage.getItem(this.MIGRATION_FLAG_KEY) === 'true'
    } catch {
      return false
    }
  }

  /**
   * Mark migration as completed
   */
  static markMigrationCompleted(): void {
    try {
      localStorage.setItem(this.MIGRATION_FLAG_KEY, 'true')
    } catch (error) {
      console.error('Failed to mark migration as completed:', error)
    }
  }

  /**
   * Get all localStorage data for buyer personas
   */
  static getLocalStorageData(): LocalStorageData {
    return {
      currentResult: PersonaDataManager.loadGenerationResult(),
      productDescription: PersonaDataManager.loadProductDescription(),
      premiumData: PersonaDataManager.loadPremiumData(),
      history: PersonaHistoryManager.getAllHistory()
    }
  }

  /**
   * Create backup of localStorage data before migration
   */
  static createBackup(): void {
    try {
      const data = this.getLocalStorageData()
      localStorage.setItem(this.BACKUP_KEY, JSON.stringify({
        ...data,
        backupTimestamp: new Date().toISOString()
      }))
      console.log('✅ Backup created successfully')
    } catch (error) {
      console.error('❌ Failed to create backup:', error)
      throw new Error('Failed to create backup before migration')
    }
  }

  /**
   * Restore from backup if migration fails
   */
  static restoreFromBackup(): boolean {
    try {
      const backupData = localStorage.getItem(this.BACKUP_KEY)
      if (!backupData) {
        console.warn('No backup found to restore')
        return false
      }

      const backup = JSON.parse(backupData)
      
      // Restore individual items
      if (backup.currentResult) {
        PersonaDataManager.saveGenerationResult(backup.currentResult)
      }
      if (backup.productDescription) {
        PersonaDataManager.saveProductDescription(backup.productDescription)
      }
      if (backup.premiumData) {
        PersonaDataManager.savePremiumData(backup.premiumData)
      }
      if (backup.history && backup.history.length > 0) {
        localStorage.setItem('buyer_personas_history', JSON.stringify(backup.history))
      }

      console.log('✅ Restored from backup successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to restore from backup:', error)
      return false
    }
  }

  /**
   * Perform complete migration from localStorage to database
   */
  static async performMigration(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      projectsCreated: 0,
      personasCreated: 0,
      conversationsCreated: 0,
      errors: [],
      warnings: []
    }

    try {
      // Check if already migrated
      if (this.isMigrationCompleted()) {
        result.warnings.push('Migration already completed')
        result.success = true
        return result
      }

      // Create backup
      this.createBackup()

      // Get localStorage data
      const localData = this.getLocalStorageData()

      // Check if there's any data to migrate
      if (!localData.currentResult && localData.history.length === 0) {
        result.warnings.push('No data found to migrate')
        this.markMigrationCompleted()
        result.success = true
        return result
      }

      console.log('🚀 Starting buyer persona migration...')

      // Migrate current result if exists
      if (localData.currentResult) {
        try {
          await this.migrateGenerationResult(
            localData.currentResult,
            localData.premiumData,
            localData.productDescription,
            result
          )
        } catch (error) {
          result.errors.push(`Failed to migrate current result: ${error}`)
        }
      }

      // Migrate history items
      for (const historyItem of localData.history) {
        try {
          await this.migrateHistoryItem(historyItem, result)
        } catch (error) {
          result.errors.push(`Failed to migrate history item ${historyItem.id}: ${error}`)
        }
      }

      // Mark migration as completed if no critical errors
      if (result.errors.length === 0) {
        this.markMigrationCompleted()
        result.success = true
        console.log('✅ Migration completed successfully')
      } else {
        console.error('❌ Migration completed with errors:', result.errors)
        result.success = false
      }

    } catch (error) {
      result.errors.push(`Migration failed: ${error}`)
      result.success = false
      console.error('❌ Migration failed:', error)
      
      // Attempt to restore from backup
      this.restoreFromBackup()
    }

    return result
  }

  /**
   * Migrate a generation result to database
   */
  private static async migrateGenerationResult(
    generationResult: GenerationResult,
    premiumData: PremiumFeatureData | null,
    productDescription: string | null,
    result: MigrationResult
  ): Promise<void> {
    // Create project
    const projectData = {
      project_name: this.extractProjectName(generationResult, productDescription),
      description: 'Migrated from localStorage',
      product_description: generationResult.original_product_description || productDescription || 'No description available',
      industry: 'General',
      target_market: 'General Market',
      business_goals: 'Improve customer understanding',
      num_personas: generationResult.buyer_personas?.length || 3,
      tags: ['migrated', 'localStorage']
    }

    const project = await buyerPersonaService.createProject(projectData)
    result.projectsCreated++

    // Create personas
    if (generationResult.buyer_personas) {
      for (const persona of generationResult.buyer_personas) {
        try {
          await this.migratePersona(persona, project.id, generationResult, premiumData)
          result.personasCreated++
        } catch (error) {
          result.errors.push(`Failed to migrate persona ${persona.name}: ${error}`)
        }
      }
    }

    // Save generation prompt if available
    if (generationResult.request_id) {
      try {
        await this.migrateGenerationPrompt(generationResult, project.id)
      } catch (error) {
        result.warnings.push(`Failed to migrate generation prompt: ${error}`)
      }
    }
  }

  /**
   * Migrate a history item to database
   */
  private static async migrateHistoryItem(historyItem: any, result: MigrationResult): Promise<void> {
    if (!historyItem.result || !historyItem.result.buyer_personas) {
      result.warnings.push(`Skipping invalid history item: ${historyItem.id}`)
      return
    }

    await this.migrateGenerationResult(
      historyItem.result,
      historyItem.premiumData,
      historyItem.productName,
      result
    )
  }

  /**
   * Migrate a single persona to database
   */
  private static async migratePersona(
    persona: BuyerPersona,
    projectId: string,
    generationResult: GenerationResult,
    premiumData: PremiumFeatureData | null
  ): Promise<void> {
    const personaData = {
      project_id: projectId,
      persona_name: persona.name,
      age: persona.age,
      gender: persona.gender,
      location: persona.location,
      education: persona.education,
      income_level: persona.income_level,
      marital_status: persona.marital_status,
      job_info: persona.job || {},
      personal_background: persona.personal_background,
      goals: persona.goals || [],
      challenges: persona.challenges || [],
      objections: persona.objections || [],
      communication_channels: persona.communication_channels || [],
      influences: persona.influences || [],
      quotes: persona.quotes || [],
      typical_day: persona.typical_day,
      brand_affinities: persona.brand_affinities || [],
      buying_process: persona.buying_process || {},
      avatar_description: persona.avatar_description,
      avatar_url: premiumData?.avatars?.avatar_url || persona.avatar_url,
      avatar_id: persona.avatar_id,
      generation_prompt: 'Migrated from localStorage',
      ai_model_used: generationResult.ai_model_used || 'Unknown',
      generation_timestamp: generationResult.generation_timestamp?.toString() || new Date().toISOString(),
      notes: 'Migrated from localStorage'
    }

    await buyerPersonaService.createPersona(personaData)
  }

  /**
   * Migrate generation prompt to database
   */
  private static async migrateGenerationPrompt(
    generationResult: GenerationResult,
    projectId: string
  ): Promise<void> {
    // This would be implemented when generation prompts are stored
    // For now, we'll skip this as the current system doesn't store prompts
    console.log('Generation prompt migration skipped - not implemented yet')
  }

  /**
   * Extract project name from generation result
   */
  private static extractProjectName(
    generationResult: GenerationResult,
    productDescription?: string | null
  ): string {
    if (productDescription) {
      const firstLine = productDescription.split('\n')[0]
      if (firstLine && firstLine.length > 0) {
        return firstLine.substring(0, 50) + (firstLine.length > 50 ? '...' : '')
      }
    }

    if (generationResult.original_product_description) {
      const firstLine = generationResult.original_product_description.split('\n')[0]
      if (firstLine && firstLine.length > 0) {
        return firstLine.substring(0, 50) + (firstLine.length > 50 ? '...' : '')
      }
    }

    return `Buyer Personas - ${new Date().toLocaleDateString()}`
  }

  /**
   * Clear localStorage data after successful migration
   */
  static clearLocalStorageData(): void {
    try {
      localStorage.removeItem('buyer_personas_result')
      localStorage.removeItem('buyer_personas_product_description')
      localStorage.removeItem('buyer_personas_premium_data')
      localStorage.removeItem('buyer_personas_history')
      console.log('✅ localStorage data cleared after migration')
    } catch (error) {
      console.error('❌ Failed to clear localStorage data:', error)
    }
  }

  /**
   * Get migration status and statistics
   */
  static async getMigrationStatus(): Promise<{
    isCompleted: boolean
    hasLocalData: boolean
    localDataCount: number
    databaseDataCount: number
  }> {
    const isCompleted = this.isMigrationCompleted()
    const localData = this.getLocalStorageData()
    const hasLocalData = !!(localData.currentResult || localData.history.length > 0)
    const localDataCount = (localData.currentResult ? 1 : 0) + localData.history.length

    let databaseDataCount = 0
    try {
      const projects = await buyerPersonaService.getProjects()
      databaseDataCount = projects.length
    } catch {
      // Ignore errors when checking database
    }

    return {
      isCompleted,
      hasLocalData,
      localDataCount,
      databaseDataCount
    }
  }
}
