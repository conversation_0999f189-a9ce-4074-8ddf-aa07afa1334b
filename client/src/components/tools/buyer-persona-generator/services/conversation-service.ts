/**
 * Conversation Service for Buyer Persona Generator
 * Handles conversation management with database persistence
 */

import { buyerPersonaService } from '@/services/buyerPersonaService'
import type { 
  BuyerPersona, 
  BuyerPersonaConversation,
  CreateBuyerPersonaConversationData 
} from '@/services/buyerPersonaService'

export interface ConversationMessage {
  id: string
  sender: 'user' | 'persona'
  message: string
  timestamp: string
  persona_state?: {
    interest_level: number
    trust_level: number
    urgency_level: number
  }
}

export interface ConversationSession {
  conversation_id: string
  persona_name: string
  conversation_type: string
  status: 'active' | 'paused' | 'completed' | 'archived'
  created_at: string
  context: any
  messages: ConversationMessage[]
  analytics: {
    total_messages: number
    conversation_score: number
    key_topics_discussed: string[]
    objections_raised: string[]
    buying_signals: string[]
  }
}

export interface StartConversationRequest {
  persona_data: BuyerPersona
  conversation_type?: string
  context?: string
  product_description?: string
}

export interface ContinueConversationRequest {
  conversation_id: string
  user_message: string
  conversation_data: {
    context: any
    messages: ConversationMessage[]
  }
}

export class ConversationService {
  private static readonly API_BASE_URL = '/api/v1/premium/conversation'

  /**
   * Start a new conversation with a buyer persona
   */
  static async startConversation(request: StartConversationRequest): Promise<{
    status: string
    conversation: ConversationSession
    database_conversation?: BuyerPersonaConversation
  }> {
    try {
      // Call the backend API to start the conversation
      const response = await fetch(`${this.API_BASE_URL}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          persona_data: request.persona_data,
          conversation_type: request.conversation_type || 'sales',
          context: request.context || 'General conversation',
          product_info: {
            description: request.product_description || 'Product or service',
            category: 'software'
          }
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.status === 'success') {
        // Save conversation to database
        const databaseConversation = await this.saveConversationToDatabase(
          data.conversation,
          request.persona_data
        )

        return {
          ...data,
          database_conversation: databaseConversation
        }
      } else {
        throw new Error(data.error || 'Failed to start conversation')
      }
    } catch (error) {
      console.error('Error starting conversation:', error)
      throw error
    }
  }

  /**
   * Continue an existing conversation
   */
  static async continueConversation(request: ContinueConversationRequest): Promise<{
    status: string
    persona_response: string
    analysis?: any
    persona_state?: any
  }> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/continue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.status === 'success') {
        // Update conversation in database
        await this.updateConversationInDatabase(
          request.conversation_id,
          request.user_message,
          data.persona_response,
          data.persona_state
        )

        return data
      } else {
        throw new Error(data.error || 'Failed to continue conversation')
      }
    } catch (error) {
      console.error('Error continuing conversation:', error)
      throw error
    }
  }

  /**
   * Save conversation session to database
   */
  private static async saveConversationToDatabase(
    conversationSession: ConversationSession,
    persona: BuyerPersona
  ): Promise<BuyerPersonaConversation | null> {
    try {
      // Find or create a project for this persona
      const projects = await buyerPersonaService.getProjects()
      let project = projects.find(p => p.project_name.includes(persona.persona_name))

      if (!project) {
        // Create a new project for this conversation
        project = await buyerPersonaService.createProject({
          project_name: `Conversations with ${persona.persona_name}`,
          description: 'Auto-generated project for persona conversations',
          product_description: conversationSession.context?.product_info?.description || 'Product or service',
          industry: 'General',
          target_market: 'General Market',
          business_goals: 'Customer interaction and feedback',
          num_personas: 1,
          tags: ['conversation', 'auto-generated']
        })
      }

      // Create conversation record
      const conversationData: CreateBuyerPersonaConversationData = {
        project_id: project.id,
        persona_id: persona.id,
        conversation_title: `Conversation - ${new Date().toLocaleDateString()}`,
        conversation_context: JSON.stringify(conversationSession.context),
        messages: conversationSession.messages.map(msg => ({
          id: msg.id,
          sender: msg.sender,
          content: msg.message,
          timestamp: msg.timestamp,
          persona_state: msg.persona_state
        })),
        tags: ['active', conversationSession.conversation_type]
      }

      const conversation = await buyerPersonaService.createConversation(conversationData)
      
      // Store the mapping between API conversation ID and database ID
      this.storeConversationMapping(conversationSession.conversation_id, conversation.id)

      return conversation
    } catch (error) {
      console.error('Error saving conversation to database:', error)
      return null
    }
  }

  /**
   * Update conversation in database with new messages
   */
  private static async updateConversationInDatabase(
    apiConversationId: string,
    userMessage: string,
    personaResponse: string,
    personaState?: any
  ): Promise<void> {
    try {
      const databaseConversationId = this.getConversationMapping(apiConversationId)
      if (!databaseConversationId) {
        console.warn('No database conversation mapping found for:', apiConversationId)
        return
      }

      const conversation = await buyerPersonaService.getConversationById(databaseConversationId)
      if (!conversation) {
        console.warn('Database conversation not found:', databaseConversationId)
        return
      }

      // Add new messages
      const timestamp = new Date().toISOString()
      const newMessages = [
        ...conversation.messages,
        {
          id: `user_${Date.now()}`,
          sender: 'user',
          content: userMessage,
          timestamp: timestamp
        },
        {
          id: `persona_${Date.now() + 1}`,
          sender: 'persona',
          content: personaResponse,
          timestamp: timestamp,
          persona_state: personaState
        }
      ]

      // Use the new auto-save function for better performance
      await buyerPersonaService.autoSaveConversationMessage(databaseConversationId, {
        role: 'user',
        content: userMessage,
        timestamp: timestamp
      })

      await buyerPersonaService.autoSaveConversationMessage(databaseConversationId, {
        role: 'assistant',
        content: personaResponse,
        timestamp: timestamp
      })
    } catch (error) {
      console.error('Error updating conversation in database:', error)
    }
  }

  /**
   * Load conversation from database
   */
  static async loadConversationFromDatabase(conversationId: string): Promise<BuyerPersonaConversation | null> {
    try {
      return await buyerPersonaService.getConversationById(conversationId)
    } catch (error) {
      console.error('Error loading conversation from database:', error)
      return null
    }
  }

  /**
   * Get all conversations for a persona
   */
  static async getPersonaConversations(personaId: string): Promise<BuyerPersonaConversation[]> {
    try {
      return await buyerPersonaService.getConversationsByPersona(personaId)
    } catch (error) {
      console.error('Error getting persona conversations:', error)
      return []
    }
  }

  /**
   * Delete a conversation
   */
  static async deleteConversation(conversationId: string): Promise<void> {
    try {
      await buyerPersonaService.deleteConversation(conversationId)
    } catch (error) {
      console.error('Error deleting conversation:', error)
      throw error
    }
  }

  /**
   * Toggle conversation favorite status
   */
  static async toggleConversationFavorite(conversationId: string, isFavorite: boolean): Promise<void> {
    try {
      await buyerPersonaService.toggleConversationFavorite(conversationId, isFavorite)
    } catch (error) {
      console.error('Error toggling conversation favorite:', error)
      throw error
    }
  }

  /**
   * Store mapping between API conversation ID and database conversation ID
   */
  private static storeConversationMapping(apiId: string, databaseId: string): void {
    try {
      const mappings = JSON.parse(localStorage.getItem('conversation_mappings') || '{}')
      mappings[apiId] = databaseId
      localStorage.setItem('conversation_mappings', JSON.stringify(mappings))
    } catch (error) {
      console.error('Error storing conversation mapping:', error)
    }
  }

  /**
   * Get database conversation ID from API conversation ID
   */
  private static getConversationMapping(apiId: string): string | null {
    try {
      const mappings = JSON.parse(localStorage.getItem('conversation_mappings') || '{}')
      return mappings[apiId] || null
    } catch (error) {
      console.error('Error getting conversation mapping:', error)
      return null
    }
  }

  /**
   * Clear conversation mappings (for cleanup)
   */
  static clearConversationMappings(): void {
    try {
      localStorage.removeItem('conversation_mappings')
    } catch (error) {
      console.error('Error clearing conversation mappings:', error)
    }
  }
}
