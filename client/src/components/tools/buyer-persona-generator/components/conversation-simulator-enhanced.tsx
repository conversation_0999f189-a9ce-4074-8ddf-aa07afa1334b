/**
 * Enhanced Conversation Simulator with Database Integration
 * Replaces the original conversation simulator with database persistence
 */

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Send, 
  X, 
  User, 
  MessageSquare, 
  Heart,
  Archive,
  Trash2,
  Clock,
  TrendingUp,
  Shield,
  Zap
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'

import { ConversationService } from '../services/conversation-service'
import type { ConversationMessage, ConversationSession } from '../services/conversation-service'
import type { BuyerPersona, BuyerPersonaConversation } from '@/services/buyerPersonaService'

interface ConversationSimulatorEnhancedProps {
  persona: BuyerPersona
  productDescription?: string
  onClose: () => void
  onSaveToFavorites?: (conversationId: string) => void
}

export function ConversationSimulatorEnhanced({ 
  persona, 
  productDescription, 
  onClose,
  onSaveToFavorites 
}: ConversationSimulatorEnhancedProps) {
  const [messages, setMessages] = useState<ConversationMessage[]>([])
  const [currentMessage, setCurrentMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [conversationSession, setConversationSession] = useState<ConversationSession | null>(null)
  const [databaseConversation, setDatabaseConversation] = useState<BuyerPersonaConversation | null>(null)
  const [conversationStarted, setConversationStarted] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [personaState, setPersonaState] = useState({
    interest_level: 50,
    trust_level: 30,
    urgency_level: 20
  })

  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const getPersonaInitials = () => {
    return persona.persona_name
      .split(' ')
      .map(name => name[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const startConversation = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await ConversationService.startConversation({
        persona_data: persona,
        conversation_type: 'sales',
        context: 'Product inquiry and sales conversation',
        product_description: productDescription
      })

      if (response.status === 'success') {
        setConversationSession(response.conversation)
        setDatabaseConversation(response.database_conversation || null)
        setMessages(response.conversation.messages)
        setConversationStarted(true)

        // Update persona state if available
        const initialMessage = response.conversation.messages[0]
        if (initialMessage?.persona_state) {
          setPersonaState(initialMessage.persona_state)
        }
      }
    } catch (error) {
      console.error('Error starting conversation:', error)
      setError('Failed to start conversation. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const sendMessage = async () => {
    if (!currentMessage.trim() || !conversationSession || isLoading) return

    const messageToSend = currentMessage
    const userMessage: ConversationMessage = {
      id: `user_${Date.now()}`,
      sender: 'user',
      message: messageToSend,
      timestamp: new Date().toISOString()
    }

    setMessages(prev => [...prev, userMessage])
    setCurrentMessage('')
    setIsLoading(true)
    setError(null)

    try {
      const response = await ConversationService.continueConversation({
        conversation_id: conversationSession.conversation_id,
        user_message: messageToSend,
        conversation_data: {
          context: conversationSession.context,
          messages: [...messages, userMessage]
        }
      })

      if (response.status === 'success') {
        const personaMessage: ConversationMessage = {
          id: `persona_${Date.now()}`,
          sender: 'persona',
          message: response.persona_response,
          timestamp: new Date().toISOString(),
          persona_state: response.persona_state
        }

        setMessages(prev => [...prev, personaMessage])

        // Update persona state
        if (response.persona_state) {
          setPersonaState(response.persona_state)
        }

        // Update conversation session
        setConversationSession(prev => prev ? {
          ...prev,
          messages: [...prev.messages, userMessage, personaMessage],
          analytics: {
            ...prev.analytics,
            total_messages: prev.analytics.total_messages + 2
          }
        } : null)
      }
    } catch (error) {
      console.error('Error sending message:', error)
      setError('Failed to send message. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const toggleFavorite = async () => {
    if (!databaseConversation) return

    try {
      await ConversationService.toggleConversationFavorite(
        databaseConversation.id,
        !databaseConversation.is_favorite
      )
      
      setDatabaseConversation(prev => prev ? {
        ...prev,
        is_favorite: !prev.is_favorite
      } : null)

      onSaveToFavorites?.(databaseConversation.id)
    } catch (error) {
      console.error('Error toggling favorite:', error)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="w-full max-w-4xl h-[80vh] bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-12 w-12 border-2 border-white/20">
                <AvatarImage src={persona.avatar_url} />
                <AvatarFallback className="bg-white/20 text-white">
                  {getPersonaInitials()}
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-bold">{persona.persona_name}</h2>
                <p className="text-purple-100">
                  {persona.age} años • {persona.job_info?.title || 'Professional'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {databaseConversation && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleFavorite}
                  className="text-white hover:bg-white/20"
                >
                  <Heart className={`w-4 h-4 ${databaseConversation.is_favorite ? 'fill-current' : ''}`} />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-white hover:bg-white/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Persona State Indicators */}
          {conversationStarted && (
            <div className="mt-4 grid grid-cols-3 gap-4">
              <div className="bg-white/10 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <TrendingUp className="w-4 h-4" />
                  <span className="text-sm font-medium">Interest</span>
                </div>
                <Progress value={personaState.interest_level} className="h-2" />
                <span className="text-xs text-purple-100">{personaState.interest_level}%</span>
              </div>
              <div className="bg-white/10 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <Shield className="w-4 h-4" />
                  <span className="text-sm font-medium">Trust</span>
                </div>
                <Progress value={personaState.trust_level} className="h-2" />
                <span className="text-xs text-purple-100">{personaState.trust_level}%</span>
              </div>
              <div className="bg-white/10 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <Zap className="w-4 h-4" />
                  <span className="text-sm font-medium">Urgency</span>
                </div>
                <Progress value={personaState.urgency_level} className="h-2" />
                <span className="text-xs text-purple-100">{personaState.urgency_level}%</span>
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col">
          {!conversationStarted ? (
            /* Start Screen */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center max-w-md">
                <MessageSquare className="w-16 h-16 mx-auto mb-4 text-purple-600" />
                <h3 className="text-xl font-semibold mb-2">Start Conversation</h3>
                <p className="text-gray-600 mb-6">
                  Begin a realistic conversation with {persona.persona_name} to understand their needs and preferences.
                </p>
                <Button 
                  onClick={startConversation} 
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? 'Starting...' : 'Start Conversation'}
                </Button>
              </div>
            </div>
          ) : (
            <>
              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                <AnimatePresence>
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`flex items-start gap-3 max-w-[80%] ${
                        message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'
                      }`}>
                        <Avatar className="h-8 w-8 flex-shrink-0">
                          {message.sender === 'user' ? (
                            <AvatarFallback>
                              <User className="h-4 w-4" />
                            </AvatarFallback>
                          ) : (
                            <>
                              <AvatarImage src={persona.avatar_url} />
                              <AvatarFallback>
                                {getPersonaInitials()}
                              </AvatarFallback>
                            </>
                          )}
                        </Avatar>
                        <div className={`rounded-lg p-3 ${
                          message.sender === 'user'
                            ? 'bg-purple-600 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          <p className="text-sm">{message.message}</p>
                          <p className={`text-xs mt-1 ${
                            message.sender === 'user' ? 'text-purple-200' : 'text-gray-500'
                          }`}>
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                <div ref={messagesEndRef} />
              </div>

              {/* Error Display */}
              {error && (
                <div className="px-4">
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                </div>
              )}

              {/* Input Area */}
              <div className="border-t bg-gray-50 p-4">
                <div className="flex gap-3">
                  <Textarea
                    value={currentMessage}
                    onChange={(e) => setCurrentMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type your message..."
                    className="flex-1 min-h-[60px] resize-none"
                    disabled={isLoading}
                  />
                  <Button
                    onClick={sendMessage}
                    disabled={!currentMessage.trim() || isLoading}
                    className="self-end"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </motion.div>
  )
}
