/**
 * Main form container for buyer persona generation
 */

import { useState } from "react";
import { motion } from "framer-motion";
import { UseFormReturn } from "react-hook-form";
import { SmartForm } from "../../buyer-persona/smart-form";
import { FormData } from "../types";
import { ModeToggle } from "./mode-toggle";
import { HistoryButton } from "./history-button";
import { TraditionalForm } from "./traditional-form";

interface PersonaFormContainerProps {
  form: UseFormReturn<FormData>;
  onSubmit: (values: FormData) => void;
  isLoading: boolean;
  onShowHistory?: () => void;
  showModeToggle?: boolean;
  supportsDualModes?: boolean;
}

export function PersonaFormContainer({
  form,
  onSubmit,
  isLoading,
  onShowHistory,
  showModeToggle = true,
  supportsDualModes = true
}: PersonaFormContainerProps) {
  const [isSmartMode, setIsSmartMode] = useState(false);

  const handleSmartFormSubmit = (smartData: any) => {
    // Convert smart form data to expected format
    const formattedData: FormData = {
      product_description: smartData.product_description || `
Producto: ${smartData.product_name} (${smartData.product_type})
Industria: ${smartData.industry}
Audiencia objetivo: ${smartData.target_audience}
Problema que resuelve: ${smartData.main_problem}
Rango de precio: ${smartData.price_range}
Propuesta de valor única: ${smartData.unique_value || 'No especificada'}
      `.trim(),
      industry: smartData.industry,
      target_market: smartData.target_audience,
      business_goals: smartData.business_goals,
      competitors: smartData.competitors,
      num_personas: smartData.num_personas || 3,
      target_countries: smartData.target_country ? [smartData.target_country] : [],
    };

    onSubmit(formattedData);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="space-y-6"
    >
      {/* Mode Toggle and History Button */}
      <div className="w-full max-w-5xl mx-auto mb-6 space-y-4">
        {/* History Button */}
        {onShowHistory && (
          <HistoryButton onShowHistory={onShowHistory} />
        )}

        {/* Mode Toggle */}
        {showModeToggle && supportsDualModes && (
          <ModeToggle isSmartMode={isSmartMode} onToggle={setIsSmartMode} />
        )}
      </div>

      {/* Render Smart Form or Traditional Form */}
      {isSmartMode ? (
        <SmartForm onSubmit={handleSmartFormSubmit} isLoading={isLoading} />
      ) : (
        <TraditionalForm form={form} onSubmit={onSubmit} isLoading={isLoading} />
      )}
    </motion.div>
  );
}
