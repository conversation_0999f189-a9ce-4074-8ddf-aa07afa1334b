-- ===============================================
-- BUYER PERSONA GENERATOR - SCHEMA FIX MIGRATION
-- ===============================================
-- This script moves buyer persona tables to the 'api' schema
-- to fix the PGRST106 error (PostgREST only has 'api' schema enabled)

-- Create api schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS api;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA api TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA api TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA api TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA api TO postgres, anon, authenticated, service_role;

-- Move tables to api schema (if they exist in public)
DO $$
BEGIN
    -- Check if tables exist in public schema and move them
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'buyer_persona_projects') THEN
        ALTER TABLE public.buyer_persona_projects SET SCHEMA api;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'buyer_personas') THEN
        ALTER TABLE public.buyer_personas SET SCHEMA api;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'buyer_persona_conversations') THEN
        ALTER TABLE public.buyer_persona_conversations SET SCHEMA api;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'buyer_persona_generation_prompts') THEN
        ALTER TABLE public.buyer_persona_generation_prompts SET SCHEMA api;
    END IF;
END $$;

-- Create tables in api schema if they don't exist
CREATE TABLE IF NOT EXISTS api.buyer_persona_projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información básica del proyecto
  project_name TEXT NOT NULL,
  description TEXT,
  product_description TEXT NOT NULL,

  -- Información del mercado y negocio
  industry TEXT,
  target_market TEXT,
  business_goals TEXT,
  competitors TEXT,
  target_countries TEXT[] DEFAULT '{}',

  -- Configuración de generación
  num_personas INTEGER DEFAULT 3 CHECK (num_personas > 0 AND num_personas <= 10),
  generation_mode TEXT DEFAULT 'traditional' CHECK (generation_mode IN ('traditional', 'questions')),

  -- Estado y organización
  project_status TEXT DEFAULT 'active' CHECK (project_status IN ('active', 'archived', 'draft')),
  is_favorite BOOLEAN DEFAULT false,
  tags TEXT[] DEFAULT '{}',

  -- Estadísticas y contadores
  personas_count INTEGER DEFAULT 0,
  conversations_count INTEGER DEFAULT 0,
  last_accessed_at TIMESTAMP WITH TIME ZONE,
  view_count INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS api.buyer_personas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario y proyecto
  user_id TEXT NOT NULL,
  project_id UUID NOT NULL REFERENCES api.buyer_persona_projects(id) ON DELETE CASCADE,

  -- Información básica de la persona
  persona_name TEXT NOT NULL,
  age INTEGER NOT NULL,
  gender TEXT,
  location TEXT,
  education TEXT,
  income_level TEXT,
  marital_status TEXT,

  -- Información profesional (JSONB para flexibilidad)
  job_info JSONB DEFAULT '{}',

  -- Información psicográfica
  goals TEXT,
  challenges TEXT,
  objections TEXT,
  communication_channels TEXT,
  influences TEXT,
  quotes TEXT,

  -- Proceso de compra (JSONB para estructura compleja)
  buying_process JSONB DEFAULT '{}',

  -- Avatar y visualización
  avatar_description TEXT,
  avatar_url TEXT,
  avatar_id TEXT,

  -- Metadata de generación
  generation_prompt TEXT,
  ai_model_used TEXT,
  generation_timestamp TIMESTAMP WITH TIME ZONE,

  -- Estado y organización
  is_favorite BOOLEAN DEFAULT false,
  tags TEXT[] DEFAULT '{}',

  -- Estadísticas
  conversation_count INTEGER DEFAULT 0,
  last_conversation_at TIMESTAMP WITH TIME ZONE,
  view_count INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS api.buyer_persona_conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario y relaciones
  user_id TEXT NOT NULL,
  project_id UUID NOT NULL REFERENCES api.buyer_persona_projects(id) ON DELETE CASCADE,
  persona_id UUID NOT NULL REFERENCES api.buyer_personas(id) ON DELETE CASCADE,

  -- Información de la conversación
  conversation_title TEXT NOT NULL,
  conversation_context TEXT,

  -- Mensajes (JSONB array para flexibilidad)
  messages JSONB DEFAULT '[]',
  total_messages INTEGER DEFAULT 0,

  -- Estado de la conversación
  conversation_status TEXT DEFAULT 'active' CHECK (conversation_status IN ('active', 'paused', 'completed', 'archived')),

  -- Análisis y insights (para futuras funcionalidades)
  conversation_summary TEXT,
  key_insights TEXT,
  sentiment_analysis JSONB DEFAULT '{}',

  -- Estado y organización
  is_favorite BOOLEAN DEFAULT false,
  tags TEXT[] DEFAULT '{}',

  -- Estadísticas
  view_count INTEGER DEFAULT 0,
  last_message_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS api.buyer_persona_generation_prompts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,
  project_id UUID NOT NULL REFERENCES api.buyer_persona_projects(id) ON DELETE CASCADE,

  -- Información del prompt
  prompt_name TEXT NOT NULL,
  prompt_description TEXT,

  -- Contenido del prompt
  base_prompt TEXT NOT NULL,
  prompt_parameters JSONB DEFAULT '{}',

  -- Resultados de generación
  generation_results JSONB DEFAULT '{}',
  personas_generated INTEGER DEFAULT 0,

  -- Configuración y reutilización
  is_template BOOLEAN DEFAULT false,
  is_favorite BOOLEAN DEFAULT false,
  tags TEXT[] DEFAULT '{}',

  -- Estadísticas de uso
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMP WITH TIME ZONE,
  success_rate DECIMAL(5,2) DEFAULT 0.00
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_buyer_persona_projects_user_id ON api.buyer_persona_projects(user_id);
CREATE INDEX IF NOT EXISTS idx_buyer_persona_projects_created_at ON api.buyer_persona_projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_buyer_persona_projects_is_favorite ON api.buyer_persona_projects(user_id, is_favorite) WHERE is_favorite = true;

CREATE INDEX IF NOT EXISTS idx_buyer_personas_user_id ON api.buyer_personas(user_id);
CREATE INDEX IF NOT EXISTS idx_buyer_personas_project_id ON api.buyer_personas(project_id);
CREATE INDEX IF NOT EXISTS idx_buyer_personas_created_at ON api.buyer_personas(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_buyer_persona_conversations_user_id ON api.buyer_persona_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_buyer_persona_conversations_project_id ON api.buyer_persona_conversations(project_id);
CREATE INDEX IF NOT EXISTS idx_buyer_persona_conversations_persona_id ON api.buyer_persona_conversations(persona_id);

CREATE INDEX IF NOT EXISTS idx_buyer_persona_generation_prompts_user_id ON api.buyer_persona_generation_prompts(user_id);
CREATE INDEX IF NOT EXISTS idx_buyer_persona_generation_prompts_project_id ON api.buyer_persona_generation_prompts(project_id);

-- Enable RLS
ALTER TABLE api.buyer_persona_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE api.buyer_personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE api.buyer_persona_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE api.buyer_persona_generation_prompts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY IF NOT EXISTS "Users can view their own buyer persona projects" ON api.buyer_persona_projects
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own buyer persona projects" ON api.buyer_persona_projects
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own buyer persona projects" ON api.buyer_persona_projects
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own buyer persona projects" ON api.buyer_persona_projects
    FOR DELETE USING (auth.uid()::text = user_id);

-- Similar policies for other tables
CREATE POLICY IF NOT EXISTS "Users can view their own buyer personas" ON api.buyer_personas
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own buyer personas" ON api.buyer_personas
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own buyer personas" ON api.buyer_personas
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own buyer personas" ON api.buyer_personas
    FOR DELETE USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can view their own buyer persona conversations" ON api.buyer_persona_conversations
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own buyer persona conversations" ON api.buyer_persona_conversations
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own buyer persona conversations" ON api.buyer_persona_conversations
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own buyer persona conversations" ON api.buyer_persona_conversations
    FOR DELETE USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can view their own buyer persona generation prompts" ON api.buyer_persona_generation_prompts
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own buyer persona generation prompts" ON api.buyer_persona_generation_prompts
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own buyer persona generation prompts" ON api.buyer_persona_generation_prompts
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own buyer persona generation prompts" ON api.buyer_persona_generation_prompts
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER IF NOT EXISTS update_buyer_persona_projects_updated_at
    BEFORE UPDATE ON api.buyer_persona_projects
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_buyer_personas_updated_at
    BEFORE UPDATE ON api.buyer_personas
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_buyer_persona_conversations_updated_at
    BEFORE UPDATE ON api.buyer_persona_conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_buyer_persona_generation_prompts_updated_at
    BEFORE UPDATE ON api.buyer_persona_generation_prompts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL ON api.buyer_persona_projects TO postgres, anon, authenticated, service_role;
GRANT ALL ON api.buyer_personas TO postgres, anon, authenticated, service_role;
GRANT ALL ON api.buyer_persona_conversations TO postgres, anon, authenticated, service_role;
GRANT ALL ON api.buyer_persona_generation_prompts TO postgres, anon, authenticated, service_role;

-- Verify the migration
SELECT 'Migration completed successfully. Tables are now in api schema.' as status;
