/**
 * Database Connection Test Script
 * Run this in the browser console to test the Supabase database integration
 */

console.log('🧪 Testing Buyer Persona Generator Database Integration');
console.log('='.repeat(60));

async function testDatabaseIntegration() {
  try {
    // Test 1: Import Supabase clients
    console.log('\n📋 Test 1: Importing Supabase clients...');
    const { supabase, supabaseApi } = await import('/src/lib/supabase.ts');
    console.log('✅ Supabase clients imported successfully');
    
    // Test 2: Check authentication
    console.log('\n📋 Test 2: Authentication check...');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('❌ Authentication failed:', authError?.message || 'No user found');
      console.log('💡 Please log in and try again');
      return false;
    }
    
    console.log('✅ User authenticated:', user.email);
    console.log('✅ User ID:', user.id);
    
    // Test 3: Import buyer persona service
    console.log('\n📋 Test 3: Importing buyer persona service...');
    const { buyerPersonaService } = await import('/src/services/buyerPersonaService.ts');
    console.log('✅ Buyer persona service imported successfully');
    
    // Test 4: Test database connection
    console.log('\n📋 Test 4: Testing database connection...');
    const projects = await buyerPersonaService.getProjects();
    console.log('✅ Database connection successful');
    console.log(`✅ Found ${projects.length} projects for user`);
    
    // Test 5: Test user stats
    console.log('\n📋 Test 5: Testing user statistics...');
    const stats = await buyerPersonaService.getUserStats();
    console.log('✅ User stats retrieved successfully');
    console.log('📊 Stats:', {
      totalProjects: stats.totalProjects,
      totalPersonas: stats.totalPersonas,
      totalConversations: stats.totalConversations
    });
    
    // Test 6: Test RLS (Row Level Security)
    console.log('\n📋 Test 6: Testing Row Level Security...');
    
    // Direct query to verify RLS is working
    const { data: directProjects, error: directError } = await supabase
      .from('buyer_persona_projects')
      .select('*')
      .limit(5);
    
    if (directError) {
      console.log('❌ Direct query failed:', directError.message);
      return false;
    }
    
    // Verify all returned projects belong to current user
    const invalidProjects = directProjects.filter(p => p.user_id !== user.id);
    if (invalidProjects.length > 0) {
      console.log('❌ RLS Policy Failed: Found projects from other users');
      return false;
    }
    
    console.log('✅ RLS Policy Working: All projects belong to current user');
    console.log(`✅ Direct query returned ${directProjects.length} projects`);
    
    // Test 7: Test migration status
    console.log('\n📋 Test 7: Testing migration functionality...');
    const { PersonaMigrationManager } = await import('/src/components/tools/buyer-persona-generator/utils/migration-manager.ts');
    const migrationStatus = await PersonaMigrationManager.getMigrationStatus();
    console.log('✅ Migration status retrieved successfully');
    console.log('📊 Migration Status:', {
      hasLocalData: migrationStatus.hasLocalData,
      isCompleted: migrationStatus.isCompleted,
      localDataCount: migrationStatus.localDataCount,
      databaseDataCount: migrationStatus.databaseDataCount
    });
    
    // Test 8: Performance test
    console.log('\n📋 Test 8: Performance testing...');
    const startTime = Date.now();
    await buyerPersonaService.getProjects();
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ Query performance: ${duration}ms`);
    if (duration < 1000) {
      console.log('✅ Performance: Excellent (< 1s)');
    } else if (duration < 3000) {
      console.log('⚠️ Performance: Good (< 3s)');
    } else {
      console.log('❌ Performance: Slow (> 3s)');
    }
    
    console.log('\n🎉 All tests passed! Database integration is working correctly.');
    return true;
    
  } catch (error) {
    console.log('❌ Test failed with error:', error.message);
    console.error('Full error:', error);
    return false;
  }
}

// Test for GoTrueClient instances
async function testGoTrueClientInstances() {
  console.log('\n🔍 Testing GoTrueClient instances...');
  
  try {
    const { supabase, supabaseApi } = await import('/src/lib/supabase.ts');
    
    // Check if both clients are using the same auth instance
    console.log('Main client auth:', !!supabase.auth);
    console.log('API client auth:', !!supabaseApi.auth);
    
    // Check if they have the same session
    const mainSession = await supabase.auth.getSession();
    const apiSession = await supabaseApi.auth.getSession();
    
    console.log('Main client session:', !!mainSession.data.session);
    console.log('API client session:', !!apiSession.data.session);
    
    if (mainSession.data.session && apiSession.data.session) {
      const sameUser = mainSession.data.session.user.id === apiSession.data.session.user.id;
      console.log('Same user in both clients:', sameUser ? '✅' : '❌');
    }
    
    console.log('✅ GoTrueClient test completed');
    
  } catch (error) {
    console.log('❌ GoTrueClient test failed:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting comprehensive database integration tests...\n');
  
  const dbTestResult = await testDatabaseIntegration();
  await testGoTrueClientInstances();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL RESULT');
  console.log('='.repeat(60));
  
  if (dbTestResult) {
    console.log('🎉 SUCCESS: Database integration is working correctly!');
    console.log('✅ Supabase connection established');
    console.log('✅ Authentication working');
    console.log('✅ RLS policies enforced');
    console.log('✅ Service layer functional');
    console.log('✅ Migration system ready');
  } else {
    console.log('❌ FAILURE: Database integration has issues');
    console.log('🔧 Please check the errors above and fix them');
  }
  
  console.log('\n💡 Next steps:');
  console.log('1. Create a test project: buyerPersonaService.createProject({...})');
  console.log('2. Test persona generation workflow');
  console.log('3. Test conversation functionality');
  console.log('4. Test migration if you have localStorage data');
}

// Auto-run tests
runAllTests();

// Export functions for manual testing
window.buyerPersonaDatabaseTests = {
  runAllTests,
  testDatabaseIntegration,
  testGoTrueClientInstances
};

console.log('\n📝 Manual test functions available in window.buyerPersonaDatabaseTests');
console.log('Use window.buyerPersonaDatabaseTests.runAllTests() to run tests again');
