/**
 * Database Validation Script
 * Comprehensive validation of database schema integrity and functionality
 */

import { supabase } from '@/lib/supabase'
import { buyerPersonaService } from '@/services/buyerPersonaService'
import { PersonaMigrationManager } from '../utils/migration-manager'

interface ValidationResult {
  test: string
  status: 'PASS' | 'FAIL' | 'WARNING'
  message: string
  details?: any
}

interface ValidationReport {
  summary: {
    total: number
    passed: number
    failed: number
    warnings: number
  }
  results: ValidationResult[]
  timestamp: string
}

export class DatabaseValidator {
  private results: ValidationResult[] = []

  /**
   * Run comprehensive database validation
   */
  async runValidation(): Promise<ValidationReport> {
    console.log('🔍 Starting database validation...')
    this.results = []

    try {
      await this.validateAuthentication()
      await this.validateSchemaIntegrity()
      await this.validateRLSPolicies()
      await this.validateCRUDOperations()
      await this.validateDataIntegrity()
      await this.validateMigrationFunctionality()
      await this.validateErrorHandling()
      await this.validatePerformance()
    } catch (error) {
      this.addResult('VALIDATION_FRAMEWORK', 'FAIL', `Validation framework error: ${error}`)
    }

    return this.generateReport()
  }

  /**
   * Validate user authentication
   */
  private async validateAuthentication(): Promise<void> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error) {
        this.addResult('AUTH_USER_CHECK', 'FAIL', `Authentication error: ${error.message}`)
        return
      }

      if (!user) {
        this.addResult('AUTH_USER_CHECK', 'WARNING', 'No authenticated user found')
        return
      }

      this.addResult('AUTH_USER_CHECK', 'PASS', `User authenticated: ${user.email}`)

      // Test service authentication
      try {
        await buyerPersonaService.getProjects()
        this.addResult('AUTH_SERVICE_CHECK', 'PASS', 'Service authentication working')
      } catch (error) {
        this.addResult('AUTH_SERVICE_CHECK', 'FAIL', `Service authentication failed: ${error}`)
      }
    } catch (error) {
      this.addResult('AUTH_VALIDATION', 'FAIL', `Authentication validation failed: ${error}`)
    }
  }

  /**
   * Validate database schema integrity
   */
  private async validateSchemaIntegrity(): Promise<void> {
    const tables = [
      'buyer_persona_projects',
      'buyer_personas',
      'buyer_persona_conversations',
      'buyer_persona_generation_prompts'
    ]

    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1)

        if (error) {
          this.addResult(`SCHEMA_${table.toUpperCase()}`, 'FAIL', `Table ${table} not accessible: ${error.message}`)
        } else {
          this.addResult(`SCHEMA_${table.toUpperCase()}`, 'PASS', `Table ${table} accessible`)
        }
      } catch (error) {
        this.addResult(`SCHEMA_${table.toUpperCase()}`, 'FAIL', `Table ${table} validation failed: ${error}`)
      }
    }

    // Validate indexes exist
    try {
      const { data, error } = await supabase.rpc('check_indexes_exist', {
        table_names: tables
      })

      if (error) {
        this.addResult('SCHEMA_INDEXES', 'WARNING', `Could not verify indexes: ${error.message}`)
      } else {
        this.addResult('SCHEMA_INDEXES', 'PASS', 'Database indexes verified')
      }
    } catch (error) {
      this.addResult('SCHEMA_INDEXES', 'WARNING', `Index validation not available: ${error}`)
    }
  }

  /**
   * Validate RLS policies
   */
  private async validateRLSPolicies(): Promise<void> {
    try {
      // Test that RLS is enabled
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        this.addResult('RLS_VALIDATION', 'WARNING', 'Cannot test RLS without authenticated user')
        return
      }

      // Test project isolation
      try {
        const projects = await buyerPersonaService.getProjects()
        this.addResult('RLS_PROJECT_ISOLATION', 'PASS', `Retrieved ${projects.length} user projects`)
      } catch (error) {
        this.addResult('RLS_PROJECT_ISOLATION', 'FAIL', `Project RLS failed: ${error}`)
      }

      // Test persona isolation
      try {
        const { data, error } = await supabase
          .from('buyer_personas')
          .select('*')
          .limit(10)

        if (error) {
          this.addResult('RLS_PERSONA_ISOLATION', 'FAIL', `Persona RLS failed: ${error.message}`)
        } else {
          // Verify all returned personas belong to current user
          const invalidPersonas = data.filter(p => p.user_id !== user.id)
          if (invalidPersonas.length > 0) {
            this.addResult('RLS_PERSONA_ISOLATION', 'FAIL', `Found ${invalidPersonas.length} personas from other users`)
          } else {
            this.addResult('RLS_PERSONA_ISOLATION', 'PASS', `All ${data.length} personas belong to current user`)
          }
        }
      } catch (error) {
        this.addResult('RLS_PERSONA_ISOLATION', 'FAIL', `Persona RLS validation failed: ${error}`)
      }

      // Test conversation isolation
      try {
        const { data, error } = await supabase
          .from('buyer_persona_conversations')
          .select('*')
          .limit(10)

        if (error) {
          this.addResult('RLS_CONVERSATION_ISOLATION', 'FAIL', `Conversation RLS failed: ${error.message}`)
        } else {
          const invalidConversations = data.filter(c => c.user_id !== user.id)
          if (invalidConversations.length > 0) {
            this.addResult('RLS_CONVERSATION_ISOLATION', 'FAIL', `Found ${invalidConversations.length} conversations from other users`)
          } else {
            this.addResult('RLS_CONVERSATION_ISOLATION', 'PASS', `All ${data.length} conversations belong to current user`)
          }
        }
      } catch (error) {
        this.addResult('RLS_CONVERSATION_ISOLATION', 'FAIL', `Conversation RLS validation failed: ${error}`)
      }
    } catch (error) {
      this.addResult('RLS_VALIDATION', 'FAIL', `RLS validation failed: ${error}`)
    }
  }

  /**
   * Validate CRUD operations
   */
  private async validateCRUDOperations(): Promise<void> {
    try {
      // Test project CRUD
      const testProject = {
        project_name: 'Validation Test Project',
        description: 'Test project for validation',
        product_description: 'Test product for validation purposes',
        industry: 'Testing',
        target_market: 'Validators',
        business_goals: 'Ensure system works',
        num_personas: 1,
        tags: ['validation', 'test']
      }

      let createdProject
      try {
        createdProject = await buyerPersonaService.createProject(testProject)
        this.addResult('CRUD_PROJECT_CREATE', 'PASS', `Project created with ID: ${createdProject.id}`)
      } catch (error) {
        this.addResult('CRUD_PROJECT_CREATE', 'FAIL', `Project creation failed: ${error}`)
        return
      }

      // Test project read
      try {
        const retrievedProject = await buyerPersonaService.getProjectById(createdProject.id)
        if (retrievedProject && retrievedProject.project_name === testProject.project_name) {
          this.addResult('CRUD_PROJECT_READ', 'PASS', 'Project retrieved successfully')
        } else {
          this.addResult('CRUD_PROJECT_READ', 'FAIL', 'Project data mismatch')
        }
      } catch (error) {
        this.addResult('CRUD_PROJECT_READ', 'FAIL', `Project read failed: ${error}`)
      }

      // Test project update
      try {
        const updatedProject = await buyerPersonaService.updateProject(createdProject.id, {
          project_name: 'Updated Validation Test Project'
        })
        if (updatedProject.project_name === 'Updated Validation Test Project') {
          this.addResult('CRUD_PROJECT_UPDATE', 'PASS', 'Project updated successfully')
        } else {
          this.addResult('CRUD_PROJECT_UPDATE', 'FAIL', 'Project update failed')
        }
      } catch (error) {
        this.addResult('CRUD_PROJECT_UPDATE', 'FAIL', `Project update failed: ${error}`)
      }

      // Test persona CRUD
      const testPersona = {
        project_id: createdProject.id,
        persona_name: 'Validation Test Persona',
        age: 30,
        job_info: { title: 'Validator' },
        buying_process: { awareness: 'Testing' }
      }

      let createdPersona
      try {
        createdPersona = await buyerPersonaService.createPersona(testPersona)
        this.addResult('CRUD_PERSONA_CREATE', 'PASS', `Persona created with ID: ${createdPersona.id}`)
      } catch (error) {
        this.addResult('CRUD_PERSONA_CREATE', 'FAIL', `Persona creation failed: ${error}`)
      }

      // Test conversation CRUD
      if (createdPersona) {
        const testConversation = {
          project_id: createdProject.id,
          persona_id: createdPersona.id,
          conversation_title: 'Validation Test Conversation',
          messages: [
            {
              id: 'test-msg-1',
              sender: 'user',
              content: 'Hello test persona',
              timestamp: new Date().toISOString()
            }
          ]
        }

        try {
          const createdConversation = await buyerPersonaService.createConversation(testConversation)
          this.addResult('CRUD_CONVERSATION_CREATE', 'PASS', `Conversation created with ID: ${createdConversation.id}`)

          // Test message addition
          try {
            await buyerPersonaService.addMessageToConversation(createdConversation.id, {
              role: 'assistant',
              content: 'Hello back!',
              timestamp: new Date().toISOString()
            })
            this.addResult('CRUD_MESSAGE_ADD', 'PASS', 'Message added to conversation')
          } catch (error) {
            this.addResult('CRUD_MESSAGE_ADD', 'FAIL', `Message addition failed: ${error}`)
          }
        } catch (error) {
          this.addResult('CRUD_CONVERSATION_CREATE', 'FAIL', `Conversation creation failed: ${error}`)
        }
      }

      // Cleanup test data
      try {
        await buyerPersonaService.deleteProject(createdProject.id)
        this.addResult('CRUD_PROJECT_DELETE', 'PASS', 'Test project deleted successfully')
      } catch (error) {
        this.addResult('CRUD_PROJECT_DELETE', 'FAIL', `Project deletion failed: ${error}`)
      }
    } catch (error) {
      this.addResult('CRUD_VALIDATION', 'FAIL', `CRUD validation failed: ${error}`)
    }
  }

  /**
   * Validate data integrity
   */
  private async validateDataIntegrity(): Promise<void> {
    try {
      // Check for orphaned records
      const { data: orphanedPersonas, error: personaError } = await supabase
        .from('buyer_personas')
        .select('id, project_id')
        .not('project_id', 'in', `(SELECT id FROM buyer_persona_projects)`)

      if (personaError) {
        this.addResult('DATA_INTEGRITY_PERSONAS', 'WARNING', `Could not check persona integrity: ${personaError.message}`)
      } else if (orphanedPersonas && orphanedPersonas.length > 0) {
        this.addResult('DATA_INTEGRITY_PERSONAS', 'FAIL', `Found ${orphanedPersonas.length} orphaned personas`)
      } else {
        this.addResult('DATA_INTEGRITY_PERSONAS', 'PASS', 'No orphaned personas found')
      }

      // Check for orphaned conversations
      const { data: orphanedConversations, error: conversationError } = await supabase
        .from('buyer_persona_conversations')
        .select('id, persona_id')
        .not('persona_id', 'in', `(SELECT id FROM buyer_personas)`)

      if (conversationError) {
        this.addResult('DATA_INTEGRITY_CONVERSATIONS', 'WARNING', `Could not check conversation integrity: ${conversationError.message}`)
      } else if (orphanedConversations && orphanedConversations.length > 0) {
        this.addResult('DATA_INTEGRITY_CONVERSATIONS', 'FAIL', `Found ${orphanedConversations.length} orphaned conversations`)
      } else {
        this.addResult('DATA_INTEGRITY_CONVERSATIONS', 'PASS', 'No orphaned conversations found')
      }
    } catch (error) {
      this.addResult('DATA_INTEGRITY', 'FAIL', `Data integrity validation failed: ${error}`)
    }
  }

  /**
   * Validate migration functionality
   */
  private async validateMigrationFunctionality(): Promise<void> {
    try {
      const migrationStatus = await PersonaMigrationManager.getMigrationStatus()
      this.addResult('MIGRATION_STATUS_CHECK', 'PASS', `Migration status retrieved: ${JSON.stringify(migrationStatus)}`)

      // Test backup functionality
      try {
        PersonaMigrationManager.createBackup()
        this.addResult('MIGRATION_BACKUP', 'PASS', 'Backup creation successful')
      } catch (error) {
        this.addResult('MIGRATION_BACKUP', 'FAIL', `Backup creation failed: ${error}`)
      }

      // Test restore functionality
      try {
        const restored = PersonaMigrationManager.restoreFromBackup()
        this.addResult('MIGRATION_RESTORE', 'PASS', `Restore functionality working: ${restored}`)
      } catch (error) {
        this.addResult('MIGRATION_RESTORE', 'FAIL', `Restore functionality failed: ${error}`)
      }
    } catch (error) {
      this.addResult('MIGRATION_VALIDATION', 'FAIL', `Migration validation failed: ${error}`)
    }
  }

  /**
   * Validate error handling
   */
  private async validateErrorHandling(): Promise<void> {
    try {
      // Test invalid project creation
      try {
        await buyerPersonaService.createProject({
          project_name: '', // Invalid empty name
          product_description: 'Test'
        })
        this.addResult('ERROR_HANDLING_VALIDATION', 'FAIL', 'Invalid project creation should have failed')
      } catch (error) {
        this.addResult('ERROR_HANDLING_VALIDATION', 'PASS', 'Invalid project creation properly rejected')
      }

      // Test non-existent record access
      try {
        const nonExistentProject = await buyerPersonaService.getProjectById('non-existent-id')
        if (nonExistentProject === null) {
          this.addResult('ERROR_HANDLING_NOT_FOUND', 'PASS', 'Non-existent records return null')
        } else {
          this.addResult('ERROR_HANDLING_NOT_FOUND', 'FAIL', 'Non-existent records should return null')
        }
      } catch (error) {
        this.addResult('ERROR_HANDLING_NOT_FOUND', 'WARNING', `Non-existent record access threw error: ${error}`)
      }
    } catch (error) {
      this.addResult('ERROR_HANDLING', 'FAIL', `Error handling validation failed: ${error}`)
    }
  }

  /**
   * Validate performance
   */
  private async validatePerformance(): Promise<void> {
    try {
      const startTime = Date.now()
      await buyerPersonaService.getProjects()
      const endTime = Date.now()
      const duration = endTime - startTime

      if (duration < 1000) {
        this.addResult('PERFORMANCE_PROJECT_LOAD', 'PASS', `Project loading took ${duration}ms`)
      } else if (duration < 3000) {
        this.addResult('PERFORMANCE_PROJECT_LOAD', 'WARNING', `Project loading took ${duration}ms (slow)`)
      } else {
        this.addResult('PERFORMANCE_PROJECT_LOAD', 'FAIL', `Project loading took ${duration}ms (too slow)`)
      }

      // Test user stats performance
      const statsStartTime = Date.now()
      await buyerPersonaService.getUserStats()
      const statsEndTime = Date.now()
      const statsDuration = statsEndTime - statsStartTime

      if (statsDuration < 2000) {
        this.addResult('PERFORMANCE_STATS_LOAD', 'PASS', `Stats loading took ${statsDuration}ms`)
      } else {
        this.addResult('PERFORMANCE_STATS_LOAD', 'WARNING', `Stats loading took ${statsDuration}ms (slow)`)
      }
    } catch (error) {
      this.addResult('PERFORMANCE_VALIDATION', 'FAIL', `Performance validation failed: ${error}`)
    }
  }

  /**
   * Add validation result
   */
  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any): void {
    this.results.push({ test, status, message, details })
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️'
    console.log(`${emoji} ${test}: ${message}`)
  }

  /**
   * Generate validation report
   */
  private generateReport(): ValidationReport {
    const summary = {
      total: this.results.length,
      passed: this.results.filter(r => r.status === 'PASS').length,
      failed: this.results.filter(r => r.status === 'FAIL').length,
      warnings: this.results.filter(r => r.status === 'WARNING').length
    }

    return {
      summary,
      results: this.results,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Run database validation and return report
 */
export async function validateDatabase(): Promise<ValidationReport> {
  const validator = new DatabaseValidator()
  return await validator.runValidation()
}

/**
 * CLI function to run validation
 */
export async function runValidationCLI(): Promise<void> {
  console.log('🚀 Starting Buyer Persona Database Validation')
  console.log('=' .repeat(50))

  const report = await validateDatabase()

  console.log('\n📊 VALIDATION SUMMARY')
  console.log('=' .repeat(50))
  console.log(`Total Tests: ${report.summary.total}`)
  console.log(`✅ Passed: ${report.summary.passed}`)
  console.log(`❌ Failed: ${report.summary.failed}`)
  console.log(`⚠️  Warnings: ${report.summary.warnings}`)

  if (report.summary.failed > 0) {
    console.log('\n❌ FAILED TESTS')
    console.log('-'.repeat(30))
    report.results
      .filter(r => r.status === 'FAIL')
      .forEach(r => console.log(`• ${r.test}: ${r.message}`))
  }

  if (report.summary.warnings > 0) {
    console.log('\n⚠️  WARNINGS')
    console.log('-'.repeat(30))
    report.results
      .filter(r => r.status === 'WARNING')
      .forEach(r => console.log(`• ${r.test}: ${r.message}`))
  }

  console.log(`\n🕐 Validation completed at: ${report.timestamp}`)
  
  if (report.summary.failed === 0) {
    console.log('🎉 All critical tests passed!')
  } else {
    console.log('🔧 Please address failed tests before proceeding.')
    process.exit(1)
  }
}
