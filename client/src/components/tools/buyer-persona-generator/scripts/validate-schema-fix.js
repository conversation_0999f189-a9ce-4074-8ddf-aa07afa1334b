/**
 * Schema Fix Validation Script
 * Tests the database schema fix and dual generation modes
 */

console.log('🔧 Validating Buyer Persona Generator Schema Fix');
console.log('='.repeat(60));

async function validateSchemaFix() {
  try {
    // Test 1: Import Supabase client with api schema
    console.log('\n📋 Test 1: Testing Supabase client with api schema...');
    const { supabase } = await import('/src/lib/supabase.ts');
    console.log('✅ Supabase client imported successfully');
    
    // Test 2: Check authentication
    console.log('\n📋 Test 2: Authentication check...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('❌ Authentication failed:', authError?.message || 'No user found');
      console.log('💡 Please log in and try again');
      return false;
    }
    
    console.log('✅ User authenticated:', user.email);
    
    // Test 3: Test direct API call to buyer_persona_projects
    console.log('\n📋 Test 3: Testing direct API call to buyer_persona_projects...');
    
    try {
      const { data: projects, error: projectError } = await supabase
        .from('buyer_persona_projects')
        .select('*')
        .limit(5);
      
      if (projectError) {
        console.log('❌ Direct API call failed:', projectError.message);
        console.log('Error code:', projectError.code);
        console.log('Error details:', projectError.details);
        
        if (projectError.code === 'PGRST106') {
          console.log('🚨 CRITICAL: Still getting PGRST106 error');
          console.log('💡 Solution needed: Run the schema migration script in Supabase');
          return false;
        }
      } else {
        console.log('✅ Direct API call successful');
        console.log(`✅ Found ${projects.length} projects`);
      }
    } catch (error) {
      console.log('❌ API call exception:', error.message);
      return false;
    }
    
    // Test 4: Test buyer persona service
    console.log('\n📋 Test 4: Testing buyer persona service...');
    
    try {
      const { buyerPersonaService } = await import('/src/services/buyerPersonaService.ts');
      const projects = await buyerPersonaService.getProjects();
      console.log('✅ Buyer persona service working');
      console.log(`✅ Service returned ${projects.length} projects`);
    } catch (error) {
      console.log('❌ Buyer persona service failed:', error.message);
      return false;
    }
    
    // Test 5: Test project creation
    console.log('\n📋 Test 5: Testing project creation...');
    
    try {
      const { buyerPersonaService } = await import('/src/services/buyerPersonaService.ts');
      
      const testProject = {
        project_name: 'Schema Fix Test Project',
        product_description: 'Test project to validate schema fix',
        industry: 'Technology',
        target_market: 'Developers',
        business_goals: 'Test database integration',
        num_personas: 2,
        generation_mode: 'traditional'
      };
      
      const createdProject = await buyerPersonaService.createProject(testProject);
      console.log('✅ Project creation successful');
      console.log('✅ Created project ID:', createdProject.id);
      
      // Clean up test project
      await buyerPersonaService.deleteProject(createdProject.id);
      console.log('✅ Test project cleaned up');
      
    } catch (error) {
      console.log('❌ Project creation failed:', error.message);
      console.log('Error details:', error);
      return false;
    }
    
    console.log('\n🎉 Schema fix validation successful!');
    return true;
    
  } catch (error) {
    console.log('❌ Schema fix validation failed:', error.message);
    console.error('Full error:', error);
    return false;
  }
}

async function validateDualGenerationModes() {
  console.log('\n🔄 Validating Dual Generation Modes');
  console.log('-'.repeat(40));
  
  try {
    // Test 1: Check if mode toggle component exists
    console.log('\n📋 Test 1: Checking mode toggle component...');
    
    try {
      const { ModeToggle } = await import('/src/components/tools/buyer-persona-generator/components/mode-toggle.tsx');
      console.log('✅ ModeToggle component imported successfully');
    } catch (error) {
      console.log('❌ ModeToggle component not found:', error.message);
      return false;
    }
    
    // Test 2: Check if traditional form exists
    console.log('\n📋 Test 2: Checking traditional form component...');
    
    try {
      const { TraditionalForm } = await import('/src/components/tools/buyer-persona-generator/components/traditional-form.tsx');
      console.log('✅ TraditionalForm component imported successfully');
    } catch (error) {
      console.log('❌ TraditionalForm component not found:', error.message);
      return false;
    }
    
    // Test 3: Check if smart form exists
    console.log('\n📋 Test 3: Checking smart form component...');
    
    try {
      const { SmartForm } = await import('/src/components/tools/buyer-persona/smart-form.tsx');
      console.log('✅ SmartForm component imported successfully');
    } catch (error) {
      console.log('❌ SmartForm component not found:', error.message);
      return false;
    }
    
    // Test 4: Check if persona form container supports dual modes
    console.log('\n📋 Test 4: Checking persona form container...');
    
    try {
      const { PersonaFormContainer } = await import('/src/components/tools/buyer-persona-generator/components/persona-form-container.tsx');
      console.log('✅ PersonaFormContainer component imported successfully');
      console.log('✅ Dual mode support should be available');
    } catch (error) {
      console.log('❌ PersonaFormContainer component not found:', error.message);
      return false;
    }
    
    console.log('\n🎉 Dual generation modes validation successful!');
    return true;
    
  } catch (error) {
    console.log('❌ Dual generation modes validation failed:', error.message);
    return false;
  }
}

async function validateCompleteWorkflow() {
  console.log('\n🔄 Validating Complete Workflow');
  console.log('-'.repeat(40));
  
  try {
    // Test complete workflow with both generation modes
    const { buyerPersonaService } = await import('/src/services/buyerPersonaService.ts');
    
    // Test 1: Traditional mode project
    console.log('\n📋 Test 1: Creating project with traditional mode...');
    
    const traditionalProject = {
      project_name: 'Traditional Mode Test',
      product_description: 'E-commerce platform for small businesses',
      industry: 'Technology',
      target_market: 'Small business owners',
      business_goals: 'Increase online sales',
      num_personas: 2,
      generation_mode: 'traditional'
    };
    
    const createdTraditionalProject = await buyerPersonaService.createProject(traditionalProject);
    console.log('✅ Traditional mode project created:', createdTraditionalProject.id);
    
    // Test 2: Questions mode project
    console.log('\n📋 Test 2: Creating project with questions mode...');
    
    const questionsProject = {
      project_name: 'Questions Mode Test',
      product_description: 'Mobile app for fitness tracking',
      industry: 'Health & Fitness',
      target_market: 'Fitness enthusiasts',
      business_goals: 'Improve user engagement',
      num_personas: 2,
      generation_mode: 'questions'
    };
    
    const createdQuestionsProject = await buyerPersonaService.createProject(questionsProject);
    console.log('✅ Questions mode project created:', createdQuestionsProject.id);
    
    // Test 3: Verify projects can be retrieved
    console.log('\n📋 Test 3: Retrieving all projects...');
    
    const allProjects = await buyerPersonaService.getProjects();
    const traditionalFound = allProjects.find(p => p.id === createdTraditionalProject.id);
    const questionsFound = allProjects.find(p => p.id === createdQuestionsProject.id);
    
    console.log('✅ Traditional project found:', !!traditionalFound);
    console.log('✅ Questions project found:', !!questionsFound);
    
    // Test 4: Clean up test projects
    console.log('\n📋 Test 4: Cleaning up test projects...');
    
    await buyerPersonaService.deleteProject(createdTraditionalProject.id);
    await buyerPersonaService.deleteProject(createdQuestionsProject.id);
    console.log('✅ Test projects cleaned up');
    
    console.log('\n🎉 Complete workflow validation successful!');
    return true;
    
  } catch (error) {
    console.log('❌ Complete workflow validation failed:', error.message);
    console.error('Full error:', error);
    return false;
  }
}

// Main validation function
async function runCompleteValidation() {
  console.log('🚀 Starting Complete Validation of Schema Fix and Dual Modes...\n');
  
  const schemaResult = await validateSchemaFix();
  const dualModeResult = await validateDualGenerationModes();
  const workflowResult = schemaResult ? await validateCompleteWorkflow() : false;
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 VALIDATION SUMMARY');
  console.log('='.repeat(60));
  
  console.log(`🔧 Schema Fix: ${schemaResult ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`🔄 Dual Generation Modes: ${dualModeResult ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`🔄 Complete Workflow: ${workflowResult ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (schemaResult && dualModeResult && workflowResult) {
    console.log('\n🎉 SUCCESS: All validations passed!');
    console.log('✅ Database schema fix working');
    console.log('✅ Dual generation modes restored');
    console.log('✅ Complete workflow functional');
    console.log('\n💡 The buyer persona generator is ready for production!');
  } else {
    console.log('\n❌ FAILURE: Some validations failed');
    
    if (!schemaResult) {
      console.log('🔧 Action needed: Run the schema migration script in Supabase');
      console.log('   Script location: /scripts/fix-schema-migration.sql');
    }
    
    if (!dualModeResult) {
      console.log('🔄 Action needed: Check dual generation mode components');
    }
    
    if (!workflowResult) {
      console.log('🔄 Action needed: Debug complete workflow issues');
    }
  }
  
  console.log('\n📝 Next steps:');
  console.log('1. If schema fix failed: Run the migration script in Supabase');
  console.log('2. Test both generation modes in the UI');
  console.log('3. Create test projects and personas');
  console.log('4. Verify data persistence and user isolation');
}

// Auto-run validation
runCompleteValidation();

// Export functions for manual testing
window.buyerPersonaValidation = {
  runCompleteValidation,
  validateSchemaFix,
  validateDualGenerationModes,
  validateCompleteWorkflow
};

console.log('\n📝 Manual validation functions available in window.buyerPersonaValidation');
