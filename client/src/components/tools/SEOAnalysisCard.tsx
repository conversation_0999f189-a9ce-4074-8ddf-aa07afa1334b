import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Heart,
  Edit3,
  Trash2,
  Calendar,
  Eye,
  RefreshCw,
  Check,
  X,
  Globe,
  FileText,
  BarChart3,
  Search,
  Target
} from 'lucide-react';
import type { SEOAnalysis } from '@/types/seoAnalysisTypes';

// Component to display a saved SEO analysis card
interface SEOAnalysisCardProps {
  analysis: SEOAnalysis;
  onLoad: (analysis: SEOAnalysis) => void;
  onToggleFavorite: (analysis: SEOAnalysis) => void;
  onDelete: (analysis: SEOAnalysis) => void;
  toolType: string;
}

// Get analysis mode icon
const getAnalysisModeIcon = (mode: string) => {
  switch (mode) {
    case 'website':
      return <Globe className="h-4 w-4 text-blue-500" />;
    case 'page':
    default:
      return <FileText className="h-4 w-4 text-green-500" />;
  }
};

// Get analysis mode label
const getAnalysisModeLabel = (mode: string) => {
  switch (mode) {
    case 'website':
      return 'Sitio Web';
    case 'page':
    default:
      return 'Página';
  }
};

// Get score badge color based on score
const getScoreBadgeColor = (score: number) => {
  if (score >= 80) return 'bg-green-100 text-green-800 border-green-200';
  if (score >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  if (score >= 40) return 'bg-orange-100 text-orange-800 border-orange-200';
  return 'bg-red-100 text-red-800 border-red-200';
};

// Format date for display
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Truncate URL for display
const truncateUrl = (url: string, maxLength: number = 50) => {
  if (url.length <= maxLength) return url;
  return url.substring(0, maxLength) + '...';
};

function SEOAnalysisCard({
  analysis,
  onLoad,
  onToggleFavorite,
  onDelete,
  toolType,
}: SEOAnalysisCardProps) {
  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start gap-4">
        {/* Analysis Mode Icon */}
        <div className="flex-shrink-0">
          <div className="w-12 h-12 rounded-lg bg-gray-50 border border-gray-200 flex items-center justify-center">
            {getAnalysisModeIcon(analysis.analysis_mode)}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate">
                {analysis.custom_name || truncateUrl(analysis.url)}
              </h3>
              {analysis.custom_name && (
                <p className="text-sm text-gray-500 truncate mt-1">
                  {truncateUrl(analysis.url)}
                </p>
              )}
            </div>
            
            {/* Favorite Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleFavorite(analysis)}
              className="flex-shrink-0 ml-2"
            >
              <Heart 
                className={`h-4 w-4 ${
                  analysis.is_favorite 
                    ? 'fill-red-500 text-red-500' 
                    : 'text-gray-400 hover:text-red-500'
                }`} 
              />
            </Button>
          </div>

          {/* Metadata */}
          <div className="flex items-center gap-3 mb-3">
            <div className="flex items-center gap-1">
              {getAnalysisModeIcon(analysis.analysis_mode)}
              <span className="text-sm text-gray-600">
                {getAnalysisModeLabel(analysis.analysis_mode)}
              </span>
            </div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getScoreBadgeColor(analysis.overall_score)}`}>
              <BarChart3 className="h-3 w-3 inline mr-1" />
              {analysis.overall_score}/100
            </div>
            {analysis.ai_enhanced && (
              <div className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
                <Target className="h-3 w-3 inline mr-1" />
                IA
              </div>
            )}
          </div>

          {/* Basic Info Summary */}
          {analysis.basic_info && (
            <div className="text-sm text-gray-600 mb-3">
              <p className="line-clamp-1">
                <strong>Título:</strong> {analysis.basic_info.title || 'Sin título'}
              </p>
              {analysis.basic_info.meta_description && (
                <p className="line-clamp-1 mt-1">
                  <strong>Descripción:</strong> {analysis.basic_info.meta_description}
                </p>
              )}
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center gap-4 text-xs text-gray-500 mb-4">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(analysis.created_at)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Eye className="h-3 w-3" />
              <span>{analysis.view_count || 0} vistas</span>
            </div>
            {analysis.content_analysis?.word_count && (
              <div className="flex items-center gap-1">
                <FileText className="h-3 w-3" />
                <span>{analysis.content_analysis.word_count} palabras</span>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onLoad(analysis)}
              className="flex items-center gap-1"
            >
              <Search className="h-3 w-3" />
              Cargar
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDelete(analysis)}
              className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-3 w-3" />
              Eliminar
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}

export { SEOAnalysisCard };
export type { SEOAnalysisCardProps };
