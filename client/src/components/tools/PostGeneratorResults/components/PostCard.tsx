import React from "react";
import { motion } from "framer-motion";
import { Download, Edit3, Share2, Heart, MessageCircle, Repeat2, Bookmark, MoreHorizontal, Palette } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PostCardProps } from "../types";
import { getImageDisplayDimensions } from "../utils/platformUtils";
import PolotnoIntegrationService from "@/services/polotno-integration-service";

const PostCard: React.FC<PostCardProps> = ({ 
  post, 
  index, 
  brandData,
  onEdit,
  onDownload,
  onShare
}) => {
  // Debug logging for PostGeneratorResults
  console.log(`🎨 PostGeneratorResults - PostCard ${index + 1} data:`, post);
  console.log(`🖼️ PostGeneratorResults - Image URL:`, post.image_url);
  console.log(`📊 PostGeneratorResults - Metadata:`, post.metadata);
  console.log(`🏷️ PostGeneratorResults - Platform:`, post.platform);

  // Calculate platform-specific dimensions
  const imageDimensions = getImageDisplayDimensions(post.platform, post.metadata?.dimensions);
  console.log(`📐 PostGeneratorResults - Image dimensions for ${post.platform}:`, imageDimensions);

  const handleEdit = () => {
    if (onEdit) onEdit(post);
  };

  const handleDownload = () => {
    if (onDownload) onDownload(post);
  };

  const handleShare = () => {
    if (onShare) onShare(post);
  };

  const handleOpenInPolotno = () => {
    console.log('🎨 Abriendo post en Editor Profesional (Polotno)');

    if (!post.image_url) {
      console.error('❌ No hay imagen para abrir en Polotno');
      return;
    }

    try {
      // Generate Polotno URL with proper text layers
      const polotnoUrl = PolotnoIntegrationService.generatePolotnoUrl(
        post.image_url,
        post.text || '',
        post.platform || 'Instagram',
        post.metadata?.dimensions || { width: 1080, height: 1080 }
      );

      console.log('🔗 URL generada para Polotno:', polotnoUrl);

      // Open in new tab
      window.open(polotnoUrl, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('❌ Error abriendo en Polotno:', error);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.2 }}
      className="mx-auto bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300"
      style={{
        width: `${Math.max(imageDimensions.width + 32, 360)}px`, // Dynamic width based on content + padding, minimum 360px
        maxWidth: '90vw' // Responsive on small screens
      }}
    >
      {/* Post Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">
                {brandData.businessName.charAt(0)}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{brandData.businessName}</h3>
              <p className="text-xs text-gray-500">{post.platform} • Ahora</p>
            </div>
          </div>
          <MoreHorizontal className="w-5 h-5 text-gray-400" />
        </div>
      </div>

      {/* Post Content */}
      <div className="p-4">
        <p className="text-gray-800 mb-4 leading-relaxed">{post.text}</p>

        {/* Platform indicator */}
        <div className="mb-4 text-center">
          <span className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            {post.platform} • {post.metadata?.dimensions || `${imageDimensions.width}x${imageDimensions.height}`}
          </span>
        </div>

        {/* Professional image container */}
        <div className="mb-4 flex justify-center">
          {post.image_url && post.image_url.trim() !== "" ? (
            <div
              className="rounded-lg overflow-hidden border border-gray-200 shadow-sm"
              style={{
                width: `${imageDimensions.width}px`,
                height: `${imageDimensions.height}px`,
              }}
            >
              <img
                src={post.image_url}
                alt={`${post.platform} post image`}
                className="w-full h-full object-cover block"
                onLoad={() => {
                  console.log("✅ PostGeneratorResults - Image loaded successfully:", post.image_url);
                  console.log("📐 PostGeneratorResults - Applied dimensions:", imageDimensions);
                }}
                onError={() => {
                  console.error("❌ PostGeneratorResults - Error loading image:", post.image_url);
                }}
              />
            </div>
          ) : (
            <div
              className="rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center border border-gray-200 shadow-sm"
              style={{
                width: `${imageDimensions.width}px`,
                height: `${imageDimensions.height}px`,
              }}
            >
              <div className="text-center">
                <div className="text-gray-400 mb-2 text-2xl">📝</div>
                <p className="text-gray-500 text-sm">Solo texto</p>
              </div>
            </div>
          )}
        </div>

        {/* Template and Dimension Info */}
        <div className="mb-4 flex flex-wrap gap-2">
          <span className="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
            {post.template}
          </span>
          <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
            {imageDimensions.aspectRatio === 1 ? 'Cuadrada' :
             imageDimensions.aspectRatio > 1 ? 'Horizontal' : 'Vertical'} • {imageDimensions.aspectRatio.toFixed(2)}:1
          </span>
        </div>
      </div>

      {/* Post Actions */}
      <div className="px-4 py-3 border-t border-gray-100">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-6">
            <button className="flex items-center space-x-1 text-gray-500 hover:text-red-500 transition-colors">
              <Heart className="w-5 h-5" />
              <span className="text-sm">24</span>
            </button>
            <button className="flex items-center space-x-1 text-gray-500 hover:text-blue-500 transition-colors">
              <MessageCircle className="w-5 h-5" />
              <span className="text-sm">8</span>
            </button>
            <button className="flex items-center space-x-1 text-gray-500 hover:text-green-500 transition-colors">
              <Repeat2 className="w-5 h-5" />
              <span className="text-sm">3</span>
            </button>
          </div>
          <button className="text-gray-500 hover:text-gray-700 transition-colors">
            <Bookmark className="w-5 h-5" />
          </button>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col space-y-2">
          {/* Primera fila */}
          <div className="flex space-x-2">
            <Button
              onClick={handleEdit}
              variant="outline"
              size="sm"
              className="flex-1 text-xs"
            >
              <Edit3 className="w-3 h-3 mr-1" />
              Editar
            </Button>
            <Button
              onClick={handleOpenInPolotno}
              variant="outline"
              size="sm"
              className="flex-1 text-xs border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef]/5"
            >
              <Palette className="w-3 h-3 mr-1" />
              Editor Profesional
            </Button>
          </div>

          {/* Segunda fila */}
          <div className="flex space-x-2">
            <Button
              onClick={handleDownload}
              variant="outline"
              size="sm"
              className="flex-1 text-xs"
            >
              <Download className="w-3 h-3 mr-1" />
              Descargar
            </Button>
            <Button
              onClick={handleShare}
              variant="outline"
              size="sm"
              className="flex-1 text-xs"
            >
              <Share2 className="w-3 h-3 mr-1" />
              Compartir
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default PostCard;
