"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MessageCircle, Send, User, Bot, Loader2, RotateCcw, Download } from "lucide-react";

interface Message {
  id: string;
  sender: "user" | "persona";
  message: string;
  timestamp: string;
  persona_state?: {
    interest_level: number;
    trust_level: number;
    urgency_level: number;
  };
}

interface PersonaData {
  name: string;
  age: number;
  job: {
    title: string;
    industry: string;
    company_size?: string;
    responsibilities?: string[];
  };
  goals: string[];
  challenges: string[];
  personal_background?: string;
  influences?: string[];
  objections?: string[];
  communication_channels?: string[];
  avatar_url?: string;
  product_context?: string;
}

interface ConversationSimulatorProps {
  personaData: PersonaData;
  avatarUrl?: string;
  onClose?: () => void;
}

export function ConversationSimulator({ personaData, avatarUrl, onClose }: ConversationSimulatorProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentMessage, setCurrentMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [conversationStarted, setConversationStarted] = useState(false);
  const [personaState, setPersonaState] = useState({
    interest_level: 50,
    trust_level: 30,
    urgency_level: 20
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Auto-start conversation when component mounts
  useEffect(() => {
    if (!conversationStarted && !isLoading) {
      startConversation();
    }
  }, []);

  const startConversation = async () => {
    setIsLoading(true);
    console.log("🚀 Starting conversation with persona:", personaData);

    try {
      // Get the original product description from localStorage or persona data
      const productDescription = localStorage.getItem('buyer_personas_product_description') ||
                                 personaData?.product_context ||
                                 "Producto o servicio no especificado";

      const requestBody = {
        persona_data: {
          name: personaData.name,
          age: personaData.age,
          job: {
            title: personaData.job?.title || "Profesional",
            industry: personaData.job?.industry || "General",
            company_size: personaData.job?.company_size || "Mediana",
            responsibilities: personaData.job?.responsibilities || ["Responsabilidades generales"]
          },
          goals: personaData.goals || ["Mejorar eficiencia"],
          challenges: personaData.challenges || ["Gestión del tiempo"],
          personal_background: personaData.personal_background || "Profesional con experiencia",
          objections: personaData.objections || ["Precio elevado", "Complejidad"],
          communication_channels: personaData.communication_channels || ["Email", "LinkedIn"],
          influences: personaData.influences || ["Colegas", "Expertos del sector"]
        },
        conversation_type: "sales",
        context: `El usuario es un vendedor que quiere presentar su producto/servicio: "${productDescription}". La persona debe actuar como ${personaData.name}, un cliente potencial interesado pero con dudas y objeciones naturales basadas en su perfil específico como ${personaData.job?.title} en ${personaData.job?.industry}.`,
        product_info: {
          description: productDescription,
          category: "software"
        }
      };

      console.log("📤 Request body:", requestBody);

      const response = await fetch("http://localhost:8001/api/v1/premium/conversation/start", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log("📥 Response status:", response.status);
      const data = await response.json();
      console.log("📥 Response data:", data);

      if (data.status === "success") {
        setConversationId(data.conversation.conversation_id);
        setMessages(data.conversation.messages || []);
        setConversationStarted(true);

        console.log("✅ Conversation started successfully");
        console.log("💬 Initial messages:", data.conversation.messages);

        // Update persona state if available
        if (data.conversation.messages && data.conversation.messages.length > 0) {
          const lastMessage = data.conversation.messages[data.conversation.messages.length - 1];
          if (lastMessage.persona_state) {
            setPersonaState(lastMessage.persona_state);
          }
        }
      } else {
        console.error("❌ Error starting conversation:", data.error_message);
        alert("Error al iniciar la conversación: " + (data.error_message || "Error desconocido"));
      }
    } catch (error) {
      console.error("❌ Network error starting conversation:", error);
      alert("Error de conexión al iniciar la conversación");
    } finally {
      setIsLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!currentMessage.trim() || !conversationId || isLoading) return;

    const messageToSend = currentMessage;
    console.log("💬 Sending message:", messageToSend);

    const userMessage: Message = {
      id: Date.now().toString(),
      sender: "user",
      message: messageToSend,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setCurrentMessage("");
    setIsLoading(true);

    try {
      // Get the original product description for context
      const productDescription = localStorage.getItem('buyer_personas_product_description') ||
                                 personaData?.product_context ||
                                 "Producto o servicio no especificado";

      const requestBody = {
        conversation_id: conversationId,
        user_message: messageToSend,
        conversation_data: {
          context: {
            persona_data: personaData,
            product_info: {
              description: productDescription,
              category: "software"
            }
          },
          messages: [...messages, userMessage]
        }
      };

      console.log("📤 Continue request body:", requestBody);

      const response = await fetch("http://localhost:8001/api/v1/premium/conversation/continue", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log("📥 Continue response status:", response.status);
      const data = await response.json();
      console.log("📥 Continue response data:", data);

      if (data.status === "success") {
        const personaMessage: Message = {
          id: (Date.now() + 1).toString(),
          sender: "persona",
          message: data.persona_response || data.conversation?.persona_response,
          timestamp: new Date().toISOString(),
          persona_state: data.analysis?.persona_state || data.persona_state
        };

        console.log("✅ Adding persona message:", personaMessage);
        setMessages(prev => [...prev, personaMessage]);

        // Update persona state
        const newPersonaState = data.analysis?.persona_state || data.persona_state;
        if (newPersonaState) {
          console.log("📊 Updating persona state:", newPersonaState);
          setPersonaState(newPersonaState);
        }
      } else {
        console.error("❌ Error sending message:", data.error_message);
        alert("Error al enviar mensaje: " + (data.error_message || "Error desconocido"));
      }
    } catch (error) {
      console.error("❌ Network error sending message:", error);
      alert("Error de conexión al enviar mensaje");
    } finally {
      setIsLoading(false);
    }
  };

  const resetConversation = () => {
    setMessages([]);
    setConversationId(null);
    setConversationStarted(false);
    setCurrentMessage("");
    setPersonaState({
      interest_level: 50,
      trust_level: 30,
      urgency_level: 20
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getStateColor = (level: number) => {
    if (level >= 70) return "bg-green-500";
    if (level >= 40) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getPersonaInitials = () => {
    const name = personaData?.name || "Persona";
    return name.split(" ").map((n: string) => n[0]).join("").toUpperCase();
  };

  return (
    <Card className="w-full max-w-4xl mx-auto h-[600px] flex flex-col overflow-hidden">
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <MessageCircle className="h-5 w-5 text-blue-600" />
            Simulador de Conversación
            <Badge variant="outline" className="ml-2">
              {personaData?.name || "Persona"}
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={resetConversation}>
              <RotateCcw className="h-4 w-4 mr-1" />
              Reiniciar
            </Button>
            {onClose && (
              <Button variant="outline" size="sm" onClick={onClose}>
                Cerrar
              </Button>
            )}
          </div>
        </div>

        {/* Persona State Indicators */}
        {conversationStarted && (
          <div className="flex items-center gap-4 mt-3 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Interés:</span>
              <div className="flex items-center gap-1">
                <div className={`w-3 h-3 rounded-full ${getStateColor(personaState.interest_level)}`}></div>
                <span className="text-sm font-medium">{personaState.interest_level}%</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Confianza:</span>
              <div className="flex items-center gap-1">
                <div className={`w-3 h-3 rounded-full ${getStateColor(personaState.trust_level)}`}></div>
                <span className="text-sm font-medium">{personaState.trust_level}%</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Urgencia:</span>
              <div className="flex items-center gap-1">
                <div className={`w-3 h-3 rounded-full ${getStateColor(personaState.urgency_level)}`}></div>
                <span className="text-sm font-medium">{personaState.urgency_level}%</span>
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0 overflow-hidden">
        {!conversationStarted ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={avatarUrl} />
                  <AvatarFallback className="text-lg">
                    {getPersonaInitials()}
                  </AvatarFallback>
                </Avatar>
              </div>
              <div>
                <h3 className="text-lg font-semibold">{personaData?.name || "Persona"}</h3>
                <p className="text-gray-600">
                  {personaData?.job?.title} en {personaData?.job?.industry}
                </p>
              </div>
              {isLoading ? (
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                    <span className="text-blue-600 font-medium">Iniciando conversación...</span>
                  </div>
                  <p className="text-sm text-gray-500">Conectando con {personaData?.name}...</p>
                </div>
              ) : (
                <Button onClick={startConversation} className="bg-blue-600 hover:bg-blue-700">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Iniciar Conversación
                </Button>
              )}
            </div>
          </div>
        ) : (
          <>
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4">
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
                  >
                    <div className={`flex items-start gap-3 max-w-[80%] ${
                      message.sender === "user" ? "flex-row-reverse" : "flex-row"
                    }`}>
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        {message.sender === "user" ? (
                          <AvatarFallback>
                            <User className="h-4 w-4" />
                          </AvatarFallback>
                        ) : (
                          <>
                            <AvatarImage src={avatarUrl} />
                            <AvatarFallback>
                              {getPersonaInitials()}
                            </AvatarFallback>
                          </>
                        )}
                      </Avatar>
                      <div className={`rounded-lg p-3 min-w-0 flex-1 max-w-full ${
                        message.sender === "user"
                          ? "bg-blue-600 text-white"
                          : "bg-gray-100 text-gray-900"
                      }`}>
                        <p className="text-sm chat-message-text force-text-wrap">
                          {message.message}
                        </p>
                        <p className={`text-xs mt-1 ${
                          message.sender === "user" ? "text-blue-100" : "text-gray-500"
                        }`}>
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {isLoading && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex justify-start"
                >
                  <div className="flex items-center gap-3 max-w-[80%]">
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      <AvatarImage src={avatarUrl} />
                      <AvatarFallback>
                        {getPersonaInitials()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="bg-gray-100 rounded-lg p-3 min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin flex-shrink-0" />
                        <span className="text-sm text-gray-600 break-words">Escribiendo...</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="border-t p-4">
              <div className="flex items-center gap-2">
                <Input
                  value={currentMessage}
                  onChange={(e) => setCurrentMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Escribe tu mensaje..."
                  disabled={isLoading}
                  className="flex-1"
                />
                <Button
                  onClick={sendMessage}
                  disabled={!currentMessage.trim() || isLoading}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Presiona Enter para enviar • Shift+Enter para nueva línea
              </p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
