import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  DownloadCloud,
  Share2,
  Copy,
  Layers,
  Grid,
  Layout,
  Maximize2,
  Edit3,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Tipo para un diseño
type Design = {
  id: string;
  title: string;
  url: string;
  createdAt: Date;
  aspectRatio: "square" | "portrait" | "landscape";
  platform: string;
};

// Datos de ejemplo
const dummyDesigns: Record<string, Design[]> = {
  "1": [
    {
      id: "design-1-1",
      title: "Post Principal Verano",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjgwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNEY0NkU1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzMiIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPlBvc3QgUHJpbmNpcGFsPC90ZXh0Pjwvc3ZnPg==",
      createdAt: new Date(2025, 3, 1),
      aspectRatio: "square",
      platform: "Instagram",
    },
    {
      id: "design-1-2",
      title: "Historia Promocional",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxOTIwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNFQzQ4OTkiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjQ4IiBmaWxsPSIjRkZGRkZGIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+SGlzdG9yaWEgUHJvbW88L3RleHQ+PC9zdmc+",
      createdAt: new Date(2025, 3, 1),
      aspectRatio: "portrait",
      platform: "Instagram Stories",
    },
    {
      id: "design-1-3",
      title: "Carrusel Productos",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjgwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMTBCOTgxIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzMiIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkNhcnJ1c2VsIDE8L3RleHQ+PC9zdmc+",
      createdAt: new Date(2025, 3, 2),
      aspectRatio: "square",
      platform: "Instagram",
    },
    {
      id: "design-1-4",
      title: "Carrusel Productos 2",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjgwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjRjU5RTBCIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzMiIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkNhcnJ1c2VsIDI8L3RleHQ+PC9zdmc+",
      createdAt: new Date(2025, 3, 2),
      aspectRatio: "square",
      platform: "Instagram",
    },
  ],
  "2": [
    {
      id: "design-2-1",
      title: "Anuncio Principal Producto X",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MjgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iIzRGNDZFNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5BbnVuY2lvIFByaW5jaXBhbDwvdGV4dD48L3N2Zz4=",
      createdAt: new Date(2025, 3, 5),
      aspectRatio: "landscape",
      platform: "Facebook",
    },
    {
      id: "design-2-2",
      title: "Video Cover",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODIwIiBoZWlnaHQ9IjMxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjRUM0ODk5Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzMiIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPlZpZGVvIENvdmVyPC90ZXh0Pjwvc3ZnPg==",
      createdAt: new Date(2025, 3, 5),
      aspectRatio: "landscape",
      platform: "Facebook",
    },
  ],
  "3": [
    {
      id: "design-3-1",
      title: "Post Día de la Madre",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjgwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNEY0NkU1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyOCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPlBvc3QgRMOtYSBNYWRyZTwvdGV4dD48L3N2Zz4=",
      createdAt: new Date(2025, 3, 8),
      aspectRatio: "square",
      platform: "Instagram",
    },
    {
      id: "design-3-2",
      title: "Historia Día de la Madre",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxOTIwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNFQzQ4OTkiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjQwIiBmaWxsPSIjRkZGRkZGIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+SGlzdG9yaWEgRMOtYSBNYWRyZTwvdGV4dD48L3N2Zz4=",
      createdAt: new Date(2025, 3, 8),
      aspectRatio: "portrait",
      platform: "Instagram Stories",
    },
    {
      id: "design-3-3",
      title: "Anuncio Facebook",
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MjgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iIzEwQjk4MSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5BbnVuY2lvIEZhY2Vib29rPC90ZXh0Pjwvc3ZnPg==",
      createdAt: new Date(2025, 3, 9),
      aspectRatio: "landscape",
      platform: "Facebook",
    },
  ],
};

interface DesignPreviewProps {
  projectId: string;
  onShowFullPreview: (imageUrl: string) => void;
}

export default function DesignPreview({
  projectId,
  onShowFullPreview,
}: DesignPreviewProps) {
  const [designs, setDesigns] = useState<Design[]>([]);
  const [selectedDesign, setSelectedDesign] = useState<Design | null>(null);
  const [currentView, setCurrentView] = useState<"grid" | "single">("grid");
  const { toast } = useToast();

  // Cargar diseños basados en el ID del proyecto
  useEffect(() => {
    // En un caso real, esta sería una llamada a API
    const projectDesigns = dummyDesigns[projectId] || [];
    setDesigns(projectDesigns);

    if (projectDesigns.length > 0) {
      setSelectedDesign(projectDesigns[0]);
    }
  }, [projectId]);

  const handleDesignSelect = (design: Design) => {
    setSelectedDesign(design);
    setCurrentView("single");
  };

  const handleNextDesign = () => {
    if (!selectedDesign || designs.length <= 1) return;

    const currentIndex = designs.findIndex((d) => d.id === selectedDesign.id);
    const nextIndex = (currentIndex + 1) % designs.length;
    setSelectedDesign(designs[nextIndex]);
  };

  const handlePrevDesign = () => {
    if (!selectedDesign || designs.length <= 1) return;

    const currentIndex = designs.findIndex((d) => d.id === selectedDesign.id);
    const prevIndex = (currentIndex - 1 + designs.length) % designs.length;
    setSelectedDesign(designs[prevIndex]);
  };

  const copyToClipboard = () => {
    // En una implementación real, esto copiaría la URL o la imagen
    toast({
      title: "Copiado al portapapeles",
      description: "El enlace al diseño ha sido copiado",
    });
  };

  const handleDownload = () => {
    // En una implementación real, esto iniciaría la descarga
    toast({
      title: "Descarga iniciada",
      description: "El diseño se está descargando",
    });
  };

  const handleShare = () => {
    // En una implementación real, esto abriría opciones de compartir
    toast({
      title: "Compartir",
      description: "Opciones para compartir el diseño",
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Cabecera con vista seleccionada */}
      <div className="mb-4 flex justify-between items-center">
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            className={
              currentView === "grid"
                ? "bg-purple-50 text-purple-600 border-purple-200"
                : ""
            }
            onClick={() => setCurrentView("grid")}
          >
            <Grid size={18} className="mr-1" />
            Cuadrícula
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={
              currentView === "single"
                ? "bg-purple-50 text-purple-600 border-purple-200"
                : ""
            }
            onClick={() =>
              currentView === "single"
                ? setCurrentView("grid")
                : setCurrentView("single")
            }
            disabled={!selectedDesign}
          >
            <Layout size={18} className="mr-1" />
            Vista Individual
          </Button>
        </div>

        <div className="flex space-x-2">
          {selectedDesign && currentView === "single" && (
            <>
              <Button variant="outline" size="sm" onClick={copyToClipboard}>
                <Copy size={16} className="mr-1" />
                Copiar
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <DownloadCloud size={16} className="mr-1" />
                Descargar
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 size={16} className="mr-1" />
                Compartir
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Área principal de diseños */}
      {currentView === "grid" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {designs.map((design) => (
            <motion.div
              key={design.id}
              whileHover={{ y: -5 }}
              transition={{ duration: 0.2 }}
              className="cursor-pointer group"
              onClick={() => handleDesignSelect(design)}
            >
              <Card className="overflow-hidden hover:shadow-md transition-shadow border border-gray-200">
                <CardContent className="p-0 relative">
                  <img
                    src={design.url}
                    alt={design.title}
                    className="w-full h-auto object-cover aspect-square"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-opacity opacity-0 group-hover:opacity-100">
                    <Button
                      variant="secondary"
                      className="bg-white hover:bg-gray-100"
                      onClick={(e) => {
                        e.stopPropagation();
                        onShowFullPreview(design.url);
                      }}
                    >
                      <Maximize2 size={16} className="mr-1" />
                      Ver
                    </Button>
                  </div>
                </CardContent>
                <div className="p-3">
                  <h3 className="font-medium truncate">{design.title}</h3>
                  <p className="text-sm text-gray-500">{design.platform}</p>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      ) : selectedDesign ? (
        <div className="flex flex-col h-full">
          <div className="relative bg-gray-50 rounded-lg flex-grow flex items-center justify-center p-4">
            <Button
              variant="outline"
              size="icon"
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white z-10"
              onClick={handlePrevDesign}
            >
              <ChevronLeft size={16} />
            </Button>

            <div className="max-w-3xl max-h-[60vh] overflow-hidden flex items-center justify-center">
              <img
                src={selectedDesign.url}
                alt={selectedDesign.title}
                className="max-w-full max-h-full object-contain rounded-md"
              />
            </div>

            <Button
              variant="outline"
              size="icon"
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white z-10"
              onClick={handleNextDesign}
            >
              <ChevronRight size={16} />
            </Button>

            <Button
              variant="outline"
              size="icon"
              className="absolute right-4 top-4 bg-white"
              onClick={() => onShowFullPreview(selectedDesign.url)}
            >
              <Maximize2 size={16} />
            </Button>
          </div>

          <div className="mt-4 flex justify-between items-center">
            <div>
              <h3 className="font-medium text-lg">{selectedDesign.title}</h3>
              <p className="text-sm text-gray-500">
                {selectedDesign.platform} • Creado el{" "}
                {selectedDesign.createdAt.toLocaleDateString()}
              </p>
            </div>
            <Button className="bg-purple-600 hover:bg-purple-700 text-white">
              <Edit3 size={16} className="mr-1" />
              Editar
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-gray-500">No hay diseños seleccionados</p>
        </div>
      )}
    </div>
  );
}
