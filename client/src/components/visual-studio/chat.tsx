import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Send,
  Sparkles,
  User,
  Clock,
  Lightbulb,
  PlusCircle,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Tipos para los mensajes
type MessageRole =
  | "assistant"
  | "user"
  | "system"
  | "agent-leo"
  | "agent-mia"
  | "agent-kai"
  | "agent-zoe";

type Message = {
  id: string;
  role: MessageRole;
  content: string;
  timestamp: Date;
  designs?: string[];
  suggestions?: string[];
  isTyping?: boolean;
  name?: string;
};

// Información de los agentes
const agentInfo: Record<
  MessageRole,
  { name: string; avatar: string; role: string; color: string }
> = {
  assistant: {
    name: "<PERSON>",
    avatar: "E",
    role: "Directora Creativa",
    color: "bg-purple-500",
  },
  "agent-leo": {
    name: "Leo",
    avatar: "L",
    role: "Especialista en Fotografía",
    color: "bg-blue-500",
  },
  "agent-mia": {
    name: "Mia",
    avatar: "M",
    role: "Experta en Tipografía",
    color: "bg-pink-500",
  },
  "agent-kai": {
    name: "Kai",
    avatar: "K",
    role: "Experto en Redes Sociales",
    color: "bg-green-500",
  },
  "agent-zoe": {
    name: "Zoe",
    avatar: "Z",
    role: "Especialista en Color",
    color: "bg-amber-500",
  },
  user: {
    name: "Usuario",
    avatar: "U",
    role: "Usuario",
    color: "bg-gray-500",
  },
  system: {
    name: "Sistema",
    avatar: "S",
    role: "Sistema",
    color: "bg-gray-500",
  },
};

// Mensajes iniciales para nuevos proyectos
const newProjectMessages: Message[] = [
  {
    id: "1",
    role: "assistant",
    content:
      "¡Hola! Soy Emma, tu directora creativa. ¿Qué tipo de diseño visual te gustaría crear hoy?",
    timestamp: new Date(),
    suggestions: [
      "Una serie de posts para Instagram",
      "Diseños para Facebook",
      "Contenido para una campaña de lanzamiento",
      "Banners para mi sitio web",
    ],
  },
];

// Mensajes para proyectos existentes
const existingProjectMessages: Message[] = [
  {
    id: "1",
    role: "assistant",
    content:
      "¡Bienvenido de vuelta a tu proyecto! ¿En qué te puedo ayudar hoy?",
    timestamp: new Date(),
  },
  {
    id: "2",
    role: "user",
    content: "Quiero revisar los diseños que creamos ayer",
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
  },
  {
    id: "3",
    role: "assistant",
    content:
      "Claro, aquí están los diseños que creamos para tu campaña de Instagram:",
    timestamp: new Date(Date.now() - 1000 * 60 * 4),
    designs: [
      "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNEY0NkU1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkRpc2XDsW8gMTwvdGV4dD48L3N2Zz4=",
      "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjRUM0ODk5Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkRpc2XDsW8gMjwvdGV4dD48L3N2Zz4=",
    ],
  },
];

interface EmmaVisualChatProps {
  projectId: string | null;
  isNewProject: boolean;
}

export default function EmmaVisualChat({
  projectId,
  isNewProject,
}: EmmaVisualChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Cargar mensajes iniciales según el tipo de proyecto
  useEffect(() => {
    if (isNewProject) {
      setMessages(newProjectMessages);
    } else if (projectId) {
      // En un caso real, cargaríamos los mensajes desde una API
      setMessages(existingProjectMessages);
    }
  }, [isNewProject, projectId]);

  // Hacer scroll al fondo cuando hay nuevos mensajes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Función para enviar un mensaje
  const sendMessage = () => {
    if (!inputMessage.trim()) return;

    // Añadir mensaje del usuario
    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");
    setIsProcessing(true);

    // Simular la respuesta de Emma (en un caso real, llamaríamos a un API)
    setTimeout(() => {
      // Mensaje de "escribiendo..."
      const typingMessage: Message = {
        id: `typing-${Date.now()}`,
        role: "assistant",
        content: "",
        timestamp: new Date(),
        isTyping: true,
      };

      setMessages((prev) => [...prev, typingMessage]);

      // Después de un momento, mostrar la respuesta real
      setTimeout(() => {
        setMessages((prev) => prev.filter((m) => !m.isTyping));

        // Determinar qué agente responde
        const respondingAgent: MessageRole = "assistant";
        let extraAgent: MessageRole | null = null;

        // Análisis básico del mensaje para decidir qué agente responde
        const msgLower = inputMessage.toLowerCase();
        if (msgLower.includes("color") || msgLower.includes("paleta")) {
          extraAgent = "agent-zoe";
        } else if (msgLower.includes("foto") || msgLower.includes("imagen")) {
          extraAgent = "agent-leo";
        } else if (
          msgLower.includes("letra") ||
          msgLower.includes("tipo") ||
          msgLower.includes("font")
        ) {
          extraAgent = "agent-mia";
        } else if (
          msgLower.includes("instagram") ||
          msgLower.includes("facebook") ||
          msgLower.includes("social")
        ) {
          extraAgent = "agent-kai";
        }

        // Respuesta de Emma
        const response: Message = {
          id: Date.now().toString(),
          role: respondingAgent,
          content: getResponseForInput(inputMessage),
          timestamp: new Date(),
          designs: shouldShowDesigns(inputMessage)
            ? [
                "https://via.placeholder.com/400x400/4F46E5/FFFFFF?text=Nuevo+Diseño",
                "https://via.placeholder.com/400x400/10B981/FFFFFF?text=Alternativa",
              ]
            : undefined,
          suggestions: [
            "Ajusta los colores",
            "Cambia la tipografía",
            "Más minimalista",
            "Más vibrante",
          ],
        };

        setMessages((prev) => [...prev, response]);

        // Si un agente especializado debe intervenir, añadir su mensaje
        if (extraAgent) {
          setTimeout(() => {
            const agentMessage: Message = {
              id: `agent-${Date.now()}`,
              role: extraAgent as MessageRole,
              content: getAgentResponse(
                extraAgent as MessageRole,
                inputMessage,
              ),
              timestamp: new Date(),
              name: agentInfo[extraAgent]?.name,
            };

            setMessages((prev) => [...prev, agentMessage]);
            setIsProcessing(false);
          }, 1000);
        } else {
          setIsProcessing(false);
        }
      }, 1500);
    }, 500);
  };

  // Función auxiliar para determinar respuestas basadas en el input
  const getResponseForInput = (input: string): string => {
    const lowerInput = input.toLowerCase();

    if (
      lowerInput.includes("hola") ||
      lowerInput.includes("buenos días") ||
      lowerInput.includes("buenas")
    ) {
      return "¡Hola! ¿En qué puedo ayudarte con tus diseños hoy?";
    } else if (lowerInput.includes("instagram")) {
      return "Puedo ayudarte a crear contenido para Instagram. ¿Prefieres posts cuadrados, historias o carruseles?";
    } else if (lowerInput.includes("diseño") || lowerInput.includes("crear")) {
      return "Entiendo que quieres crear un nuevo diseño. ¿Para qué plataforma lo necesitas? Podemos optimizarlo específicamente para cada canal.";
    } else if (lowerInput.includes("color")) {
      return "Los colores son fundamentales en el diseño. ¿Tienes una paleta de marca establecida o quieres que te sugiera opciones?";
    } else if (
      lowerInput.includes("muestra") ||
      lowerInput.includes("ejemplo")
    ) {
      return "Aquí tienes algunos diseños de ejemplo basados en tus requisitos. ¿Alguno de estos se acerca a lo que buscas?";
    } else {
      return "Estoy analizando tu solicitud. ¿Puedes darme más detalles sobre el estilo visual que prefieres o el objetivo del diseño?";
    }
  };

  // Función para determinar si mostrar ejemplos de diseños
  const shouldShowDesigns = (input: string): boolean => {
    const lowerInput = input.toLowerCase();
    return (
      lowerInput.includes("muestra") ||
      lowerInput.includes("ejemplo") ||
      lowerInput.includes("crea") ||
      lowerInput.includes("diseña") ||
      lowerInput.includes("generar")
    );
  };

  // Función para obtener respuestas de agentes especializados
  const getAgentResponse = (agentRole: MessageRole, input: string): string => {
    switch (agentRole) {
      case "agent-leo":
        return "¡Hola! Soy Leo, especialista en fotografía. Para este tipo de diseño, recomendaría usar imágenes con alto contraste y profundidad de campo reducida para destacar el producto.";
      case "agent-mia":
        return "Mia aquí, experta en tipografía. Para este proyecto sugiero combinar una fuente sans-serif moderna para títulos con una serif legible para el cuerpo de texto.";
      case "agent-kai":
        return "Kai al habla. Según las últimas tendencias de Instagram, este formato de contenido está teniendo un 38% más de engagement. Te recomiendo incluir elementos interactivos.";
      case "agent-zoe":
        return "Zoe, tu especialista en color. Esta paleta que mencionas funcionaría bien, aunque sugeriría añadir un tono de acento complementario para crear más dinamismo visual.";
      default:
        return "";
    }
  };

  // Función para usar una sugerencia
  const useSuggestion = (suggestion: string) => {
    setInputMessage(suggestion);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Área de mensajes */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className={`flex ${message.role === "user" ? "justify-end" : "justify-start"} mb-4`}
            >
              {message.role !== "user" && (
                <div className="mr-2 flex-shrink-0">
                  <Avatar
                    className={`${message.role in agentInfo ? agentInfo[message.role].color : "bg-gray-300"} text-white`}
                  >
                    <AvatarFallback>
                      {message.role in agentInfo
                        ? agentInfo[message.role].avatar
                        : "?"}
                    </AvatarFallback>
                  </Avatar>
                </div>
              )}

              <div
                className={`max-w-[80%] ${message.role === "user" ? "bg-purple-500 text-white" : "bg-gray-100 text-gray-800"} rounded-2xl p-3 ${message.role.startsWith("agent") && message.role !== "assistant" ? "border-l-4 " + agentInfo[message.role].color.replace("bg-", "border-") : ""}`}
              >
                {message.role.startsWith("agent") &&
                  message.role !== "assistant" && (
                    <div className="mb-1 text-xs font-medium text-gray-500">
                      {message.name || agentInfo[message.role]?.name}
                      <span className="ml-1 text-xs text-gray-400">
                        {agentInfo[message.role]?.role}
                      </span>
                    </div>
                  )}

                {message.isTyping ? (
                  <div className="flex space-x-2 items-center px-2">
                    <div
                      className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"
                      style={{ animationDelay: "0ms" }}
                    ></div>
                    <div
                      className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"
                      style={{ animationDelay: "150ms" }}
                    ></div>
                    <div
                      className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"
                      style={{ animationDelay: "300ms" }}
                    ></div>
                  </div>
                ) : (
                  <div>
                    <p className="whitespace-pre-wrap">{message.content}</p>

                    {/* Mostrar diseños si existen */}
                    {message.designs && message.designs.length > 0 && (
                      <div className="mt-3 grid grid-cols-2 gap-2">
                        {message.designs.map((design, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={design}
                              alt={`Diseño ${index + 1}`}
                              className="rounded-md w-full h-auto cursor-pointer hover:opacity-90 transition-opacity"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all opacity-0 group-hover:opacity-100">
                              <Button
                                variant="secondary"
                                size="sm"
                                className="text-xs"
                              >
                                Ampliar
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Mostrar sugerencias si existen */}
                    {message.suggestions && message.suggestions.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {message.suggestions.map((suggestion, index) => (
                          <Button
                            key={index}
                            variant="outline"
                            size="sm"
                            className="text-xs bg-white hover:bg-purple-50 text-purple-700 border-purple-200"
                            onClick={() => useSuggestion(suggestion)}
                          >
                            {suggestion}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                <div className="mt-1 text-xs text-right opacity-70">
                  {message.timestamp.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </div>
              </div>

              {message.role === "user" && (
                <div className="ml-2 flex-shrink-0">
                  <Avatar className="bg-gray-300 text-white">
                    <AvatarFallback>U</AvatarFallback>
                  </Avatar>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>
        <div ref={messagesEndRef} />
      </div>

      {/* Input para enviar mensajes */}
      <div className="p-3 border-t border-gray-200">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            sendMessage();
          }}
          className="flex items-center gap-2"
        >
          <Input
            className="flex-grow"
            placeholder="Escribe tu mensaje a Emma y su equipo..."
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            disabled={isProcessing}
          />
          <Button
            type="submit"
            className="bg-purple-600 hover:bg-purple-700"
            disabled={!inputMessage.trim() || isProcessing}
          >
            <Send size={18} />
          </Button>
        </form>
        <div className="mt-2 text-xs text-gray-400 flex items-center">
          <Sparkles size={12} className="mr-1" />
          Powered by Emma AI
        </div>
      </div>
    </div>
  );
}
