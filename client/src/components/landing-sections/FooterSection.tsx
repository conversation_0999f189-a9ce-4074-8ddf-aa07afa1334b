"use client"

import { motion } from "framer-motion"
import { <PERSON> } from "wouter"
import { Mail, Phone, MapPin, Twitter, Linkedin, Instagram, Youtube } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/contexts/LanguageContext"

// Función para obtener secciones del footer traducidas
const getFooterSections = (t: (key: string) => string) => [
  {
    title: t('landing.footer_platform'),
    links: [
      { name: t('landing.footer_ai_agents'), href: "/dashboard/agentes" },
      { name: t('landing.footer_tools'), href: "/dashboard/herramientas" },
      { name: t('landing.footer_pricing'), href: "#pricing" },
      { name: "Marketplace", href: "/dashboard/agents-marketplace" },
    ],
  },
  {
    title: t('landing.footer_resources'),
    links: [
      { name: t('landing.footer_help_center'), href: "/dashboard" },
      { name: t('landing.footer_tutorials'), href: "/dashboard" },
      { name: "API", href: "/dashboard" },
      { name: t('landing.footer_integrations'), href: "/dashboard" },
    ],
  },
  {
    title: t('landing.footer_company'),
    links: [
      { name: t('landing.footer_about_us'), href: "/dashboard" },
      { name: t('landing.footer_contact'), href: "/dashboard" },
      { name: t('landing.footer_terms'), href: "/dashboard" },
      { name: t('landing.footer_privacy'), href: "/dashboard" },
    ],
  },
]

const socialLinks = [
  { name: "Twitter", icon: Twitter, href: "#", color: "hover:text-blue-400" },
  { name: "LinkedIn", icon: Linkedin, href: "#", color: "hover:text-blue-600" },
  { name: "Instagram", icon: Instagram, href: "#", color: "hover:text-pink-500" },
  { name: "YouTube", icon: Youtube, href: "#", color: "hover:text-red-500" },
]

export function FooterSection() {
  const { t, currentLanguage } = useLanguage()
  const currentYear = new Date().getFullYear()
  const footerSections = getFooterSections(t)

  return (
    <footer className="bg-white border-t-4 border-black relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#3018ef]/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#dd3a5a]/5 rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12 mb-12">
          {/* Brand Section */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <Link href="/">
              <div className="inline-flex items-center mb-6">
                <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] w-12 h-12 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg mr-3">
                  E
                </div>
                <span className="text-2xl font-black text-gray-900">
                  Emma Studio
                </span>
              </div>
            </Link>
            
            <p className="text-gray-600 mb-6 font-medium leading-relaxed">
              {t('landing.footer_description')}
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center text-gray-600">
                <Mail className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium"><EMAIL></span>
              </div>
              <div className="flex items-center text-gray-600">
                <Phone className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium">+525529959460</span>
              </div>
              <div className="flex items-center text-gray-600">
                <MapPin className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium">Volcán 212, Lomas - Virreyes, Lomas de Chapultepec, Miguel Hidalgo, 11000 Ciudad de México, CDMX</span>
              </div>
            </div>
          </motion.div>

          {/* Footer Links */}
          {footerSections.map((section, index) => (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 * (index + 1) }}
            >
              <h3 className="text-lg font-black text-gray-900 mb-6">
                {section.title}
              </h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link href={link.href}>
                      <span className="text-gray-600 hover:text-[#3018ef] transition-colors duration-300 font-medium cursor-pointer">
                        {link.name}
                      </span>
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Newsletter Section */}
        <motion.div
          className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 p-12 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="text-center">
            <h3 className="text-2xl font-black text-gray-900 mb-4">
              {t('landing.footer_newsletter_title')}
            </h3>
            <p className="text-gray-600 mb-6 text-lg leading-relaxed">
              {t('landing.footer_newsletter_desc')}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder={t('landing.footer_email_placeholder')}
                className="flex-1 px-6 py-4 rounded-2xl border border-gray-200 shadow-lg font-medium focus:outline-none focus:ring-2 focus:ring-[#3018ef] focus:border-[#3018ef] transition-all duration-300 text-lg bg-white/80 backdrop-blur-sm"
              />
              <Button variant="blue" size="lg" className="px-6 py-4 text-lg font-bold">
                {t('landing.footer_subscribe')}
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Bottom Section */}
        <motion.div
          className="flex flex-col md:flex-row justify-between items-center pt-8 border-t-3 border-black"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          {/* Copyright */}
          <div className="text-gray-600 font-bold mb-4 md:mb-0 text-lg">
            © {currentYear} Emma Studio. {t('landing.footer_rights')}
          </div>

          {/* Social Links */}
          <div className="flex space-x-4">
            {socialLinks.map((social) => {
              const IconComponent = social.icon
              return (
                <motion.a
                  key={social.name}
                  href={social.href}
                  className={`w-12 h-12 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center text-gray-600 ${social.color} transition-all duration-300 hover:shadow-xl hover:scale-110 shadow-lg border border-gray-200`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <IconComponent size={20} />
                </motion.a>
              )
            })}
          </div>
        </motion.div>

        {/* Final CTA */}
        <motion.div
          className="text-center mt-12 pt-8 border-t-3 border-black"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <p className="text-gray-600 mb-6 text-lg font-bold">
            {t('landing.footer_ready_revolutionize')}
          </p>
          <Link href="/dashboard">
            <Button variant="red" size="lg" className="px-8 py-4 text-lg font-bold">
              {t('landing.footer_start_now')}
            </Button>
          </Link>
        </motion.div>
      </div>
    </footer>
  )
}
