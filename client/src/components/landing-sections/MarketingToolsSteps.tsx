"use client"

import { FeatureSteps } from "@/components/ui/feature-steps"
import { useLanguage } from "@/contexts/LanguageContext"

// Función para obtener características de marketing traducidas
const getMarketingFeatures = (currentLanguage: string) => [
  {
    step: currentLanguage === 'en' ? "Design Tools" : "Herramientas de Diseño",
    title: currentLanguage === 'en' ? "🎨 Marketing Design Tools" : "🎨 Herramientas de Diseño Marketing",
    content: currentLanguage === 'en'
      ? "Specialized tools to create and analyze visual marketing elements"
      : "Herramientas especializadas para crear y analizar elementos visuales de marketing",
    image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w2NDI3NzN8MHwxfGFsbHwxMjN8fHx8fHwyfHwxNzIzODA2OTM5fA&ixlib=rb-4.0.3&q=80&w=1080",
    tools: currentLanguage === 'en' ? [
      "Visual Complexity Analyzer - Evaluate how complex your design is for the audience",
      "Color Palette Generator - Create perfect combinations for your brand",
      "Interactive Mood Board - Organize visual ideas and creative references for your projects"
    ] : [
      "Analizador de Complejidad Visual - Evalúa qué tan complejo es tu diseño para la audiencia",
      "Generador de Paletas de Colores - Crea combinaciones perfectas para tu marca",
      "Mood Board Interactivo - Organiza ideas visuales y referencias creativas para tus proyectos"
    ]
  },
  {
    step: currentLanguage === 'en' ? "Audience Tools" : "Herramientas de Audiencias",
    title: currentLanguage === 'en' ? "👥 Audience Tools" : "👥 Herramientas de Audiencias",
    content: currentLanguage === 'en'
      ? "Deep analysis and understanding of your target audience"
      : "Análisis profundo y comprensión de tu audiencia objetivo",
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w2NDI3NzN8MHwxfGFsbHwxMjN8fHx8fHwyfHwxNzIzODA2OTM5fA&ixlib=rb-4.0.3&q=80&w=1080",
    tools: currentLanguage === 'en' ? [
      "Buyer Persona Generator - Create detailed profiles of your ideal customer",
      "Focus Group Simulator - Simulate virtual focus groups with AI"
    ] : [
      "Generador de Buyer Personas - Crea perfiles detallados de tu cliente ideal",
      "Simulador de Focus Group - Simula grupos de enfoque virtuales con IA"
    ]
  },
  {
    step: currentLanguage === 'en' ? "SEO Tools" : "Herramientas SEO",
    title: currentLanguage === 'en' ? "🔍 SEO and Content Tools" : "🔍 Herramientas SEO y Contenido",
    content: currentLanguage === 'en'
      ? "Optimization and analysis for search engines and content"
      : "Optimización y análisis para motores de búsqueda y contenido",
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w2NDI3NzN8MHwxfGFsbHwxMjN8fHx8fHwyfHwxNzIzODA2OTM5fA&ixlib=rb-4.0.3&q=80&w=1080",
    tools: currentLanguage === 'en' ? [
      "SEO Analyzer - Complete analysis of your website to improve positioning",
      "SEO & GPT Optimizer - Optimize content for Google and AI models",
      "SEO Ecommerce Optimizer - Specialized SEO optimization for online stores",
      "Title Analyzer - Optimize headlines for maximum impact and conversion"
    ] : [
      "Analizador SEO - Análisis completo de tu sitio web para mejorar posicionamiento",
      "SEO & GPT Optimizer - Optimiza contenido para Google y modelos de IA",
      "SEO Ecommerce Optimizer - Optimización SEO especializada para tiendas online",
      "Analizador de Títulos - Optimiza headlines para máximo impacto y conversión"
    ]
  }
]

export function MarketingToolsSteps() {
  const { t, currentLanguage } = useLanguage()

  // Obtener características traducidas
  const marketingFeatures = getMarketingFeatures(currentLanguage)

  return (
    <section className="py-20 sm:py-24 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm px-8 py-4 rounded-2xl border border-white/20 shadow-lg text-sm font-semibold text-gray-800 mb-8">
            🛠️ {t('landing.specialized_tools')}
          </div>

          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
            {currentLanguage === 'en'
              ? <>Marketing Tools <span className="text-[#dd3a5a]">for</span> <span className="text-[#3018ef]">Specialists</span></>
              : <>{t('landing.marketing_tools_title').split(' ')[0]} {t('landing.marketing_tools_title').split(' ')[1]}{" "}
                <span className="text-[#dd3a5a]">{t('landing.marketing_tools_title').split(' ')[2]}</span>{" "}
                <span className="text-[#3018ef]">Especializadas</span></>
            }
          </h2>

          <p className="text-lg font-normal text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {currentLanguage === 'en'
              ? 'Each tool is designed by experts to solve specific marketing problems'
              : 'Cada herramienta está diseñada por expertos para resolver problemas específicos de marketing'
            }
          </p>
        </div>

        <FeatureSteps
          features={marketingFeatures}
          autoPlayInterval={4000}
          title={undefined}
          className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 max-w-6xl mx-auto"
        />
      </div>
    </section>
  )
}
