"use client"

import { <PERSON> } from "wouter"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Floating, { FloatingElement } from "@/components/ui/parallax-floating"
import { useLanguage } from "@/contexts/LanguageContext"
import { useState, useEffect } from "react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

const emmaImages = [
  {
    url: "/Anuncio.png",
    title: "Anuncio Publicitario",
    alt: "Anuncio"
  },
  {
    url: "/Astronauta.png",
    title: "<PERSON>tronaut<PERSON>",
    alt: "Astronauta"
  },
  {
    url: "/Gato lentes.webp",
    title: "Gato con Lentes",
    alt: "Gato Lentes"
  },
  {
    url: "/Hombre real.jpeg",
    title: "Hombre Real",
    alt: "Hombre Real"
  },
  {
    url: "/Labial.jpg",
    title: "<PERSON>ial",
    alt: "Labial"
  },
  {
    url: "/PHOTO-2025-06-30-19-02-40.jpg",
    title: "Foto Profesional",
    alt: "Foto Profesional"
  },
  {
    url: "/libro.jpg",
    title: "Libro",
    alt: "Libro"
  },
  {
    url: "/poster.jpg",
    title: "Poster",
    alt: "Poster"
  }
]

export function HeroSection() {
  const { t, currentLanguage } = useLanguage();
  const [isPrelaunchModalOpen, setIsPrelaunchModalOpen] = useState(false);

  // Palabras rotativas que venden emociones y beneficios para Emma
  const rotatingWords = currentLanguage === 'en' ? [
    { text: "incredible", emoji: "✨" },
    { text: "viral", emoji: "🚀" },
    { text: "successful", emoji: "💎" },
    { text: "unique", emoji: "🌟" },
    { text: "powerful", emoji: "⚡" },
    { text: "premium", emoji: "👑" },
    { text: "professional", emoji: "🎯" },
    { text: "epic", emoji: "🔥" },
    { text: "unstoppable", emoji: "💪" },
    { text: "fast", emoji: "⚡" },
    { text: "automatic", emoji: "🤖" },
    { text: "intelligent", emoji: "🧠" },
    { text: "profitable", emoji: "💰" }
  ] : [
    { text: "increíble", emoji: "✨" },
    { text: "viral", emoji: "🚀" },
    { text: "exitoso", emoji: "💎" },
    { text: "único", emoji: "🌟" },
    { text: "poderoso", emoji: "⚡" },
    { text: "premium", emoji: "👑" },
    { text: "profesional", emoji: "🎯" },
    { text: "épico", emoji: "🔥" },
    { text: "imparable", emoji: "💪" },
    { text: "rápido", emoji: "⚡" },
    { text: "automático", emoji: "🤖" },
    { text: "inteligente", emoji: "🧠" },
    { text: "rentable", emoji: "💰" }
  ];

  // Estado para la rotación de palabras
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  // Efecto para rotar las palabras automáticamente
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentWordIndex((prevIndex) => {
        const nextIndex = prevIndex === rotatingWords.length - 1 ? 0 : prevIndex + 1;
        console.log('🔄 Rotating word:', prevIndex, '->', nextIndex, rotatingWords[nextIndex].text, rotatingWords[nextIndex].emoji);
        return nextIndex;
      });
    }, 2500); // Cambia cada 2.5 segundos

    return () => clearInterval(interval);
  }, [rotatingWords.length]);

  console.log('🎯 Current word index:', currentWordIndex, 'Word:', rotatingWords[currentWordIndex].text, rotatingWords[currentWordIndex].emoji);

  console.log('🎯 HeroSection render - Current language:', currentLanguage);
  console.log('🎯 Hero prefix translation:', t('landing.hero_prefix'));
  console.log('🎯 Hero word translation:', t('landing.hero_word'));
  return (
    <>
      <section className="w-full min-h-screen relative bg-white overflow-hidden">
      {/* Floating Images Background */}
      <Floating
        className="absolute inset-0 w-full h-full z-[1] pointer-events-none"
      >
        {/* Floating Images - Balanced Radial Distribution */}
        {/* Top Left Quadrant */}
        <FloatingElement
          depth={0.5}
          className="z-[2] floating-img-1 absolute"
          style={{ top: '10%', left: '5%' }}
        >
          <motion.img
            src={emmaImages[0].url}
            alt={emmaImages[0].alt}
            className="w-20 h-16 sm:w-28 sm:h-20 md:w-32 md:h-24 lg:w-36 lg:h-28 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform -rotate-[8deg] shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          />
        </FloatingElement>

        {/* Top Center */}
        <FloatingElement
          depth={1}
          className="z-[2] floating-img-2"
          style={{ top: '8%', left: '50%', transform: 'translateX(-50%)' }}
        >
          <motion.img
            src={emmaImages[1].url}
            alt={emmaImages[1].alt}
            className="w-24 h-18 sm:w-32 sm:h-24 md:w-40 md:h-30 lg:w-48 lg:h-36 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform rotate-[5deg] shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          />
        </FloatingElement>

        {/* Bottom Left - Movida más abajo */}
        <FloatingElement
          depth={4}
          className="z-[2] floating-img-3"
          style={{ bottom: '5%', left: '12%' }}
        >
          <motion.img
            src={emmaImages[2].url}
            alt={emmaImages[2].alt}
            className="w-28 h-28 sm:w-36 sm:h-36 md:w-44 md:h-44 lg:w-52 lg:h-52 object-cover -rotate-[12deg] hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
          />
        </FloatingElement>

        {/* Top Right Quadrant */}
        <FloatingElement
          depth={2}
          className="z-[2] floating-img-4"
          style={{ top: '15%', right: '8%' }}
        >
          <motion.img
            src={emmaImages[3].url}
            alt={emmaImages[3].alt}
            className="w-20 h-16 sm:w-28 sm:h-22 md:w-36 md:h-28 lg:w-44 lg:h-32 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[15deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.1 }}
          />
        </FloatingElement>

        {/* Bottom Right */}
        <FloatingElement
          depth={1}
          className="z-[2] floating-img-5"
          style={{ bottom: '15%', right: '12%' }}
        >
          <motion.img
            src={emmaImages[4].url}
            alt={emmaImages[4].alt}
            className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[8deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.3 }}
          />
        </FloatingElement>

        {/* Middle Left - Reposicionada */}
        <FloatingElement
          depth={3}
          className="z-[2] floating-img-6"
          style={{ top: '75%', left: '4%', transform: 'translateY(-50%)' }}
        >
          <motion.img
            src={emmaImages[5].url}
            alt={emmaImages[5].alt}
            className="w-18 h-14 sm:w-24 sm:h-18 md:w-32 md:h-24 lg:w-36 lg:h-28 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[12deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
          />
        </FloatingElement>

        {/* Middle Right */}
        <FloatingElement
          depth={2.5}
          className="z-[2] floating-img-7"
          style={{ top: '50%', right: '4%', transform: 'translateY(-50%)' }}
        >
          <motion.img
            src={emmaImages[6].url}
            alt={emmaImages[6].alt}
            className="w-20 h-16 sm:w-28 sm:h-20 md:w-36 md:h-28 lg:w-40 lg:h-32 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl -rotate-[10deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.7 }}
          />
        </FloatingElement>

        {/* Bottom Center */}
        <FloatingElement
          depth={1.5}
          className="z-[2] floating-img-8"
          style={{ bottom: '8%', left: '50%', transform: 'translateX(-50%)' }}
        >
          <motion.img
            src={emmaImages[7].url}
            alt={emmaImages[7].alt}
            className="w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[6deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.9 }}
          />
        </FloatingElement>

        {/* Top Left Inner */}
        <FloatingElement
          depth={3.5}
          className="z-[2] floating-img-9"
          style={{ top: '25%', left: '20%' }}
        >
          <motion.video
            autoPlay
            loop
            muted
            playsInline
            className="w-20 h-20 sm:w-28 sm:h-28 md:w-36 md:h-36 lg:w-44 lg:h-44 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl -rotate-[18deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2.1 }}
          >
            <source src="/video gato brincando.mp4" type="video/mp4" />
          </motion.video>
        </FloatingElement>
      </Floating>

      {/* Emma AI Agent - Interfaz Moderna del Mundo Real */}
      <motion.div
        className="absolute left-8 top-1/4 z-20 hidden xl:block"
        initial={{ opacity: 0, x: -30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 1.5 }}
      >
        <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl p-2.5 w-56 border-2 border-gray-300/60 ring-1 ring-gray-400/20">
          {/* Header con Avatar y Estado */}
          <div className="flex items-center gap-2 mb-3">
            <div className="relative">
              <div className="w-8 h-8 bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-xs">E</span>
              </div>
              <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
            </div>
            <div className="flex-1">
              <h4 className="font-bold text-gray-900 text-xs">Emma AI</h4>
              <p className="text-xs text-green-600 flex items-center gap-1">
                <span className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></span>
                {currentLanguage === 'en' ? 'Working on 3 campaigns' : 'Trabajando en 3 campañas'}
              </p>
            </div>
          </div>

          {/* Actividades en Tiempo Real */}
          <div className="space-y-2">
            {/* Email Campaign */}
            <motion.div
              className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-2 border border-blue-100"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2.0 }}
            >
              <div className="flex items-center gap-2 mb-1">
                <div className="w-5 h-5 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-xs">📧</span>
                </div>
                <span className="text-xs font-semibold text-gray-800">
                  {currentLanguage === 'en' ? 'Email Campaign' : 'Campaña Email'}
                </span>
                <div className="ml-auto flex gap-0.5">
                  <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
              <div className="text-xs text-gray-600 mb-1">
                {currentLanguage === 'en' ? 'Sending to 2,847...' : 'Enviando a 2,847...'}
              </div>
              <div className="w-full bg-blue-200 rounded-full h-1">
                <motion.div
                  className="bg-blue-500 h-1 rounded-full"
                  initial={{ width: '0%' }}
                  animate={{ width: '73%' }}
                  transition={{ duration: 3, ease: "easeOut", delay: 2.2 }}
                />
              </div>
              <div className="text-xs text-blue-600 mt-0.5 font-medium">73% {currentLanguage === 'en' ? 'sent' : 'enviado'}</div>
            </motion.div>

            {/* Social Media Analytics */}
            <motion.div
              className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-2 border border-purple-100"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2.4 }}
            >
              <div className="flex items-center gap-2 mb-1">
                <div className="w-5 h-5 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-xs">📊</span>
                </div>
                <span className="text-xs font-semibold text-gray-800">
                  {currentLanguage === 'en' ? 'Social Analytics' : 'Análisis Social'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <div className="text-center">
                  <div className="text-sm font-bold text-purple-600">+247%</div>
                  <div className="text-xs text-gray-500">{currentLanguage === 'en' ? 'Engagement' : 'Interacción'}</div>
                </div>
                <div className="text-center">
                  <div className="text-sm font-bold text-pink-600">12.4K</div>
                  <div className="text-xs text-gray-500">{currentLanguage === 'en' ? 'Followers' : 'Seguidores'}</div>
                </div>
              </div>
            </motion.div>

            {/* Social Media Management - Más moderno */}
            <motion.div
              className="bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg p-2 border border-pink-100"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2.8 }}
            >
              <div className="flex items-center gap-2 mb-1">
                <div className="w-5 h-5 bg-pink-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-xs">💬</span>
                </div>
                <span className="text-xs font-semibold text-gray-800">
                  {currentLanguage === 'en' ? 'Social Engagement' : 'Engagement Social'}
                </span>
              </div>
              <div className="flex justify-between text-xs text-gray-600">
                <span>{currentLanguage === 'en' ? '23 TikTok • 15 Instagram' : '23 TikTok • 15 Instagram'}</span>
                <div className="flex items-center gap-1">
                  <div className="w-1.5 h-1.5 bg-pink-500 rounded-full animate-pulse"></div>
                  <span>{currentLanguage === 'en' ? 'Replying...' : 'Respondiendo...'}</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Quick Action Button */}
          <motion.div
            className="mt-3 pt-2 border-t border-gray-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 3.2 }}
          >
            <button className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white text-xs font-semibold py-1.5 px-3 rounded-lg hover:opacity-90 transition-opacity">
              {currentLanguage === 'en' ? '🚀 Start Campaign' : '🚀 Iniciar Campaña'}
            </button>
          </motion.div>
        </div>
      </motion.div>

      {/* Buyer Persona Generator - Esquina superior derecha */}
      <motion.div
        className="absolute right-4 top-8 z-20 hidden xl:block"
        initial={{ opacity: 0, x: 30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 1.7 }}
      >
          {/* Buyer Persona Generator - Herramienta específica y potente */}
          <motion.div
            className="bg-white/95 backdrop-blur-md rounded-lg shadow-lg p-2 w-48 border-2 border-gray-300/60 ring-1 ring-gray-400/20 hover:shadow-xl transition-all duration-300"
            whileHover={{ scale: 1.01, y: -1 }}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.8 }}
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="w-6 h-6 bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white text-xs">🎯</span>
              </div>
              <div>
                <h4 className="font-bold text-gray-900 text-xs">{t('landing.hero_tools.buyer_persona.title')}</h4>
                <p className="text-xs text-gray-500">{t('landing.hero_tools.buyer_persona.subtitle')}</p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-[#3018ef]/5 to-[#dd3a5a]/5 rounded-lg p-2 border border-[#3018ef]/10">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-5 h-5 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">👩</span>
                </div>
                <div>
                  <span className="text-xs text-gray-800 font-semibold">{t('landing.hero_tools.buyer_persona.sample_name')}</span>
                  <p className="text-xs text-gray-600">{t('landing.hero_tools.buyer_persona.sample_details')}</p>
                </div>
              </div>
              <div className="flex gap-1 mb-2">
                <span className="bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full text-xs font-medium">{t('landing.hero_tools.buyer_persona.tag_tech_savvy')}</span>
                <span className="bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full text-xs font-medium">{t('landing.hero_tools.buyer_persona.tag_b2b')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-1 h-1 bg-[#3018ef] rounded-full animate-bounce"></div>
                  <div className="w-1 h-1 bg-[#3018ef] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-1 h-1 bg-[#3018ef] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-xs text-gray-600">{t('landing.hero_tools.buyer_persona.analyzing')}</span>
              </div>
            </div>
          </motion.div>
      </motion.div>

      {/* SEO + GPT Optimizer - Posición media derecha */}
      <motion.div
        className="absolute right-6 top-64 z-20 hidden xl:block"
        initial={{ opacity: 0, x: 30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 2.0 }}
      >
        <motion.div
          className="bg-white/95 backdrop-blur-md rounded-lg shadow-lg p-2 w-48 border-2 border-gray-300/60 ring-1 ring-gray-400/20 hover:shadow-xl transition-all duration-300"
          whileHover={{ scale: 1.01, y: -1 }}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 2.0 }}
        >
            <div className="flex items-center gap-2 mb-1">
              <div className="w-5 h-5 bg-gradient-to-br from-[#00d2d3] to-[#54a0ff] rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white text-xs">📊</span>
              </div>
              <div>
                <h4 className="font-bold text-gray-900 text-xs">{t('landing.hero_tools.seo_gpt.title')}</h4>
                <p className="text-xs text-gray-500">{t('landing.hero_tools.seo_gpt.subtitle')}</p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-[#00d2d3]/5 to-[#54a0ff]/5 rounded-lg p-2 border border-[#00d2d3]/10">
              <div className="flex justify-between items-center mb-2">
                <span className="text-xs text-gray-600 font-medium">{t('landing.hero_tools.seo_gpt.score_label')}</span>
                <div className="flex items-center gap-1">
                  <span className="text-sm font-bold text-green-600">98</span>
                  <span className="text-xs text-gray-500">/100</span>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5 mb-2 overflow-hidden">
                <motion.div
                  className="bg-gradient-to-r from-[#00d2d3] to-[#54a0ff] h-1.5 rounded-full"
                  initial={{ width: '0%' }}
                  animate={{ width: '98%' }}
                  transition={{ duration: 2, ease: "easeOut", delay: 2.2 }}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-600">{t('landing.hero_tools.seo_gpt.optimizing')}</span>
                </div>
                <span className="text-xs text-green-600 font-semibold">{t('landing.hero_tools.seo_gpt.excellent')}</span>
              </div>
            </div>
        </motion.div>
      </motion.div>

      {/* Post Generator - Esquina inferior derecha */}
      <motion.div
        className="absolute right-4 bottom-8 z-20 hidden xl:block"
        initial={{ opacity: 0, x: 30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 2.2 }}
      >
        <motion.div
          className="bg-white/95 backdrop-blur-md rounded-lg shadow-lg p-2 w-48 border-2 border-gray-300/60 ring-1 ring-gray-400/20 hover:shadow-xl transition-all duration-300"
          whileHover={{ scale: 1.01, y: -1 }}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 2.2 }}
        >
            <div className="flex items-center gap-2 mb-2">
              <div className="w-6 h-6 bg-gradient-to-br from-[#ff6b6b] to-[#ee5a24] rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white text-xs">📱</span>
              </div>
              <div>
                <h4 className="font-bold text-gray-900 text-xs">{t('landing.hero_tools.post_generator.title')}</h4>
                <p className="text-xs text-gray-500">{t('landing.hero_tools.post_generator.subtitle')}</p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-[#ff6b6b]/5 to-[#ee5a24]/5 rounded-lg p-2 border border-[#ff6b6b]/10">
              <div className="w-full h-16 bg-gradient-to-br from-[#ff6b6b]/10 to-[#ee5a24]/10 rounded-md mb-2 flex items-center justify-center border border-[#ff6b6b]/20 relative overflow-hidden">
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                  animate={{ x: [-100, 300] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                />
                <span className="text-xs text-gray-600 font-medium">🎨 {t('landing.hero_tools.post_generator.generating')}</span>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-800 font-semibold">{t('landing.hero_tools.post_generator.sample_text')}</p>
                <div className="flex gap-1 flex-wrap">
                  <span className="bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full text-xs font-medium">{t('landing.hero_tools.post_generator.tag_ai')}</span>
                  <span className="bg-purple-100 text-purple-700 px-1.5 py-0.5 rounded-full text-xs font-medium">{t('landing.hero_tools.post_generator.tag_marketing')}</span>
                  <span className="bg-pink-100 text-pink-700 px-1.5 py-0.5 rounded-full text-xs font-medium">{t('landing.hero_tools.post_generator.tag_viral')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-600">{t('landing.hero_tools.post_generator.optimizing')}</span>
                </div>
              </div>
            </div>
        </motion.div>
      </motion.div>

      {/* Main Content - HERO ARRIBA */}
      <div className="absolute inset-0 flex flex-col justify-center items-center z-10 pt-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 md:px-8 text-center">
        <motion.div
          className="text-center mb-8 sm:mb-12"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 30 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
        >
          {/* Título Principal - SÚPER LEGIBLE Y LLAMATIVO CON AZUL EMMA */}
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-tight mb-6">
            <strong className="text-[#3018ef]">
              {currentLanguage === 'en' ? "The world's first virtual marketing agency" : "La primera agencia virtual de marketing del mundo"}
            </strong>
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
            {currentLanguage === 'en' ? 'Specialized AI • Real Results • Available 24/7' : 'IA especializada • Resultados reales • Disponible 24/7'}
          </p>
        </motion.div>

        <motion.div
          className="text-center mb-12 sm:mb-16"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 30 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.6 }}
        >
          {/* Texto Rotativo - AHORA VA ABAJO */}
          <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-4 max-w-4xl mx-auto leading-tight">
            {currentLanguage === 'en' ? (
              <>Create <span className="bg-gradient-to-r from-[#3018ef] via-[#8b5cf6] to-[#dd3a5a] bg-clip-text text-transparent font-black">
                {rotatingWords[currentWordIndex].text}
              </span>{" "}
              <span className="text-gray-900">
                {rotatingWords[currentWordIndex].emoji}
              </span>{" "}content with <span className="text-[#dd3a5a]">Emma AI</span></>
            ) : (
              <>Crea contenido{" "}
              <span className="bg-gradient-to-r from-[#3018ef] via-[#8b5cf6] to-[#dd3a5a] bg-clip-text text-transparent font-black">
                {rotatingWords[currentWordIndex].text}
              </span>
              {" "}
              <span className="text-gray-900">
                {rotatingWords[currentWordIndex].emoji}
              </span>
              {" "}con <span className="text-[#dd3a5a]">Emma AI</span></>
            )}
          </p>
        </motion.div>

        <motion.div
          className="flex flex-col sm:flex-row justify-center gap-6 sm:gap-8 items-center"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 40 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.8 }}
        >
          {/* Botón Principal */}
          <motion.div
            whileHover={{
              scale: 1.05,
              y: -2
            }}
            whileTap={{ scale: 0.98 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            <Link href="/register">
              <Button
                variant="red"
                size="lg"
                className="relative overflow-hidden text-lg font-bold tracking-tight px-10 py-5 rounded-2xl shadow-2xl w-full sm:w-auto min-w-[220px] bg-gradient-to-r from-[#dd3a5a] to-[#ff6b8a] hover:from-[#c73650] hover:to-[#dd3a5a] border-0 text-white group"
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  {currentLanguage === 'en' ? 'Make Marketing Effortless' : 'Haz Marketing Sin Esfuerzo'}
                  <span className="text-xl">→</span>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Button>
            </Link>
          </motion.div>

          {/* Botón Secundario */}
          <motion.div
            whileHover={{
              scale: 1.05,
              y: -2
            }}
            whileTap={{ scale: 0.98 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            <Button
              variant="outline"
              size="lg"
              className="relative overflow-hidden text-lg font-bold tracking-tight px-10 py-5 rounded-2xl border-2 border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white transition-all duration-300 w-full sm:w-auto min-w-[220px] bg-white/80 backdrop-blur-sm shadow-xl group"
              onClick={() => setIsPrelaunchModalOpen(true)}
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                🚀 {currentLanguage === 'en' ? 'Join Beta Prelaunch' : 'Únete al Pre-lanzamiento Beta'}
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Button>
          </motion.div>
        </motion.div>
        </div>
      </div>


    </section>

    {/* Modal de Pre-lanzamiento Beta */}
    <Dialog open={isPrelaunchModalOpen} onOpenChange={setIsPrelaunchModalOpen}>
      <DialogContent className="max-w-4xl w-full max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-2xl font-bold text-center text-[#3018ef]">
            {currentLanguage === 'en' ? '🚀 Join Emma AI Beta Prelaunch' : '🚀 Únete al Pre-lanzamiento Beta de Emma AI'}
          </DialogTitle>
        </DialogHeader>
        <div className="p-6 pt-4">
          <iframe
            className="airtable-embed w-full rounded-lg"
            src="https://airtable.com/embed/appzYoWLqBEF1LPWG/pagp59D6zMN9JsG9X/form"
            style={{ background: 'transparent', border: '1px solid #ccc', width: '100%', height: '533px' }}
          />
        </div>
      </DialogContent>
    </Dialog>
    </>
  )
}
