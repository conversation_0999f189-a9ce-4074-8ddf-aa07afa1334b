"use client";

import { useState, useEffect } from "react"
import { <PERSON> } from "wouter"
import {
  <PERSON>,
  Shield,
  Clock,
  TrendingUp,
  ArrowRight,
  <PERSON>rkles,
  Zap
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/contexts/LanguageContext"

interface Guarantee {
  icon: React.ReactNode
  title: string
  description: string
  color: string
}









const getClientStats = (t: (key: string) => string) => [
  { metric: "4.9/5", label: t('landing.average_rating'), icon: "⭐" },
  { metric: "1,000+", label: t('landing.companies_transformed'), icon: "🚀" },
  { metric: "300%", label: t('landing.average_roi_increase'), icon: "📈" }
]

const getFinalBenefits = (t: (key: string) => string) => [
  `✅ ${t('landing.setup_5_minutes')}`,
  `✅ ${t('landing.no_credit_card')}`,
  `✅ ${t('landing.support_24_7')}`,
  `✅ ${t('landing.guaranteed_results')}`
]

const getGuarantees = (t: (key: string) => string): Guarantee[] => [
  {
    icon: <Shield className="w-8 h-8" />,
    title: t('landing.guarantee_30_days'),
    description: t('landing.guarantee_30_days_desc'),
    color: "text-[#3018ef]"
  },
  {
    icon: <Clock className="w-8 h-8" />,
    title: t('landing.no_contracts'),
    description: t('landing.no_contracts_desc'),
    color: "text-[#dd3a5a]"
  },
  {
    icon: <TrendingUp className="w-8 h-8" />,
    title: t('landing.guaranteed_results_title'),
    description: t('landing.guaranteed_results_desc'),
    color: "text-[#3018ef]"
  }
]

const getUrgencyFactors = (t: (key: string) => string) => [
  t('landing.urgency_50_companies'),
  t('landing.urgency_special_price'),
  t('landing.urgency_support_limited'),
  t('landing.urgency_early_access')
]

export function FinalCTA() {
  const { t } = useLanguage();
  const [isVisible, setIsVisible] = useState(false)
  const [currentUrgency, setCurrentUrgency] = useState(0)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const section = document.getElementById('final-cta-section')
    if (section) observer.observe(section)

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentUrgency((prev) => (prev + 1) % getUrgencyFactors(t).length)
    }, 3000)

    return () => clearInterval(interval)
  }, [t])

  return (
    <section
      id="final-cta-section"
      className="py-20 sm:py-24 bg-white relative overflow-hidden"
    >
      {/* Clean Background */}
      <div className="absolute inset-0 bg-white" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Main CTA */}
        <div className="text-center mb-16">
          <div
            className={`transition-all duration-700 ease-out ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="inline-block bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm px-8 py-4 rounded-2xl border border-white/20 shadow-lg text-sm font-semibold text-gray-800 mb-8">
              <Rocket className="w-4 h-4 mr-2 inline" />
              {t('landing.digital_transformation_starts')}
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {t('landing.ready_to_revolutionize')}{" "}
              <span className="text-[#3018ef]">
                {t('landing.revolutionize')}
              </span>{" "}
              {t('landing.your_marketing')}
            </h2>

            <p className="text-xl sm:text-2xl font-medium text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed">
              {t('landing.join_1000_companies')}
            </p>

            {/* Urgency Indicator */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100 p-6 mb-8 max-w-3xl mx-auto">
              <div className="flex items-center justify-center">
                <Zap className="w-6 h-6 text-[#dd3a5a] mr-4 animate-pulse" />
                <span className="text-lg font-semibold text-gray-900">
                  {getUrgencyFactors(t)[currentUrgency]}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Buttons */}
        <div
          className={`flex flex-col sm:flex-row gap-6 justify-center items-center mb-16 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '200ms' }}
        >
          <Link href="/register">
            <Button variant="red" size="lg" className="group px-8 py-4 text-lg font-bold">
              <Sparkles className="w-6 h-6 mr-3" />
              {t('landing.start_now')}
              <ArrowRight className="w-6 h-6 ml-3 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>

          <Link href="#demo">
            <Button variant="white" size="lg" className="px-8 py-4 text-lg font-bold">
              {t('landing.see_demo')}
            </Button>
          </Link>
        </div>

        {/* Guarantees */}
        <div
          className={`grid md:grid-cols-3 gap-8 mb-16 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '400ms' }}
        >
          {getGuarantees(t).map((guarantee, index) => (
            <div
              key={index}
              className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl hover:scale-105 transition-all duration-300"
            >
              <div className={`flex justify-center ${guarantee.color} mb-6`}>
                {guarantee.icon}
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-900">{guarantee.title}</h3>
              <p className="text-base text-gray-600 font-medium">{guarantee.description}</p>
            </div>
          ))}
        </div>

        {/* Social Proof */}
        <div
          className={`text-center transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 p-12 max-w-5xl mx-auto">
            <h3 className="text-3xl font-bold mb-8 text-gray-900">
              {t('landing.what_clients_say')}
            </h3>

            <div className="grid md:grid-cols-3 gap-8 mb-10">
              {getClientStats(t).map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-3xl font-bold mb-2 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">{stat.metric}</div>
                  <div className="text-base text-gray-600 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>

            <blockquote className="text-xl italic mb-6 text-gray-700 font-medium max-w-3xl mx-auto">
              "{t('landing.testimonial_quote')}"
            </blockquote>

            <div className="flex items-center justify-center">
              <div className="text-center">
                <div className="font-bold text-gray-900 text-lg">Carlos Mendoza</div>
                <div className="text-base text-[#3018ef] font-semibold">CEO, TechStartup Inc.</div>
              </div>
            </div>
          </div>
        </div>

        {/* Final Push */}
        <div
          className={`text-center mt-12 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '800ms' }}
        >
          <p className="text-lg mb-6 text-gray-600">
            {t('landing.dont_wait_longer')}
          </p>

          <div className="flex flex-wrap justify-center gap-4 text-sm">
            {getFinalBenefits(t).map((benefit, index) => (
              <span
                key={index}
                className="px-6 py-3 bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200 shadow-lg text-gray-900 font-semibold hover:scale-105 transition-transform duration-300"
              >
                {benefit}
              </span>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
