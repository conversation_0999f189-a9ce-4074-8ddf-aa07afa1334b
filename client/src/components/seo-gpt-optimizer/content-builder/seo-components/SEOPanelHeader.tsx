/**
 * GPT Ranking Panel Header Component
 * Header section for the GPT Ranking intelligence panel
 */

import React from 'react';
import { Brain, Clock, RefreshCw } from 'lucide-react';
import { formatTime } from './seoUtils';

interface GPTRankingPanelHeaderProps {
  lastAnalyzed?: Date | null;
  loading: boolean;
  onRefresh: () => void;
  className?: string;
}

const GPTRankingPanelHeader: React.FC<GPTRankingPanelHeaderProps> = ({
  lastAnalyzed,
  loading,
  onRefresh,
  className = ''
}) => {
  return (
    <div 
      className={`gpt-ranking-panel-header ${className}`}
      style={{
        padding: '16px 20px',
        borderBottom: '1px solid #e8eaed',
        background: 'linear-gradient(135deg, #f8f9fa 0%, #e8f0fe 100%)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}
    >
      {/* Left side - Title and subtitle */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Brain size={20} color="#3018ef" />
        <div>
          <h3 style={{
            margin: 0,
            color: '#202124',
            fontSize: '16px',
            fontWeight: '600'
          }}>
            GPT Ranking
          </h3>
          <p style={{
            margin: 0,
            fontSize: '12px',
            color: '#5f6368'
          }}>
            Análisis en tiempo real
          </p>
        </div>
      </div>
      
      {/* Right side - Last analyzed time and refresh button */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {lastAnalyzed && (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '4px' 
          }}>
            <Clock size={12} color="#5f6368" />
            <span style={{ 
              fontSize: '12px', 
              color: '#5f6368' 
            }}>
              {formatTime(lastAnalyzed)}
            </span>
          </div>
        )}
        
        <button
          onClick={onRefresh}
          disabled={loading}
          style={{
            padding: '8px',
            border: 'none',
            borderRadius: '12px',
            background: loading ? '#f3f4f6' : 'transparent',
            cursor: loading ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
          }}
          onMouseEnter={(e) => {
            if (!loading) {
              e.currentTarget.style.background = '#f3f4f6';
              e.currentTarget.style.transform = 'scale(1.05)';
              e.currentTarget.style.boxShadow = '0 2px 8px 0 rgba(0, 0, 0, 0.1)';
            }
          }}
          onMouseLeave={(e) => {
            if (!loading) {
              e.currentTarget.style.background = 'transparent';
              e.currentTarget.style.transform = 'scale(1)';
              e.currentTarget.style.boxShadow = 'none';
            }
          }}
          title="Actualizar análisis"
          aria-label="Actualizar análisis GPT Ranking"
        >
          <RefreshCw 
            size={16} 
            color={loading ? '#9aa0a6' : '#5f6368'}
            style={{ 
              animation: loading ? 'spin 1s linear infinite' : 'none'
            }}
          />
        </button>
      </div>
      
      {/* CSS for refresh button animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default GPTRankingPanelHeader;
