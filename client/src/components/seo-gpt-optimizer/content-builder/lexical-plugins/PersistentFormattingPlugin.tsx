/**
 * Simple Persistent Formatting Plugin
 * Just ensures the editor maintains focus and format state
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect } from 'react';

export function PersistentFormattingPlugin(): null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // Simple approach - let the toolbar handle everything
    // This plugin just ensures the editor is properly initialized
    console.log('🎨 PersistentFormattingPlugin initialized');
    return () => {
      // Cleanup
    };
  }, [editor]);

  return null;
}

export default PersistentFormattingPlugin;
