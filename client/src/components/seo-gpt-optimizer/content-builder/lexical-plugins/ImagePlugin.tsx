/**
 * Lexical Image Plugin
 * Plugin for handling image insertion and management in the editor
 * 
 * Features:
 * - Image insertion commands
 * - Drag and drop support
 * - Paste image support
 * - Image node registration
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $createParagraphNode,
  $insertNodes,
  $isRootOrShadowRoot,
  COMMAND_PRIORITY_EDITOR,
  createCommand,
  LexicalCommand,
} from 'lexical';
import { $wrapNodeInElement, mergeRegister } from '@lexical/utils';
import { useEffect } from 'react';

import {
  $createSimpleImageNode,
  SimpleImageNode,
} from '../lexical-nodes/SimpleImageNode';

export type InsertImagePayload = Readonly<{
  src: string;
  altText: string;
  key?: string;
}>;

export const INSERT_IMAGE_COMMAND: LexicalCommand<InsertImagePayload> =
  createCommand('INSERT_IMAGE_COMMAND');

export function ImagePlugin({
  captionsEnabled,
}: {
  captionsEnabled?: boolean;
}): JSX.Element | null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (!editor.hasNodes([SimpleImageNode])) {
      throw new Error('ImagePlugin: SimpleImageNode not registered on editor');
    }

    return mergeRegister(
      editor.registerCommand<InsertImagePayload>(
        INSERT_IMAGE_COMMAND,
        (payload) => {
          console.log('🖼️ ImagePlugin: Inserting image with payload:', {
            altText: payload.altText,
            width: payload.width,
            height: payload.height
          });

          try {
            const imageNode = $createSimpleImageNode(payload);
            $insertNodes([imageNode]);

            if ($isRootOrShadowRoot(imageNode.getParentOrThrow())) {
              $wrapNodeInElement(imageNode, $createParagraphNode).selectEnd();
            }

            console.log('✅ ImagePlugin: Image inserted successfully');
            return true;
          } catch (error) {
            console.error('❌ ImagePlugin: Error inserting image:', error);
            return false;
          }
        },
        COMMAND_PRIORITY_EDITOR,
      ),
    );
  }, [captionsEnabled, editor]);

  return null;
}

// Helper function to insert image with enhanced quality settings
export function insertImage(
  editor: any,
  src: string,
  altText: string,
  caption?: string,
  width?: number,
  height?: number
): void {
  console.log('🖼️ ImagePlugin: Inserting image with payload:', {
    src,
    altText,
    caption,
    width,
    height
  });

  try {
    // Ensure proper image dimensions
    const imageWidth = width || 600;
    const imageHeight = height || 400;
    const maxWidth = Math.min(imageWidth, 800); // Limit max width for better layout

    editor.dispatchCommand(INSERT_IMAGE_COMMAND, {
      src,
      altText: altText || 'Generated image',
      caption: caption || altText || 'Generated image',
      width: imageWidth,
      height: imageHeight,
      maxWidth: maxWidth,
      showCaption: !!caption,
      captionsEnabled: true,
    });
    console.log('✅ ImagePlugin: Image insertion command dispatched successfully');
  } catch (error) {
    console.error('❌ ImagePlugin: Error dispatching image insertion command:', error);
    throw error;
  }
}

// Helper function to insert image with custom formatting
export function insertImageWithFormatting(
  editor: any,
  src: string,
  altText: string,
  options: {
    caption?: string;
    width?: number;
    height?: number;
    alignment?: 'left' | 'center' | 'right';
    quality?: 'high' | 'medium' | 'low';
  } = {}
): void {
  const {
    caption,
    width = 600,
    height = 400,
    alignment = 'center',
    quality = 'high'
  } = options;

  console.log('🖼️ ImagePlugin: Inserting image with custom formatting:', {
    src,
    altText,
    options
  });

  try {
    editor.dispatchCommand(INSERT_IMAGE_COMMAND, {
      src,
      altText: altText || 'Generated image',
      caption: caption || altText || 'Generated image',
      width,
      height,
      maxWidth: Math.min(width, 800),
      showCaption: !!caption,
      captionsEnabled: true,
      alignment,
      quality
    });
    console.log('✅ ImagePlugin: Image with custom formatting inserted successfully');
  } catch (error) {
    console.error('❌ ImagePlugin: Error inserting image with formatting:', error);
    throw error;
  }
}
