/**
 * Editor Functionality Test Component
 * Comprehensive testing for Google Docs-style editor features
 */

import React, { useState, useRef } from 'react';
import { Play, CheckCircle, XCircle, AlertCircle, FileText } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message: string;
  duration?: number;
}

const EditorFunctionalityTest: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Toolbar Visibility', status: 'pending', message: 'Not started' },
    { name: 'Bold Formatting', status: 'pending', message: 'Not started' },
    { name: 'Italic Formatting', status: 'pending', message: 'Not started' },
    { name: 'Underline Formatting', status: 'pending', message: 'Not started' },
    { name: 'Font Size Controls', status: 'pending', message: 'Not started' },
    { name: 'Font Family Controls', status: 'pending', message: 'Not started' },
    { name: 'Heading Styles', status: 'pending', message: 'Not started' },
    { name: 'Text Alignment', status: 'pending', message: 'Not started' },
    { name: 'Bulleted Lists', status: 'pending', message: 'Not started' },
    { name: 'Numbered Lists', status: 'pending', message: 'Not started' },
    { name: 'Text Color Controls', status: 'pending', message: 'Not started' },
    { name: 'Undo/Redo Functionality', status: 'pending', message: 'Not started' },
    { name: 'Persistent Bold Formatting', status: 'pending', message: 'Not started' },
    { name: 'Toggle Behavior', status: 'pending', message: 'Not started' }
  ]);

  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, ...updates } : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: Toolbar Visibility
      await runTest(0, async () => {
        const toolbar = document.querySelector('.advanced-formatting-toolbar');
        if (!toolbar) {
          throw new Error('Advanced formatting toolbar not found');
        }
        return 'Toolbar is visible and properly rendered';
      });

      // Test 2: Bold Formatting
      await runTest(1, async () => {
        const boldButton = document.querySelector('.toolbar-btn[title*="Negrita"]');
        if (!boldButton) {
          throw new Error('Bold button not found in toolbar');
        }
        return 'Bold button is available and clickable';
      });

      // Test 3: Italic Formatting
      await runTest(2, async () => {
        const italicButton = document.querySelector('.toolbar-btn[title*="Cursiva"]');
        if (!italicButton) {
          throw new Error('Italic button not found in toolbar');
        }
        return 'Italic button is available and clickable';
      });

      // Test 4: Underline Formatting
      await runTest(3, async () => {
        const underlineButton = document.querySelector('.toolbar-btn[title*="Subrayado"]');
        if (!underlineButton) {
          throw new Error('Underline button not found in toolbar');
        }
        return 'Underline button is available and clickable';
      });

      // Test 5: Font Size Controls
      await runTest(4, async () => {
        const fontSizeDropdown = document.querySelector('.size-dropdown');
        if (!fontSizeDropdown) {
          throw new Error('Font size dropdown not found');
        }
        return 'Font size controls are properly implemented';
      });

      // Test 6: Font Family Controls
      await runTest(5, async () => {
        const fontFamilyDropdown = document.querySelector('.toolbar-dropdown[title*="Fuente"]');
        if (!fontFamilyDropdown) {
          throw new Error('Font family dropdown not found');
        }
        return 'Font family controls are properly implemented';
      });

      // Test 7: Heading Styles
      await runTest(6, async () => {
        const headingDropdown = document.querySelector('.heading-dropdown');
        if (!headingDropdown) {
          throw new Error('Heading styles dropdown not found');
        }
        return 'Heading styles are properly implemented';
      });

      // Test 8: Text Alignment
      await runTest(7, async () => {
        const alignButtons = document.querySelectorAll('.toolbar-btn[title*="Alinear"], .toolbar-btn[title*="Centrar"]');
        if (alignButtons.length < 3) {
          throw new Error('Text alignment buttons not found (expected at least 3)');
        }
        return 'Text alignment controls are properly implemented';
      });

      // Test 9: Bulleted Lists
      await runTest(8, async () => {
        const bulletListButton = document.querySelector('.toolbar-btn[title*="Lista con viñetas"]');
        if (!bulletListButton) {
          throw new Error('Bulleted list button not found');
        }
        return 'Bulleted list functionality is available';
      });

      // Test 10: Numbered Lists
      await runTest(9, async () => {
        const numberedListButton = document.querySelector('.toolbar-btn[title*="Lista numerada"]');
        if (!numberedListButton) {
          throw new Error('Numbered list button not found');
        }
        return 'Numbered list functionality is available';
      });

      // Test 11: Text Color Controls
      await runTest(10, async () => {
        const colorButton = document.querySelector('.color-picker-btn');
        if (!colorButton) {
          throw new Error('Text color button not found');
        }
        return 'Text color controls are properly implemented';
      });

      // Test 12: Undo/Redo Functionality
      await runTest(11, async () => {
        const undoButton = document.querySelector('.toolbar-btn[title*="Deshacer"]');
        const redoButton = document.querySelector('.toolbar-btn[title*="Rehacer"]');
        if (!undoButton || !redoButton) {
          throw new Error('Undo/Redo buttons not found');
        }
        return 'Undo/Redo functionality is properly implemented';
      });

      // Test 13: Persistent Bold Formatting
      await runTest(12, async () => {
        const boldButton = document.querySelector('.toolbar-btn[title*="Negrita"]') as HTMLButtonElement;
        if (!boldButton) {
          throw new Error('Bold button not found');
        }

        // Click bold button
        boldButton.click();

        // Check if button appears active
        await new Promise(resolve => setTimeout(resolve, 100));
        const isActive = boldButton.classList.contains('active') ||
                        boldButton.getAttribute('aria-pressed') === 'true' ||
                        boldButton.style.background !== 'transparent';

        if (!isActive) {
          throw new Error('Bold button does not appear active after clicking');
        }

        return 'Bold formatting persists and button shows active state';
      });

      // Test 14: Toggle Behavior
      await runTest(13, async () => {
        const boldButton = document.querySelector('.toolbar-btn[title*="Negrita"]') as HTMLButtonElement;
        if (!boldButton) {
          throw new Error('Bold button not found');
        }

        // Click bold button twice to test toggle
        boldButton.click();
        await new Promise(resolve => setTimeout(resolve, 100));
        boldButton.click();
        await new Promise(resolve => setTimeout(resolve, 100));

        // Check if button appears inactive
        const isInactive = !boldButton.classList.contains('active') &&
                          boldButton.getAttribute('aria-pressed') !== 'true';

        if (!isInactive) {
          throw new Error('Bold button does not toggle off properly');
        }

        return 'Toggle behavior works correctly for formatting buttons';
      });

    } catch (error) {
      console.error('Test suite failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const runTest = async (index: number, testFn: () => Promise<string>) => {
    const startTime = Date.now();
    
    updateTest(index, { status: 'running', message: 'Running...' });
    
    try {
      const message = await testFn();
      const duration = Date.now() - startTime;
      
      updateTest(index, { 
        status: 'success', 
        message, 
        duration 
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      
      updateTest(index, { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error',
        duration 
      });
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'running':
        return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'running':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const successCount = tests.filter(t => t.status === 'success').length;
  const errorCount = tests.filter(t => t.status === 'error').length;

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center gap-3 mb-6">
        <FileText className="w-8 h-8 text-blue-600" />
        <h2 className="text-2xl font-bold text-gray-900">Editor Functionality Test Suite</h2>
      </div>

      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-blue-800">
          This test suite verifies that all Google Docs-style editor features are working correctly.
          Click "Run Tests" to validate the toolbar controls and formatting functionality.
        </p>
      </div>

      <div className="flex items-center justify-between mb-6">
        <div className="flex gap-4">
          <span className="text-sm text-gray-600">
            ✅ Passed: {successCount}/{tests.length}
          </span>
          <span className="text-sm text-gray-600">
            ❌ Failed: {errorCount}/{tests.length}
          </span>
        </div>
        
        <button
          onClick={runTests}
          disabled={isRunning}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Play className="w-4 h-4" />
          {isRunning ? 'Running Tests...' : 'Run Tests'}
        </button>
      </div>

      <div className="space-y-3">
        {tests.map((test, index) => (
          <div
            key={index}
            className={`p-4 border rounded-lg transition-colors ${getStatusColor(test.status)}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getStatusIcon(test.status)}
                <span className="font-medium text-gray-900">{test.name}</span>
              </div>
              {test.duration && (
                <span className="text-sm text-gray-500">{test.duration}ms</span>
              )}
            </div>
            <p className="mt-2 text-sm text-gray-600">{test.message}</p>
          </div>
        ))}
      </div>

      {successCount === tests.length && successCount > 0 && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-800 font-medium">🎉 All tests passed! The editor is fully functional.</p>
        </div>
      )}
    </div>
  );
};

export default EditorFunctionalityTest;
