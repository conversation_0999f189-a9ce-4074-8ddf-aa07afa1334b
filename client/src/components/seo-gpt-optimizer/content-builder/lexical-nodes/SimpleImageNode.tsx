/**
 * SUPER SIMPLE ImageNode for Lexical Editor
 * No complex features, just basic image display
 */

import React from 'react';
import {
  $applyNodeReplacement,
  DecoratorNode,
  LexicalNode,
  NodeKey,
  SerializedLexicalNode,
  Spread,
} from 'lexical';

export type SerializedSimpleImageNode = Spread<
  {
    altText: string;
    src: string;
    type: 'simple-image';
    version: 1;
  },
  SerializedLexicalNode
>;

// SUPER SIMPLE Image Node Class
export class SimpleImageNode extends DecoratorNode<JSX.Element> {
  __src: string;
  __altText: string;

  static getType(): string {
    return 'simple-image';
  }

  static clone(node: SimpleImageNode): SimpleImageNode {
    return new SimpleImageNode(node.__src, node.__altText, node.__key);
  }

  constructor(src: string, altText: string, key?: NodeKey) {
    super(key);
    this.__src = src;
    this.__altText = altText;
  }

  exportJSON(): SerializedSimpleImageNode {
    return {
      altText: this.__altText,
      src: this.__src,
      type: 'simple-image',
      version: 1,
    };
  }

  static importJSON(serializedNode: SerializedSimpleImageNode): SimpleImageNode {
    const { altText, src } = serializedNode;
    return new SimpleImageNode(src, altText);
  }

  // Simple getters
  getSrc(): string {
    return this.__src;
  }

  getAltText(): string {
    return this.__altText;
  }

  decorate(): JSX.Element {
    console.log('🖼️ SimpleImageNode.decorate() called with src:', this.__src.substring(0, 50));
    
    // SUPER SIMPLE JSX - NO DIVS, NO COMPLEX STYLES
    return <img src={this.__src} alt={this.__altText} style={{maxWidth:'100%'}} />;
  }
}

export function $createSimpleImageNode({
  src,
  altText,
  key,
}: { src: string; altText: string; key?: NodeKey }): SimpleImageNode {
  return $applyNodeReplacement(new SimpleImageNode(src, altText, key));
}

export function $isSimpleImageNode(
  node: LexicalNode | null | undefined,
): node is SimpleImageNode {
  return node instanceof SimpleImageNode;
}
