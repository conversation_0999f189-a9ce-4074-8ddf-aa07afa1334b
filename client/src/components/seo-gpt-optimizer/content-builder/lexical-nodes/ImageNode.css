/**
 * Image Node Styles
 * CSS for enhanced image editing experience in Lexical editor
 */

/* Image selection and focus states */
.image-node img.focused {
  outline: none;
  box-shadow: 0 0 0 2px #3018ef, 0 4px 12px rgba(48, 24, 239, 0.15);
  transition: all 0.2s ease;
}

.image-node img.draggable {
  cursor: move;
}

/* Resize handles */
.resize-handle {
  position: absolute;
  background: #3018ef;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.resize-handle:hover {
  background: #dd3a5a;
  transform: scale(1.1);
}

.resize-handle.corner {
  width: 12px;
  height: 12px;
}

.resize-handle.se {
  bottom: -6px;
  right: -6px;
  cursor: nw-resize;
}

.resize-handle.sw {
  bottom: -6px;
  left: -6px;
  cursor: ne-resize;
}

.resize-handle.ne {
  top: -6px;
  right: -6px;
  cursor: nw-resize;
}

.resize-handle.nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

/* Size indicator */
.size-indicator {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #3018ef, #dd3a5a);
  color: white;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Image container */
.image-container {
  position: relative;
  display: inline-block;
  margin: 8px 0;
}

.image-container:hover .resize-handle {
  opacity: 1;
}

/* Caption styles */
.image-caption {
  margin-top: 8px;
  font-size: 12px;
  color: #5f6368;
  font-style: italic;
  text-align: center;
  line-height: 1.4;
}

.image-caption.editable {
  border: 1px dashed #ccc;
  padding: 4px 8px;
  border-radius: 4px;
  background: #f9f9f9;
}

.image-caption.editable:focus {
  outline: none;
  border-color: #3018ef;
  background: white;
}

/* Responsive behavior */
@media (max-width: 768px) {
  .resize-handle {
    width: 16px;
    height: 16px;
  }
  
  .size-indicator {
    font-size: 10px;
    padding: 3px 8px;
  }
}

/* Loading state */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Error state */
.image-error {
  border: 2px dashed #ff4444;
  background: #fff5f5;
  color: #ff4444;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  font-size: 14px;
}
