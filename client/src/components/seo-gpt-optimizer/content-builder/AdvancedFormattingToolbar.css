/**
 * Modern Canva-Style Formatting Toolbar
 * Clean, minimal design with rounded elements
 */

.advanced-formatting-toolbar {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  flex-wrap: wrap;
  position: relative;
  z-index: 10;
  backdrop-filter: blur(8px);
}

/* Toolbar Groups */
.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;
  padding: 4px 6px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.toolbar-group:hover {
  background: #f9fafb;
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background: #e5e7eb;
  margin: 0 8px;
  border-radius: 0.5px;
}

/* Modern Canva-Style Toolbar Buttons */
.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-weight: 500;
}

.toolbar-btn:hover {
  background: #f3f4f6;
  color: #111827;
}

.toolbar-btn.active {
  background: #3b82f6;
  color: white;
}

.toolbar-btn:active {
  background: #e5e7eb;
}

/* Modern Dropdown Buttons */
.toolbar-dropdown {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #ffffff;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  min-width: 90px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e5e7eb;
}

.toolbar-dropdown:hover {
  border-color: #3b82f6;
  background: #f9fafb;
}

.toolbar-dropdown.size-dropdown {
  min-width: 60px;
}

.dropdown-text {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Modern Dropdown Menus */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  z-index: 1000;
  min-width: 220px;
  max-height: 320px;
  overflow-y: auto;
  margin-top: 8px;
  backdrop-filter: blur(8px);
}

.font-dropdown {
  min-width: 180px;
}

.size-dropdown-menu {
  min-width: 80px;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: #374151;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  margin: 2px 8px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-item:hover {
  background: #f3f4f6;
  color: #111827;
}

.dropdown-item.active {
  background: #3b82f6;
  color: white;
}

/* Modern Color Picker */
.color-picker {
  min-width: 300px;
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
}

.color-section {
  margin-bottom: 20px;
}

.color-section:last-child {
  margin-bottom: 0;
}

.color-section h4 {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 6px;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 2px solid #e5e7eb;
}

.color-swatch:hover {
  border-color: #3b82f6;
}

.color-swatch[style*="ffffff"] {
  border-color: #d1d5db;
}

/* Color Button */
.color-btn {
  flex-direction: column;
  gap: 2px;
  padding: 4px;
}

.color-indicator {
  width: 20px;
  height: 3px;
  border-radius: 1px;
  border: 1px solid #dadce0;
}

/* Emma Branding */
.advanced-formatting-toolbar .toolbar-btn.active {
  background: #3b82f6;
  color: white;
}

.advanced-formatting-toolbar .toolbar-dropdown:focus,
.advanced-formatting-toolbar .toolbar-dropdown:hover {
  border-color: #3b82f6;
  background: #f9fafb;
}

.advanced-formatting-toolbar .dropdown-item.active {
  background: #3b82f6;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .advanced-formatting-toolbar {
    padding: 6px 12px;
    gap: 2px;
  }
  
  .toolbar-btn {
    width: 28px;
    height: 28px;
  }
  
  .toolbar-dropdown {
    padding: 4px 6px;
    font-size: 12px;
    min-width: 60px;
  }
  
  .dropdown-menu {
    min-width: 160px;
  }
  
  .color-picker {
    min-width: 240px;
    padding: 12px;
  }
  
  .color-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}

/* Scrollbar Styling */
.dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #dadce0;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #bdc1c6;
}

/* Animation */
.dropdown-menu {
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Focus States */
.toolbar-btn:focus,
.toolbar-dropdown:focus {
  outline: 2px solid #4285f4;
  outline-offset: 2px;
}

.dropdown-item:focus {
  background: #e8f0fe;
  outline: none;
}

/* Loading State */
.toolbar-btn.loading {
  opacity: 0.6;
  cursor: not-allowed;
}

.toolbar-btn.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid #dadce0;
  border-top: 2px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tooltip Enhancement */
.toolbar-btn[title]:hover::before {
  content: attr(title);
  position: absolute;
  bottom: -32px;
  left: 50%;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1001;
  pointer-events: none;
}

/* Removed tooltip arrow for simpler design */
