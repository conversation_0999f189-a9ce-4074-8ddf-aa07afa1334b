/**
 * SEO Intelligence Panel - Real-time SEO analysis and optimization
 * Provides live SEO scoring, suggestions, and content generation
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain, Target, TrendingUp, CheckCircle, AlertCircle,
  Lightbulb, Wand2, Image, BarChart3, Eye, Sparkles, Award
} from 'lucide-react';
import { seoIntelligenceService, SEOAnalysis } from '../../../services/seoIntelligenceService';

interface SEOIntelligencePanelProps {
  content: string;
  onContentGenerate: (content: string) => void;
  onImageGenerate: () => void;
  className?: string;
}

const SEOIntelligencePanel: React.FC<SEOIntelligencePanelProps> = ({
  content,
  onContentGenerate,
  onImageGenerate,
  className = ''
}) => {
  const [analysis, setAnalysis] = useState<SEOAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<'analysis' | 'generate' | 'suggestions'>('analysis');
  const [confidenceAnalysis, setConfidenceAnalysis] = useState<any>(null);
  const [isAnalyzingConfidence, setIsAnalyzingConfidence] = useState(false);

  // Manual analysis - no automatic triggers

  const analyzeContent = useCallback(async (forceAnalysis = false) => {
    if (!content.trim()) {
      setAnalysis(null);
      return;
    }

    setIsAnalyzing(true);
    try {
      // Extract keywords from content for analysis
      const extractedKeywords = content
        .toLowerCase()
        .split(/\s+/)
        .filter(word => word.length > 3)
        .slice(0, 5);

      const result = await seoIntelligenceService.analyzeContent(
        content,
        extractedKeywords.length > 0 ? extractedKeywords : ['SEO', 'contenido'],
        forceAnalysis // Pass flag for deeper analysis
      );
      console.log('✅ Analysis result received:', result);
      setAnalysis(result);
    } catch (error) {
      console.error('Failed to analyze content:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [content]);

  const generateContextualSuggestions = useCallback(async () => {
    if (!content.trim()) return;

    setIsAnalyzing(true);
    try {
      // Trigger deep analysis specifically for suggestions
      await analyzeContent(true);
    } catch (error) {
      console.error('Failed to generate suggestions:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [content, analyzeContent]);

  const generateOptimizedContent = useCallback(async () => {
    setIsGenerating(true);
    try {
      // Extract topic from content or use primary keywords
      const extractTopic = () => {
        if (content.trim()) {
          // Try to extract from first heading
          const headingMatch = content.match(/^#\s+(.+)$/m);
          if (headingMatch) return headingMatch[1];

          // Use first sentence as topic
          const firstSentence = content.split('.')[0];
          if (firstSentence.length > 10 && firstSentence.length < 100) {
            return firstSentence.trim();
          }
        }

        // Fallback to primary keywords
        const primaryKeywords = analysis?.keywords.primary || ['SEO', 'contenido'];
        return primaryKeywords.join(' y ');
      };

      const topic = extractTopic();

      const generatedContent = await seoIntelligenceService.generateContent({
        topic,
        keywords: analysis?.keywords.primary || ['SEO', 'contenido'],
        contentType: 'educational',
        targetLength: 1200,
        tone: 'professional',
        includeImages: true
      });

      onContentGenerate(generatedContent.content);
    } catch (error) {
      console.error('Failed to generate content:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [content, analysis, onContentGenerate]);

  const analyzeConfidence = useCallback(async () => {
    if (!content.trim()) {
      setConfidenceAnalysis(null);
      return;
    }

    setIsAnalyzingConfidence(true);
    try {
      // Extract keywords from content for analysis
      const extractedKeywords = content
        .toLowerCase()
        .split(/\s+/)
        .filter(word => word.length > 3)
        .slice(0, 5);

      const response = await fetch('http://localhost:8000/api/v1/seo-gpt/content/confidence-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_id: 'd62ebeaf-e223-4292-bb6f-2a26fefa7ad5', // Default project ID
          content: content,
          target_keywords: extractedKeywords.length > 0 ? extractedKeywords : ['SEO', 'contenido'],
          deep_analysis: false
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze confidence');
      }

      const result = await response.json();
      console.log('✅ Confidence analysis result:', result);
      setConfidenceAnalysis(result);
    } catch (error) {
      console.error('Failed to analyze confidence:', error);
    } finally {
      setIsAnalyzingConfidence(false);
    }
  }, [content]);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBg = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-500 rounded-lg">
            <Brain className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Emma SEO Intelligence</h3>
            <p className="text-xs text-gray-600">Análisis en tiempo real</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex">
        {[
          { id: 'analysis', label: 'Análisis', icon: BarChart3 },
          { id: 'generate', label: 'Generar', icon: Wand2 },
          { id: 'suggestions', label: 'Sugerencias', icon: Lightbulb }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-semibold transition-all duration-200 rounded-lg mx-1 ${
              activeTab === tab.id
                ? 'text-white bg-blue-500'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="p-4 max-h-[600px] overflow-y-auto">
        <AnimatePresence mode="wait">
          {activeTab === 'analysis' && (
            <motion.div
              key="analysis"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-4"
            >
              {/* SEO y LLMs Analysis */}
              <div className="text-center py-4">
                <Brain className="w-12 h-12 mx-auto mb-3 text-[#3018ef]" />
                <h4 className="font-semibold text-gray-900 mb-2">Análisis SEO y LLMs</h4>
                <p className="text-sm text-gray-600 mb-6">
                  Evalúa tu contenido para SEO tradicional y LLMs como Perplexity, ChatGPT
                </p>
              </div>

              {/* Confidence Analysis Button */}
              <div className="mb-4">
                <button
                  onClick={analyzeConfidence}
                  disabled={isAnalyzingConfidence || !content.trim()}
                  className="w-full p-4 bg-blue-500 hover:bg-blue-600 text-white rounded-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-3 font-semibold"
                >
                  {isAnalyzingConfidence ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Analizando SEO y LLMs...
                    </>
                  ) : (
                    <>
                      <Award className="w-5 h-5" />
                      Analizar SEO y LLMs
                    </>
                  )}
                </button>
              </div>

              {/* LLM Confidence Analysis Results */}
              {confidenceAnalysis && (
                <div className="border-t border-gray-200 pt-4">
                  <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                    <Brain className="w-4 h-4 text-purple-600" />
                    Análisis SEO y LLMs
                  </h4>

                  {/* Overall LLM Confidence Score */}
                  <div className="p-4 rounded-lg border-2" style={{
                    borderColor: confidenceAnalysis.overall_confidence?.color || '#ef4444',
                    backgroundColor: `${confidenceAnalysis.overall_confidence?.color || '#ef4444'}10`
                  }}>
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h5 className="font-medium text-gray-900">Puntuación SEO y LLMs</h5>
                        <p className="text-sm text-gray-600">Google, Perplexity, ChatGPT, Claude</p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold" style={{
                          color: confidenceAnalysis.overall_confidence?.color || '#ef4444'
                        }}>
                          {confidenceAnalysis.overall_confidence?.score || 0}
                        </div>
                        <span className="inline-block px-2 py-1 rounded-full text-xs font-medium text-white" style={{
                          backgroundColor: confidenceAnalysis.overall_confidence?.color || '#ef4444'
                        }}>
                          {confidenceAnalysis.overall_confidence?.badge || 'Mejorable'}
                        </span>
                      </div>
                    </div>

                    {/* LLM Performance Stats */}
                    <div className="grid grid-cols-3 gap-2 mt-3 text-xs">
                      <div className="text-center p-2 bg-white/30 rounded">
                        <div className="font-semibold">{confidenceAnalysis.overall_confidence?.summary?.total_weaknesses || 0}</div>
                        <div className="text-gray-600">Problemas SEO</div>
                      </div>
                      <div className="text-center p-2 bg-white/30 rounded">
                        <div className="font-semibold">{confidenceAnalysis.overall_confidence?.summary?.high_priority_issues || 0}</div>
                        <div className="text-gray-600">Críticos</div>
                      </div>
                      <div className="text-center p-2 bg-white/30 rounded">
                        <div className="font-semibold">{confidenceAnalysis.overall_confidence?.summary?.improvement_opportunities || 0}</div>
                        <div className="text-gray-600">Optimizaciones</div>
                      </div>
                    </div>
                  </div>

                  {/* Top LLM Issues */}
                  {confidenceAnalysis.weaknesses_analysis && confidenceAnalysis.weaknesses_analysis.length > 0 && (
                    <div className="mt-3">
                      <h6 className="text-sm font-medium text-gray-900 mb-2">Problemas SEO y LLMs:</h6>
                      <div className="space-y-2">
                        {confidenceAnalysis.weaknesses_analysis.slice(0, 3).map((weakness: any, index: number) => (
                          <div key={index} className="p-2 bg-gray-50 rounded text-xs">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{weakness.issue}</span>
                              <span className="px-2 py-1 rounded text-white text-xs" style={{
                                backgroundColor: weakness.confidence?.color || '#ef4444'
                              }}>
                                {weakness.confidence?.badge || 'Mejorable'}
                              </span>
                            </div>
                            <div className="text-gray-600 mt-1">
                              <span className="font-medium">Solución:</span> {weakness.target_value || 'Mejorar este aspecto'}
                            </div>
                            <div className="text-gray-600 text-xs mt-1">
                              <span className="font-medium">Impacto:</span> {weakness.impact || 'Mejora el SEO y engagement'}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {!content.trim() && (
                <div className="text-center py-8 text-gray-500">
                  <Brain className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>Escribe contenido para analizar SEO y LLMs</p>
                  <p className="text-xs mt-1">Google • Perplexity • ChatGPT • Claude</p>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'generate' && (
            <motion.div
              key="generate"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-4"
            >
              <div className="text-center">
                <Sparkles className="w-12 h-12 mx-auto mb-3 text-[#3018ef]" />
                <h4 className="font-semibold text-gray-900 mb-2">Generación Inteligente</h4>
                <p className="text-sm text-gray-600 mb-6">
                  Crea contenido optimizado para SEO y SAIO automáticamente
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={generateOptimizedContent}
                  disabled={isGenerating}
                  className="w-full p-4 bg-green-500 hover:bg-green-600 text-white rounded-xl disabled:opacity-50 transition-all duration-200 flex items-center justify-center gap-3 font-semibold"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Generando...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4" />
                      Generar Blog SEO Completo
                    </>
                  )}
                </button>

                <button
                  onClick={onImageGenerate}
                  className="w-full p-3 border-2 border-blue-500 text-blue-500 hover:bg-blue-50 rounded-xl transition-all duration-200 flex items-center justify-center gap-2 font-semibold"
                >
                  <Image className="w-4 h-4" />
                  Generar Imágenes IA
                </button>
              </div>
            </motion.div>
          )}

          {activeTab === 'suggestions' && (
            <motion.div
              key="suggestions"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-4"
            >
              {/* Manual Analysis Trigger */}
              {!content.trim() ? (
                <div className="text-center py-12 text-gray-500">
                  <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center">
                    <Lightbulb className="w-8 h-8 text-[#3018ef]" />
                  </div>
                  <h3 className="font-medium text-gray-900 mb-2">Sugerencias Inteligentes</h3>
                  <p className="text-sm text-gray-600 max-w-sm mx-auto">
                    Escribe tu contenido y obtén sugerencias específicas y accionables para mejorarlo
                  </p>
                </div>
              ) : !analysis?.suggestions?.length ? (
                <div className="text-center py-8">
                  <div className="mb-6">
                    <div className="w-12 h-12 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center">
                      <Lightbulb className="w-6 h-6 text-[#3018ef]" />
                    </div>
                    <h3 className="font-medium text-gray-900 mb-2">Análisis Contextual</h3>
                    <p className="text-sm text-gray-600 mb-4 max-w-md mx-auto">
                      Nuestro sistema analizará tu contenido específico y te dará recomendaciones personalizadas
                    </p>
                  </div>

                  <button
                    onClick={generateContextualSuggestions}
                    disabled={isAnalyzing}
                    className="inline-flex items-center gap-2 px-8 py-3 bg-purple-500 hover:bg-purple-600 text-white rounded-xl transition-all duration-200 disabled:opacity-50 font-semibold"
                  >
                    {isAnalyzing ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Analizando tu contenido...
                      </>
                    ) : (
                      <>
                        <Lightbulb className="w-5 h-5" />
                        Generar Sugerencias Personalizadas
                      </>
                    )}
                  </button>

                  <div className="mt-4 flex items-center justify-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Análisis contextual
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Sugerencias específicas
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      Accionables
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  {/* Suggestions Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Lightbulb className="w-4 h-4 text-[#3018ef]" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Sugerencias Personalizadas</h4>
                        <p className="text-xs text-gray-500">Basadas en tu contenido específico</p>
                      </div>
                    </div>
                    <button
                      onClick={generateContextualSuggestions}
                      disabled={isAnalyzing}
                      className="text-xs text-blue-500 hover:text-blue-600 transition-colors px-3 py-1 rounded-lg hover:bg-gray-50 font-medium"
                    >
                      {isAnalyzing ? 'Analizando...' : 'Actualizar'}
                    </button>
                  </div>

                  {/* Suggestions List */}
                  <div className="space-y-4">
                    {analysis.suggestions.map((suggestion, index) => {
                      // Parse the suggestion to extract title and content
                      const lines = suggestion.split('\n');
                      const title = lines[0]?.replace(/^\*\*|\*\*$/g, '').replace(/^[📝🏗️✂️❓📋📞💫🎯📈⚠️🔗]+\s*/, '') || '';
                      const content = lines.slice(1).join('\n');

                      return (
                        <div
                          key={index}
                          className="group relative p-5 bg-yellow-50 border border-yellow-200 rounded-xl hover:bg-yellow-100 transition-all duration-300"
                        >
                          <div className="flex items-start gap-4">
                            <div className="w-8 h-8 bg-yellow-500 rounded-xl flex items-center justify-center flex-shrink-0">
                              <Lightbulb className="w-4 h-4 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="text-sm font-semibold text-gray-900 mb-2 leading-tight">
                                {title}
                              </h4>
                              <div className="text-xs text-gray-700 leading-relaxed space-y-2">
                                {content.split('\n\n').map((section, sectionIndex) => {
                                  if (!section.trim()) return null;

                                  // Handle different section types
                                  if (section.includes('**¿Por qué?**')) {
                                    const text = section.replace('**¿Por qué?**', '').trim();
                                    return (
                                      <div key={sectionIndex} className="bg-blue-50/50 p-3 rounded-lg border-l-3 border-blue-400">
                                        <div className="flex items-start gap-2">
                                          <span className="text-blue-600 font-medium text-xs">¿Por qué?</span>
                                          <span className="text-blue-800 text-xs">{text}</span>
                                        </div>
                                      </div>
                                    );
                                  } else if (section.includes('**Cómo hacerlo:**')) {
                                    const text = section.replace('**Cómo hacerlo:**', '').trim();
                                    const steps = text.split('•').filter(step => step.trim());
                                    return (
                                      <div key={sectionIndex} className="bg-green-50/50 p-3 rounded-lg border-l-3 border-green-400">
                                        <div className="text-green-600 font-medium text-xs mb-2">Cómo hacerlo:</div>
                                        <ul className="space-y-1">
                                          {steps.map((step, stepIndex) => (
                                            <li key={stepIndex} className="flex items-start gap-2 text-green-800 text-xs">
                                              <span className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                                              <span>{step.trim()}</span>
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                    );
                                  } else if (section.includes('**Impacto SEO:**')) {
                                    const text = section.replace('**Impacto SEO:**', '').trim();
                                    return (
                                      <div key={sectionIndex} className="bg-purple-50/50 p-3 rounded-lg border-l-3 border-purple-400">
                                        <div className="flex items-start gap-2">
                                          <span className="text-purple-600 font-medium text-xs">Impacto SEO:</span>
                                          <span className="text-purple-800 text-xs font-medium">{text}</span>
                                        </div>
                                      </div>
                                    );
                                  }

                                  return (
                                    <p key={sectionIndex} className="text-gray-600 text-xs">
                                      {section.trim()}
                                    </p>
                                  );
                                })}
                              </div>
                            </div>
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Action Footer */}
                  <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                    <div className="flex items-center gap-2 text-sm text-blue-800">
                      <CheckCircle className="w-4 h-4 text-blue-600" />
                      <span className="font-medium">
                        {analysis.suggestions.length} sugerencias específicas generadas
                      </span>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      Implementa estas mejoras para optimizar tu contenido SEO
                    </p>
                  </div>
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default SEOIntelligencePanel;
