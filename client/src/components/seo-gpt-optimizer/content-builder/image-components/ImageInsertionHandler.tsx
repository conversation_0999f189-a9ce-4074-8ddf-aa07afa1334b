/**
 * Image Insertion Handler
 * Component that handles image insertion with proper Lexical context
 */

import React, { useEffect, useImperativeHandle, forwardRef } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { insertImage } from '../lexical-plugins/ImagePlugin';
import { type GeneratedContentImage } from '../../../../services/content-builder/imageGenerationService';
import { imageQualityService } from '../../../../services/imageQualityService';

// Extended interface to support both generated and uploaded images
export interface UploadedImage {
  url: string;
  altText: string;
  caption?: string;
  width?: number;
  height?: number;
}

export interface ImageInsertionHandlerRef {
  insertImage: (image: GeneratedContentImage | UploadedImage) => void;
}

const ImageInsertionHandler = forwardRef<ImageInsertionHandlerRef>((props, ref) => {
  const [editor] = useLexicalComposerContext();

  useImperativeHandle(ref, () => ({
    insertImage: async (image: GeneratedContentImage | UploadedImage) => {
      console.log('🖼️ ImageInsertionHandler: Inserting image:', image);
      try {
        // Ensure we have valid image data
        if (!image.url) {
          throw new Error('Image URL is required');
        }

        // Check if this is a generated image (has more properties) or uploaded image
        const isGeneratedImage = 'prompt' in image;

        // For uploaded images, skip optimization to avoid issues with local URLs
        let optimizedImage = image;

        if (isGeneratedImage) {
          // Only optimize generated images from external URLs
          try {
            const optimized = await imageQualityService.optimizeImageForEditor(image.url, {
              maxWidth: 800,
              maxHeight: 600,
              quality: 0.9,
              maintainAspectRatio: true
            });

            optimizedImage = {
              ...image,
              url: optimized.url,
              width: optimized.width,
              height: optimized.height
            };

            console.log('✅ Generated image optimized for editor:', optimized);
          } catch (optimizationError) {
            console.warn('⚠️ Image optimization failed, using original:', optimizationError);
            // Continue with original image
          }
        } else {
          console.log('📤 Uploaded image, skipping optimization');
        }

        insertImage(
          editor,
          optimizedImage.url,
          optimizedImage.altText || 'Uploaded image',
          optimizedImage.caption || optimizedImage.altText || 'Uploaded image',
          optimizedImage.width || 600,
          optimizedImage.height || 400
        );
        console.log('✅ ImageInsertionHandler: Image inserted successfully');
      } catch (error) {
        console.error('❌ ImageInsertionHandler: Error inserting image:', error);
        throw error;
      }
    }
  }), [editor]);

  return null;
});

ImageInsertionHandler.displayName = 'ImageInsertionHandler';

export default ImageInsertionHandler;
