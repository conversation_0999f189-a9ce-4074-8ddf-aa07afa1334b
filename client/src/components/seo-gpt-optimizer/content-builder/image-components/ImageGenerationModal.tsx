/**
 * Image Generation Modal
 * Full-featured modal for AI image generation and insertion
 * 
 * Features:
 * - Smart content analysis
 * - Multiple generation options
 * - Real-time preview
 * - SEO optimization
 * - Direct editor insertion
 */

import React, { useState, useEffect, useCallback } from 'react';
import { X, Wand2, <PERSON>ting<PERSON>, Zap } from 'lucide-react';

// Components
import ImageInsertionPanel from './ImageInsertionPanel';
import { type GeneratedContentImage } from '../../../../services/content-builder/imageGenerationService';

// Types
interface ImageGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  content: string;
  targetKeywords?: string[];
  onImageInsert: (image: GeneratedContentImage) => void;
  className?: string;
}

// Modal overlay component
const ModalOverlay: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}> = ({ isOpen, onClose, children }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
        padding: '20px'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      {children}
    </div>
  );
};

// Main modal component
const ImageGenerationModal: React.FC<ImageGenerationModalProps> = ({
  isOpen,
  onClose,
  content,
  targetKeywords = [],
  onImageInsert,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'smart' | 'custom' | 'settings'>('smart');
  const [insertedCount, setInsertedCount] = useState(0);

  // Handle image insertion
  const handleImageInsert = useCallback(async (image: GeneratedContentImage) => {
    try {
      await onImageInsert(image);
      setInsertedCount(prev => prev + 1);
      
      // Show success feedback
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to insert image:', error);
      // Could add error notification here
    }
  }, [onImageInsert]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setInsertedCount(0);
      setActiveTab('smart');
    }
  }, [isOpen]);

  return (
    <ModalOverlay isOpen={isOpen} onClose={onClose}>
      <div
        className={`image-generation-modal ${className}`}
        style={{
          width: '100%',
          maxWidth: '800px',
          maxHeight: '90vh',
          background: 'white',
          borderRadius: '12px',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div style={{
          padding: '20px 24px',
          borderBottom: '1px solid #e8eaed',
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e8f0fe 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '8px',
              background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Wand2 size={20} color="white" />
            </div>
            
            <div>
              <h2 style={{
                margin: 0,
                fontSize: '18px',
                fontWeight: '600',
                color: '#202124'
              }}>
                Generación Inteligente de Imágenes
              </h2>
              <p style={{
                margin: 0,
                fontSize: '13px',
                color: '#5f6368'
              }}>
                Powered by Emma • {insertedCount} imágenes insertadas
              </p>
            </div>
          </div>

          <button
            onClick={onClose}
            style={{
              width: '36px',
              height: '36px',
              border: 'none',
              borderRadius: '12px',
              background: 'transparent',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#f3f4f6';
              e.currentTarget.style.transform = 'scale(1.05)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'transparent';
              e.currentTarget.style.transform = 'scale(1)';
            }}
            title="Cerrar"
          >
            <X size={18} color="#5f6368" />
          </button>
        </div>

        {/* Tabs */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid #e8eaed',
          background: '#f8f9fa'
        }}>
          {[
            { id: 'smart', label: 'Sugerencias IA', icon: Zap },
            { id: 'custom', label: 'Generación Manual', icon: Wand2 },
            { id: 'settings', label: 'Configuración', icon: Settings }
          ].map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                style={{
                  flex: 1,
                  padding: '12px 16px',
                  border: 'none',
                  borderRadius: '12px',
                  background: isActive ? '#3b82f6' : 'transparent',
                  color: isActive ? 'white' : '#6b7280',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '6px',
                  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                  boxShadow: 'none',
                  margin: '0 4px'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = '#f3f4f6';
                    e.currentTarget.style.color = '#111827';
                    e.currentTarget.style.transform = 'translateY(-1px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = 'transparent';
                    e.currentTarget.style.color = '#6b7280';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }
                }}
              >
                <Icon size={14} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div style={{ flex: 1, overflow: 'hidden' }}>
          {activeTab === 'smart' && (
            <ImageInsertionPanel
              content={content}
              targetKeywords={targetKeywords}
              onImageInsert={handleImageInsert}
            />
          )}
          
          {activeTab === 'custom' && (
            <div style={{
              padding: '40px',
              textAlign: 'center',
              color: '#5f6368'
            }}>
              <Wand2 size={48} color="#9aa0a6" style={{ margin: '0 auto 16px' }} />
              <h3 style={{ margin: '0 0 8px 0', fontSize: '16px' }}>
                Generación Manual
              </h3>
              <p style={{ margin: 0, fontSize: '14px' }}>
                Próximamente: Generación manual con prompts personalizados
              </p>
            </div>
          )}
          
          {activeTab === 'settings' && (
            <div style={{
              padding: '40px',
              textAlign: 'center',
              color: '#5f6368'
            }}>
              <Settings size={48} color="#9aa0a6" style={{ margin: '0 auto 16px' }} />
              <h3 style={{ margin: '0 0 8px 0', fontSize: '16px' }}>
                Configuración Avanzada
              </h3>
              <p style={{ margin: 0, fontSize: '14px' }}>
                Próximamente: Configuración de estilos, resoluciones y preferencias
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div style={{
          padding: '16px 24px',
          borderTop: '1px solid #e8eaed',
          background: '#f8f9fa',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{
            fontSize: '11px',
            color: '#9aa0a6'
          }}>
            Las imágenes se generan usando Emma para máxima calidad
          </div>
          
          <div style={{
            display: 'flex',
            gap: '8px'
          }}>
            <button
              onClick={onClose}
              style={{
                padding: '10px 20px',
                border: '1px solid #e5e7eb',
                borderRadius: '12px',
                background: 'white',
                color: '#6b7280',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = '#f9fafb';
                e.currentTarget.style.borderColor = '#3018ef';
                e.currentTarget.style.transform = 'translateY(-1px)';
                e.currentTarget.style.boxShadow = '0 4px 12px 0 rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'white';
                e.currentTarget.style.borderColor = '#e5e7eb';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              Cerrar
            </button>
          </div>
        </div>
      </div>
    </ModalOverlay>
  );
};

export default ImageGenerationModal;
