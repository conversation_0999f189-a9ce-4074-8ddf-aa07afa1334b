import React from 'react';

export default function SimpleImageTest() {
  const insertImageDirectly = () => {
    console.log('🖼️ INSERTING IMAGE DIRECTLY INTO DOM');
    
    // Get the editor DOM element directly
    const editorElement = document.querySelector('[contenteditable="true"]');
    
    if (!editorElement) {
      alert('❌ No contenteditable element found');
      return;
    }
    
    // Create image element directly
    const img = document.createElement('img');
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iIzAwZmYwMCIvPjx0ZXh0IHg9IjE1MCIgeT0iODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iYmxhY2siIHRleHQtYW5jaG9yPSJtaWRkbGUiPkZVQ0sgWUVBSCEgSU1BR0UgV09SS1MhPC90ZXh0Pjwvc3ZnPg==';
    img.alt = 'FUCK YEAH IMAGE WORKS';
    img.style.maxWidth = '100%';
    img.style.border = '3px solid red';
    img.style.margin = '10px 0';
    
    // Insert directly into DOM
    editorElement.appendChild(img);
    
    alert('✅ IMAGE INSERTED DIRECTLY INTO DOM!');
    console.log('✅ IMAGE INSERTED DIRECTLY INTO DOM!');
  };

  return (
    <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 9999 }}>
      <button 
        onClick={insertImageDirectly}
        style={{
          backgroundColor: 'red',
          color: 'white',
          padding: '10px 20px',
          border: 'none',
          borderRadius: '5px',
          fontSize: '16px',
          fontWeight: 'bold',
          cursor: 'pointer'
        }}
      >
        FUCK LEXICAL - INSERT IMAGE
      </button>
    </div>
  );
}
