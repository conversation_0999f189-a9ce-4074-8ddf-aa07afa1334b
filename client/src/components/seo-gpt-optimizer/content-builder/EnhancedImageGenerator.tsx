/**
 * Enhanced Image Generator - Seamless Ideogram AI integration
 * Uses Ideogram 3.0 Quality model with inline image insertion
 */

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Image,
  Wand2,
  X,
  Copy,
  Download,
  Plus,
  Sparkles
} from 'lucide-react';

// Import proven utility functions
import { downloadImage as utilDownloadImage, copyImageUrl as utilCopyImageUrl } from '../../../utils/imageUtils';

interface EnhancedImageGeneratorProps {
  onImageInsert: (imageData: {
    url: string;
    altText: string;
    caption: string;
    width?: number;
    height?: number;
    aspectRatio: string;
    seoScore: number;
    keywords: string[];
    placement: string;
  }) => void;
  onImageGenerated: (imageUrl: string, prompt: string) => void;
  onClose: () => void;
  persistedImages: Array<{
    id: string;
    url: string;
    prompt: string;
    timestamp: number;
  }>;
  className?: string;
}

interface GeneratedImage {
  id: string;
  url: string;
  originalUrl?: string; // Original URL from Ideogram (before proxy)
  prompt: string;
  timestamp: Date;
}

const EnhancedImageGenerator: React.FC<EnhancedImageGeneratorProps> = ({
  onImageInsert,
  onImageGenerated,
  onClose,
  persistedImages,
  className = ''
}) => {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [newGeneratedImages, setNewGeneratedImages] = useState<GeneratedImage[]>([]);
  const [selectedStyle, setSelectedStyle] = useState('realistic');

  // Combine persisted images with newly generated ones
  const allImages = useMemo(() => {
    const persistedConverted = persistedImages.map(img => ({
      ...img,
      timestamp: new Date(img.timestamp)
    }));
    return [...newGeneratedImages, ...persistedConverted].sort((a, b) =>
      b.timestamp.getTime() - a.timestamp.getTime()
    );
  }, [newGeneratedImages, persistedImages]);

  const styleOptions = [
    { id: 'realistic', label: 'Realista', description: 'Fotografías realistas' },
    { id: 'artistic', label: 'Artístico', description: 'Arte conceptual' },
    { id: 'minimal', label: 'Minimalista', description: 'Diseño limpio' },
    { id: 'vibrant', label: 'Vibrante', description: 'Colores intensos' }
  ];

  const generateImage = useCallback(async () => {
    if (!prompt.trim() || isGenerating) return;

    setIsGenerating(true);

    try {
      // Enhance prompt based on style
      let enhancedPrompt = prompt;
      switch (selectedStyle) {
        case 'realistic':
          enhancedPrompt = `Professional photograph of ${prompt}, high quality, detailed, realistic lighting`;
          break;
        case 'artistic':
          enhancedPrompt = `Artistic interpretation of ${prompt}, creative, conceptual art style`;
          break;
        case 'minimal':
          enhancedPrompt = `Minimalist design of ${prompt}, clean, simple, modern aesthetic`;
          break;
        case 'vibrant':
          enhancedPrompt = `Vibrant illustration of ${prompt}, bright colors, energetic, dynamic`;
          break;
      }

      // Map style to Spanish names for backend
      const styleMap = {
        'realistic': 'realista',
        'artistic': 'artístico',
        'minimal': 'minimalista',
        'vibrant': 'vibrante'
      };

      // Call the new SEO image generation endpoint
      const response = await fetch('http://localhost:8000/api/v1/seo-gpt/content/generate-seo-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_id: 'demo-project', // TODO: Get from context
          prompt: prompt,
          style: styleMap[selectedStyle as keyof typeof styleMap] || 'realista',
          aspect_ratio: 'ASPECT_16_9',
          keywords: [], // TODO: Get from context
          alt_text: `Imagen ${styleMap[selectedStyle as keyof typeof styleMap]} - ${prompt}`
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.image_url) {
        console.log('✅ Image generation successful, URL received:', result.image_url);

        // Download and store the image locally for reliable access
        let localImageUrl = result.image_url;
        try {
          const downloadResponse = await fetch('/api/v1/temp-images/download-and-store', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              url: result.image_url,
              id: `enhanced_${Date.now()}`
            })
          });

          if (downloadResponse.ok) {
            const downloadResult = await downloadResponse.json();
            if (downloadResult.success && downloadResult.internal_url) {
              localImageUrl = downloadResult.internal_url;
              console.log('✅ Image downloaded and stored locally:', localImageUrl);
            }
          }
        } catch (error) {
          console.warn('⚠️ Failed to download image locally, using proxy:', error);
          // Fallback to proxy if local storage fails
          if (result.image_url.includes('ideogram.ai') || result.image_url.includes('cdn') || !result.image_url.startsWith('http://localhost:8000')) {
            localImageUrl = `http://localhost:8000/api/v1/seo-gpt/content/proxy-image?url=${encodeURIComponent(result.image_url)}`;
            console.log('🔄 Using backend proxy as fallback:', localImageUrl);
          }
        }

        // Test if the local image URL is accessible
        const testImage = new Image();
        testImage.onload = () => console.log('✅ Local image URL is accessible and loads correctly');
        testImage.onerror = (e) => console.error('❌ Local image URL failed to load:', e);
        testImage.src = localImageUrl;

        const newImage: GeneratedImage = {
          id: Date.now().toString(),
          url: localImageUrl, // Use the local URL for display
          originalUrl: result.image_url, // Keep original URL for reference
          prompt: result.prompt_used || prompt,
          timestamp: new Date()
        };

        setNewGeneratedImages(prev => [newImage, ...prev]);
        onImageGenerated(newImage.url, newImage.prompt);
        console.log('🎨 New image added to state:', newImage);
      } else {
        console.error('❌ Image generation failed:', result);
        throw new Error('Failed to generate image with SEO optimization');
      }
      
    } catch (error) {
      console.error('Error generating image:', error);
      // Show user-friendly error without alert
    } finally {
      setIsGenerating(false);
    }
  }, [prompt, selectedStyle, isGenerating]);

  const handleImageSelect = useCallback((image: GeneratedImage) => {
    try {
      console.log('🎨 Inserting image into editor:', image.url);

      // Show insertion feedback
      const button = document.querySelector(`[title="Insertar en blog"]`) as HTMLElement;
      if (button) {
        const originalText = button.innerHTML;
        button.innerHTML = '⏳';
        button.style.backgroundColor = '#3b82f6';

        // Reset button after insertion
        setTimeout(() => {
          button.innerHTML = '✅';
          button.style.backgroundColor = '#10b981';
          setTimeout(() => {
            button.innerHTML = originalText;
            button.style.backgroundColor = '';
          }, 2000);
        }, 500);
      }

      // Call the insertion handler with proper data structure
      const imageData = {
        url: image.url,
        altText: image.prompt,
        caption: image.prompt,
        width: 600,
        height: 400,
        aspectRatio: '16:9',
        seoScore: 85,
        keywords: [],
        placement: 'middle'
      };
      onImageInsert(imageData);

      // DON'T close the modal - let user insert multiple images
      console.log('✅ Image insertion completed, modal stays open');

    } catch (error) {
      console.error('❌ Error inserting image:', error);

      // Show error feedback
      const button = document.querySelector(`[title="Insertar en blog"]`) as HTMLElement;
      if (button) {
        button.innerHTML = '❌';
        button.style.backgroundColor = '#ef4444';
        setTimeout(() => {
          button.innerHTML = '+';
          button.style.backgroundColor = '';
        }, 3000);
      }

      alert('❌ Error al insertar la imagen en el editor. Verifica que el editor esté listo e intenta de nuevo.');
    }
  }, [onImageInsert]);

  const copyImageUrl = useCallback(async (url: string) => {
    try {
      console.log('📋 Copying URL to clipboard:', url);

      // Try using the proven utility function first
      const result = await utilCopyImageUrl(url);

      if (result.success) {
        console.log('✅ URL copied successfully using utility');

        // Show success feedback with better UX
        const button = document.querySelector(`[title="Copiar URL"]`) as HTMLElement;
        if (button) {
          const originalText = button.innerHTML;
          button.innerHTML = '✅';
          button.style.backgroundColor = '#10b981';
          setTimeout(() => {
            button.innerHTML = originalText;
            button.style.backgroundColor = '';
          }, 2000);
        }
      } else {
        throw new Error(result.error || 'Utility copy failed');
      }

    } catch (error) {
      console.error('❌ Error copying URL, trying fallback:', error);

      try {
        // Fallback: Direct clipboard API
        await navigator.clipboard.writeText(url);
        console.log('✅ URL copied successfully with fallback');

        const button = document.querySelector(`[title="Copiar URL"]`) as HTMLElement;
        if (button) {
          const originalText = button.innerHTML;
          button.innerHTML = '✅';
          button.style.backgroundColor = '#10b981';
          setTimeout(() => {
            button.innerHTML = originalText;
            button.style.backgroundColor = '';
          }, 2000);
        }

      } catch (fallbackError) {
        console.error('❌ All copy methods failed:', fallbackError);

        // Show error feedback
        const button = document.querySelector(`[title="Copiar URL"]`) as HTMLElement;
        if (button) {
          button.innerHTML = '❌';
          button.style.backgroundColor = '#ef4444';
          setTimeout(() => {
            button.innerHTML = '📋';
            button.style.backgroundColor = '';
          }, 3000);
        }

        // Final fallback: show URL in a modal or prompt
        const fallbackMessage = `No se pudo copiar automáticamente. URL de la imagen:\n\n${url}`;
        if (window.confirm(fallbackMessage + '\n\n¿Quieres intentar copiar de nuevo?')) {
          // Try one more time
          try {
            await navigator.clipboard.writeText(url);
            alert('✅ URL copiada exitosamente');
          } catch (retryError) {
            // Final fallback - show in prompt for manual copy
            prompt('Copia esta URL manualmente:', url);
          }
        }
      }
    }
  }, []);

  const downloadImage = useCallback(async (url: string, prompt: string) => {
    try {
      console.log('📥 Starting download for image:', url);

      // Create filename from prompt
      const filename = `emma-seo-image-${prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, '-')}.png`;

      // Show download feedback
      const button = document.querySelector(`[title="Descargar"]`) as HTMLElement;
      if (button) {
        const originalText = button.innerHTML;
        button.innerHTML = '⏳';
        button.style.backgroundColor = '#3b82f6';

        // Reset button after download
        setTimeout(() => {
          button.innerHTML = originalText;
          button.style.backgroundColor = '';
        }, 3000);
      }

      // For external URLs, ensure we use the proxy
      let downloadUrl = url;
      if (url.includes('ideogram.ai') || url.includes('cdn') || !url.startsWith('http://localhost:8000')) {
        downloadUrl = `http://localhost:8000/api/v1/seo-gpt/content/proxy-image?url=${encodeURIComponent(url)}`;
        console.log('📥 Using backend proxy for download:', downloadUrl);
      }

      // Try using the proven utility function first
      const result = await utilDownloadImage(downloadUrl, filename);

      if (result.success) {
        console.log('✅ Download completed using utility function');

        // Show success feedback
        setTimeout(() => {
          if (button) {
            button.innerHTML = '✅';
            button.style.backgroundColor = '#10b981';
            setTimeout(() => {
              button.innerHTML = '💾';
              button.style.backgroundColor = '';
            }, 2000);
          }
        }, 1000);
      } else {
        throw new Error(result.error || 'Utility download failed');
      }

    } catch (error) {
      console.error('❌ Download failed, trying fallback:', error);

      try {
        // Fallback: Direct download approach
        // Use the same proxy URL we determined above
        console.log('📥 Fallback download using URL:', downloadUrl);

        // Create download link
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `emma-seo-image-${prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, '-')}.png`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('✅ Fallback download initiated');

      } catch (fallbackError) {
        console.error('❌ All download methods failed:', fallbackError);

        // Show error feedback
        const button = document.querySelector(`[title="Descargar"]`) as HTMLElement;
        if (button) {
          button.innerHTML = '❌';
          button.style.backgroundColor = '#ef4444';
          setTimeout(() => {
            button.innerHTML = '💾';
            button.style.backgroundColor = '';
          }, 3000);
        }

        // Final fallback: open image in new tab
        const newWindow = window.open(url, '_blank');
        if (newWindow) {
          alert('💡 La imagen se abrió en una nueva pestaña. Haz clic derecho y selecciona "Guardar imagen como..."');
        } else {
          alert('❌ No se pudo descargar la imagen. Intenta copiar la URL y descargarla manualmente.');
        }
      }
    }
  }, []);

  return (
    <motion.div
      className={`fixed inset-0 bg-black bg-opacity-30 flex items-start justify-end z-50 p-4 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="bg-white rounded-2xl shadow-2xl w-[650px] max-h-[85vh] overflow-hidden mt-16 mr-8"
        initial={{ scale: 0.9, opacity: 0, x: 100 }}
        animate={{ scale: 1, opacity: 1, x: 0 }}
        exit={{ scale: 0.9, opacity: 0, x: 100 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header with Emma Styling */}
        <div className="p-6 border-b border-white/20 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-xl shadow-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Generador de Imágenes IA</h2>
                <p className="text-sm text-gray-600 font-medium">Powered by Emma</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-3 text-gray-500 hover:text-[#dd3a5a] hover:bg-white/30 backdrop-blur-sm rounded-xl border border-white/30 transition-all duration-200"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[70vh] overflow-y-auto">
          {/* Generation Form */}
          <div className="space-y-6 mb-8">
            {/* Prompt Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descripción de la imagen
              </label>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Ej: Una oficina moderna con plantas, luz natural y ambiente profesional"
                className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none"
                rows={3}
              />
            </div>

            {/* Style Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Estilo de imagen
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {styleOptions.map((style) => (
                  <button
                    key={style.id}
                    onClick={() => setSelectedStyle(style.id)}
                    className={`p-3 rounded-xl border-2 transition-all duration-200 ${
                      selectedStyle === style.id
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-gray-300 text-gray-700'
                    }`}
                  >
                    <div className="text-sm font-medium">{style.label}</div>
                    <div className="text-xs text-gray-500 mt-1">{style.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Generate Button with Emma Styling */}
            <button
              onClick={generateImage}
              disabled={!prompt.trim() || isGenerating}
              className="w-full px-8 py-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-xl hover:from-[#3018ef]/90 hover:to-[#dd3a5a]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-3 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              {isGenerating ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Generando imagen...
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5" />
                  Generar Imagen con IA
                </>
              )}
            </button>
          </div>

          {/* Generated Images */}
          <AnimatePresence>
            {allImages.length > 0 && (
              <motion.div
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <h3 className="text-lg font-semibold text-gray-900">Imágenes generadas</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {allImages.map((image) => (
                    <motion.div
                      key={image.id}
                      className="relative group bg-gray-100 rounded-xl overflow-hidden"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <img
                        src={image.url}
                        alt={image.prompt}
                        className="w-full h-48 object-cover"
                        onLoad={() => console.log('✅ Image loaded successfully:', image.url)}
                        onError={(e) => {
                          console.error('❌ Image failed to load:', image.url);
                          console.error('Error details:', e);
                          // Show a placeholder or error state
                          e.currentTarget.style.display = 'none';
                          const parent = e.currentTarget.parentElement;
                          if (parent && !parent.querySelector('.image-error')) {
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'image-error w-full h-48 bg-gray-200 flex items-center justify-center text-gray-500';
                            errorDiv.innerHTML = `
                              <div class="text-center">
                                <div class="text-2xl mb-2">🖼️</div>
                                <div class="text-sm">Error cargando imagen</div>
                                <div class="text-xs mt-1 px-2">${image.url.substring(0, 50)}...</div>
                              </div>
                            `;
                            parent.appendChild(errorDiv);
                          }
                        }}
                      />
                      
                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                          <button
                            onClick={() => handleImageSelect(image)}
                            className="p-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors"
                            title="Insertar en blog"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => copyImageUrl(image.url)}
                            className="p-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors"
                            title="Copiar URL"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => downloadImage(image.url, image.prompt)}
                            className="p-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors"
                            title="Descargar"
                          >
                            <Download className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                      
                      {/* Prompt */}
                      <div className="p-3 bg-white">
                        <p className="text-sm text-gray-600 truncate" title={image.prompt}>
                          {image.prompt}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default EnhancedImageGenerator;
