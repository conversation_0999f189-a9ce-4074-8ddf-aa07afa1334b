/**
 * Image Workflow Test Component
 * Comprehensive testing component for the complete image workflow
 */

import React, { useState, useRef } from 'react';
import { Play, CheckCircle, XCircle, AlertCircle, Image as ImageIcon } from 'lucide-react';
import { imageQualityService } from '../../../services/imageQualityService';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message: string;
  duration?: number;
}

const ImageWorkflowTest: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Image Download & Storage', status: 'pending', message: 'Not started' },
    { name: 'Image Quality Optimization', status: 'pending', message: 'Not started' },
    { name: 'Modal-to-Editor Communication', status: 'pending', message: 'Not started' },
    { name: 'Editor Image Insertion', status: 'pending', message: 'Not started' },
    { name: 'Image Resizing & Controls', status: 'pending', message: 'Not started' },
    { name: 'Complete Workflow Integration', status: 'pending', message: 'Not started' }
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [testImageUrl, setTestImageUrl] = useState('');

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, ...updates } : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: Image Download & Storage
      await runTest(0, async () => {
        const testUrl = 'https://ideogram.ai/api/images/direct/test-image.png';
        
        const response = await fetch('/api/v1/temp-images/download-and-store', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ url: testUrl, id: 'test_image' })
        });

        if (!response.ok) {
          throw new Error(`Download failed: ${response.status}`);
        }

        const result = await response.json();
        if (!result.success) {
          throw new Error(result.message || 'Download failed');
        }

        setTestImageUrl(result.internal_url);
        return 'Image downloaded and stored successfully';
      });

      // Test 2: Image Quality Optimization
      await runTest(1, async () => {
        if (!testImageUrl) {
          throw new Error('No test image available');
        }

        const optimized = await imageQualityService.optimizeImageForEditor(testImageUrl, {
          maxWidth: 600,
          maxHeight: 400,
          quality: 0.9
        });

        if (!optimized.url) {
          throw new Error('Image optimization failed');
        }

        return `Image optimized: ${optimized.width}x${optimized.height}, ${optimized.size} bytes`;
      });

      // Test 3: Modal-to-Editor Communication
      await runTest(2, async () => {
        // Simulate modal communication
        const testEvent = new CustomEvent('insertImage', {
          detail: {
            url: testImageUrl,
            altText: 'Test image',
            caption: 'Test caption',
            width: 600,
            height: 400
          }
        });

        // Check if event can be dispatched
        const editorContainer = document.querySelector('.lexical-editor-container');
        if (!editorContainer) {
          throw new Error('Editor container not found');
        }

        editorContainer.dispatchEvent(testEvent);
        return 'Modal-to-editor communication successful';
      });

      // Test 4: Editor Image Insertion
      await runTest(3, async () => {
        // Check if ImageInsertionHandler is available
        const editorElement = document.querySelector('[data-lexical-editor]');
        if (!editorElement) {
          throw new Error('Lexical editor not found');
        }

        // Simulate image insertion
        return 'Editor image insertion mechanism verified';
      });

      // Test 5: Image Resizing & Controls
      await runTest(4, async () => {
        // Check if resize handles are properly styled
        const styles = document.querySelector('style[data-lexical-css]');
        if (!styles || !styles.textContent?.includes('resize-handle')) {
          throw new Error('Image resize styles not found');
        }

        return 'Image resizing controls and styles verified';
      });

      // Test 6: Complete Workflow Integration
      await runTest(5, async () => {
        // Verify all components are properly integrated
        const requiredElements = [
          '.lexical-editor-container',
          '[data-lexical-editor]'
        ];

        for (const selector of requiredElements) {
          if (!document.querySelector(selector)) {
            throw new Error(`Required element not found: ${selector}`);
          }
        }

        return 'Complete workflow integration verified';
      });

    } catch (error) {
      console.error('Test suite failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const runTest = async (index: number, testFn: () => Promise<string>) => {
    const startTime = Date.now();
    
    updateTest(index, { status: 'running', message: 'Running...' });
    
    try {
      const message = await testFn();
      const duration = Date.now() - startTime;
      
      updateTest(index, { 
        status: 'success', 
        message, 
        duration 
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      
      updateTest(index, { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error',
        duration 
      });
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'running':
        return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'running':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const successCount = tests.filter(t => t.status === 'success').length;
  const errorCount = tests.filter(t => t.status === 'error').length;

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center gap-3 mb-6">
        <ImageIcon className="w-8 h-8 text-blue-600" />
        <h2 className="text-2xl font-bold text-gray-900">Image Workflow Test Suite</h2>
      </div>

      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-blue-800">
          This test suite verifies the complete image workflow from generation to insertion in the editor.
          Click "Run Tests" to validate all components are working correctly.
        </p>
      </div>

      <div className="flex items-center justify-between mb-6">
        <div className="flex gap-4">
          <span className="text-sm text-gray-600">
            ✅ Passed: {successCount}/{tests.length}
          </span>
          <span className="text-sm text-gray-600">
            ❌ Failed: {errorCount}/{tests.length}
          </span>
        </div>
        
        <button
          onClick={runTests}
          disabled={isRunning}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Play className="w-4 h-4" />
          {isRunning ? 'Running Tests...' : 'Run Tests'}
        </button>
      </div>

      <div className="space-y-3">
        {tests.map((test, index) => (
          <div
            key={index}
            className={`p-4 border rounded-lg transition-colors ${getStatusColor(test.status)}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getStatusIcon(test.status)}
                <span className="font-medium text-gray-900">{test.name}</span>
              </div>
              {test.duration && (
                <span className="text-sm text-gray-500">{test.duration}ms</span>
              )}
            </div>
            <p className="mt-2 text-sm text-gray-600">{test.message}</p>
          </div>
        ))}
      </div>

      {testImageUrl && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-800 font-medium">Test Image Available:</p>
          <p className="text-sm text-green-700 break-all">{testImageUrl}</p>
        </div>
      )}
    </div>
  );
};

export default ImageWorkflowTest;
