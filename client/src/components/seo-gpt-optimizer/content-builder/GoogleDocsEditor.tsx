/**
 * GoogleDocsEditor - Professional rich text editor powered by Lexical
 * Google Docs-style interface with Emma branding and advanced editing features
 */

import React, { useEffect, useRef, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import {
  Bold, Italic, Underline, Image, Wand2, <PERSON>rkles,
  Printer, Share, Star, PanelRightOpen, PanelRightClose
} from 'lucide-react';

// Lexical imports
import { $getRoot } from 'lexical';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin';
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import {
  ListNode,
  ListItemNode,
} from '@lexical/list';
import { LinkNode } from '@lexical/link';
import { HeadingNode } from '@lexical/rich-text';
import {
  FORMAT_TEXT_COMMAND,
  UNDO_COMMAND,
  REDO_COMMAND,
} from 'lexical';

// Import image support
import { SimpleImageNode, $createSimpleImageNode } from './lexical-nodes/SimpleImageNode';
import { ImagePlugin, INSERT_IMAGE_COMMAND, insertImage } from './lexical-plugins/ImagePlugin';
import { $getSelection, $isRangeSelection } from 'lexical';
import ImageInsertionHandler, { ImageInsertionHandlerRef } from './image-components/ImageInsertionHandler';
import PersistentFormattingPlugin from './lexical-plugins/PersistentFormattingPlugin';
import SimpleImageTest from './SimpleImageTest';

// Import Lexical editor styles
import './LexicalEditor.css';
import './lexical-nodes/ImageNode.css';
import AdvancedFormattingToolbar from './AdvancedFormattingToolbar';
import './AdvancedFormattingToolbar.css';

interface GoogleDocsEditorProps {
  content: string;
  onChange: (content: string) => void;
  onImageInsert?: () => void;
  projectId: string;
  className?: string;
  documentTitle?: string;
  onTitleChange?: (title: string) => void;
  collaborators?: Array<{
    id: string;
    name: string;
    avatar?: string;
    color: string;
  }>;
  showSidebar?: boolean;
  onToggleSidebar?: () => void;
}

export interface GoogleDocsEditorRef {
  insertImage: (imageData: any) => void;
}

const GoogleDocsEditor = forwardRef<GoogleDocsEditorRef, GoogleDocsEditorProps>(({
  content,
  onChange,
  onImageInsert,
  projectId,
  className = '',
  documentTitle = 'Documento sin título',
  onTitleChange,
  collaborators = [],
  showSidebar = true,
  onToggleSidebar
}, ref) => {
  const titleRef = useRef<HTMLInputElement>(null);
  const imageInsertionRef = useRef<ImageInsertionHandlerRef>(null);
  const editorRef = useRef<any>(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    insertImage: (imageData: any) => {
      console.log('🎯 GoogleDocsEditor: insertImage called directly with:', imageData);
      if (imageInsertionRef.current && imageInsertionRef.current.insertImage) {
        console.log('✅ Calling ImageInsertionHandler.insertImage directly');
        imageInsertionRef.current.insertImage(imageData);
      } else {
        console.error('❌ ImageInsertionHandler ref not available');
        // Fallback: dispatch event to the editor
        const imageInsertEvent = new CustomEvent('insertImage', {
          detail: imageData
        });
        if (editorRef.current) {
          editorRef.current.dispatchEvent(imageInsertEvent);
        }
      }
    }
  }), []);

  // State management
  const [isConnected, setIsConnected] = useState(false);
  const [currentTitle, setCurrentTitle] = useState(documentTitle);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [lastSaved] = useState<Date>(new Date());
  const [isSaving] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(100);

  // Upload Image Plugin Component
  function UploadImagePlugin() {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
      editorRef.current = editor;
    }, [editor]);

    return null;
  }

  // SUPER SIMPLE TEST - NO BULLSHIT
  const testImageInsertion = () => {
    alert('🧪 TEST BUTTON CLICKED! Check console...');
    console.log('🧪 TEST BUTTON CLICKED!');

    if (!editorRef.current) {
      alert('❌ Editor not available');
      console.error('❌ Editor not available');
      return;
    }

    alert('✅ Editor is available, inserting text...');
    console.log('✅ Editor is available');

    // Just insert some fucking text to see if ANYTHING works
    editorRef.current.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        selection.insertText('🖼️ FUCK YEAH THIS WORKS! 🖼️ ');
      }
    });

    alert('✅ Text inserted, now trying image...');
    console.log('✅ Text inserted');

    // Now try using the command like before
    setTimeout(() => {
      console.log('🖼️ Trying image insertion with command...');

      try {
        const success = editorRef.current.dispatchCommand(INSERT_IMAGE_COMMAND, {
          src: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2ZmMDAwMCIvPjx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleXQtYW5jaG9yPSJtaWRkbGUiPkZVQ0sgWUVBSDwvdGV4dD48L3N2Zz4=',
          altText: 'Test Image',
          width: 200,
          height: 100
        });

        alert(`✅ Command result: ${success}`);
        console.log('🖼️ Command result:', success);
      } catch (error) {
        alert(`❌ Command failed: ${error.message}`);
        console.error('❌ Command failed:', error);
      }
    }, 1000);
  };

  // Menu handlers
  const handleMenuClick = (menuItem: string) => {
    switch (menuItem) {
      case 'Archivo':
        // Opciones de archivo
        console.log('📁 Menú Archivo clicked');
        const fileOptions = [
          '📄 Nuevo documento',
          '💾 Guardar',
          '📤 Exportar como PDF',
          '📤 Exportar como Word',
          '🖨️ Imprimir'
        ];
        const choice = prompt(`Selecciona una opción:\n${fileOptions.map((opt, i) => `${i+1}. ${opt}`).join('\n')}\n\nIngresa el número:`);
        if (choice && Number(choice) >= 1 && Number(choice) <= fileOptions.length) {
          alert(`Seleccionaste: ${fileOptions[Number(choice)-1]}`);
        }
        break;
      case 'Editar':
        // Mostrar opciones de edición útiles
        console.log('✏️ Menú Editar clicked');
        const editOptions = [
          'Deshacer (Ctrl+Z)',
          'Rehacer (Ctrl+Y)',
          'Copiar (Ctrl+C)',
          'Pegar (Ctrl+V)',
          'Seleccionar todo (Ctrl+A)'
        ];
        alert(`Opciones de Editar:\n${editOptions.join('\n')}`);
        break;
      case 'Ver':
        // Opciones de vista - cambiar zoom
        console.log('👁️ Menú Ver clicked');
        const newZoom = prompt(`Zoom actual: ${zoomLevel}%\nIngresa nuevo zoom (50-200):`, zoomLevel.toString());
        if (newZoom && !isNaN(Number(newZoom))) {
          const zoom = Math.max(50, Math.min(200, Number(newZoom)));
          setZoomLevel(zoom);
          console.log(`🔍 Zoom cambiado a ${zoom}%`);
        }
        break;
      case 'Test Image':
        // Test image insertion
        testImageInsertion();
        break;
      case 'Insertar':
        // Abrir selector de archivos para insertar imagen desde computadora
        console.log('➕ Menú Insertar clicked - Abriendo selector de archivos');
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = async (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            console.log('📷 Imagen seleccionada:', file.name);

            try {
              // Show loading feedback
              const loadingMessage = document.createElement('div');
              loadingMessage.textContent = '⏳ Procesando imagen...';
              loadingMessage.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3b82f6;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              `;
              document.body.appendChild(loadingMessage);

              // Convert file to data URL (base64) for direct insertion
              const reader = new FileReader();
              reader.onload = (event) => {
                const imageDataUrl = event.target?.result as string;

                // Remove loading message
                document.body.removeChild(loadingMessage);

                // Insert image using helper function
                if (editorRef.current) {
                  console.log('📝 Insertando imagen usando insertImage helper');

                  try {
                    insertImage(
                      editorRef.current,
                      imageDataUrl,
                      `Uploaded image: ${file.name}`,
                      file.name,
                      600,
                      400
                    );
                    console.log('✅ Image inserted successfully using helper function');
                  } catch (error) {
                    console.error('❌ Error inserting image with helper:', error);
                    throw error;
                  }

                  // Show success feedback
                  const successMessage = document.createElement('div');
                  successMessage.textContent = '✅ Imagen insertada correctamente';
                  successMessage.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #10b981;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    z-index: 10000;
                    font-weight: 500;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                  `;
                  document.body.appendChild(successMessage);
                  setTimeout(() => {
                    document.body.removeChild(successMessage);
                  }, 3000);
                } else {
                  throw new Error('Editor no está listo para insertar imágenes');
                }
              };

              reader.onerror = () => {
                // Remove loading message
                document.body.removeChild(loadingMessage);
                throw new Error('Error al leer el archivo');
              };

              reader.readAsDataURL(file);

            } catch (error) {
              console.error('❌ Error procesando/insertando imagen:', error);

              // Remove loading message if it exists
              const loadingMessage = document.querySelector('div[style*="Procesando imagen"]');
              if (loadingMessage) {
                document.body.removeChild(loadingMessage);
              }

              // Show error feedback
              const errorMessage = document.createElement('div');
              errorMessage.textContent = `❌ Error: ${error.message}`;
              errorMessage.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ef4444;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              `;
              document.body.appendChild(errorMessage);
              setTimeout(() => {
                document.body.removeChild(errorMessage);
              }, 5000);
            }
          }
        };
        input.click();
        break;
      case 'Formato':
        // Mostrar opciones de formato (Fuente, Párrafo, etc.)
        console.log('🎨 Menú Formato clicked');
        break;
      case 'Herramientas':
        // Mostrar herramientas (Corrector ortográfico, etc.)
        console.log('🔧 Menú Herramientas clicked');
        break;
      default:
        console.log(`❓ Menú ${menuItem} clicked`);
    }
  };

  // Lexical editor configuration with advanced formatting support
  const initialConfig = {
    namespace: 'EmmaDocsEditor',
    theme: {
      text: {
        bold: 'PlaygroundEditorTheme__bold',
        italic: 'PlaygroundEditorTheme__italic',
        underline: 'PlaygroundEditorTheme__underline',
        strikethrough: 'PlaygroundEditorTheme__strikethrough',
        underlineStrikethrough: 'PlaygroundEditorTheme__underlineStrikethrough',
        subscript: 'PlaygroundEditorTheme__subscript',
        superscript: 'PlaygroundEditorTheme__superscript',
        code: 'PlaygroundEditorTheme__textCode',
      },
      paragraph: 'PlaygroundEditorTheme__paragraph',
      heading: {
        h1: 'PlaygroundEditorTheme__h1',
        h2: 'PlaygroundEditorTheme__h2',
        h3: 'PlaygroundEditorTheme__h3',
        h4: 'PlaygroundEditorTheme__h4',
        h5: 'PlaygroundEditorTheme__h5',
        h6: 'PlaygroundEditorTheme__h6',
      },
      list: {
        nested: {
          listitem: 'PlaygroundEditorTheme__listItem',
        },
        ol: 'PlaygroundEditorTheme__ol',
        ul: 'PlaygroundEditorTheme__ul',
        listitem: 'PlaygroundEditorTheme__listItem',
        listitemChecked: 'PlaygroundEditorTheme__listItemChecked',
        listitemUnchecked: 'PlaygroundEditorTheme__listItemUnchecked',
      },
      link: 'PlaygroundEditorTheme__link',
      code: 'PlaygroundEditorTheme__code',
      codeBlock: 'PlaygroundEditorTheme__codeBlock',
      mark: 'PlaygroundEditorTheme__mark',
      markOverlap: 'PlaygroundEditorTheme__markOverlap',
      quote: 'PlaygroundEditorTheme__quote',
      hashtag: 'PlaygroundEditorTheme__hashtag',
      image: 'PlaygroundEditorTheme__image',
    },
    nodes: [
      HeadingNode,
      ListNode,
      ListItemNode,
      LinkNode,
      SimpleImageNode,
    ],
    onError: (error: Error) => {
      console.error('Lexical error:', error);
    },
  };

  // Initialize editor
  useEffect(() => {
    setIsConnected(true);
  }, [projectId]);

  // Handle image insertion events
  useEffect(() => {
    const handleImageInsert = (event: CustomEvent) => {
      console.log('📝 GoogleDocsEditor received insertImage event:', event.detail);
      const imageData = event.detail;

      try {
        // Validate image data
        if (!imageData || !imageData.url) {
          throw new Error('Invalid image data: missing URL');
        }

        if (imageInsertionRef.current) {
          console.log('📝 Calling insertImage on ImageInsertionHandler');
          imageInsertionRef.current.insertImage(imageData);
          console.log('✅ Image insertion completed successfully');

          // Show success feedback
          const successMessage = document.createElement('div');
          successMessage.textContent = '✅ Imagen insertada correctamente';
          successMessage.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          `;
          document.body.appendChild(successMessage);
          setTimeout(() => {
            document.body.removeChild(successMessage);
          }, 3000);

        } else {
          console.error('❌ ImageInsertionHandler ref is not available');
          throw new Error('Editor no está listo para insertar imágenes');
        }
      } catch (error) {
        console.error('❌ Error inserting image in editor:', error);

        // Show error feedback
        const errorMessage = document.createElement('div');
        errorMessage.textContent = `❌ Error: ${error.message}`;
        errorMessage.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: #ef4444;
          color: white;
          padding: 12px 20px;
          border-radius: 8px;
          z-index: 10000;
          font-weight: 500;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        document.body.appendChild(errorMessage);
        setTimeout(() => {
          document.body.removeChild(errorMessage);
        }, 5000);
      }
    };

    window.addEventListener('insertImage', handleImageInsert as EventListener);

    return () => {
      window.removeEventListener('insertImage', handleImageInsert as EventListener);
    };
  }, []);

  // Handle Lexical editor changes
  const handleLexicalChange = useCallback((editorState: any) => {
    editorState.read(() => {
      const root = $getRoot();
      const textContent = root.getTextContent();
      const htmlContent = root.getChildren().map((child: any) => {
        if (child.getType() === 'paragraph') {
          return `<p>${child.getTextContent()}</p>`;
        }
        return child.getTextContent();
      }).join('');

      onChange(htmlContent || textContent);
      updateWordCount(textContent);
    });
  }, [onChange]);

  // Update word count
  const updateWordCount = useCallback((text: string) => {
    const words = text.replace(/<[^>]*>/g, '').trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (content) {
      updateWordCount(content);
    }
  }, [content, updateWordCount]);

  // Lexical Keyboard Shortcuts Plugin
  const KeyboardShortcutsPlugin = () => {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.ctrlKey || event.metaKey) {
          switch (event.key) {
            case 'b':
              event.preventDefault();
              editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');
              break;
            case 'i':
              event.preventDefault();
              editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');
              break;
            case 'u':
              event.preventDefault();
              editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline');
              break;
            case 'z':
              if (event.shiftKey) {
                event.preventDefault();
                editor.dispatchCommand(REDO_COMMAND, undefined);
              } else {
                event.preventDefault();
                editor.dispatchCommand(UNDO_COMMAND, undefined);
              }
              break;
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }, [editor]);

    return null; // This is a plugin, not a UI component
  };

  return (
    <div className={`google-docs-editor ${className}`} style={{ height: '100vh', display: 'flex', flexDirection: 'column', background: '#f8f9fa' }}>
      {/* Compact Google Docs Header */}
      <div style={{ background: 'white', borderBottom: '1px solid #e8eaed' }}>
        {/* Top Menu Bar - Compact */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '4px 16px', minHeight: '40px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* Emma Logo - Smaller */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
              <div style={{
                width: '24px',
                height: '24px',
                background: '#8b5cf6',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Sparkles size={12} color="white" />
              </div>
              <span style={{ fontWeight: '600', color: '#202124', fontSize: '14px' }}>Emma Docs</span>
            </div>

            {/* Document Title - Inline */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {isEditingTitle ? (
                <input
                  ref={titleRef}
                  value={currentTitle}
                  onChange={(e) => setCurrentTitle(e.target.value)}
                  onBlur={() => {
                    setIsEditingTitle(false);
                    onTitleChange?.(currentTitle);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      setIsEditingTitle(false);
                      onTitleChange?.(currentTitle);
                    }
                  }}
                  style={{
                    fontSize: '14px',
                    fontWeight: '400',
                    color: '#202124',
                    background: 'transparent',
                    border: 'none',
                    borderBottom: '1px solid #3018ef',
                    outline: 'none',
                    padding: '2px 4px',
                    minWidth: '200px'
                  }}
                  autoFocus
                />
              ) : (
                <span
                  onClick={() => setIsEditingTitle(true)}
                  style={{
                    fontSize: '14px',
                    fontWeight: '400',
                    color: '#202124',
                    cursor: 'pointer',
                    padding: '2px 4px',
                    borderRadius: '3px',
                    transition: 'background 0.2s'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
                  onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
                >
                  {currentTitle}
                </span>
              )}
              <Star size={14} style={{ color: '#9aa0a6', cursor: 'pointer' }} />
            </div>
          </div>

          {/* Right Side Actions - Compact */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            {/* Collaborators */}
            {collaborators.length > 0 && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                {collaborators.slice(0, 2).map((collaborator) => (
                  <div
                    key={collaborator.id}
                    style={{
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      background: collaborator.color,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '10px',
                      fontWeight: '500'
                    }}
                    title={collaborator.name}
                  >
                    {collaborator.name.charAt(0).toUpperCase()}
                  </div>
                ))}
              </div>
            )}

            {/* Modern Share Button */}
            <button style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '8px 16px',
              border: 'none',
              borderRadius: '12px',
              background: '#3b82f6',
              color: 'white',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '600',
              transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: 'none'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#2563eb';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = '#3b82f6';
            }}
            >
              <Share size={16} />
              <span>Compartir</span>
            </button>
          </div>
        </div>

        {/* Modern Menu Navigation */}
        <div style={{ padding: '0 20px', borderTop: '1px solid #e5e7eb', background: '#fafbfc' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            {['Archivo', 'Editar', 'Ver', 'Insertar', 'Test Image', 'Formato', 'Herramientas'].map((item) => (
              <button
                key={item}
                onClick={() => handleMenuClick(item)}
                style={{
                  padding: '8px 12px',
                  border: 'none',
                  background: 'transparent',
                  color: '#6b7280',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  borderRadius: '8px',
                  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#f3f4f6';
                  e.currentTarget.style.color = '#111827';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.color = '#6b7280';
                }}
              >
                {item}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Advanced Formatting Toolbar */}
      {/* Legacy Toolbar (Hidden) - Keep for reference */}
      <div style={{ display: 'none', background: 'white', borderBottom: '1px solid #e8eaed', padding: '4px 16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flexWrap: 'wrap' }}>
          {/* Print Button */}
          <button
            onClick={() => window.print()}
            style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
            onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
            title="Imprimir"
          >
            <Printer size={14} color="#5f6368" />
          </button>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Zoom */}
          <select
            value={zoomLevel}
            onChange={(e) => setZoomLevel(Number(e.target.value))}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '60px',
              opacity: 1
            }}
          >
            <option value={75}>75%</option>
            <option value={100}>100%</option>
            <option value={125}>125%</option>
            <option value={150}>150%</option>
          </select>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Font Controls - Placeholder for future implementation */}
          <select
            onChange={(_e) => {/* Font family will be implemented with Lexical commands */}}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '100px',
              opacity: 1
            }}
          >
            <option value="Arial">Arial</option>
            <option value="Calibri">Calibri</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Georgia">Georgia</option>
          </select>

          <select
            onChange={(_e) => {/* Font size will be implemented with Lexical commands */}}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '50px',
              opacity: 1
            }}
          >
            <option value="1">8</option>
            <option value="2">10</option>
            <option value="3">12</option>
            <option value="4">14</option>
            <option value="5">18</option>
            <option value="6">24</option>
            <option value="7">36</option>
          </select>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Formatting Buttons - Working with keyboard shortcuts */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '1px' }}>
            <button
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Negrita (Ctrl+B)"
            >
              <Bold size={14} color="#5f6368" />
            </button>
            <button
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Cursiva (Ctrl+I)"
            >
              <Italic size={14} color="#5f6368" />
            </button>
            <button
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Subrayado (Ctrl+U)"
            >
              <Underline size={14} color="#5f6368" />
            </button>
          </div>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Note: Alignment and Lists are now handled by LexicalToolbar */}

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Sidebar Toggle Button - Bigger and More Visible */}
          {onToggleSidebar && (
            <button
              onClick={onToggleSidebar}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                background: showSidebar ? '#3b82f6' : '#ffffff',
                color: showSidebar ? '#ffffff' : '#6b7280',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = showSidebar ? '#2563eb' : '#f9fafb';
                e.currentTarget.style.borderColor = '#3b82f6';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = showSidebar ? '#3b82f6' : '#ffffff';
                e.currentTarget.style.borderColor = '#e5e7eb';
              }}
              title={showSidebar ? 'Ocultar Emma SEO Intelligence' : 'Mostrar Emma SEO Intelligence'}
            >
              {showSidebar ? <PanelRightClose size={16} /> : <PanelRightOpen size={16} />}
              <span>{showSidebar ? 'Ocultar SEO' : 'Emma SEO'}</span>
            </button>
          )}


        </div>
      </div>

      {/* Main Lexical Editor with Toolbar */}
      <LexicalComposer initialConfig={initialConfig}>
        {/* Advanced Formatting Toolbar */}
        <div style={{ background: 'white', borderBottom: '1px solid #e8eaed', padding: '8px 16px' }}>
          <AdvancedFormattingToolbar
            showSidebar={showSidebar}
            onToggleSidebar={onToggleSidebar}
            onImageInsert={onImageInsert}
          />
        </div>

        {/* Modern Document Area */}
        <div style={{ flex: 1, overflow: 'auto', background: '#f8fafc', padding: '32px', display: 'flex', justifyContent: 'center' }}>
          <div style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top center' }}>
            <div
              className="lexical-editor lexical-editor-container"
              data-lexical-editor="main-editor"
              style={{
                width: '8.5in',
                minHeight: '11in',
                background: '#ffffff',
                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                borderRadius: '16px',
                padding: '1in',
                fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                fontSize: '11pt',
                lineHeight: '1.6',
                color: '#111827',
                border: '1px solid #e5e7eb'
              }}
            >
              <RichTextPlugin
                contentEditable={
                  <ContentEditable
                    style={{
                      outline: 'none',
                      minHeight: '9in',
                      width: '100%',
                      resize: 'none',
                      fontSize: '11pt',
                      lineHeight: '1.6',
                      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                      color: '#111827'
                    }}
                    aria-placeholder="Comienza a escribir tu documento..."
                    placeholder={
                      <div style={{
                        color: '#9ca3af',
                        position: 'absolute',
                        top: '0',
                        pointerEvents: 'none',
                        userSelect: 'none',
                        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                        fontSize: '11pt',
                        lineHeight: '1.6'
                      }}>
                        Comienza a escribir tu documento...
                      </div>
                    }
                  />
                }
                placeholder={
                  <div style={{
                    color: '#9aa0a6',
                    position: 'absolute',
                    top: '0',
                    pointerEvents: 'none',
                    userSelect: 'none'
                  }}>
                    Comienza a escribir tu documento...
                  </div>
                }
                ErrorBoundary={LexicalErrorBoundary}
              />
              <HistoryPlugin />
              <ListPlugin />
              <CheckListPlugin />
              <TabIndentationPlugin />
              <LinkPlugin />
              <AutoFocusPlugin />
              <ImagePlugin captionsEnabled={true} />
              <UploadImagePlugin />
              <ImageInsertionHandler ref={imageInsertionRef} />
              <PersistentFormattingPlugin />
              <OnChangePlugin onChange={handleLexicalChange} />
              <KeyboardShortcutsPlugin />
            </div>
          </div>
        </div>
      </LexicalComposer>

      {/* Status Bar */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        padding: '8px 16px', 
        background: 'white', 
        borderTop: '1px solid #e8eaed', 
        fontSize: '13px', 
        color: '#5f6368' 
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {/* Save Status */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {isSaving ? (
              <>
                <div style={{ width: '12px', height: '12px', border: '2px solid #3018ef', borderTop: '2px solid transparent', borderRadius: '50%', animation: 'spin 1s linear infinite' }} />
                <span>Guardando...</span>
              </>
            ) : (
              <>
                <div style={{ width: '8px', height: '8px', background: '#34a853', borderRadius: '50%' }} />
                <span>Guardado {lastSaved.toLocaleTimeString()}</span>
              </>
            )}
          </div>

          <span>{wordCount} palabras</span>

          {isConnected ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{ width: '8px', height: '8px', background: '#34a853', borderRadius: '50%' }} />
              <span style={{ color: '#34a853' }}>Colaboración activa</span>
            </div>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{ width: '8px', height: '8px', background: '#ea4335', borderRadius: '50%' }} />
              <span style={{ color: '#ea4335' }}>Sin conexión</span>
            </div>
          )}
        </div>

        {/* Emma AI Indicator */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Sparkles size={14} color="#3018ef" />
          <span style={{
            color: '#8b5cf6',
            fontWeight: '500'
          }}>
            Emma AI
          </span>
        </div>
      </div>
    </div>
  );
});

GoogleDocsEditor.displayName = 'GoogleDocsEditor';

export default GoogleDocsEditor;
export { GoogleDocsEditor as EtherpadEditor };
export type { GoogleDocsEditorRef };
