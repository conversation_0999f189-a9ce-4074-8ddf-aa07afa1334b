/**
 * Emma Content Builder - Built on Etherpad Core
 * Real-time collaborative editor with <PERSON>'s branding and features
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Save, CheckCircle, Image as ImageIcon, FileText,
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, AlignJustify,
  List, ListOrdered, Minus, Link, Type, Palette, Wand2, Sparkles
} from 'lucide-react';

import EnhancedImageGenerator from './EnhancedImageGenerator';
import GoogleDocsEditor, { GoogleDocsEditorRef } from './GoogleDocsEditor';
import SEOIntelligencePanel from './SEOIntelligencePanel';
import ImageWorkflowTest from './ImageWorkflowTest';
import EditorFunctionalityTest from './EditorFunctionalityTest';
import './EtherpadEditor.css';

interface ContentBuilderProps {
  projectId: string;
  initialContent?: string;
  initialTopic?: string;
  onContentChange?: (content: string) => void;
  className?: string;
}

const ContentBuilder: React.FC<ContentBuilderProps> = ({
  projectId,
  initialContent = '',
  initialTopic = '',
  onContentChange,
  className = ''
}) => {
  const [content, setContent] = useState(initialContent);
  const [title, setTitle] = useState(initialTopic || 'Documento sin título');
  const [showImageGenerator, setShowImageGenerator] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showTestSuite, setShowTestSuite] = useState(false);
  const [showEditorTest, setShowEditorTest] = useState(false);

  // State for persisting generated images
  const [generatedImages, setGeneratedImages] = useState<Array<{
    id: string;
    url: string;
    prompt: string;
    timestamp: number;
  }>>([]);

  const editorRef = useRef<HTMLDivElement>(null);
  const googleDocsEditorRef = useRef<GoogleDocsEditorRef>(null);

  // Handle adding generated images to persistent state
  const handleImageGenerated = useCallback((imageUrl: string, prompt: string) => {
    const newImage = {
      id: Date.now().toString(),
      url: imageUrl,
      prompt: prompt,
      timestamp: Date.now()
    };
    setGeneratedImages(prev => [newImage, ...prev]);
    console.log('🎨 ContentBuilder: Image added to persistent state:', newImage);
  }, []);

  // Handle content changes with auto-save
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);

    if (onContentChange) {
      onContentChange(newContent);
    }
    // Auto-save with Emma branding
    setSaveStatus('saving');
    setTimeout(() => setSaveStatus('saved'), 1000);
  }, [onContentChange]);

  // Handle title changes
  const handleTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
  }, []);

  // Handle image insertion from modal
  const handleImageInsert = useCallback((imageData: GeneratedContentImage) => {
    console.log('🎨 ContentBuilder: Handling image insertion:', imageData);

    try {
      // Method 1: Try to call the editor's insertion method directly
      if (googleDocsEditorRef.current && googleDocsEditorRef.current.insertImage) {
        console.log('🎯 Using direct editor reference method');
        googleDocsEditorRef.current.insertImage(imageData);
        console.log('✅ Image inserted via direct reference');
      } else {
        console.warn('❌ GoogleDocsEditor ref not available, trying event dispatch');

        // Method 2: Dispatch event specifically to the editor container
        const editorContainer = document.querySelector('.lexical-editor-container') ||
                               document.querySelector('[data-lexical-editor]') ||
                               editorRef.current;

        if (editorContainer) {
          const imageInsertEvent = new CustomEvent('insertImage', {
            detail: imageData,
            bubbles: true
          });
          editorContainer.dispatchEvent(imageInsertEvent);
          console.log('✅ Event dispatched to editor container');
        } else {
          // Method 3: Fallback to window event
          console.log('🎯 Using window event as fallback');
          const imageInsertEvent = new CustomEvent('insertImage', {
            detail: imageData
          });
          window.dispatchEvent(imageInsertEvent);
          console.log('✅ Event dispatched to window');
        }
      }

      // Add to persistent state
      handleImageGenerated(imageData.url, imageData.altText);

      console.log('✅ Image insertion process completed successfully');

    } catch (error) {
      console.error('❌ Error inserting image into main editor:', error);
      alert('❌ Error al insertar imagen en el editor principal. Intenta de nuevo.');
    }
  }, [handleImageGenerated]);

  // Simple stats calculation
  const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
  const charCount = content.length;



  return (
    <div className="h-screen flex">
      {/* Google Docs Editor - Full Width */}
      <div className="flex-1">
        <GoogleDocsEditor
          ref={googleDocsEditorRef}
          content={content}
          onChange={handleContentChange}
          onImageInsert={() => setShowImageGenerator(true)}
          projectId={projectId}
          documentTitle={title}
          onTitleChange={handleTitleChange}
          collaborators={[
            {
              id: 'emma-ai',
              name: 'Emma AI',
              color: '#3018ef'
            }
          ]}
          className="h-full"
          showSidebar={showSidebar}
          onToggleSidebar={() => setShowSidebar(!showSidebar)}
        />
      </div>

      {/* SEO Intelligence Sidebar - Conditional */}
      {showSidebar && (
        <div className="w-80 flex-shrink-0 border-l border-gray-200 bg-white">
          <SEOIntelligencePanel
            content={content}
            onContentGenerate={handleContentChange}
            onImageGenerate={() => setShowImageGenerator(true)}
            className="h-full"
          />
        </div>
      )}

      {/* Image Generator Modal */}
      <AnimatePresence>
        {showImageGenerator && (
          <EnhancedImageGenerator
            onImageInsert={handleImageInsert}
            onImageGenerated={handleImageGenerated}
            onClose={() => setShowImageGenerator(false)}
            persistedImages={generatedImages}
          />
        )}
      </AnimatePresence>

      {/* Image Workflow Test Modal */}
      <AnimatePresence>
        {showTestSuite && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="relative max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto">
              <ImageWorkflowTest />
              <button
                onClick={() => setShowTestSuite(false)}
                className="absolute top-4 right-4 p-2 bg-white rounded-xl shadow-lg hover:bg-gray-100 hover:scale-105 transition-all duration-200"
              >
                ✕
              </button>
            </div>
          </div>
        )}
      </AnimatePresence>

      {/* Editor Functionality Test Modal */}
      <AnimatePresence>
        {showEditorTest && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="relative max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto">
              <EditorFunctionalityTest />
              <button
                onClick={() => setShowEditorTest(false)}
                className="absolute top-4 right-4 p-2 bg-white rounded-xl shadow-lg hover:bg-gray-100 hover:scale-105 transition-all duration-200"
              >
                ✕
              </button>
            </div>
          </div>
        )}
      </AnimatePresence>

      {/* Test Suite Buttons (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 flex flex-col gap-2 z-40">
          <button
            onClick={() => setShowTestSuite(true)}
            className="p-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-200 font-semibold"
            title="Run Image Workflow Tests"
          >
            🧪
          </button>
          <button
            onClick={() => setShowEditorTest(true)}
            className="p-3 bg-green-500 hover:bg-green-600 text-white rounded-xl transition-all duration-200 font-semibold"
            title="Test Editor Functionality"
          >
            📝
          </button>
        </div>
      )}
    </div>
  );
};

export default ContentBuilder;
