import React from "react";
import { motion } from "framer-motion";
import { Rocket, FileText, TrendingUp, Lightbulb } from "lucide-react";
import { <PERSON> } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

const CTASection: React.FC = () => {
  const { t } = useLanguage();

  const useCases = t('profesionales_ia.cta.use_cases') as Array<{title: string, description: string}>;

  const useCaseIcons = [
    <FileText size={32} className="text-white" />,
    <TrendingUp size={32} className="text-white" />,
    <Lightbulb size={32} className="text-white" />
  ];

  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-[#3018ef]/5 via-white to-[#dd3a5a]/5">
      <div className="container mx-auto">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl sm:text-5xl font-black mb-6">
                {t('profesionales_ia.cta.title_part1')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]">{t('profesionales_ia.cta.title_part2')}</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                {t('profesionales_ia.cta.subtitle')}
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                className="bg-white/20 backdrop-blur-md rounded-3xl border border-white/30 shadow-2xl p-8 text-center hover:bg-white/30 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <div className="flex justify-center mb-6">
                  <div className="bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] p-4 rounded-3xl shadow-xl text-white">
                    {useCaseIcons[index]}
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900">{useCase.title}</h3>
                <p className="text-gray-700">
                  {useCase.description}
                </p>
              </motion.div>
            ))}
          </div>

          <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl shadow-2xl p-8 text-white text-center">
            <h3 className="text-3xl font-black mb-6">{t('profesionales_ia.cta.ready_title')}</h3>
            <p className="text-xl mb-8">
              {t('profesionales_ia.cta.ready_subtitle')}
            </p>
            <div className="flex justify-center">
              <Link href="/login">
                <motion.button
                  className="bg-white/20 backdrop-blur-md text-white font-bold py-4 px-8 rounded-3xl border border-white/30 shadow-xl hover:bg-white/30 transition-all duration-300 w-full sm:w-auto"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="flex items-center justify-center gap-2">
                    {t('profesionales_ia.cta.start_now')} <Rocket size={20} />
                  </span>
                </motion.button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
