import React, { useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Rocket, Play } from "lucide-react";
import { <PERSON> } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

interface HeroProps {
  textIndex: number;
}

const Hero: React.FC<HeroProps> = ({ textIndex }) => {
  const heroRef = useRef<HTMLDivElement>(null);
  const { t } = useLanguage();
  const rotatingTexts = t('profesionales_ia.rotating_texts') as string[];

  return (
    <section ref={heroRef} className="pt-40 pb-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-white">
      <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef]/5 via-white to-[#dd3a5a]/5 z-0"></div>

      {/* Elementos decorativos con estilo Emma */}
      <motion.div
        className="absolute top-40 right-[10%] w-20 h-20 rounded-3xl bg-gradient-to-br from-[#3018ef] to-[#3018ef]/80 backdrop-blur-sm shadow-2xl"
        animate={{
          y: [0, -20, 0],
          rotate: [0, 10, 0]
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      />

      <motion.div
        className="absolute bottom-20 left-[10%] w-16 h-16 rounded-3xl bg-gradient-to-br from-[#dd3a5a] to-[#dd3a5a]/80 backdrop-blur-sm shadow-xl"
        animate={{
          y: [0, 15, 0],
          rotate: [0, -8, 0]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1
        }}
      />

      <motion.div
        className="absolute top-60 left-[15%] w-12 h-12 rounded-3xl bg-white/20 backdrop-blur-md border border-white/30 shadow-xl"
        animate={{
          y: [0, 10, 0],
          x: [0, 10, 0]
        }}
        transition={{ 
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2
        }}
      />
      
      <div className="container mx-auto relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <p className="text-xl sm:text-2xl font-medium mb-4 text-gray-700">
              {t('profesionales_ia.hero.subtitle')}
            </p>
            <div className="text-5xl sm:text-6xl md:text-7xl font-black mb-16 leading-tight text-[#3018ef]">
              <div className="h-[120px] sm:h-[140px] md:h-[160px] overflow-hidden">
                <AnimatePresence mode="wait">
                  <motion.span
                    key={rotatingTexts[textIndex]}
                    initial={{ opacity: 0, y: 40 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -40 }}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 20,
                    }}
                    className="block"
                  >
                    {rotatingTexts[textIndex]}
                  </motion.span>
                </AnimatePresence>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link href="/login">
              <motion.button
                className="bg-[#dd3a5a] text-white font-bold py-4 px-8 rounded-3xl shadow-2xl hover:bg-[#c73351] transition-all duration-300 w-full sm:w-auto backdrop-blur-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="flex items-center justify-center gap-2">
                  {t('profesionales_ia.hero.cta_start_free')} <Rocket size={20} />
                </span>
              </motion.button>
            </Link>

            <Link href="/login">
              <motion.button
                className="bg-white/20 backdrop-blur-md text-[#3018ef] font-bold py-4 px-8 rounded-3xl border border-white/30 shadow-xl hover:bg-white/30 transition-all duration-300 w-full sm:w-auto"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="flex items-center justify-center gap-2">
                  {t('profesionales_ia.hero.cta_see_demo')} <Play size={20} />
                </span>
              </motion.button>
            </Link>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
