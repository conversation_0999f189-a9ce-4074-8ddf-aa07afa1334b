import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Target } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

const WhatIsSection: React.FC = () => {
  const { t } = useLanguage();

  const definitionCards = t('profesionales_ia.what_is.definition_cards') as Array<{title: string, description: string}>;
  const digitalFeatures = t('profesionales_ia.what_is.digital_professional_features') as Array<{title: string, description: string}>;
  const howWorksPoints = t('profesionales_ia.what_is.how_works_points') as string[];

  const cardIcons = [
    <Brain size={32} className="text-white" />,
    <Clock size={32} className="text-white" />,
    <Target size={32} className="text-white" />
  ];

  return (
    <section id="que-es" className="py-24 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl sm:text-5xl font-black mb-6">
                {t('profesionales_ia.what_is.title')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">{t('profesionales_ia.what_is.title_highlight')}</span>{t('profesionales_ia.what_is.title_suffix')}
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('profesionales_ia.what_is.subtitle')}
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {definitionCards.map((card, index) => (
              <motion.div
                key={index}
                className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl p-8 text-center border border-white/20"
                whileHover={{ y: -5, scale: 1.02 }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <div className="flex justify-center mb-6">
                  <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] p-4 rounded-2xl shadow-lg">
                    {cardIcons[index]}
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4">{card.title}</h3>
                <p className="text-gray-600">
                  {card.description}
                </p>
              </motion.div>
            ))}
          </div>

          <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl shadow-xl p-8 text-white backdrop-blur-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-3xl font-black mb-6">{t('profesionales_ia.what_is.what_does_title')}</h3>
                <ul className="space-y-4">
                  {digitalFeatures.map((feature, index) => (
                    <motion.li
                      key={index}
                      className="flex items-start gap-3"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <div className="bg-white p-1 rounded-full mt-1">
                        <CheckCircle className="text-purple-600" size={20} />
                      </div>
                      <div>
                        <h4 className="font-bold text-xl">{feature.title}</h4>
                        <p>{feature.description}</p>
                      </div>
                    </motion.li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-3xl font-black mb-6">{t('profesionales_ia.what_is.how_works_title')}</h3>
                <ul className="space-y-4">
                  {howWorksPoints.map((point, index) => (
                    <motion.li
                      key={index}
                      className="flex items-start gap-3"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <div className="bg-white p-1 rounded-full mt-1">
                        <CheckCircle className="text-purple-600" size={20} />
                      </div>
                      <p>{point}</p>
                    </motion.li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatIsSection;
