import React from "react";
import { Zap, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin, Github } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

const Footer: React.FC = () => {
  const { t } = useLanguage();

  return (
    <footer className="bg-white border-t-4 border-black py-16 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#3018ef]/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#dd3a5a]/5 rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Logo y descripción */}
          <div className="col-span-1 md:col-span-1">
            <div className="flex items-center mb-6">
              <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] w-12 h-12 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg mr-3">
                E
              </div>
              <span className="text-2xl font-black text-gray-900">Emma Studio</span>
            </div>
            <p className="text-gray-600 mb-6 font-medium leading-relaxed">
              {t('profesionales_ia.footer.description')}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-600 hover:text-[#3018ef] transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-blue-400 transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-pink-500 transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">
                <Linkedin size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-[#dd3a5a] transition-colors">
                <Github size={20} />
              </a>
            </div>
          </div>

          {/* Enlaces rápidos */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-gray-900">{t('profesionales_ia.footer.quick_links')}</h3>
            <ul className="space-y-3">
              <li>
                <a href="#que-es" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.what_is')}</a>
              </li>
              <li>
                <a href="#como-funciona" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.how_it_works')}</a>
              </li>
              <li>
                <a href="#beneficios" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.benefits')}</a>
              </li>
              <li>
                <a href="#agentes" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.agents')}</a>
              </li>
              <li>
                <a href="#preguntas" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.faq')}</a>
              </li>
            </ul>
          </div>

          {/* Servicios */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-gray-900">{t('profesionales_ia.footer.services')}</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.ai_copywriting')}</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.ux_ui_design')}</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.marketing_strategy')}</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.data_analysis')}</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.email_marketing')}</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('profesionales_ia.footer.social_media')}</a>
              </li>
            </ul>
          </div>

          {/* Contacto */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-gray-900">{t('profesionales_ia.footer.contact')}</h3>
            <div className="space-y-3">
              <div className="flex items-center text-gray-600">
                <Mail className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium">{t('profesionales_ia.footer.email')}</span>
              </div>
              <div className="flex items-center text-gray-600">
                <Phone className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium">{t('profesionales_ia.footer.phone')}</span>
              </div>
              <div className="flex items-center text-gray-600">
                <MapPin className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium">{t('profesionales_ia.footer.address')}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-600 text-sm mb-4 md:mb-0 font-medium">
            &copy; {new Date().getFullYear()} Emma Studio. {t('profesionales_ia.footer.copyright')}
          </p>
          <div className="flex space-x-6">
            <a href="#" className="text-gray-600 hover:text-[#3018ef] text-sm transition-colors font-medium">
              {t('profesionales_ia.footer.terms')}
            </a>
            <a href="#" className="text-gray-600 hover:text-[#3018ef] text-sm transition-colors font-medium">
              {t('profesionales_ia.footer.privacy')}
            </a>
            <a href="#" className="text-gray-600 hover:text-[#3018ef] text-sm transition-colors font-medium">
              {t('profesionales_ia.footer.cookies')}
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
