import React from "react";
import { motion } from "framer-motion";
import { CheckCircle, XCircle } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

const ComparisonSection: React.FC = () => {
  const { t } = useLanguage();

  const comparisonData = t('soluciones_negocio.comparison.comparisons') as Array<{
    traditional: string;
    emma: string;
    differences: Array<{
      trad: string;
      emma: string;
    }>;
  }>;

  return (
    <section id="comparativa" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-black mb-4 text-gray-900">
              {t('soluciones_negocio.comparison.title')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]">{t('soluciones_negocio.comparison.title_highlight')}</span> {t('soluciones_negocio.comparison.title_suffix')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('soluciones_negocio.comparison.subtitle')}
            </p>
          </motion.div>
        </div>

        <div className="space-y-16">
          {comparisonData.map((comparison, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                <div className="bg-white/20 backdrop-blur-md p-8 rounded-3xl shadow-2xl border border-white/30">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{comparison.traditional}</h3>
                  <div className="space-y-4">
                    {comparison.differences.map((diff, idx) => (
                      <div key={idx} className="flex items-start">
                        <XCircle className="w-6 h-6 text-red-500 mr-3 flex-shrink-0" />
                        <p className="text-gray-700">{diff.trad}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="hidden lg:flex justify-center">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-full border-t-2 border-dashed border-gray-300"></div>
                    </div>
                    <div className="relative flex justify-center">
                      <span className="bg-gray-50 px-4 text-lg font-bold text-gray-500">{t('soluciones_negocio.comparison.vs')}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] p-8 rounded-3xl shadow-2xl text-white">
                  <h3 className="text-2xl font-bold mb-6">{comparison.emma}</h3>
                  <div className="space-y-4">
                    {comparison.differences.map((diff, idx) => (
                      <div key={idx} className="flex items-start">
                        <CheckCircle className="w-6 h-6 text-green-400 mr-3 flex-shrink-0" />
                        <p className="text-white">{diff.emma}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-20 bg-white/20 backdrop-blur-md p-8 rounded-3xl shadow-2xl border border-white/30"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] mb-2">150%</div>
              <p className="text-gray-700">{t('soluciones_negocio.comparison.stats.roi_increase')}</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] mb-2">85%</div>
              <p className="text-gray-700">{t('soluciones_negocio.comparison.stats.time_reduction')}</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] mb-2">67%</div>
              <p className="text-gray-700">{t('soluciones_negocio.comparison.stats.cost_reduction')}</p>
            </div>
          </div>
        </motion.div>
        
        <motion.div
          className="mt-12 mb-8"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          {/* Elementos decorativos */}
          <motion.div
            className="absolute -left-10 w-20 h-20 rounded-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] opacity-20 blur-xl hidden lg:block"
            animate={{
              y: [0, -15, 0],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute -right-10 w-20 h-20 rounded-full bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] opacity-20 blur-xl hidden lg:block"
            animate={{
              y: [0, 15, 0],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1
            }}
          />
          
          <h2 className="text-6xl sm:text-7xl font-black text-center">
            <span className="bg-gradient-to-r from-[#3018ef] via-[#dd3a5a] to-[#3018ef] text-transparent bg-clip-text">
              {t('soluciones_negocio.comparison.final_message_line1')}
            </span>
            <br />
            <span className="bg-gradient-to-r from-[#dd3a5a] via-[#3018ef] to-[#dd3a5a] text-transparent bg-clip-text">
              {t('soluciones_negocio.comparison.final_message_line2')}
            </span>
          </h2>
        </motion.div>
      </div>
    </section>
  );
};

export default ComparisonSection;
