import React, { useState } from "react";
import { motion } from "framer-motion";
import { testimonials, faqs } from "./data";
import { ChevronDown, ChevronUp, Quote } from "lucide-react";

const TestimonialsAndFAQ: React.FC = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(0);

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="container mx-auto">
        {/* Testimonials Section */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl font-black mb-4 text-gray-900">
                Lo Que <PERSON>n <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]">Nuestros Clientes</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Empresas de todos los tamaños están transformando su marketing con nuestros agentes IA.
                Descubre sus historias de éxito.
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl hover:scale-105 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="flex items-center mb-6">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full border-2 border-gray-200 object-cover mr-4 shadow-lg"
                  />
                  <div>
                    <h4 className="font-bold text-gray-900 text-lg">{testimonial.name}</h4>
                    <p className="text-[#3018ef] text-sm font-semibold">{testimonial.position}, {testimonial.company}</p>
                  </div>
                </div>
                <div className="relative mb-6">
                  <Quote className="absolute -top-3 -left-3 w-8 h-8 text-[#3018ef] opacity-30" />
                  <p className="text-gray-700 leading-relaxed pl-6 font-medium text-lg">
                    "{testimonial.quote}"
                  </p>
                </div>
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div>
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl font-black mb-4 text-gray-900">
                Preguntas <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]">Frecuentes</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Respuestas a las preguntas más comunes sobre nuestros agentes IA y cómo pueden ayudar a tu negocio.
              </p>
            </motion.div>
          </div>

          <div className="max-w-3xl mx-auto">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                className="mb-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <button
                  className={`w-full text-left p-6 rounded-3xl flex justify-between items-center ${
                    openFAQ === index
                      ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white"
                      : "bg-white/20 backdrop-blur-md text-gray-900 hover:bg-white/30 border border-white/30"
                  } transition-all duration-300 shadow-xl`}
                  onClick={() => toggleFAQ(index)}
                >
                  <span className="font-bold">{faq.question}</span>
                  {openFAQ === index ? (
                    <ChevronUp className="w-5 h-5 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 flex-shrink-0" />
                  )}
                </button>
                {openFAQ === index && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-white/90 backdrop-blur-sm p-6 rounded-b-3xl shadow-xl border border-white/30"
                  >
                    <p className="text-gray-700">{faq.answer}</p>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsAndFAQ;
