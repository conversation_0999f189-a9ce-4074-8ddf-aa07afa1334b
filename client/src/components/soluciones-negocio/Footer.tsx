import React from "react";
import { motion } from "framer-motion";
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from "lucide-react";
import { <PERSON> } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

const Footer: React.FC = () => {
  const { t } = useLanguage();

  return (
    <footer className="bg-white border-t-4 border-black pt-20 pb-10 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#3018ef]/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#dd3a5a]/5 rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-16">
          <div>
            <div className="flex items-center mb-6">
              <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] w-12 h-12 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg mr-3">
                E
              </div>
              <span className="text-2xl font-black text-gray-900">Emma Studio</span>
            </div>
            <p className="text-gray-600 mb-6 font-medium leading-relaxed">
              {t('soluciones_negocio.footer.description')}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-600 hover:text-[#3018ef] transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-blue-400 transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-pink-500 transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-6 text-gray-900">{t('soluciones_negocio.footer.solutions.title')}</h3>
            <ul className="space-y-3">
              <li>
                <a href="/marketing-automation" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.solutions.marketing_automation')}</a>
              </li>
              <li>
                <a href="/content-creation" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.solutions.content_creation')}</a>
              </li>
              <li>
                <a href="/data-analytics" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.solutions.data_analytics')}</a>
              </li>
              <li>
                <a href="/social-media" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.solutions.social_media')}</a>
              </li>
              <li>
                <a href="/email-marketing" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.solutions.email_marketing')}</a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-6 text-gray-900">{t('soluciones_negocio.footer.company.title')}</h3>
            <ul className="space-y-3">
              <li>
                <a href="/about" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.company.about')}</a>
              </li>
              <li>
                <a href="/careers" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.company.careers')}</a>
              </li>
              <li>
                <a href="/blog" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.company.blog')}</a>
              </li>
              <li>
                <a href="/press" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.company.press')}</a>
              </li>
              <li>
                <a href="/partners" className="text-gray-600 hover:text-[#3018ef] transition-colors font-medium">{t('soluciones_negocio.footer.company.partners')}</a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-6 text-gray-900">{t('soluciones_negocio.footer.contact.title')}</h3>
            <div className="space-y-3">
              <div className="flex items-center text-gray-600">
                <Mail className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium">{t('soluciones_negocio.footer.contact.email')}</span>
              </div>
              <div className="flex items-center text-gray-600">
                <Phone className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium">{t('soluciones_negocio.footer.contact.phone')}</span>
              </div>
              <div className="flex items-center text-gray-600">
                <MapPin className="w-5 h-5 mr-3 text-[#3018ef]" />
                <span className="font-medium">{t('soluciones_negocio.footer.contact.address')}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-600 text-sm mb-4 md:mb-0 font-medium">
            &copy; {new Date().getFullYear()} Emma Studio. {t('soluciones_negocio.footer.copyright')}
          </p>
          <div className="flex space-x-6">
            <a href="/privacy" className="text-gray-600 hover:text-[#3018ef] text-sm transition-colors font-medium">
              {t('soluciones_negocio.footer.legal.privacy')}
            </a>
            <a href="/terms" className="text-gray-600 hover:text-[#3018ef] text-sm transition-colors font-medium">
              {t('soluciones_negocio.footer.legal.terms')}
            </a>
            <a href="/cookies" className="text-gray-600 hover:text-[#3018ef] text-sm transition-colors font-medium">
              {t('soluciones_negocio.footer.legal.cookies')}
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
