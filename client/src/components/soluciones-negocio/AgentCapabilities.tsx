import React from "react";
import { motion } from "framer-motion";
import {
  Lightbulb,
  BrainCircuit,
  Search,
  Mail,
  Users,
  PieChart,
  Workflow,
  Target,
  Layers
} from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";


const AgentCapabilities: React.FC = () => {
  const { t } = useLanguage();

  const agentCapabilities = t('soluciones_negocio.agent_capabilities.capabilities') as Array<{
    title: string;
    description: string;
  }>;

  const getIcon = (index: number) => {
    const icons = [
      <Lightbulb size={24} className="text-white" />,
      <BrainCircuit size={24} className="text-white" />,
      <Search size={24} className="text-white" />,
      <Mail size={24} className="text-white" />,
      <Users size={24} className="text-white" />,
      <PieChart size={24} className="text-white" />,
      <Workflow size={24} className="text-white" />,
      <Target size={24} className="text-white" />,
      <Layers size={24} className="text-white" />
    ];
    return icons[index] || icons[0];
  };

  const customizationFeatures = t('soluciones_negocio.agent_capabilities.advanced_customization.features') as string[];

  return (
    <section id="capacidades" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-black mb-4 text-gray-900">
              {t('soluciones_negocio.agent_capabilities.title')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]">{t('soluciones_negocio.agent_capabilities.title_highlight')}</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('soluciones_negocio.agent_capabilities.subtitle')}
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {agentCapabilities.map((capability, index) => (
            <motion.div
              key={index}
              className="bg-white/20 backdrop-blur-md rounded-3xl shadow-2xl border border-white/30 hover:bg-white/30 transition-all duration-300 overflow-hidden"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] p-4 flex items-center justify-center">
                <div className="w-14 h-14 rounded-3xl flex items-center justify-center bg-white/20 backdrop-blur-sm shadow-xl text-white">
                  {getIcon(index)}
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">{capability.title}</h3>
                <p className="text-gray-700">{capability.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-16 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl p-8 text-white shadow-xl"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold mb-4">{t('soluciones_negocio.agent_capabilities.advanced_customization.title')}</h3>
              <p className="text-white/90 mb-6">
                {t('soluciones_negocio.agent_capabilities.advanced_customization.description')}
              </p>
              <ul className="space-y-3">
                {customizationFeatures.map((feature, idx) => (
                  <li key={idx} className="flex items-start">
                    <svg
                      className="w-5 h-5 text-green-300 mr-2 mt-1 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      ></path>
                    </svg>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="block">
              <div className="relative p-8">
                <img
                  src="/Customization.png"
                  alt="Personalización Avanzada"
                  className="w-full h-auto object-cover rounded-xl"
                />
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AgentCapabilities;
