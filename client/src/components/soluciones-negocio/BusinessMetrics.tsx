import React from "react";
import { motion } from "framer-motion";

const BusinessMetrics: React.FC = () => {
  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <motion.div
          className="bg-white/20 backdrop-blur-md rounded-3xl p-8 border border-white/30 shadow-2xl"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <motion.h3
                className="text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] mb-2"
                initial={{ scale: 0.8 }}
                whileInView={{ scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                150%
              </motion.h3>
              <p className="text-gray-700">Aumento promedio en ROI</p>
            </div>

            <div className="text-center">
              <motion.h3
                className="text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] mb-2"
                initial={{ scale: 0.8 }}
                whileInView={{ scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                85%
              </motion.h3>
              <p className="text-gray-700">Reducción en tiempo de ejecución</p>
            </div>

            <div className="text-center">
              <motion.h3
                className="text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] mb-2"
                initial={{ scale: 0.8 }}
                whileInView={{ scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                67%
              </motion.h3>
              <p className="text-gray-700">Reducción en costos operativos</p>
            </div>
          </div>
          
          <motion.h2
            className="text-4xl sm:text-5xl font-black mt-10 text-center bg-gradient-to-r from-[#3018ef] via-[#dd3a5a] to-[#3018ef] text-transparent bg-clip-text py-4 px-6 rounded-3xl shadow-xl mx-auto max-w-3xl backdrop-blur-sm bg-white/10"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            SI GASTAS MENOS, GANAS MÁS
          </motion.h2>
        </motion.div>
      </div>
    </section>
  );
};

export default BusinessMetrics;
