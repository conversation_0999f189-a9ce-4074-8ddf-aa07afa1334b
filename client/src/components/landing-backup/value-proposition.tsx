import { motion } from "framer-motion";

export default function ValueProposition() {
  return (
    <section className="py-16 relative z-10 overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <motion.h2
            className="text-3xl sm:text-4xl font-black mb-6 bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] inline-block"
            whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
          >
            Marketing Potenciado por IA que Funciona
          </motion.h2>

          <motion.p
            className="text-lg sm:text-xl font-bold leading-relaxed bg-white px-6 py-4 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Nuestra plataforma entrega automatización total y crecimiento
            escalable mediante
            <span className="text-blue-500">
              {" "}
              Equipo de Marketing Automatizado
            </span>
            ,<span className="text-pink-500"> Optimización para ROI</span>, y
            <span className="text-purple-500">
              {" "}
              Dominación Impulsada por IA
            </span>
            . Sin contratar personal, sin gestionar equipos, solo resultados.
          </motion.p>

          <div className="grid grid-cols-3 gap-4 mt-8">
            {[
              {
                title: "Automated Marketing Workforce",
                icon: "🤖",
                color: "blue",
              },
              { title: "Optimized for ROI", icon: "💰", color: "green" },
              { title: "AI-Powered Domination", icon: "🚀", color: "pink" },
            ].map((item, index) => (
              <motion.div
                key={index}
                className={`bg-white p-4 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
                whileHover={{
                  y: -5,
                  boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
                }}
              >
                <div className="text-3xl mb-2">{item.icon}</div>
                <div className={`font-black text-sm text-${item.color}-500`}>
                  {item.title}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Decorative elements */}
      <motion.div
        className="absolute -top-8 -right-8 w-16 h-16 bg-yellow-300 rounded-full border-3 border-black"
        animate={{
          y: [0, -10, 0],
          rotate: [0, 10, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className="absolute -bottom-8 -left-8 w-24 h-24 bg-blue-300 rounded-full border-3 border-black"
        animate={{
          y: [0, 10, 0],
          rotate: [0, -10, 0],
          scale: [1, 0.9, 1],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </section>
  );
}
