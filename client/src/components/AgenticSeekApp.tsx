/**
 * AgenticSeek App Component
 * IFRAME que carga el frontend original de AgenticSeek desde el backend
 */

import React, { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';

/**
 * AgenticSeek App - Interfaz principal de Emma AI
 * Carga el frontend original de AgenticSeek desde el backend
 */
export default function AgenticSeekApp() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // URL del frontend de AgenticSeek servido desde el backend
  const agenticSeekUrl = 'http://localhost:8001/agenticseek-frontend/';

  useEffect(() => {
    // Verificar que el backend esté disponible
    const checkBackend = async () => {
      try {
        const response = await fetch(agenticSeekUrl);
        if (response.ok) {
          setIsLoading(false);
        } else {
          setError('Backend no disponible');
          setIsLoading(false);
        }
      } catch (err) {
        setError('Error de conexión con el backend');
        setIsLoading(false);
      }
    };

    checkBackend();
  }, []);

  if (isLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Cargando Emma AI...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <p className="text-gray-600">Asegúrate de que el backend esté ejecutándose en el puerto 8000</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <iframe
        src={agenticSeekUrl}
        className="w-full h-full border-0"
        title="Emma AI - AgenticSeek Interface"
        style={{
          minHeight: '100vh',
          background: 'white'
        }}
        onLoad={() => {
          console.log('AgenticSeek frontend loaded successfully');
        }}
        onError={() => {
          setError('Error cargando la interfaz de AgenticSeek');
        }}
      />
    </div>
  );
}
