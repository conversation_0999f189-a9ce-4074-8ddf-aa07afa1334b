import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/use-auth';
import { supabase, getAuthenticatedApiClient } from '@/lib/supabase';
import { seoAnalysisService } from '@/services/seoAnalysisService';

export default function SEODashboardTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, isLoading: authLoading } = useAuth();

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testDatabaseConnection = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    try {
      addResult('🔍 Testing database connection...');

      // Test 1: Basic connection - try to fetch a small sample using correct API client
      const apiClient = await getAuthenticatedApiClient();
      const { data: connectionTest, error: connectionError } = await apiClient
        .from('seo_analyses')
        .select('id')
        .limit(1);

      if (connectionError) {
        addResult(`❌ Database connection failed: ${connectionError.message}`);
        addResult(`   Code: ${connectionError.code}`);
        addResult(`   Details: ${connectionError.details}`);
        addResult(`   Hint: ${connectionError.hint || 'No hint available'}`);
        return;
      }

      addResult('✅ Database connection successful');

      // Test 2: Check authentication
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        addResult(`❌ Authentication error: ${authError.message}`);
        addResult('💡 This explains the 406 error - RLS requires authentication');
        return;
      } else if (authUser) {
        addResult(`✅ User authenticated: ${authUser.id}`);
        addResult(`   Email: ${authUser.email}`);
      } else {
        addResult('❌ No authenticated user');
        addResult('💡 This explains the 406 error - RLS requires authentication');
        addResult('🔧 Please log in to test the database connection');
        return;
      }

      // Test 3: Try to fetch analyses
      if (user?.id) {
        addResult(`🔍 Fetching analyses for user: ${user.id}`);
        
        try {
          const analyses = await seoAnalysisService.getUserAnalyses(user.id, {
            limit: 10,
            orderBy: 'created_at',
            orderDirection: 'desc'
          });
          
          addResult(`✅ Successfully fetched ${analyses.length} analyses`);
          
          if (analyses.length > 0) {
            addResult('📊 Sample analysis:');
            const sample = analyses[0];
            addResult(`   ID: ${sample.id}`);
            addResult(`   URL: ${sample.url}`);
            addResult(`   Status: ${sample.status}`);
            addResult(`   Score: ${sample.overall_score}`);
            addResult(`   Created: ${new Date(sample.created_at).toLocaleString()}`);
            
            // Test filtering logic
            const activeStatuses = ["pending", "in_progress", "processing"];
            const completedStatuses = ["complete", "completed", "success"];
            const failedStatuses = ["error", "cancelled", "failed"];

            const activeAnalyses = analyses.filter(a => activeStatuses.includes(a.status));
            const completedAnalyses = analyses.filter(a => completedStatuses.includes(a.status));
            const failedAnalyses = analyses.filter(a => failedStatuses.includes(a.status));

            addResult('📈 Dashboard Filter Results:');
            addResult(`   Active: ${activeAnalyses.length}`);
            addResult(`   Completed: ${completedAnalyses.length}`);
            addResult(`   Failed: ${failedAnalyses.length}`);
          }
          
        } catch (serviceError: any) {
          addResult(`❌ Service error: ${serviceError.message}`);
        }
      } else {
        addResult('⚠️ No user ID available for testing');
      }

      // Test 4: Create a test analysis
      if (user?.id) {
        addResult('🧪 Creating test analysis...');
        
        try {
          const testAnalysisData = {
            user_id: user.id,
            url: 'https://example.com',
            analysis_mode: 'page' as const,
            tool_type: 'seo_analyzer',
            analysis_version: '1.0',
            overall_score: 85,
            basic_info: {
              title: 'Test Page',
              title_length: 9,
              meta_description: 'Test description',
              meta_description_length: 16,
              h1_tags: ['Test H1'],
              h1_count: 1,
              meta_robots: null,
              canonical_url: null,
              language: 'en'
            },
            content_analysis: {
              word_count: 100,
              images: { total: 1, without_alt: 0 },
              links: { total: 2, internal_count: 1, external_count: 1 },
              headings_structure: { h1: ['Test H1'] },
              top_keywords: [{ word: 'test', count: 5 }]
            },
            seo_checks: {
              has_title: true,
              has_meta_description: true,
              has_h1: true
            },
            recommendations: [
              {
                type: 'improvement',
                priority: 'medium',
                title: 'Test Recommendation',
                description: 'This is a test recommendation',
                impact: 'medium'
              }
            ],
            achievements: [],
            open_graph: {},
            twitter_card: {},
            preview_data: {},
            performance_metrics: undefined,
            ai_enhanced: false,
            status: 'completed' as const
          };

          const savedAnalysis = await seoAnalysisService.saveAnalysis(testAnalysisData);
          addResult(`✅ Test analysis created: ${savedAnalysis.id}`);
          
          // Verify it can be retrieved
          const updatedAnalyses = await seoAnalysisService.getUserAnalyses(user.id, {
            limit: 1,
            orderBy: 'created_at',
            orderDirection: 'desc'
          });
          
          if (updatedAnalyses.length > 0 && updatedAnalyses[0].id === savedAnalysis.id) {
            addResult('✅ Test analysis successfully retrieved');
          } else {
            addResult('❌ Test analysis not found in retrieval');
          }
          
        } catch (createError: any) {
          addResult(`❌ Error creating test analysis: ${createError.message}`);
        }
      }

    } catch (error: any) {
      addResult(`💥 Unexpected error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading) {
    return <div>Loading authentication...</div>;
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>SEO Dashboard Debug Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!user && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="font-medium text-yellow-800">⚠️ Authentication Required</h3>
            <p className="text-sm text-yellow-700 mt-1">
              You need to be logged in to test the database connection due to Row Level Security (RLS) policies.
              The 406 error occurs because unauthenticated requests are rejected.
            </p>
          </div>
        )}

        <div className="flex items-center gap-4 flex-wrap">
          <Button
            onClick={testDatabaseConnection}
            disabled={isLoading || !user}
          >
            {isLoading ? 'Testing...' : 'Run Database Test'}
          </Button>

          <Button
            onClick={async () => {
              setIsLoading(true);
              setTestResults([]);

              try {
                addResult('🧪 Creating test SEO analysis...');

                if (!user?.id) {
                  addResult('❌ No user ID available');
                  return;
                }

                const testAnalysisData = {
                  user_id: user.id,
                  url: 'https://example.com',
                  analysis_mode: 'page' as const,
                  tool_type: 'seo_analyzer',
                  analysis_version: '1.0',
                  overall_score: 85,
                  basic_info: { title: 'Test Page', description: 'Test description' },
                  content_analysis: { wordCount: 500, headings: [] },
                  seo_checks: { titleLength: true, metaDescription: true },
                  recommendations: { critical: [], important: [], minor: [] },
                  achievements: [],
                  open_graph: {},
                  twitter_card: {},
                  preview_data: {},
                  performance_metrics: {},
                  ai_enhanced: false,
                  status: 'completed' as const,
                  is_favorite: false
                };

                addResult('💾 Saving test analysis...');
                const savedAnalysis = await seoAnalysisService.saveAnalysis(testAnalysisData);
                addResult(`✅ Test analysis saved with ID: ${savedAnalysis.id}`);

                addResult('🔍 Fetching analyses to verify...');
                const analyses = await seoAnalysisService.getUserAnalyses(user.id, {
                  limit: 10,
                  orderBy: 'created_at',
                  orderDirection: 'desc'
                });

                addResult(`✅ Found ${analyses.length} analyses after save`);

              } catch (error: any) {
                addResult(`❌ Test analysis error: ${error.message}`);
                console.error('Test analysis error:', error);
              } finally {
                setIsLoading(false);
              }
            }}
            disabled={isLoading || !user}
            variant="default"
          >
            Create Test Analysis
          </Button>

          <div className="text-sm text-muted-foreground">
            User: {user?.email || 'Not authenticated'}
          </div>
        </div>

        {testResults.length > 0 && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">Test Results:</h3>
            <div className="space-y-1 text-sm font-mono">
              {testResults.map((result, index) => (
                <div key={index} className="whitespace-pre-wrap">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
