import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence, animate } from "framer-motion";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/contexts/LanguageContext";

// Counter Component - same as in PricingContainer
const Counter = ({ from, to }: { from: number; to: number }) => {
  const nodeRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const node = nodeRef.current;
    if (!node) return;
    const controls = animate(from, to, {
      duration: 1,
      onUpdate(value) {
        node.textContent = value.toFixed(0);
      },
    });
    return () => controls.stop();
  }, [from, to]);

  return <span ref={nodeRef} />;
};

export default function Pricing() {
  const { t } = useLanguage();
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      name: t('pricing.plans.basic.name'),
      monthlyPrice: 49,
      yearlyPrice: 39, // 20% discount
      isPopular: false,
      features: Array.isArray(t('pricing.plans.basic.features'))
        ? (t('pricing.plans.basic.features') as unknown) as string[]
        : [],
      accentColor: "bg-blue-500",
      textColor: "text-blue-500",
    },
    {
      name: t('pricing.plans.professional.name'),
      monthlyPrice: 99,
      yearlyPrice: 79, // 20% discount
      isPopular: true,
      features: Array.isArray(t('pricing.plans.professional.features'))
        ? (t('pricing.plans.professional.features') as unknown) as string[]
        : [],
      accentColor: "bg-pink-500",
      textColor: "text-pink-500",
    },
    {
      name: t('pricing.plans.enterprise.name'),
      monthlyPrice: 249,
      yearlyPrice: 199, // 20% discount
      isPopular: false,
      features: Array.isArray(t('pricing.plans.enterprise.features'))
        ? (t('pricing.plans.enterprise.features') as unknown) as string[]
        : [],
      accentColor: "bg-purple-500",
      textColor: "text-purple-500",
    },
  ];

  return (
    <section id="precios" className="py-20 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            {t('landing.pricing.title')}
          </h2>
          <p className="text-xl font-bold max-w-3xl mx-auto">
            {t('landing.pricing.subtitle')}
          </p>
        </motion.div>

        {/* Coming Soon Message */}
        <div className="flex justify-center items-center gap-4 mb-12">
          <motion.div
            className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-xl border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)] font-bold"
            animate={{
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            🚀 Pricing Available Soon - Stay Tuned!
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              className={cn(
                "relative bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] transition-all duration-200",
                plan.isPopular ? "md:transform md:scale-105 md:z-10" : "",
              )}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              whileHover={{ y: -5 }}
            >
              {plan.isPopular && (
                <motion.div
                  className="absolute -top-6 left-0 right-0 text-center"
                  animate={{
                    y: [0, -5, 0],
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                  }}
                >
                  <span className="inline-block px-4 py-1 bg-pink-500 text-white text-sm font-bold rounded-md border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">
                    {t('landing.pricing.most_popular')}
                  </span>
                </motion.div>
              )}

              {/* Coming Soon Badge */}
              <motion.div
                className={cn(
                  "absolute -top-4 -right-4 w-20 h-16 rounded-xl flex items-center justify-center border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)]",
                  plan.accentColor,
                )}
                animate={{
                  rotate: [0, 10, 0, -10, 0],
                  scale: [1, 1.1, 0.9, 1.1, 1],
                  y: [0, -5, 5, -3, 0],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: [0.76, 0, 0.24, 1],
                }}
              >
                <div className="text-center text-white px-2">
                  <div className="text-xs font-black leading-tight">
                    Coming
                  </div>
                  <div className="text-xs font-black leading-tight">
                    Soon
                  </div>
                </div>
              </motion.div>

              <div className={cn("mb-4", plan.isPopular ? "mt-2" : "")}>
                <h3 className="text-xl font-black text-black mb-2">
                  {plan.name}
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {index === 0
                    ? t('landing.pricing.plan_descriptions.basic')
                    : index === 1
                      ? t('landing.pricing.plan_descriptions.professional')
                      : t('landing.pricing.plan_descriptions.enterprise')}
                </p>
              </div>

              <div className="space-y-2 mb-6">
                {plan.features.map((feature, i) => (
                  <motion.div
                    key={feature}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: i * 0.1 }}
                    whileHover={{
                      x: 5,
                      scale: 1.02,
                      transition: { type: "spring", stiffness: 400 },
                    }}
                    className="flex items-center gap-2 p-2 bg-gray-50 rounded-md border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]"
                  >
                    <motion.span
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      className={cn(
                        "w-5 h-5 rounded-md flex items-center justify-center text-white font-bold text-xs border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.9)]",
                        plan.accentColor,
                      )}
                    >
                      ✓
                    </motion.span>
                    <span className="text-black font-bold text-sm">
                      {feature}
                    </span>
                  </motion.div>
                ))}
              </div>

              <motion.button
                className={cn(
                  "w-full py-2 rounded-lg text-white font-black text-sm border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] active:shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] transition-all duration-200",
                  plan.accentColor,
                )}
                whileHover={{
                  scale: 1.02,
                  transition: { duration: 0.2 },
                }}
                whileTap={{
                  scale: 0.95,
                  rotate: [-1, 1, 0],
                }}
              >
                {t('landing.pricing.start_now')} →
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
