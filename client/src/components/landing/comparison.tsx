import { motion } from "framer-motion";
import { Check, X } from "lucide-react";

export default function Comparison() {
  const comparisonPoints = [
    {
      category: "Costo",
      ai: "Pricing Available Soon",
      human: "$5,000-$15,000/mes por equipo",
      highlight: "AI will cost significantly less than human teams",
    },
    {
      category: "Horas de Trabajo",
      ai: "24/7 sin descansos",
      human: "8 horas/día, 5 días/semana",
      highlight: "IA trabaja 4x más horas que humanos",
    },
    {
      category: "Escalabilidad",
      ai: "Instantánea, sin límite",
      human: "Semanas para contratar, capacitar",
      highlight: "Escala 50x más rápido que equipos humanos",
    },
    {
      category: "Productividad",
      ai: "Múltiples tareas simultáneas",
      human: "Limitado a 1-2 tareas efectivas",
      highlight: "300% más producción por unidad de tiempo",
    },
    {
      category: "Optimización",
      ai: "Automática y constante",
      human: "Manual y periódica",
      highlight: "Mejora continua basada en datos",
    },
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Contrata al Equipo de Marketing IA Más Inteligente
          </h2>
          <p className="text-xl max-w-2xl mx-auto font-bold">
            Rendimiento superior con precios disponibles pronto
          </p>
        </motion.div>

        <div className="bg-white rounded-2xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] p-8 overflow-hidden">
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="col-span-1 font-black text-xl text-center"></div>
            <motion.div
              className="col-span-1 bg-blue-100 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] p-4 text-center"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4 }}
              whileHover={{
                y: -5,
                boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
              }}
            >
              <div className="flex justify-center items-center space-x-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full border-2 border-black flex items-center justify-center">
                  <span className="text-white font-bold text-xs">AI</span>
                </div>
                <h3 className="font-black">Agentes IA</h3>
              </div>
            </motion.div>
            <motion.div
              className="col-span-1 bg-gray-100 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] p-4 text-center"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.2 }}
              whileHover={{
                y: -5,
                boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
              }}
            >
              <div className="flex justify-center items-center space-x-2">
                <div className="w-8 h-8 bg-gray-500 rounded-full border-2 border-black flex items-center justify-center">
                  <span className="text-white font-bold text-xs">H</span>
                </div>
                <h3 className="font-black">Empleados Humanos</h3>
              </div>
            </motion.div>
          </div>

          {comparisonPoints.map((point, index) => (
            <motion.div
              key={index}
              className="grid grid-cols-3 gap-4 mb-6 items-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.1 * index }}
            >
              <motion.div
                className="col-span-1 font-bold text-lg p-3 bg-gray-50 rounded-lg border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] text-center"
                whileHover={{ x: -5 }}
              >
                {point.category}
              </motion.div>
              <motion.div
                className="col-span-1 bg-blue-50 rounded-lg border-2 border-black p-3 text-center relative"
                whileHover={{ x: 5 }}
              >
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-5 h-5 bg-blue-200 rounded-full border border-black flex items-center justify-center flex-shrink-0">
                    <Check size={12} className="text-blue-700" />
                  </div>
                  <span className="font-bold text-sm text-blue-700">
                    {point.ai}
                  </span>
                </div>
              </motion.div>
              <motion.div
                className="col-span-1 bg-gray-50 rounded-lg border-2 border-black p-3 text-center"
                whileHover={{ x: 5 }}
              >
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-5 h-5 bg-gray-200 rounded-full border border-black flex items-center justify-center flex-shrink-0">
                    <X size={12} className="text-gray-500" />
                  </div>
                  <span className="font-bold text-sm text-gray-500">
                    {point.human}
                  </span>
                </div>
              </motion.div>
            </motion.div>
          ))}

          <motion.div
            className="bg-green-100 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] p-4 text-center mt-10"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
            whileHover={{
              scale: 1.02,
              boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
            }}
          >
            <h3 className="font-black text-xl text-green-700 mb-2">
              Precios Competitivos
            </h3>
            <p className="font-bold">
              Los agentes IA ofrecerán precios significativamente más competitivos que contratar un equipo
              de marketing tradicional - ¡Precios disponibles pronto!
            </p>
          </motion.div>
        </div>

        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <motion.button
            className="bg-black text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-white hover:text-black transition-all duration-300"
            whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
            whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
          >
            Descubre Nuestros Precios Pronto →
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
