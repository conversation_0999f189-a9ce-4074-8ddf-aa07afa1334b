import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "wouter";
import {
  User,
  Briefcase,
  LifeBuoy,
  MessageSquareText,
  DollarSign,
  Calculator,
} from "lucide-react";
import { emmaAiLogo } from "../../assets";
import LanguageSelector from "@/components/common/LanguageSelector";
import { useLanguage } from "@/contexts/LanguageContext";

export default function Header() {
  const { t } = useLanguage();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 100);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Mobile header positioning fix
  useEffect(() => {
    const fixMobileHeader = () => {
      const header = document.querySelector('header[class*="landing-header-mobile-fix"]');
      if (header && window.innerWidth < 768) {
        // Force header to stay at top on mobile
        (header as HTMLElement).style.position = 'fixed';
        (header as HTMLElement).style.top = '0';
        (header as HTMLElement).style.left = '0';
        (header as HTMLElement).style.right = '0';
        (header as HTMLElement).style.zIndex = '50';
        (header as HTMLElement).style.width = '100%';
      }
    };

    // Fix on mount and resize
    fixMobileHeader();
    window.addEventListener('resize', fixMobileHeader);
    window.addEventListener('orientationchange', fixMobileHeader);

    return () => {
      window.removeEventListener('resize', fixMobileHeader);
      window.removeEventListener('orientationchange', fixMobileHeader);
    };
  }, []);

  return (
    <header
      className={`fixed top-0 left-0 w-full z-50 landing-header-mobile-fix ${scrolled ? "py-2" : "py-4"} transition-all duration-300`}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 50,
        // Ensure proper mobile positioning
        WebkitTransform: 'translateZ(0)',
        transform: 'translateZ(0)',
      }}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 flex justify-between items-center"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <img
                src={emmaAiLogo}
                alt="Emma AI Logo"
                className="h-28 w-auto"
              />
            </Link>
          </div>

          <nav className="hidden md:flex items-center space-x-6">
            {[
              {
                href: "/profesionales-ia",
                label: t('navigation.professionals'),
                icon: <User size={16} />,
              },
              {
                href: "/soluciones-negocio",
                label: t('navigation.solutions'),
                icon: <Briefcase size={16} />,
              },
              {
                href: "/planes-servicios",
                label: t('navigation.plans'),
                icon: <DollarSign size={16} />,
              },
              {
                href: "/calculadora-ahorro",
                label: t('navigation.calculator'),
                icon: <Calculator size={16} />,
              },
            ].map((link) => (
              <a
                key={link.href}
                href={link.href}
                className="font-bold hover:text-purple-600 transition-colors flex items-center"
              >
                <div className="mr-1">{link.icon}</div>
                {link.label}
              </a>
            ))}
          </nav>

          <div className="flex items-center space-x-2">
            <LanguageSelector variant="button" className="hidden sm:block" />
            <Link href="/login">
              <motion.button
                className="hidden sm:block bg-red-500 text-white font-bold py-2 px-4 rounded-lg border-2 border-black hover:bg-red-600 transition-all duration-200"
                whileHover={{
                  y: -2,
                  boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)",
                }}
                whileTap={{ y: 0 }}
              >
                {t('navigation.login')}
              </motion.button>
            </Link>

            <motion.button
              className="bg-purple-500 text-white font-black py-2 px-6 rounded-lg border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:bg-purple-600 transition-all duration-200"
              whileHover={{
                y: -4,
                boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
              }}
              whileTap={{ y: 0, boxShadow: "2px 2px 0px 0px rgba(0,0,0,0.9)" }}
            >
              {t('landing.get_started')}
            </motion.button>
          </div>

          <div className="md:hidden">
            <button
              className="focus:outline-none relative z-50"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              style={{
                // Ensure mobile menu button is always accessible
                position: 'relative',
                zIndex: 51,
              }}
            >
              <div className="w-10 h-10 bg-gray-100 rounded-lg border-2 border-black flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={mobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16m-7 6h7"}
                  />
                </svg>
              </div>
            </button>
          </div>
        </motion.div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <motion.div
            className="md:hidden mt-2 bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-4 mobile-menu"
            style={{
              position: 'relative',
              zIndex: 49,
              // Ensure mobile menu appears correctly
              WebkitTransform: 'translateZ(0)',
              transform: 'translateZ(0)',
            }}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <nav className="flex flex-col space-y-4">
              {[
                {
                  href: "/profesionales-ia",
                  label: t('navigation.professionals'),
                  icon: <User size={16} />,
                },
                {
                  href: "/soluciones-negocio",
                  label: t('navigation.solutions'),
                  icon: <Briefcase size={16} />,
                },
                {
                  href: "/planes-servicios",
                  label: t('navigation.plans'),
                  icon: <DollarSign size={16} />,
                },
                {
                  href: "/calculadora-ahorro",
                  label: t('navigation.calculator'),
                  icon: <Calculator size={16} />,
                },
              ].map((link) => (
                <a
                  key={link.href}
                  href={link.href}
                  className="font-bold hover:text-purple-600 transition-colors py-2 border-b border-gray-100 last:border-0 flex items-center"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <div className="mr-2">{link.icon}</div>
                  {link.label}
                </a>
              ))}
              <div className="pt-2">
                <Link href="/login">
                  <motion.button
                    className="w-full bg-red-500 text-white font-bold py-2 px-4 rounded-lg border-2 border-black hover:bg-red-600 transition-all duration-200 mb-2"
                    whileHover={{
                      y: -2,
                      boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)",
                    }}
                    whileTap={{ y: 0 }}
                  >
                    {t('navigation.login')}
                  </motion.button>
                </Link>

                <motion.button
                  className="w-full bg-purple-500 text-white font-black py-2 px-6 rounded-lg border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:bg-purple-600 transition-all duration-200"
                  whileHover={{
                    y: -2,
                    boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
                  }}
                  whileTap={{
                    y: 0,
                    boxShadow: "2px 2px 0px 0px rgba(0,0,0,0.9)",
                  }}
                >
                  {t('landing.get_started')}
                </motion.button>
              </div>
            </nav>
          </motion.div>
        )}
      </div>
    </header>
  );
}
