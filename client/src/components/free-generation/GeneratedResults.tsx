import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Download,
  ZoomIn,
  Sparkles,
  Edit3
} from 'lucide-react';
import { AdMiniEditor } from '@/components/tools/ad-creator/shared/AdMiniEditor';
import { GeneratedAd } from '@/types/ad-creator-types';
import { useToast } from '@/hooks/use-toast';

interface GeneratedResult {
  id: string;
  status: string;
  message: string;
  image_url?: string;
  all_images?: string[];
  num_generated?: number;
  revised_prompt?: string;
  platform?: string;
  size?: string;
  metadata?: any;
}

interface GeneratedResultsProps {
  result: GeneratedResult;
  onImageClick: (imageUrl: string) => void;
  onDownload: (imageUrl: string, filename: string) => void;
  onReset: () => void;
}

export function GeneratedResults({
  result,
  onImageClick,
  onDownload,
  onReset
}: GeneratedResultsProps) {
  const { toast } = useToast();
  const [editingImageUrl, setEditingImageUrl] = useState<string | null>(null);

  // Debug logs
  console.log('🎨 GeneratedResults received result:', result);
  console.log('🖼️ All images in component:', result.all_images);
  console.log('🔢 Number of images in component:', result.all_images?.length);
  console.log('🔥 EDIT BUTTON SHOULD BE VISIBLE NOW!');

  // Handle edited image
  const handleEditedImage = (editedAd: GeneratedAd) => {
    // For now, we'll just show a toast and close the editor
    // In a full implementation, you might want to add the edited image to the results
    toast({
      title: "¡Imagen editada!",
      description: "La imagen ha sido procesada exitosamente",
    });
    setEditingImageUrl(null);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="mt-8"
    >
      <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
        <CardContent className="p-8">
          <div className="text-center mb-6">
            <h3 className="text-3xl font-bold text-gray-800 mb-3">
              🚀 ¡Campaña Premium Lista!
            </h3>
            <p className="text-gray-600 text-lg">
              Generado con tecnología Emma Enterprise - Calidad nivel agencias
            </p>
          </div>

          {/* Mostrar todas las versiones generadas */}
          {result.all_images && result.all_images.length > 0 && (
            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                🎨 {result.num_generated || result.all_images.length} Versiones Premium Generadas
              </h4>
              <div className={`grid gap-4 ${
                result.all_images.length === 1 ? 'grid-cols-1 justify-center' :
                result.all_images.length === 2 ? 'grid-cols-1 md:grid-cols-2' :
                'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
              }`}>
                {result.all_images.map((imageUrl: string, index: number) => (
                  <div key={index} className="relative group">
                    <img
                      src={imageUrl}
                      alt={`Versión ${index + 1}`}
                      className="w-full rounded-xl shadow-lg border-2 border-gray-200 hover:shadow-2xl transition-all duration-300 cursor-pointer"
                      onClick={() => {
                        console.log(`🖱️ Clicked on image ${index + 1}:`, imageUrl);
                        onImageClick(imageUrl);
                      }}
                    />
                    <div className="absolute top-2 left-2 bg-[#3018ef] text-white px-2 py-1 rounded-full text-xs font-semibold">
                      V{index + 1}
                    </div>

                    <div className="absolute inset-0 bg-black/0 hover:bg-black/20 rounded-xl transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={(e) => {
                            console.log(`📥 Download clicked for image ${index + 1}:`, imageUrl);
                            e.stopPropagation();
                            onDownload(imageUrl, `emma-ad-v${index + 1}`);
                          }}
                          className="bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 px-3 py-2 rounded-full shadow-lg text-sm font-medium flex items-center gap-2"
                        >
                          <Download className="w-4 h-4" />
                          Descargar
                        </Button>
                        <Button
                          size="sm"
                          onClick={(e) => {
                            console.log(`👁️ View clicked for image ${index + 1}:`, imageUrl);
                            e.stopPropagation();
                            onImageClick(imageUrl);
                          }}
                          className="bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 px-3 py-2 rounded-full shadow-lg text-sm font-medium flex items-center gap-2"
                        >
                          <ZoomIn className="w-4 h-4" />
                          Ver
                        </Button>
                        <Button
                          size="sm"
                          onClick={(e) => {
                            console.log(`✏️ Edit clicked for image ${index + 1}:`, imageUrl);
                            e.stopPropagation();
                            setEditingImageUrl(imageUrl);
                          }}
                          className="bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 px-3 py-2 rounded-full shadow-lg text-sm font-medium flex items-center gap-2"
                        >
                          <Edit3 className="w-4 h-4" />
                          Editar
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="text-center space-y-6">
            <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-4">
              <p className="mb-2"><strong>Plataforma:</strong> {result.platform}</p>
              <p className="mb-2"><strong>Tamaño:</strong> {result.size}</p>
              <p><strong>Versiones generadas:</strong> {result.num_generated || 1}</p>
            </div>

            <div className="flex gap-4 justify-center flex-wrap">
              <Button
                onClick={() => {
                  if (result.image_url) {
                    onImageClick(result.image_url);
                  }
                }}
                className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-white px-6 py-3"
              >
                <ZoomIn className="w-5 h-5 mr-2" />
                Ampliar Principal
              </Button>

              <Button
                onClick={() => {
                  if (result.image_url) {
                    onDownload(result.image_url, 'emma-ad-principal');
                  }
                }}
                variant="outline"
                className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white px-6 py-3"
              >
                <Download className="w-5 h-5 mr-2" />
                Descargar Principal
              </Button>

              {result.all_images && result.all_images.length > 1 && (
                <Button
                  onClick={() => {
                    console.log('📥 DOWNLOADING ALL IMAGES NOW:', result.all_images);

                    // Descargar todas las imágenes una por una
                    result.all_images.forEach((url: string, index: number) => {
                      console.log(`📥 Downloading image ${index + 1}:`, url);
                      setTimeout(() => {
                        onDownload(url, `emma-ad-v${index + 1}`);
                      }, index * 500);
                    });
                  }}
                  variant="outline"
                  className="border-[#dd3a5a] text-[#dd3a5a] hover:bg-[#dd3a5a] hover:text-white px-6 py-3"
                >
                  <Download className="w-5 h-5 mr-2" />
                  Descargar Todas ({result.all_images.length})
                </Button>
              )}

              <Button
                onClick={onReset}
                variant="outline"
                className="border-gray-400 text-gray-600 hover:bg-gray-100 hover:text-gray-800 px-6 py-3"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Nueva Campaña
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mini Editor Modal */}
      {editingImageUrl && (
        <AdMiniEditor
          ad={{
            id: `free-gen-${Date.now()}`,
            image_url: editingImageUrl,
            prompt: result.revised_prompt || 'Imagen generada',
            revised_prompt: result.revised_prompt,
            response_id: result.id,
            timestamp: Date.now(),
            metadata: {
              platform: result.platform,
              size: result.size,
              source: 'free-generation'
            }
          }}
          isOpen={!!editingImageUrl}
          onClose={() => setEditingImageUrl(null)}
          onSave={handleEditedImage}
        />
      )}
    </motion.div>
  );
}
