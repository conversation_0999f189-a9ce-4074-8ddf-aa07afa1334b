import React from 'react';
import { useLanguage, Language } from '@/contexts/LanguageContext';
import { Globe } from 'lucide-react';

interface LanguageSelectorProps {
  className?: string;
  showLabel?: boolean;
  variant?: 'button' | 'dropdown' | 'toggle';
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ 
  className = '', 
  showLabel = false,
  variant = 'toggle'
}) => {
  const { currentLanguage, changeLanguage, availableLanguages } = useLanguage();

  const languageNames: Record<Language, string> = {
    es: 'Español',
    en: 'English'
  };

  const languageFlags: Record<Language, string> = {
    es: '🇪🇸',
    en: '🇺🇸'
  };

  if (variant === 'toggle') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {showLabel && (
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Globe className="w-4 h-4" />
            <span>Idioma:</span>
          </div>
        )}
        <div className="flex items-center bg-white/30 backdrop-blur-sm rounded-lg p-1 border border-white/20">
          {availableLanguages.map((lang) => (
            <button
              key={lang}
              onClick={() => changeLanguage(lang)}
              className={`
                px-3 py-2 rounded-md text-sm font-bold transition-all duration-200
                flex items-center gap-1 min-w-[44px] min-h-[44px] justify-center
                ${currentLanguage === lang
                  ? 'bg-white/80 text-[#3018ef] shadow-lg scale-105'
                  : 'text-gray-700 hover:text-[#3018ef] hover:bg-white/50'
                }
              `}
            >
              <span className="text-sm">{languageFlags[lang]}</span>
              <span className="font-bold">{lang.toUpperCase()}</span>
            </button>
          ))}
        </div>
      </div>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <select
          value={currentLanguage}
          onChange={(e) => changeLanguage(e.target.value as Language)}
          className="
            appearance-none bg-white border border-gray-300 rounded-lg
            px-3 py-2 pr-8 text-sm font-medium
            focus:outline-none focus:ring-2 focus:ring-[#3018ef] focus:border-transparent
            cursor-pointer
          "
        >
          {availableLanguages.map((lang) => (
            <option key={lang} value={lang}>
              {languageFlags[lang]} {languageNames[lang]}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>
    );
  }

  // Button variant
  return (
    <button
      onClick={() => {
        const nextLang = currentLanguage === 'es' ? 'en' : 'es';
        changeLanguage(nextLang);
      }}
      className={`
        flex items-center gap-2 px-3 py-2 rounded-lg
        bg-white border border-gray-300 hover:border-[#3018ef]
        text-sm font-medium text-gray-700 hover:text-[#3018ef]
        transition-all duration-200
        focus:outline-none focus:ring-2 focus:ring-[#3018ef] focus:border-transparent
        ${className}
      `}
      title={`Switch to ${currentLanguage === 'es' ? 'English' : 'Español'}`}
    >
      <Globe className="w-4 h-4" />
      <span className="text-xs">{languageFlags[currentLanguage]}</span>
      <span>{languageNames[currentLanguage]}</span>
    </button>
  );
};

export default LanguageSelector;
