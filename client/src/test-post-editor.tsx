import React from "react";
import PostEditor from "./components/tools/PostGenerator/components/PostEditor";

// Test data
const testPost = {
  id: "test-post-1",
  content: "Este es un post de prueba que necesita ser mejorado con IA. Habla sobre marketing digital y estrategias de contenido.",
  imageUrl: "",
  platform: "instagram",
  template: "viral",
  hashtags: ["marketing", "digital", "contenido"],
  cta: "¡Descubre más!",
  metadata: {
    businessName: "Mi Empresa",
    brandColor: "#3018ef",
    theme: "Balance",
    provider: "test",
    imageGenerated: false
  }
};

const testBrandData = {
  brandUrl: "https://example.com",
  brandDescription: "Una empresa de marketing digital",
  businessName: "Mi Empresa",
  brandColor: "#3018ef",
  voice: "Profesional y amigable",
  topics: ["marketing", "digital"],
  ctas: ["¡Descubre más!", "Contáctanos"]
};

const TestPostEditor: React.FC = () => {
  const handleSave = (updatedPost: any) => {
    console.log("Post guardado:", updatedPost);
    alert("Post guardado exitosamente!");
  };

  const handleCancel = () => {
    console.log("Edición cancelada");
    alert("Edición cancelada");
  };

  return (
    <div>
      <h1 style={{ padding: "20px", textAlign: "center" }}>
        Test del PostEditor con Mejora de IA
      </h1>
      <PostEditor
        post={testPost}
        brandData={testBrandData}
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default TestPostEditor;
