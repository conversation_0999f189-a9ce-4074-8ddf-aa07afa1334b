import axios from "axios";
import { supabase } from "./supabase";

/**
 * Standardized API client for Emma Studio
 * Uses the /api/v1 prefix for all endpoints
 */
export const api = axios.create({
  baseURL: "",  // Empty baseURL to avoid path duplication with <PERSON><PERSON>'s proxy
  withCredentials: false,
  timeout: 180000, // 3 minute timeout para generación de imágenes
});

// 🔧 FIX: Add authentication interceptor to include JWT token
api.interceptors.request.use(
  async (config) => {
    // Get current session and add auth header
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.access_token) {
        config.headers.Authorization = `Bearer ${session.access_token}`;
      }
    } catch (error) {
      console.warn("Failed to get auth session for API request:", error);
    }

    // Always log in development environment
    if (import.meta.env.DEV) {
      console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data,
        baseURL: config.baseURL
      });
    }
    return config;
  },
  (error) => {
    console.error("API Request Error:", error);
    // Log more details about the error
    if (error.request) {
      console.error("Request details:", {
        url: error.request.url,
        method: error.request.method,
        headers: error.request.headers
      });
    }
    return Promise.reject(error);
  }
);

// Enhanced global request/response interception
api.interceptors.response.use(
  (response) => {
    // Log successful responses in development
    if (import.meta.env.DEV) {
      console.log(`API Response (${response.status}):`, {
        url: response.config.url,
        method: response.config.method?.toUpperCase(),
        data: response.data
      });
    }
    return response;
  },
  (error) => {
    const status = error?.response?.status;
    const url = error?.config?.url || 'unknown URL';
    const method = error?.config?.method?.toUpperCase() || 'unknown method';

    if (status === 401) {
      console.warn(`Unauthorized (${method} ${url}) – redirecting to login`);
      // Redirect to login page on authentication failure
      setTimeout(() => {
        window.location.href = '/login';
      }, 100);
    }

    // Extract structured error message if available
    const errorData = error?.response?.data;
    const message = errorData?.error?.message ||
                   errorData?.detail ||
                   error.message;

    // Log detailed error information
    console.error(`API error (${status}) for ${method} ${url}:`, {
      message,
      data: errorData,
      error: error
    });

    // TODO: integrate with toast/notification system

    return Promise.reject(error);
  },
);

export default api;
