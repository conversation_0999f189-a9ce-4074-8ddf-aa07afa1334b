/**
 * Color Extraction Utility
 * Extracts dominant colors from images using canvas-based pixel analysis
 * Based on the Color Palette Generator implementation
 */

export interface ExtractedColor {
  hex: string;
  name: string;
  frequency: number;
}

export interface ColorExtractionResult {
  success: boolean;
  colors: ExtractedColor[];
  error?: string;
}

/**
 * Extract dominant colors from an image file
 */
export async function extractColorsFromImage(
  file: File,
  colorCount: number = 5
): Promise<ColorExtractionResult> {
  try {
    console.log("🎨 Starting color extraction from:", file.name);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return {
        success: false,
        colors: [],
        error: "El archivo debe ser una imagen válida"
      };
    }

    // Create image element
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return {
        success: false,
        colors: [],
        error: "No se pudo crear el contexto del canvas"
      };
    }

    // Load image and extract colors
    return new Promise((resolve) => {
      img.onload = () => {
        try {
          // Set canvas size to image size
          canvas.width = img.width;
          canvas.height = img.height;

          // Draw image on canvas
          ctx.drawImage(img, 0, 0);

          // Extract image data
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height).data;

          console.log("🎨 Extracting colors from image data...");

          // Extract dominant colors
          const dominantHexColors = extractDominantColors(
            imageData,
            canvas.width,
            canvas.height,
            colorCount
          );

          console.log("🎨 Extracted colors:", dominantHexColors);

          // Validate extracted colors
          const validColors = dominantHexColors.filter(hex => {
            const isValid = /^#[0-9A-F]{6}$/i.test(hex);
            if (!isValid) {
              console.warn("⚠️ Invalid hex color:", hex);
            }
            return isValid;
          });

          if (validColors.length === 0) {
            console.error("❌ No valid colors extracted");
            resolve({
              success: false,
              colors: [],
              error: "No se pudieron extraer colores válidos de la imagen"
            });
            return;
          }

          // Convert to ExtractedColor objects
          const colors: ExtractedColor[] = validColors.map((hex, index) => ({
            hex,
            name: getColorName(hex),
            frequency: validColors.length - index // Higher index = higher frequency
          }));

          console.log("🎨 Final color objects:", colors);

          resolve({
            success: true,
            colors,
            error: undefined
          });
        } catch (error) {
          console.error("❌ Error processing image:", error);
          resolve({
            success: false,
            colors: [],
            error: "Error al procesar la imagen"
          });
        }
      };

      img.onerror = () => {
        resolve({
          success: false,
          colors: [],
          error: "Error al cargar la imagen"
        });
      };

      // Load image from file
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    });
  } catch (error) {
    console.error("❌ Error in color extraction:", error);
    return {
      success: false,
      colors: [],
      error: "Error inesperado durante la extracción de colores"
    };
  }
}

/**
 * Extract dominant colors from image data
 * Based on the Color Palette Generator implementation
 */
function extractDominantColors(
  imageData: Uint8ClampedArray,
  width: number,
  height: number,
  colorCount: number
): string[] {
  // Create a map to count color frequency
  const colorMap: { [key: string]: number } = {};

  // Sample pixels (1 out of every 10 for performance)
  const sampleRate = 10;

  for (let y = 0; y < height; y += sampleRate) {
    for (let x = 0; x < width; x += sampleRate) {
      const idx = (y * width + x) * 4;

      // Get RGB values
      let r = imageData[idx];
      let g = imageData[idx + 1];
      let b = imageData[idx + 2];
      const a = imageData[idx + 3]; // Alpha channel

      // Skip transparent pixels
      if (a < 128) continue;

      // Reduce color precision to group similar colors
      // This reduces each channel to 16 possible values instead of 256
      r = Math.round(r / 16) * 16;
      g = Math.round(g / 16) * 16;
      b = Math.round(b / 16) * 16;

      // Convert to hexadecimal safely
      // Use >>> 0 to convert to unsigned 32-bit integer and avoid negative numbers
      const rgbValue = ((r << 16) | (g << 8) | b) >>> 0;
      const hex = `#${rgbValue.toString(16).padStart(6, "0")}`;

      // Increment counter for this color
      colorMap[hex] = (colorMap[hex] || 0) + 1;
    }
  }

  // Sort colors by frequency
  const sortedColors = Object.entries(colorMap)
    .sort((a, b) => b[1] - a[1])
    .map((entry) => entry[0]);

  // Filter very similar colors
  const uniqueColors: string[] = [];
  const minColorDifference = 30; // Minimum difference between colors

  for (const color of sortedColors) {
    if (uniqueColors.length >= colorCount) break;

    const rgb = hexToRgb(color);
    if (!rgb) continue;

    // Check if this color is too similar to existing colors
    const isSimilar = uniqueColors.some(existingColor => {
      const existingRgb = hexToRgb(existingColor);
      if (!existingRgb) return false;

      const diff = Math.abs(rgb.r - existingRgb.r) + 
                   Math.abs(rgb.g - existingRgb.g) + 
                   Math.abs(rgb.b - existingRgb.b);
      return diff < minColorDifference;
    });

    if (!isSimilar) {
      uniqueColors.push(color);
    }
  }

  return uniqueColors;
}

/**
 * Convert hex color to RGB
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Convert hex color to HSL
 */
function hexToHSL(hex: string): [number, number, number] {
  const rgb = hexToRgb(hex);
  if (!rgb) return [0, 0, 0];

  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)];
}

/**
 * Get color name from hex value
 */
function getColorName(hex: string): string {
  const [h, s, l] = hexToHSL(hex);

  // Handle grayscale colors
  if (s < 10) {
    if (l > 90) return "Blanco";
    if (l > 70) return "Gris Claro";
    if (l > 30) return "Gris";
    if (l > 10) return "Gris Oscuro";
    return "Negro";
  }

  // Basic names based on hue
  if ((h >= 0 && h <= 20) || h >= 340) return "Rojo";
  if (h >= 21 && h <= 45) return "Naranja";
  if (h >= 46 && h <= 70) return "Amarillo";
  if (h >= 71 && h <= 160) return "Verde";
  if (h >= 161 && h <= 200) return "Turquesa";
  if (h >= 201 && h <= 240) return "Azul";
  if (h >= 241 && h <= 280) return "Violeta";
  if (h >= 281 && h <= 320) return "Rosa";
  if (h >= 321 && h <= 339) return "Magenta";

  return "Color";
}
