/**
 * Datos de plantillas para Emma Visual Studio
 * Estas plantillas son utilizadas en el selector de plantillas y
 * proporcionan elementos visuales predefinidos para diferentes tipos de contenido
 */

// Tipo para plantillas
export type Template = {
  id: string;
  name: string;
  thumbnail: string;
  category: string;
  tags?: string[];
  isFavorite?: boolean;
  elements: any[]; // Los elementos predefinidos para cargar en el canvas
};

// Imágenes para plantillas (usando SVG locales)
const placeholderImages = {
  instagram:
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxMDgwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiM2MzY2ZjEiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjQ4IiBmaWxsPSIjRkZGRkZGIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+SW5zdGFncmFtIFBvc3Q8L3RleHQ+PC9zdmc+",
  instagramStory:
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxOTIwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNhODU1ZjciLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjQ4IiBmaWxsPSIjRkZGRkZGIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+SW5zdGFncmFtIFN0b3J5PC90ZXh0Pjwvc3ZnPg==",
  facebook:
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iIzNiODJmNiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDgiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5GYWNlYm9vayBQb3N0PC90ZXh0Pjwvc3ZnPg==",
  twitter:
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2NzUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iIzBlYTVlOSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDgiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5Ud2l0dGVyIFBvc3Q8L3RleHQ+PC9zdmc+",
  linkedIn:
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MjciIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iIzBkOTQ4OCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDgiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5MaW5rZWRJbiBQb3N0PC90ZXh0Pjwvc3ZnPg==",
  web: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI4MDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2Y5NzMxNiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDgiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5XZWIgQmFubmVyPC90ZXh0Pjwvc3ZnPg==",
};

// Plantillas de ejemplo para distintas categorías
export const visualTemplates: Template[] = [
  // Instagram Feed
  {
    id: "instagram-feed-1",
    name: "Post Minimalista",
    thumbnail: placeholderImages.instagram,
    category: "instagram",
    tags: ["feed", "minimalista", "moderno"],
    isFavorite: true,
    elements: [
      {
        id: "background",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        fill: "#ffffff",
        rotation: 0,
      },
      {
        id: "title",
        type: "text",
        x: 540,
        y: 300,
        width: 800,
        height: 60,
        text: "TÍTULO IMPACTANTE",
        fontSize: 48,
        fontFamily: "Arial",
        fill: "#000000",
        align: "center",
        rotation: 0,
      },
      {
        id: "subtitle",
        type: "text",
        x: 540,
        y: 400,
        width: 700,
        height: 40,
        text: "Subtítulo o descripción secundaria",
        fontSize: 28,
        fontFamily: "Arial",
        fill: "#666666",
        align: "center",
        rotation: 0,
      },
      {
        id: "accent-circle",
        type: "circle",
        x: 540,
        y: 600,
        radius: 120,
        fill: "#f3f4f6",
        rotation: 0,
      },
    ],
  },
  {
    id: "instagram-feed-2",
    name: "Post Promocional",
    thumbnail: placeholderImages.instagram,
    category: "instagram",
    tags: ["feed", "promoción", "ventas"],
    elements: [
      {
        id: "background",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        fill: "#3b82f6",
        rotation: 0,
      },
      {
        id: "title",
        type: "text",
        x: 540,
        y: 200,
        width: 800,
        height: 60,
        text: "¡OFERTA ESPECIAL!",
        fontSize: 64,
        fontFamily: "Impact",
        fill: "#ffffff",
        align: "center",
        rotation: 0,
      },
      {
        id: "price",
        type: "text",
        x: 540,
        y: 400,
        width: 300,
        height: 120,
        text: "50% OFF",
        fontSize: 120,
        fontFamily: "Arial",
        fontStyle: "bold",
        fill: "#ffff00",
        align: "center",
        rotation: 0,
      },
      {
        id: "call-to-action",
        type: "text",
        x: 540,
        y: 650,
        width: 600,
        height: 40,
        text: "¡COMPRA AHORA!",
        fontSize: 32,
        fontFamily: "Arial",
        fill: "#ffffff",
        align: "center",
        rotation: 0,
      },
    ],
  },

  // Instagram Stories
  {
    id: "instagram-story-1",
    name: "Story Elegante",
    thumbnail: placeholderImages.instagramStory,
    category: "stories",
    tags: ["story", "elegante", "minimalista"],
    elements: [
      {
        id: "background",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        fill: "#f8fafc",
        rotation: 0,
      },
      {
        id: "title",
        type: "text",
        x: 540,
        y: 800,
        width: 800,
        height: 60,
        text: "NUEVA COLECCIÓN",
        fontSize: 48,
        fontFamily: "Arial",
        fill: "#0f172a",
        align: "center",
        rotation: 0,
      },
      {
        id: "subtitle",
        type: "text",
        x: 540,
        y: 900,
        width: 700,
        height: 40,
        text: "Disponible ahora",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#64748b",
        align: "center",
        rotation: 0,
      },
      {
        id: "swipe-up",
        type: "text",
        x: 540,
        y: 1600,
        width: 400,
        height: 40,
        text: "Desliza hacia arriba ↑",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#0f172a",
        align: "center",
        rotation: 0,
      },
    ],
  },

  // Facebook Posts
  {
    id: "facebook-post-1",
    name: "Post Informativo",
    thumbnail: placeholderImages.facebook,
    category: "facebook",
    tags: ["post", "informativo", "educativo"],
    elements: [
      {
        id: "background",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1200,
        height: 630,
        fill: "#ffffff",
        rotation: 0,
      },
      {
        id: "header-bar",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1200,
        height: 100,
        fill: "#3b82f6",
        rotation: 0,
      },
      {
        id: "title",
        type: "text",
        x: 600,
        y: 50,
        width: 1000,
        height: 60,
        text: "5 CONSEJOS PARA MEJORAR TU ESTRATEGIA",
        fontSize: 32,
        fontFamily: "Arial",
        fill: "#ffffff",
        align: "center",
        rotation: 0,
      },
      {
        id: "content-1",
        type: "text",
        x: 600,
        y: 180,
        width: 1000,
        height: 40,
        text: "1. Define objetivos claros",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#000000",
        align: "center",
        rotation: 0,
      },
      {
        id: "content-2",
        type: "text",
        x: 600,
        y: 250,
        width: 1000,
        height: 40,
        text: "2. Conoce a tu audiencia",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#000000",
        align: "center",
        rotation: 0,
      },
      {
        id: "content-3",
        type: "text",
        x: 600,
        y: 320,
        width: 1000,
        height: 40,
        text: "3. Crea contenido de valor",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#000000",
        align: "center",
        rotation: 0,
      },
      {
        id: "content-4",
        type: "text",
        x: 600,
        y: 390,
        width: 1000,
        height: 40,
        text: "4. Analiza resultados",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#000000",
        align: "center",
        rotation: 0,
      },
      {
        id: "content-5",
        type: "text",
        x: 600,
        y: 460,
        width: 1000,
        height: 40,
        text: "5. Optimiza constantemente",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#000000",
        align: "center",
        rotation: 0,
      },
      {
        id: "footer",
        type: "text",
        x: 600,
        y: 570,
        width: 1000,
        height: 40,
        text: "www.tuempresa.com",
        fontSize: 20,
        fontFamily: "Arial",
        fill: "#3b82f6",
        align: "center",
        rotation: 0,
      },
    ],
  },

  // Twitter Posts
  {
    id: "twitter-post-1",
    name: "Tweet Impactante",
    thumbnail: placeholderImages.twitter,
    category: "twitter",
    tags: ["twitter", "simple", "impacto"],
    elements: [
      {
        id: "background",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1200,
        height: 675,
        fill: "#1da1f2",
        rotation: 0,
      },
      {
        id: "container",
        type: "rectangle",
        x: 150,
        y: 100,
        width: 900,
        height: 475,
        fill: "#ffffff",
        cornerRadius: 20,
        rotation: 0,
      },
      {
        id: "quote",
        type: "text",
        x: 600,
        y: 300,
        width: 800,
        height: 300,
        text: '"Las grandes oportunidades nacen de haber sabido aprovechar las pequeñas"',
        fontSize: 36,
        fontFamily: "Georgia",
        fill: "#333333",
        align: "center",
        rotation: 0,
      },
      {
        id: "author",
        type: "text",
        x: 600,
        y: 450,
        width: 800,
        height: 40,
        text: "- Bill Gates",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#1da1f2",
        align: "center",
        rotation: 0,
      },
    ],
  },

  // LinkedIn Posts
  {
    id: "linkedin-post-1",
    name: "Post Profesional",
    thumbnail: placeholderImages.linkedIn,
    category: "linkedin",
    tags: ["linkedin", "profesional", "negocios"],
    elements: [
      {
        id: "background",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1200,
        height: 627,
        fill: "#f0f6ff",
        rotation: 0,
      },
      {
        id: "header",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1200,
        height: 80,
        fill: "#0a66c2",
        rotation: 0,
      },
      {
        id: "title",
        type: "text",
        x: 600,
        y: 40,
        width: 1000,
        height: 40,
        text: "OPORTUNIDAD PROFESIONAL",
        fontSize: 28,
        fontFamily: "Arial",
        fill: "#ffffff",
        align: "center",
        rotation: 0,
      },
      {
        id: "position",
        type: "text",
        x: 600,
        y: 160,
        width: 1000,
        height: 50,
        text: "Marketing Manager",
        fontSize: 40,
        fontFamily: "Arial",
        fontStyle: "bold",
        fill: "#0a66c2",
        align: "center",
        rotation: 0,
      },
      {
        id: "requirements",
        type: "text",
        x: 600,
        y: 280,
        width: 1000,
        height: 40,
        text: "Requisitos:",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#333333",
        align: "center",
        rotation: 0,
      },
      {
        id: "req-text",
        type: "text",
        x: 600,
        y: 380,
        width: 800,
        height: 200,
        text: "• 5+ años de experiencia\n• Inglés avanzado\n• Conocimientos en SEO y SEM\n• Habilidades de liderazgo",
        fontSize: 22,
        fontFamily: "Arial",
        fill: "#333333",
        align: "center",
        rotation: 0,
      },
      {
        id: "cta",
        type: "text",
        x: 600,
        y: 550,
        width: 1000,
        height: 40,
        text: "Aplica en www.tuempresa.com/carreras",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#0a66c2",
        align: "center",
        rotation: 0,
      },
    ],
  },

  // Web Banners
  {
    id: "web-banner-1",
    name: "Banner Promocional",
    thumbnail: placeholderImages.web,
    category: "web",
    tags: ["web", "banner", "promoción"],
    elements: [
      {
        id: "background",
        type: "rectangle",
        x: 0,
        y: 0,
        width: 1200,
        height: 300,
        fill: "#f97316",
        rotation: 0,
      },
      {
        id: "title",
        type: "text",
        x: 300,
        y: 120,
        width: 500,
        height: 60,
        text: "OFERTA ESPECIAL",
        fontSize: 48,
        fontFamily: "Impact",
        fill: "#ffffff",
        align: "left",
        rotation: 0,
      },
      {
        id: "subtitle",
        type: "text",
        x: 300,
        y: 180,
        width: 500,
        height: 40,
        text: "Hasta 70% de descuento",
        fontSize: 24,
        fontFamily: "Arial",
        fill: "#ffffff",
        align: "left",
        rotation: 0,
      },
      {
        id: "cta-button",
        type: "rectangle",
        x: 900,
        y: 150,
        width: 200,
        height: 60,
        fill: "#ffffff",
        cornerRadius: 30,
        rotation: 0,
      },
      {
        id: "cta-text",
        type: "text",
        x: 900,
        y: 150,
        width: 200,
        height: 60,
        text: "COMPRAR",
        fontSize: 24,
        fontFamily: "Arial",
        fontStyle: "bold",
        fill: "#f97316",
        align: "center",
        rotation: 0,
      },
    ],
  },
];
