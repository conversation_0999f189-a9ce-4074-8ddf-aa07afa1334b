import { FileText, Briefcase, Book, Mail, Search, Megaphone, Video, BarChart3, BookOpen, Brain, BriefcaseBusiness, CalendarDays, Code, CopyCheck, FileQuestion, Globe, Image, Lightbulb, ListChecks, MessageSquare, Mic, PenTool, Router, Settings, Tag, Target, User, Users, Zap, Layout, Info, ShoppingBag, Check, Send, Bot, LayoutDashboard, FileCheck, FileSpreadsheet, HelpCircle, Newspaper, Film, Share2, Store, ArrowBigRightDash, Radio, FileText as DocumentText } from 'lucide-react';

export type QuestionFieldType = 
  | 'text' 
  | 'textarea' 
  | 'select' 
  | 'radio' 
  | 'checkbox' 
  | 'number' 
  | 'date' 
  | 'editor' 
  | 'url'
  | 'tone'
  | 'slider'
  | 'keywords'
  | 'audience';

export interface ContentFormQuestion {
  id: string;
  type: QuestionFieldType;
  label: string;
  placeholder?: string;
  helperText?: string;
  required?: boolean;
  min?: number;
  max?: number;
  options?: { value: string; label: string }[];
  defaultValue?: any;
  icon?: React.ElementType;
  advanced?: boolean;
  section?: string;
}

export interface ContentFormSection {
  id: string;
  title: string;
  description?: string;
  icon?: React.ElementType;
}

export interface ContentFormData {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  sections: ContentFormSection[];
  questions: ContentFormQuestion[];
  agents: {
    id: string;
    name: string;
    avatar: string;
    role: string;
    description: string;
  }[];
  tips?: string[];
  examples?: {
    title: string;
    description: string;
    link?: string;
  }[];
}

// Mapeo de formularios personalizados por ID de subtipo
const contentFormData: Record<string, ContentFormData> = {
  // Categoría: Blog y Contenido Extenso
  'blog-post': {
    id: 'blog-post',
    title: 'Post de Blog',
    description: 'Crea contenido de blog optimizado y atractivo para tu audiencia',
    icon: Book,
    sections: [
      {
        id: 'basics',
        title: 'Información básica',
        description: 'Información esencial sobre tu artículo',
        icon: FileText
      },
      {
        id: 'audience',
        title: 'Audiencia y objetivos',
        description: 'Define para quién escribes y qué quieres lograr',
        icon: Users
      },
      {
        id: 'content',
        title: 'Contenido',
        description: 'Define el enfoque y elementos del contenido',
        icon: Book
      },
      {
        id: 'seo',
        title: 'SEO y distribución',
        description: 'Optimiza tu contenido para buscadores',
        icon: Search
      }
    ],
    questions: [
      // Sección: Información básica
      {
        id: 'title',
        type: 'text',
        label: '¿Cuál es el título de tu post?',
        placeholder: 'Ej. 10 Estrategias Infalibles para Mejorar tu Marketing Digital en 2025',
        helperText: 'Un buen título es claro, específico y contiene palabras clave relevantes',
        required: true,
        section: 'basics'
      },
      {
        id: 'topic',
        type: 'textarea',
        label: '¿De qué trata tu post?',
        placeholder: 'Describe brevemente el tema principal y el enfoque que le darás',
        helperText: 'Sé específico sobre el tema y ángulo que cubrirás',
        required: true,
        section: 'basics'
      },
      {
        id: 'category',
        type: 'select',
        label: 'Categoría principal',
        placeholder: 'Selecciona la categoría',
        helperText: 'Elige la categoría que mejor describe tu contenido',
        options: [
          { value: 'marketing-digital', label: 'Marketing Digital' },
          { value: 'redes-sociales', label: 'Redes Sociales' },
          { value: 'seo', label: 'SEO' },
          { value: 'contenidos', label: 'Estrategia de Contenidos' },
          { value: 'email-marketing', label: 'Email Marketing' },
          { value: 'analitica', label: 'Analítica y Datos' },
          { value: 'conversion', label: 'Conversión y Ventas' },
          { value: 'branding', label: 'Branding y Marca Personal' },
          { value: 'tendencias', label: 'Tendencias y Novedades' },
          { value: 'otros', label: 'Otros' }
        ],
        required: true,
        section: 'basics'
      },
      
      // Sección: Audiencia y objetivos
      {
        id: 'audience',
        type: 'audience',
        label: 'Audiencia objetivo',
        placeholder: 'Describe a tu audiencia ideal para este contenido',
        helperText: 'Define quién leerá tu contenido: edad, intereses, nivel de conocimiento, etc.',
        required: true,
        section: 'audience'
      },
      {
        id: 'knowledge_level',
        type: 'select',
        label: 'Nivel de conocimiento de la audiencia',
        helperText: 'Esto ayudará a adaptar el vocabulario y la profundidad del contenido',
        options: [
          { value: 'principiante', label: 'Principiante' },
          { value: 'intermedio', label: 'Intermedio' },
          { value: 'avanzado', label: 'Avanzado' },
          { value: 'mixto', label: 'Mixto (diferentes niveles)' }
        ],
        required: true,
        section: 'audience'
      },
      {
        id: 'goals',
        type: 'select',
        label: 'Objetivo principal del post',
        helperText: 'Define qué quieres lograr con este contenido',
        options: [
          { value: 'informar', label: 'Informar y educar' },
          { value: 'entretener', label: 'Entretener e inspirar' },
          { value: 'resolver', label: 'Resolver problemas específicos' },
          { value: 'vender', label: 'Promocionar producto/servicio' },
          { value: 'posicionar', label: 'Posicionarse como experto' },
          { value: 'generar-leads', label: 'Generar leads' },
          { value: 'engagement', label: 'Aumentar engagement' }
        ],
        required: true,
        section: 'audience'
      },
      
      // Sección: Contenido
      {
        id: 'tone',
        type: 'tone',
        label: 'Tono de voz',
        helperText: 'Selecciona el tono que debe tener el contenido',
        required: true,
        section: 'content'
      },
      {
        id: 'key_points',
        type: 'textarea',
        label: 'Puntos clave a cubrir',
        placeholder: 'Enumera los puntos o temas principales que quieres incluir',
        helperText: 'Incluye 3-5 puntos importantes para estructurar el contenido',
        required: true,
        section: 'content'
      },
      {
        id: 'content_structure',
        type: 'select',
        label: 'Estructura preferida',
        options: [
          { value: 'lista', label: 'Lista numerada (paso a paso)' },
          { value: 'tutorial', label: 'Tutorial detallado' },
          { value: 'guia', label: 'Guía completa' },
          { value: 'comparativa', label: 'Comparativa' },
          { value: 'caso-estudio', label: 'Caso de estudio' },
          { value: 'preguntas', label: 'Preguntas y respuestas' },
          { value: 'tradicional', label: 'Artículo tradicional' }
        ],
        section: 'content'
      },
      {
        id: 'content_length',
        type: 'select',
        label: 'Longitud aproximada',
        options: [
          { value: 'corto', label: 'Corto (500-800 palabras)' },
          { value: 'medio', label: 'Medio (800-1500 palabras)' },
          { value: 'largo', label: 'Largo (1500-2500 palabras)' },
          { value: 'muy-largo', label: 'Muy largo (2500+ palabras)' }
        ],
        section: 'content'
      },
      {
        id: 'special_elements',
        type: 'checkbox',
        label: 'Elementos especiales a incluir',
        options: [
          { value: 'imagenes', label: 'Sugerencias para imágenes' },
          { value: 'estadisticas', label: 'Estadísticas y datos' },
          { value: 'ejemplos', label: 'Ejemplos prácticos' },
          { value: 'citas', label: 'Citas de expertos' },
          { value: 'infografias', label: 'Ideas para infografías' },
          { value: 'recursos', label: 'Recursos descargables' },
          { value: 'videos', label: 'Sugerencias para videos' }
        ],
        section: 'content'
      },
      
      // Sección: SEO y distribución
      {
        id: 'keywords',
        type: 'keywords',
        label: 'Palabras clave principales',
        placeholder: 'Ej. marketing digital, estrategia redes sociales, tendencias marketing 2025',
        helperText: 'Incluye palabras clave primarias y secundarias (separadas por comas)',
        required: true,
        section: 'seo'
      },
      {
        id: 'meta_description',
        type: 'textarea',
        label: 'Meta descripción',
        placeholder: 'Escribe una meta descripción atractiva y con palabras clave (máx. 160 caracteres)',
        helperText: 'Esta descripción aparecerá en los resultados de búsqueda',
        max: 160,
        section: 'seo'
      },
      {
        id: 'cta',
        type: 'text',
        label: 'Call-to-Action (CTA)',
        placeholder: 'Ej. Descarga nuestra guía gratuita, Agenda una consultoría',
        helperText: 'Acción que quieres que realice el lector al finalizar',
        section: 'seo'
      }
    ],
    agents: [
      {
        id: 'blog-strategist',
        name: 'Carlos Rivera',
        avatar: '/avatars/male-1.png',
        role: 'Estratega de Contenidos',
        description: 'Experto en crear contenido de blog optimizado para SEO y conversión con más de 8 años de experiencia'
      },
      {
        id: 'audience-specialist',
        name: 'Ana Martínez',
        avatar: '/avatars/female-1.png',
        role: 'Especialista en Audiencias',
        description: 'Experta en identificar necesidades de audiencias y adaptar el tono y enfoque del contenido'
      },
      {
        id: 'seo-expert',
        name: 'Miguel Sánchez',
        avatar: '/avatars/male-2.png',
        role: 'Experto SEO',
        description: 'Especialista en optimización para buscadores y estructura de contenido'
      }
    ],
    tips: [
      'Usa encabezados (H2, H3, H4) para estructurar tu contenido y facilitar la lectura',
      'Incluye una introducción atractiva que enganche al lector y presente el problema',
      'Incorpora elementos visuales como imágenes, gráficos o videos para mejorar el engagement',
      'Cierra con una conclusión que resuma los puntos clave y un CTA claro',
      'Actualiza periódicamente tus posts más populares para mantener el contenido fresco'
    ],
    examples: [
      {
        title: '7 Estrategias de Email Marketing que Aumentarán tus Conversiones en 2025',
        description: 'Artículo completo con estadísticas, ejemplos y plantillas descargables'
      },
      {
        title: 'Guía Definitiva de SEO Local: Cómo Posicionar tu Negocio en Google Maps',
        description: 'Tutorial paso a paso con capturas de pantalla y casos de éxito'
      },
      {
        title: 'Análisis: ¿Por qué fracasan el 90% de las estrategias de contenido?',
        description: 'Estudio en profundidad con entrevistas a expertos y soluciones prácticas'
      }
    ]
  },
  
  // Categoría: Email Marketing
  'promo-email': {
    id: 'promo-email',
    title: 'Email Promocional',
    description: 'Crea emails persuasivos para promocionar productos, servicios o ofertas especiales',
    icon: Mail,
    sections: [
      {
        id: 'basics',
        title: 'Información básica',
        description: 'Información esencial sobre la promoción',
        icon: FileText
      },
      {
        id: 'audience',
        title: 'Audiencia y objetivo',
        description: 'Define a quién te diriges y qué quieres lograr',
        icon: Target
      },
      {
        id: 'offer',
        title: 'Oferta y contenido',
        description: 'Detalles sobre la oferta, beneficios y elementos del mensaje',
        icon: Tag
      },
      {
        id: 'technical',
        title: 'Elementos técnicos',
        description: 'Aspectos técnicos del correo y seguimiento',
        icon: Settings
      }
    ],
    questions: [
      // Sección: Información básica
      {
        id: 'subject',
        type: 'text',
        label: 'Asunto del email',
        placeholder: 'Ej. ¡Oferta exclusiva! 50% dto. solo 48 horas',
        helperText: 'El asunto determina si tu email será abierto. Hazlo atractivo pero evita el clickbait',
        required: true,
        section: 'basics'
      },
      {
        id: 'preheader',
        type: 'text',
        label: 'Preheader (texto previo)',
        placeholder: 'Ej. Aproveche nuestra mayor oferta del año con envío gratuito incluido',
        helperText: 'Este texto se muestra junto al asunto en la bandeja de entrada (máx. 85-100 caracteres)',
        max: 100,
        section: 'basics'
      },
      {
        id: 'campaign_name',
        type: 'text',
        label: 'Nombre de la campaña',
        placeholder: 'Ej. Black Friday 2025, Lanzamiento Producto X',
        helperText: 'Nombre interno para identificar esta promoción',
        section: 'basics'
      },
      
      // Sección: Audiencia y objetivo
      {
        id: 'audience',
        type: 'audience',
        label: 'Audiencia objetivo',
        placeholder: 'Describe a quién va dirigido este email promocional',
        helperText: 'Cuanto más específico seas, más efectivo será el mensaje',
        required: true,
        section: 'audience'
      },
      {
        id: 'segmentation',
        type: 'select',
        label: 'Segmentación recomendada',
        options: [
          { value: 'todos', label: 'Lista completa' },
          { value: 'clientes', label: 'Solo clientes actuales' },
          { value: 'leads', label: 'Solo leads (no clientes)' },
          { value: 'inactivos', label: 'Usuarios inactivos' },
          { value: 'abandonos', label: 'Carritos abandonados' },
          { value: 'interesados', label: 'Interesados en categoría específica' },
          { value: 'custom', label: 'Segmentación personalizada' }
        ],
        section: 'audience'
      },
      {
        id: 'custom_segment',
        type: 'textarea',
        label: 'Detalles de segmentación personalizada',
        placeholder: 'Describe los criterios específicos para este segmento',
        advanced: true,
        section: 'audience'
      },
      {
        id: 'goal',
        type: 'select',
        label: 'Objetivo principal del email',
        options: [
          { value: 'venta', label: 'Generar ventas directas' },
          { value: 'trafico', label: 'Aumentar tráfico al sitio web' },
          { value: 'leads', label: 'Generar leads' },
          { value: 'engagement', label: 'Mejorar engagement' },
          { value: 'reactivacion', label: 'Reactivar usuarios' },
          { value: 'evento', label: 'Promocionar evento' },
          { value: 'fidelizacion', label: 'Fidelizar clientes' }
        ],
        required: true,
        section: 'audience'
      },
      
      // Sección: Oferta y contenido
      {
        id: 'offer_type',
        type: 'select',
        label: 'Tipo de oferta',
        options: [
          { value: 'descuento', label: 'Descuento porcentual' },
          { value: 'precio-fijo', label: 'Precio fijo especial' },
          { value: 'envio-gratis', label: 'Envío gratuito' },
          { value: '2x1', label: '2x1 o similar' },
          { value: 'regalo', label: 'Regalo con compra' },
          { value: 'lanzamiento', label: 'Lanzamiento de producto' },
          { value: 'exclusivo', label: 'Acceso exclusivo' },
          { value: 'tiempo-limitado', label: 'Oferta por tiempo limitado' }
        ],
        section: 'offer'
      },
      {
        id: 'offer_details',
        type: 'textarea',
        label: 'Detalles de la oferta',
        placeholder: 'Describe la oferta específicamente: porcentajes, condiciones, productos incluidos, etc.',
        required: true,
        section: 'offer'
      },
      {
        id: 'validity',
        type: 'date',
        label: 'Fecha de caducidad',
        helperText: 'Cuándo termina esta promoción',
        section: 'offer'
      },
      {
        id: 'key_benefits',
        type: 'textarea',
        label: 'Beneficios clave',
        placeholder: 'Lista los 3-5 beneficios principales que debe destacar el email',
        required: true,
        section: 'offer'
      },
      {
        id: 'tone',
        type: 'tone',
        label: 'Tono de voz',
        helperText: 'Selecciona el tono que debe tener el mensaje',
        required: true,
        section: 'offer'
      },
      {
        id: 'cta_text',
        type: 'text',
        label: 'Texto para el botón principal (CTA)',
        placeholder: 'Ej. ¡Comprar ahora!, Ver oferta exclusiva',
        helperText: 'Hazlo claro, directo y orientado a la acción (máx. 30 caracteres)',
        required: true,
        max: 30,
        section: 'offer'
      },
      
      // Sección: Elementos técnicos
      {
        id: 'cta_url',
        type: 'url',
        label: 'URL de destino',
        placeholder: 'https://ejemplo.com/pagina-de-destino',
        helperText: 'Página a la que llevará el CTA principal',
        section: 'technical'
      },
      {
        id: 'tracking',
        type: 'text',
        label: 'Parámetros de seguimiento',
        placeholder: 'Ej. utm_source=email&utm_medium=promotional&utm_campaign=spring_sale',
        helperText: 'UTMs u otros parámetros para seguimiento en Analytics',
        advanced: true,
        section: 'technical'
      },
      {
        id: 'images',
        type: 'textarea',
        label: 'Elementos visuales sugeridos',
        placeholder: 'Describe las imágenes o elementos visuales para el email',
        helperText: 'Sugiere el tipo de imágenes que se deberían incluir',
        section: 'technical'
      },
      {
        id: 'specific_instructions',
        type: 'textarea',
        label: 'Instrucciones adicionales',
        placeholder: 'Cualquier otra información importante para crear el email',
        section: 'technical'
      }
    ],
    agents: [
      {
        id: 'email-copywriter',
        name: 'Laura Gómez',
        avatar: '/avatars/female-2.png',
        role: 'Especialista en Email Marketing',
        description: 'Experta en email marketing con enfoque en creación de campañas de alto rendimiento y tasas de conversión'
      },
      {
        id: 'conversion-expert',
        name: 'Daniel Torres',
        avatar: '/avatars/male-3.png',
        role: 'Experto en Conversión',
        description: 'Especialista en optimización de conversiones y testing A/B para maximizar resultados'
      }
    ],
    tips: [
      'Crea urgencia auténtica con fechas límite claras y contadores de tiempo',
      'Personaliza el contenido según el segmento para aumentar la relevancia',
      'Usa imágenes atractivas del producto y testimonios para reforzar el mensaje',
      'Mantén el diseño móvil-first, ya que la mayoría abrirá desde dispositivos móviles',
      'Realiza tests A/B con diferentes asuntos para optimizar la tasa de apertura'
    ],
    examples: [
      {
        title: 'Email promocional Black Friday',
        description: 'Email con descuentos escalonados según valor de compra y tiempo limitado'
      },
      {
        title: 'Lanzamiento de nuevo producto exclusivo',
        description: 'Email con acceso anticipado para clientes VIP con video de demostración'
      },
      {
        title: 'Reactivación de clientes inactivos',
        description: 'Email con oferta personalizada basada en compras anteriores'
      }
    ]
  },
  
  // Categoría: SEO
  'seo-content': {
    id: 'seo-content',
    title: 'Contenido SEO',
    description: 'Crea contenido optimizado para posicionarse en buscadores con palabras clave estratégicas',
    icon: Search,
    sections: [
      {
        id: 'basics',
        title: 'Información básica',
        description: 'Información esencial sobre el contenido',
        icon: FileText
      },
      {
        id: 'keywords',
        title: 'Estrategia de palabras clave',
        description: 'Palabras clave y estrategia de posicionamiento',
        icon: Tag
      },
      {
        id: 'content',
        title: 'Estructura y contenido',
        description: 'Organización y elementos del contenido',
        icon: ListChecks
      },
      {
        id: 'technical',
        title: 'Aspectos técnicos SEO',
        description: 'Elementos técnicos para optimizar el posicionamiento',
        icon: Settings
      }
    ],
    questions: [
      // Sección: Información básica
      {
        id: 'title',
        type: 'text',
        label: 'Título de la página',
        placeholder: 'Ej. Guía Completa de SEO Local: Estrategias y Mejores Prácticas [2025]',
        helperText: 'Incluye la palabra clave principal cerca del inicio (55-60 caracteres ideal)',
        required: true,
        max: 60,
        section: 'basics'
      },
      {
        id: 'content_type',
        type: 'select',
        label: 'Tipo de contenido',
        options: [
          { value: 'guia', label: 'Guía completa' },
          { value: 'tutorial', label: 'Tutorial paso a paso' },
          { value: 'listado', label: 'Listado (Ej. "10 mejores...")' },
          { value: 'comparativa', label: 'Comparativa' },
          { value: 'review', label: 'Review de producto/servicio' },
          { value: 'problema-solucion', label: 'Problema-solución' },
          { value: 'preguntas-respuestas', label: 'FAQ/Preguntas y respuestas' },
          { value: 'definicion', label: 'Definición o término' },
          { value: 'caso-estudio', label: 'Caso de estudio' }
        ],
        required: true,
        section: 'basics'
      },
      {
        id: 'meta_description',
        type: 'textarea',
        label: 'Meta descripción',
        placeholder: 'Escribe una descripción persuasiva con palabras clave que aparecerá en los resultados de búsqueda',
        helperText: 'Incluye la palabra clave principal y una propuesta de valor clara (150-160 caracteres)',
        required: true,
        max: 160,
        section: 'basics'
      },
      {
        id: 'url_slug',
        type: 'text',
        label: 'Slug de URL recomendado',
        placeholder: 'Ej. guia-seo-local-estrategias',
        helperText: 'URL amigable que incluya palabras clave principales separadas por guiones',
        section: 'basics'
      },
      
      // Sección: Estrategia de palabras clave
      {
        id: 'primary_keyword',
        type: 'text',
        label: 'Palabra clave principal',
        placeholder: 'Ej. seo local para negocios',
        helperText: 'Término principal que quieres posicionar (alto volumen y competencia adecuada)',
        required: true,
        section: 'keywords'
      },
      {
        id: 'secondary_keywords',
        type: 'keywords',
        label: 'Palabras clave secundarias',
        placeholder: 'Ej. posicionamiento local google maps, optimización seo local',
        helperText: 'Términos relacionados para incluir a lo largo del contenido (separados por comas)',
        required: true,
        section: 'keywords'
      },
      {
        id: 'long_tail_keywords',
        type: 'textarea',
        label: 'Palabras clave long tail',
        placeholder: 'Ej. cómo aparecer primero en google maps, cómo optimizar negocio local en google',
        helperText: 'Frases específicas de cola larga con menor competencia (una por línea)',
        section: 'keywords'
      },
      {
        id: 'search_intent',
        type: 'select',
        label: 'Intención de búsqueda principal',
        options: [
          { value: 'informacional', label: 'Informacional (busca información)' },
          { value: 'navegacional', label: 'Navegacional (busca un sitio específico)' },
          { value: 'transaccional', label: 'Transaccional (quiere comprar)' },
          { value: 'comercial', label: 'Comercial-investigativa (evalúa opciones)' }
        ],
        helperText: 'Identifica qué busca el usuario al utilizar estas palabras clave',
        required: true,
        section: 'keywords'
      },
      
      // Sección: Estructura y contenido
      {
        id: 'content_structure',
        type: 'textarea',
        label: 'Estructura de contenido sugerida',
        placeholder: 'Enumera las secciones principales y subsecciones que debería tener el contenido',
        helperText: 'Define los H2, H3 y H4 para organizarlo jerárquicamente',
        required: true,
        section: 'content'
      },
      {
        id: 'questions_to_answer',
        type: 'textarea',
        label: 'Preguntas clave a responder',
        placeholder: 'Ej. ¿Qué es el SEO local?, ¿Cómo optimizar Google My Business?',
        helperText: 'Incluye preguntas específicas que el contenido debería resolver',
        section: 'content'
      },
      {
        id: 'content_length',
        type: 'select',
        label: 'Longitud recomendada',
        options: [
          { value: 'corto', label: 'Corto (800-1000 palabras)' },
          { value: 'medio', label: 'Medio (1000-1500 palabras)' },
          { value: 'largo', label: 'Largo (1500-2500 palabras)' },
          { value: 'extenso', label: 'Extenso (2500+ palabras)' }
        ],
        helperText: 'Basado en el análisis de la competencia para este término',
        required: true,
        section: 'content'
      },
      {
        id: 'content_elements',
        type: 'checkbox',
        label: 'Elementos a incluir',
        options: [
          { value: 'toc', label: 'Tabla de contenidos' },
          { value: 'imagenes', label: 'Imágenes optimizadas' },
          { value: 'video', label: 'Video embebido' },
          { value: 'infografia', label: 'Infografía' },
          { value: 'tablas', label: 'Tablas comparativas' },
          { value: 'citas', label: 'Citas de fuentes autorizadas' },
          { value: 'estadisticas', label: 'Estadísticas actualizadas' },
          { value: 'faq', label: 'Sección de FAQ estructurada' },
          { value: 'cta', label: 'Call-to-Action específico' }
        ],
        helperText: 'Elementos que mejorarán el contenido y su posicionamiento',
        section: 'content'
      },
      {
        id: 'tone',
        type: 'tone',
        label: 'Tono de voz',
        helperText: 'Selecciona el tono que debe tener el contenido',
        required: true,
        section: 'content'
      },
      
      // Sección: Aspectos técnicos SEO
      {
        id: 'internal_linking',
        type: 'textarea',
        label: 'Estrategia de enlaces internos',
        placeholder: 'Sugiere páginas del sitio a las que enlazar desde este contenido',
        helperText: 'Incluye el texto ancla recomendado para cada enlace',
        section: 'technical'
      },
      {
        id: 'external_linking',
        type: 'textarea',
        label: 'Enlaces externos recomendados',
        placeholder: 'Sugiere fuentes autorizadas externas para citar',
        helperText: 'Enlaces a sitios relevantes y con autoridad mejoran la credibilidad',
        section: 'technical'
      },
      {
        id: 'schema_markup',
        type: 'select',
        label: 'Schema Markup recomendado',
        options: [
          { value: 'article', label: 'Article' },
          { value: 'howto', label: 'HowTo' },
          { value: 'faq', label: 'FAQ' },
          { value: 'product', label: 'Product' },
          { value: 'review', label: 'Review' },
          { value: 'service', label: 'Service' },
          { value: 'localBusiness', label: 'LocalBusiness' },
          { value: 'video', label: 'VideoObject' }
        ],
        helperText: 'Markup para datos estructurados que mejoran los resultados enriquecidos',
        section: 'technical'
      },
      {
        id: 'additional_recommendations',
        type: 'textarea',
        label: 'Recomendaciones adicionales',
        placeholder: 'Cualquier otra información técnica específica para este contenido',
        section: 'technical'
      }
    ],
    agents: [
      {
        id: 'seo-analyst',
        name: 'Javier Rodríguez',
        avatar: '/avatars/male-4.png',
        role: 'Analista SEO Senior',
        description: 'Especialista en investigación de palabras clave y estrategias de posicionamiento orgánico'
      },
      {
        id: 'content-strategist',
        name: 'Lucía Fernández',
        avatar: '/avatars/female-3.png',
        role: 'Estratega de Contenidos SEO',
        description: 'Experta en creación de contenido orientado al posicionamiento y satisfacción de intención de búsqueda'
      }
    ],
    tips: [
      'Utiliza la palabra clave principal en el primer párrafo y de forma natural a lo largo del texto',
      'Crea títulos y H2 que incluyan palabras clave pero que sean atractivos para el lector',
      'Optimiza las imágenes con nombres de archivo descriptivos y atributos alt relevantes',
      'Incluye enlaces internos a contenido relacionado para mejorar la estructura del sitio',
      'Actualiza regularmente el contenido posicionado para mantener su relevancia'
    ],
    examples: [
      {
        title: 'Guía Completa de SEO On-Page: Factores Clave para Posicionar en 2025',
        description: 'Guía extensa con ejemplos prácticos, checklist descargable y casos de estudio'
      },
      {
        title: '10 Estrategias de Link Building que Funcionan en 2025 (Con Ejemplos)',
        description: 'Artículo con ejemplos reales, plantillas de outreach y métricas de éxito'
      },
      {
        title: '¿Qué es el Core Web Vitals y Cómo Afecta tu SEO? Guía Completa',
        description: 'Contenido técnico con instrucciones paso a paso para mejorar métricas'
      }
    ]
  },
  
  // Categoría: Comunicación Corporativa
  'press-release': {
    id: 'press-release',
    title: 'Comunicado de Prensa',
    description: 'Crea anuncios formales para medios de comunicación sobre novedades relevantes de tu organización',
    icon: Megaphone,
    sections: [
      {
        id: 'basics',
        title: 'Información básica',
        description: 'Datos esenciales del comunicado',
        icon: FileText
      },
      {
        id: 'content',
        title: 'Contenido del comunicado',
        description: 'Detalles específicos para el cuerpo del comunicado',
        icon: ListChecks
      },
      {
        id: 'company',
        title: 'Información de la empresa',
        description: 'Datos sobre la organización emisora',
        icon: Briefcase
      },
      {
        id: 'distribution',
        title: 'Distribución',
        description: 'Aspectos sobre la difusión del comunicado',
        icon: Globe
      }
    ],
    questions: [
      // Sección: Información básica
      {
        id: 'headline',
        type: 'text',
        label: 'Titular del comunicado',
        placeholder: 'Ej. [Empresa] anuncia el lanzamiento de [producto] que revolucionará [industria]',
        helperText: 'Debe ser claro, conciso y captar la esencia de la noticia',
        required: true,
        section: 'basics'
      },
      {
        id: 'subheadline',
        type: 'text',
        label: 'Subtítulo (opcional)',
        placeholder: 'Un subtítulo que complemente o amplíe la información del titular',
        helperText: 'Añade contexto o un dato clave que refuerce el titular',
        section: 'basics'
      },
      {
        id: 'release_date',
        type: 'date',
        label: 'Fecha de publicación',
        helperText: 'Fecha en que el comunicado debe ser publicado',
        required: true,
        section: 'basics'
      },
      {
        id: 'location',
        type: 'text',
        label: 'Ubicación',
        placeholder: 'Ej. Madrid, España',
        helperText: 'Ciudad y país desde donde se emite el comunicado',
        required: true,
        section: 'basics'
      },
      {
        id: 'type',
        type: 'select',
        label: 'Tipo de comunicado',
        options: [
          { value: 'lanzamiento', label: 'Lanzamiento de producto/servicio' },
          { value: 'evento', label: 'Anuncio de evento' },
          { value: 'partnership', label: 'Asociación/Partnership' },
          { value: 'crecimiento', label: 'Expansión/Crecimiento empresarial' },
          { value: 'ejecutivo', label: 'Cambio ejecutivo/organizacional' },
          { value: 'premio', label: 'Premio/Reconocimiento' },
          { value: 'financiero', label: 'Resultados financieros' },
          { value: 'rse', label: 'Responsabilidad Social Empresarial' },
          { value: 'investigacion', label: 'Estudio/Investigación' },
          { value: 'crisis', label: 'Gestión de crisis/Respuesta' }
        ],
        section: 'basics'
      },
      
      // Sección: Contenido del comunicado
      {
        id: 'news_summary',
        type: 'textarea',
        label: 'Resumen de la noticia',
        placeholder: 'Resume en 2-3 frases la esencia de la noticia (quién, qué, cuándo, dónde, por qué)',
        helperText: 'Este será el primer párrafo que capture la información esencial',
        required: true,
        section: 'content'
      },
      {
        id: 'key_messages',
        type: 'textarea',
        label: 'Mensajes clave',
        placeholder: 'Enumera los 3-5 mensajes principales que quieres transmitir',
        helperText: 'Puntos que deben ser incluidos en el comunicado sin falta',
        required: true,
        section: 'content'
      },
      {
        id: 'quotes',
        type: 'textarea',
        label: 'Citas de portavoces',
        placeholder: 'Ej. "Estamos entusiasmados con este lanzamiento que representa un avance significativo..." - [Nombre], [Cargo]',
        helperText: 'Incluye al menos una cita de un directivo relevante (nombre y cargo)',
        section: 'content'
      },
      {
        id: 'background',
        type: 'textarea',
        label: 'Contexto/Antecedentes',
        placeholder: 'Información de fondo que ayude a entender la importancia de la noticia',
        helperText: 'Historia o datos relevantes que enmarcan la noticia',
        section: 'content'
      },
      {
        id: 'data_statistics',
        type: 'textarea',
        label: 'Datos y estadísticas',
        placeholder: 'Cifras, porcentajes o estadísticas que respalden la noticia',
        helperText: 'Información cuantitativa que aporte credibilidad',
        section: 'content'
      },
      {
        id: 'tone',
        type: 'select',
        label: 'Tono del comunicado',
        options: [
          { value: 'formal', label: 'Formal y corporativo' },
          { value: 'informativo', label: 'Informativo y neutral' },
          { value: 'entusiasta', label: 'Entusiasta y positivo' },
          { value: 'tecnico', label: 'Técnico y detallado' },
          { value: 'innovador', label: 'Innovador y visionario' }
        ],
        required: true,
        section: 'content'
      },
      
      // Sección: Información de la empresa
      {
        id: 'company_description',
        type: 'textarea',
        label: 'Descripción de la empresa',
        placeholder: 'Párrafo estándar que describe la empresa (boilerplate)',
        helperText: 'Breve descripción que se incluirá al final del comunicado',
        required: true,
        section: 'company'
      },
      {
        id: 'contact_person',
        type: 'text',
        label: 'Persona de contacto',
        placeholder: 'Nombre y apellido de la persona de contacto para prensa',
        section: 'company'
      },
      {
        id: 'contact_details',
        type: 'text',
        label: 'Datos de contacto',
        placeholder: 'Email y teléfono de contacto para prensa',
        section: 'company'
      },
      {
        id: 'website',
        type: 'url',
        label: 'Sitio web',
        placeholder: 'URL del sitio web corporativo',
        section: 'company'
      },
      {
        id: 'social_media',
        type: 'textarea',
        label: 'Perfiles de redes sociales',
        placeholder: 'Enlaces a los perfiles sociales relevantes de la empresa',
        section: 'company'
      },
      
      // Sección: Distribución
      {
        id: 'target_media',
        type: 'checkbox',
        label: 'Medios objetivo',
        options: [
          { value: 'generalistas', label: 'Medios generalistas' },
          { value: 'economicos', label: 'Medios económicos/negocios' },
          { value: 'sectoriales', label: 'Medios sectoriales/especializados' },
          { value: 'tecnologicos', label: 'Medios tecnológicos' },
          { value: 'locales', label: 'Medios locales/regionales' },
          { value: 'blogs', label: 'Blogs influyentes' },
          { value: 'internacional', label: 'Medios internacionales' }
        ],
        helperText: 'Tipos de medios a los que dirigir el comunicado',
        section: 'distribution'
      },
      {
        id: 'geographical_scope',
        type: 'select',
        label: 'Alcance geográfico',
        options: [
          { value: 'local', label: 'Local' },
          { value: 'regional', label: 'Regional' },
          { value: 'nacional', label: 'Nacional' },
          { value: 'internacional', label: 'Internacional' },
          { value: 'global', label: 'Global' }
        ],
        section: 'distribution'
      },
      {
        id: 'embargo',
        type: 'select',
        label: '¿Embargo?',
        options: [
          { value: 'no', label: 'No, para publicación inmediata' },
          { value: 'si', label: 'Sí, con fecha de embargo' }
        ],
        helperText: 'Indica si el comunicado tiene restricción de publicación',
        section: 'distribution'
      },
      {
        id: 'embargo_details',
        type: 'textarea',
        label: 'Detalles del embargo',
        placeholder: 'Fecha y hora exacta en que se levanta el embargo',
        section: 'distribution'
      },
      {
        id: 'additional_materials',
        type: 'checkbox',
        label: 'Materiales adicionales',
        options: [
          { value: 'imagenes', label: 'Imágenes' },
          { value: 'video', label: 'Video' },
          { value: 'infografia', label: 'Infografía' },
          { value: 'documentacion', label: 'Documentación técnica' },
          { value: 'factsheet', label: 'Fact sheet' }
        ],
        helperText: 'Materiales que acompañarán al comunicado',
        section: 'distribution'
      }
    ],
    agents: [
      {
        id: 'pr-specialist',
        name: 'Elena Morales',
        avatar: '/avatars/female-4.png',
        role: 'Especialista en Relaciones Públicas',
        description: 'Experta en comunicados de prensa y relaciones con medios con 10+ años en comunicación corporativa'
      },
      {
        id: 'corporate-comms',
        name: 'Ricardo Blanco',
        avatar: '/avatars/male-5.png',
        role: 'Director de Comunicación Corporativa',
        description: 'Especialista en gestión de mensajes corporativos y comunicación estratégica para diferentes audiencias'
      }
    ],
    tips: [
      'Estructura el comunicado con la información más importante al principio (pirámide invertida)',
      'Redacta un titular impactante pero informativo, evitando jerga corporativa excesiva',
      'Incluye al menos una cita directa de un portavoz relevante de la empresa',
      'Mantén el comunicado conciso (1-2 páginas) y organizado en párrafos cortos',
      'Asegúrate de incluir todos los datos de contacto necesarios para que los periodistas puedan ampliar información'
    ],
    examples: [
      {
        title: 'Empresa X anuncia una inversión de 20 millones de euros en nueva planta sostenible',
        description: 'Comunicado corporativo sobre expansión con enfoque en sostenibilidad'
      },
      {
        title: 'Compañía Y nombra a María Rodríguez como nueva CEO para liderar expansión internacional',
        description: 'Comunicado sobre cambio de liderazgo con biografía y visión estratégica'
      },
      {
        title: 'Firma Z lanza tecnología revolucionaria que reduce el consumo energético un 40%',
        description: 'Comunicado de lanzamiento con datos técnicos, beneficios y testimonios de clientes beta'
      }
    ]
  },
  
  // Categoría: Contenido Audiovisual
  'video-script': {
    id: 'video-script',
    title: 'Guion para Video',
    description: 'Crea guiones estructurados para videos corporativos, tutoriales, anuncios y más',
    icon: Video,
    sections: [
      {
        id: 'basics',
        title: 'Información básica',
        description: 'Datos esenciales sobre el video',
        icon: FileText
      },
      {
        id: 'audience',
        title: 'Audiencia y objetivos',
        description: 'A quién va dirigido y qué quieres lograr',
        icon: Users
      },
      {
        id: 'structure',
        title: 'Estructura y formato',
        description: 'Organización y elementos del guion',
        icon: ListChecks
      },
      {
        id: 'technical',
        title: 'Aspectos técnicos',
        description: 'Consideraciones técnicas para la producción',
        icon: Settings
      }
    ],
    questions: [
      // Sección: Información básica
      {
        id: 'title',
        type: 'text',
        label: 'Título del video',
        placeholder: 'Ej. Cómo maximizar tus conversiones con email marketing',
        helperText: 'Título principal que describe el contenido del video',
        required: true,
        section: 'basics'
      },
      {
        id: 'video_type',
        type: 'select',
        label: 'Tipo de video',
        options: [
          { value: 'tutorial', label: 'Tutorial/How-to' },
          { value: 'explicativo', label: 'Video explicativo' },
          { value: 'corporativo', label: 'Corporativo/Institucional' },
          { value: 'producto', label: 'Demostración de producto' },
          { value: 'testimonial', label: 'Testimonial/Caso de éxito' },
          { value: 'entrevista', label: 'Entrevista' },
          { value: 'anuncio', label: 'Anuncio publicitario' },
          { value: 'social', label: 'Contenido para redes sociales' },
          { value: 'webinar', label: 'Webinar/Presentación' }
        ],
        required: true,
        section: 'basics'
      },
      {
        id: 'duration',
        type: 'select',
        label: 'Duración estimada',
        options: [
          { value: 'corto', label: 'Corto (menos de 1 minuto)' },
          { value: 'medio-corto', label: 'Medio-corto (1-3 minutos)' },
          { value: 'medio', label: 'Medio (3-5 minutos)' },
          { value: 'largo', label: 'Largo (5-10 minutos)' },
          { value: 'extendido', label: 'Extendido (más de 10 minutos)' }
        ],
        required: true,
        section: 'basics'
      },
      {
        id: 'brief_description',
        type: 'textarea',
        label: 'Descripción breve',
        placeholder: 'Resume en 2-3 frases el contenido principal del video',
        helperText: 'Servirá para la descripción en plataformas como YouTube',
        required: true,
        section: 'basics'
      },
      
      // Sección: Audiencia y objetivos
      {
        id: 'audience',
        type: 'audience',
        label: 'Audiencia objetivo',
        placeholder: 'Describe a quién va dirigido este video (edad, intereses, nivel de conocimiento, etc.)',
        helperText: 'Cuanto más específico seas, mejor se podrá adaptar el guion',
        required: true,
        section: 'audience'
      },
      {
        id: 'knowledge_level',
        type: 'select',
        label: 'Nivel de conocimiento de la audiencia',
        options: [
          { value: 'principiante', label: 'Principiante (sin conocimientos previos)' },
          { value: 'intermedio', label: 'Intermedio (conocimientos básicos)' },
          { value: 'avanzado', label: 'Avanzado (conocimientos sólidos)' },
          { value: 'experto', label: 'Experto (alto nivel de especialización)' },
          { value: 'mixto', label: 'Mixto (diferentes niveles)' }
        ],
        section: 'audience'
      },
      {
        id: 'goal',
        type: 'select',
        label: 'Objetivo principal',
        options: [
          { value: 'educar', label: 'Educar/enseñar' },
          { value: 'informar', label: 'Informar' },
          { value: 'entretener', label: 'Entretener' },
          { value: 'inspirar', label: 'Inspirar/motivar' },
          { value: 'vender', label: 'Vender producto/servicio' },
          { value: 'marca', label: 'Construir marca' },
          { value: 'engagement', label: 'Generar engagement' },
          { value: 'leads', label: 'Captar leads' }
        ],
        required: true,
        section: 'audience'
      },
      {
        id: 'call_to_action',
        type: 'text',
        label: 'Call-to-Action (CTA)',
        placeholder: 'Ej. Suscríbete al canal, Descarga nuestra guía, Agenda una demo',
        helperText: 'Acción clara que quieres que realice el espectador tras ver el video',
        section: 'audience'
      },
      {
        id: 'tone',
        type: 'tone',
        label: 'Tono de voz',
        helperText: 'Selecciona el tono que debe tener el guion',
        required: true,
        section: 'audience'
      },
      
      // Sección: Estructura y formato
      {
        id: 'key_points',
        type: 'textarea',
        label: 'Puntos clave a cubrir',
        placeholder: 'Enumera los puntos o temas principales que debe abordar el video',
        helperText: 'Lista los mensajes esenciales que no pueden faltar',
        required: true,
        section: 'structure'
      },
      {
        id: 'structure_preference',
        type: 'select',
        label: 'Estructura preferida',
        options: [
          { value: 'problema-solucion', label: 'Problema-Solución' },
          { value: 'cronologica', label: 'Cronológica (paso a paso)' },
          { value: 'narrativa', label: 'Narrativa/Storytelling' },
          { value: 'comparativa', label: 'Comparativa' },
          { value: 'qya', label: 'Preguntas y respuestas' },
          { value: 'entrevista', label: 'Formato entrevista' },
          { value: 'presentacion', label: 'Presentación tradicional' }
        ],
        section: 'structure'
      },
      {
        id: 'script_format',
        type: 'select',
        label: 'Formato de guion',
        options: [
          { value: 'completo', label: 'Guion completo (diálogos y direcciones)' },
          { value: 'dialogo', label: 'Solo diálogos/narración' },
          { value: 'dos-columnas', label: 'Formato dos columnas (audio/visual)' },
          { value: 'esquema', label: 'Esquema/Outline con puntos clave' }
        ],
        helperText: 'Formato en que prefieres recibir el guion',
        required: true,
        section: 'structure'
      },
      {
        id: 'intro_notes',
        type: 'textarea',
        label: 'Notas para la introducción',
        placeholder: 'Cómo quieres que empiece el video, enfoque del gancho inicial...',
        helperText: 'Los primeros 10-15 segundos son cruciales para retener al espectador',
        section: 'structure'
      },
      {
        id: 'closing_notes',
        type: 'textarea',
        label: 'Notas para el cierre',
        placeholder: 'Cómo quieres que termine el video, mensaje final, etc.',
        helperText: 'Un buen cierre refuerza el mensaje y motiva a la acción',
        section: 'structure'
      },
      
      // Sección: Aspectos técnicos
      {
        id: 'presenters',
        type: 'select',
        label: 'Presentadores/Narradores',
        options: [
          { value: 'narrador', label: 'Narrador en off (sin aparecer en cámara)' },
          { value: 'presentador', label: 'Presentador frente a cámara' },
          { value: 'multiple', label: 'Múltiples presentadores/entrevista' },
          { value: 'sin-narrador', label: 'Sin narrador (solo texto y gráficos)' }
        ],
        section: 'technical'
      },
      {
        id: 'visual_elements',
        type: 'checkbox',
        label: 'Elementos visuales a incluir',
        options: [
          { value: 'pantalla', label: 'Capturas de pantalla' },
          { value: 'graficos', label: 'Gráficos/infografías' },
          { value: 'animacion', label: 'Animaciones' },
          { value: 'texto', label: 'Texto en pantalla' },
          { value: 'producto', label: 'Demostración de producto' },
          { value: 'broll', label: 'B-roll (imágenes de apoyo)' },
          { value: 'entrevistas', label: 'Clips de entrevistas' }
        ],
        helperText: 'Elementos que deben considerarse en el guion',
        section: 'technical'
      },
      {
        id: 'graphics_text',
        type: 'textarea',
        label: 'Textos/gráficos en pantalla',
        placeholder: 'Describe textos, títulos o gráficos que deben aparecer',
        helperText: 'Elementos textuales importantes para reforzar el mensaje',
        section: 'technical'
      },
      {
        id: 'music_mood',
        type: 'text',
        label: 'Ambiente musical',
        placeholder: 'Ej. Energético y moderno, Corporativo y profesional, Emotivo',
        helperText: 'Describe el estilo o ambiente musical recomendado',
        section: 'technical'
      },
      {
        id: 'special_notes',
        type: 'textarea',
        label: 'Notas especiales',
        placeholder: 'Cualquier consideración adicional para la producción del video',
        section: 'technical'
      }
    ],
    agents: [
      {
        id: 'video-scriptwriter',
        name: 'Pablo Herrera',
        avatar: '/avatars/male-6.png',
        role: 'Guionista Audiovisual',
        description: 'Especialista en guiones para videos corporativos y publicitarios con enfoque en mensajes claros y efectivos'
      },
      {
        id: 'content-director',
        name: 'Marta López',
        avatar: '/avatars/female-5.png',
        role: 'Directora de Contenido',
        description: 'Experta en narrativa visual y estructura de contenidos para diferentes formatos y plataformas'
      }
    ],
    tips: [
      'Comienza con un gancho potente en los primeros 5-10 segundos para captar la atención',
      'Mantén un ritmo ágil: frases cortas y directas funcionan mejor en video',
      'Incluye pausas naturales en el guion para permitir transiciones visuales',
      'Escribe como hablas: un estilo conversacional es más efectivo que uno formal',
      'Visualiza cada escena mientras escribes para asegurar coherencia entre palabras e imágenes'
    ],
    examples: [
      {
        title: 'Tutorial: Cómo configurar tu primera campaña de email marketing en 10 minutos',
        description: 'Guion paso a paso con capturas de pantalla y consejos prácticos'
      },
      {
        title: 'Video corporativo: Nuestra misión de transformar la industria logística',
        description: 'Guion narrativo que combina historia de la empresa con visión de futuro'
      },
      {
        title: 'Anuncio: Nueva aplicación de gestión financiera personal',
        description: 'Guion de 30 segundos que muestra problema-solución-beneficios'
      }
    ]
  },
  
  // Comunicaciones Corporativas (ya existente)
  'corporate-comms': {
    id: 'corporate-comms',
    title: 'Comunicaciones Corporativas',
    description: 'Crea mensajes formales y efectivos para comunicar información corporativa a stakeholders clave',
    icon: Briefcase,
    sections: [
      {
        id: 'basics',
        title: 'Información básica',
        description: 'Información esencial sobre la comunicación',
        icon: FileText
      },
      {
        id: 'audience',
        title: 'Audiencia y propósito',
        description: 'Define a quién te diriges y con qué objetivo',
        icon: Users
      },
      {
        id: 'content',
        title: 'Contenido del mensaje',
        description: 'Detalles sobre qué comunicar y cómo',
        icon: MessageSquare
      },
      {
        id: 'structure',
        title: 'Estructura y estilo',
        description: 'Organización y tono del mensaje',
        icon: ListChecks
      }
    ],
    questions: [
      // Sección: Información básica
      {
        id: 'title',
        type: 'text',
        label: '¿Cuál es el título de la comunicación?',
        placeholder: 'Ej. Actualización de políticas internas - Q2 2025',
        required: true,
        section: 'basics'
      },
      {
        id: 'topic',
        type: 'select',
        label: 'Tema principal',
        options: [
          { value: 'cambio-organizacional', label: 'Cambio organizacional' },
          { value: 'actualizacion-politicas', label: 'Actualización de políticas' },
          { value: 'resultados-financieros', label: 'Resultados financieros' },
          { value: 'evento-corporativo', label: 'Evento corporativo' },
          { value: 'cambio-liderazgo', label: 'Cambio en el liderazgo' },
          { value: 'iniciativa-rse', label: 'Iniciativa de RSE/Sostenibilidad' },
          { value: 'crisis', label: 'Gestión de crisis' },
          { value: 'logro', label: 'Logro o reconocimiento' },
          { value: 'otro', label: 'Otro' }
        ],
        required: true,
        section: 'basics'
      },
      {
        id: 'other_topic',
        type: 'text',
        label: 'Especificar otro tema',
        placeholder: 'Describe el tema si seleccionaste "Otro"',
        section: 'basics'
      },
      {
        id: 'urgency',
        type: 'select',
        label: 'Nivel de urgencia',
        options: [
          { value: 'baja', label: 'Baja - Informativo' },
          { value: 'media', label: 'Media - Requiere atención' },
          { value: 'alta', label: 'Alta - Acción necesaria' },
          { value: 'critica', label: 'Crítica - Inmediata' }
        ],
        section: 'basics'
      },
      
      // Sección: Audiencia y propósito
      {
        id: 'audience',
        type: 'select',
        label: 'Audiencia principal',
        options: [
          { value: 'todos-empleados', label: 'Todos los empleados' },
          { value: 'directivos', label: 'Equipo directivo' },
          { value: 'departamento', label: 'Departamento específico' },
          { value: 'accionistas', label: 'Accionistas/Inversores' },
          { value: 'clientes', label: 'Clientes' },
          { value: 'proveedores', label: 'Proveedores/Partners' },
          { value: 'medios', label: 'Medios de comunicación' },
          { value: 'reguladores', label: 'Reguladores/Autoridades' },
          { value: 'publico', label: 'Público general' },
          { value: 'mixto', label: 'Audiencia mixta' }
        ],
        required: true,
        section: 'audience'
      },
      {
        id: 'specific_audience',
        type: 'text',
        label: 'Detalles específicos de audiencia',
        placeholder: 'Si es un departamento o grupo específico, especifícalo aquí',
        section: 'audience'
      },
      {
        id: 'purpose',
        type: 'select',
        label: 'Propósito principal',
        options: [
          { value: 'informar', label: 'Informar' },
          { value: 'anunciar', label: 'Anunciar cambio/novedad' },
          { value: 'instruir', label: 'Dar instrucciones' },
          { value: 'motivar', label: 'Motivar/Inspirar' },
          { value: 'aclarar', label: 'Aclarar situación' },
          { value: 'solicitar', label: 'Solicitar acción' },
          { value: 'responder', label: 'Responder a situación' },
          { value: 'agradecer', label: 'Agradecer/Reconocer' }
        ],
        required: true,
        section: 'audience'
      },
      {
        id: 'expected_response',
        type: 'text',
        label: 'Respuesta esperada',
        placeholder: 'Qué quieres que haga la audiencia después de leer el mensaje',
        section: 'audience'
      },
      
      // Sección: Contenido del mensaje
      {
        id: 'key_messages',
        type: 'textarea',
        label: 'Mensajes clave',
        placeholder: 'Enumera los 3-5 mensajes principales que debe incluir la comunicación',
        required: true,
        section: 'content'
      },
      {
        id: 'context',
        type: 'textarea',
        label: 'Contexto',
        placeholder: 'Información de fondo relevante para entender el mensaje',
        section: 'content'
      },
      {
        id: 'data_include',
        type: 'textarea',
        label: 'Datos a incluir',
        placeholder: 'Cifras, fechas o datos específicos que deben mencionarse',
        section: 'content'
      },
      {
        id: 'sensitive_aspects',
        type: 'textarea',
        label: 'Aspectos sensibles',
        placeholder: 'Temas delicados que requieren un tratamiento especial',
        section: 'content'
      },
      {
        id: 'next_steps',
        type: 'textarea',
        label: 'Próximos pasos',
        placeholder: 'Acciones futuras que se tomarán o que deben tomarse',
        section: 'content'
      },
      
      // Sección: Estructura y estilo
      {
        id: 'tone',
        type: 'select',
        label: 'Tono de voz',
        options: [
          { value: 'formal', label: 'Formal y corporativo' },
          { value: 'profesional', label: 'Profesional pero cercano' },
          { value: 'directo', label: 'Directo y conciso' },
          { value: 'inspirador', label: 'Inspirador y motivador' },
          { value: 'empatico', label: 'Empático y comprensivo' },
          { value: 'asertivo', label: 'Asertivo y firme' }
        ],
        required: true,
        section: 'structure'
      },
      {
        id: 'length',
        type: 'select',
        label: 'Extensión preferida',
        options: [
          { value: 'breve', label: 'Breve (menos de 300 palabras)' },
          { value: 'media', label: 'Media (300-600 palabras)' },
          { value: 'extensa', label: 'Extensa (más de 600 palabras)' }
        ],
        section: 'structure'
      },
      {
        id: 'format',
        type: 'checkbox',
        label: 'Elementos a incluir',
        options: [
          { value: 'introduccion', label: 'Introducción contextual' },
          { value: 'bullets', label: 'Lista de puntos clave' },
          { value: 'citas', label: 'Citas de directivos' },
          { value: 'faq', label: 'Preguntas frecuentes' },
          { value: 'conclusiones', label: 'Conclusiones/Resumen' },
          { value: 'contacto', label: 'Información de contacto' },
          { value: 'recursos', label: 'Enlaces a recursos' }
        ],
        section: 'structure'
      },
      {
        id: 'branding_elements',
        type: 'text',
        label: 'Elementos de marca',
        placeholder: 'Eslogan, valores o mensajes de marca a incorporar',
        section: 'structure'
      },
      {
        id: 'signature',
        type: 'text',
        label: 'Firma/Remitente',
        placeholder: 'Quién firma la comunicación (nombre y cargo)',
        required: true,
        section: 'structure'
      }
    ],
    agents: [
      {
        id: 'corporate-writer',
        name: 'Ana García',
        avatar: '/avatars/female-1.png',
        role: 'Especialista en Comunicación Corporativa',
        description: 'Experta en redacción corporativa con enfoque en mensajes claros y alineados con los valores de marca'
      },
      {
        id: 'stakeholder-expert',
        name: 'Javier Méndez',
        avatar: '/avatars/male-1.png',
        role: 'Asesor de Relaciones con Stakeholders',
        description: 'Especialista en adaptar mensajes corporativos para diferentes audiencias y contextos organizacionales'
      }
    ],
    tips: [
      'Comienza con un resumen ejecutivo que capture la esencia del mensaje en 2-3 frases',
      'Utiliza párrafos cortos y un lenguaje claro, evitando jerga innecesaria',
      'Anticipa posibles preguntas y abórdalas directamente en el mensaje',
      'Contextualiza los cambios o anuncios explicando el "por qué" antes del "qué"',
      'Incluye siempre próximos pasos claros y canales de contacto para dudas'
    ],
    examples: [
      {
        title: 'Anuncio de restructuración departamental',
        description: 'Comunicado interno sobre cambios organizativos con cronograma de implementación'
      },
      {
        title: 'Actualización de política de trabajo flexible',
        description: 'Comunicación sobre nuevas modalidades de trabajo con FAQ y recursos de apoyo'
      },
      {
        title: 'Comunicado de crisis: Respuesta a incidente de seguridad',
        description: 'Mensaje transparente explicando la situación, medidas tomadas y próximos pasos'
      }
    ]
  }
};

export default contentFormData;
