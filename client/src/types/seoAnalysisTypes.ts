/**
 * TypeScript types for SEO Analysis database integration
 * Following the established patterns from design_analyses and headline_analyses
 */

// Base SEO Analysis interface for database storage
export interface SEOAnalysis {
  id: string
  created_at: string
  updated_at: string
  
  // User identification
  user_id: string
  
  // Analysis input parameters
  url: string
  analysis_mode: 'page' | 'website'
  
  // Tool metadata
  tool_type: string
  analysis_version: string
  
  // Analysis results
  overall_score: number
  basic_info: SEOBasicInfo
  content_analysis: SEOContentAnalysis
  seo_checks: SEOChecks
  recommendations: SEORecommendation[]
  achievements: Achievement[]
  open_graph: SEOOpenGraph
  twitter_card: SEOTwitterCard
  preview_data: SEOPreviewData
  performance_metrics?: PerformanceMetrics
  
  // Analysis metadata
  analysis_duration_ms?: number
  status: 'processing' | 'completed' | 'failed'
  error_message?: string
  ai_enhanced: boolean
  
  // Organization and favorites
  is_favorite: boolean
  custom_name?: string
  tags: string[]
  notes?: string
  
  // Usage statistics
  view_count: number
  last_viewed_at?: string
  regeneration_count: number
}

// Data for creating a new SEO analysis
export interface CreateSEOAnalysisData {
  user_id: string
  url: string
  analysis_mode: 'page' | 'website'
  tool_type?: string
  analysis_version?: string
  overall_score: number
  basic_info: SEOBasicInfo
  content_analysis: SEOContentAnalysis
  seo_checks: SEOChecks
  recommendations: SEORecommendation[]
  achievements?: Achievement[]
  open_graph?: SEOOpenGraph
  twitter_card?: SEOTwitterCard
  preview_data?: SEOPreviewData
  performance_metrics?: PerformanceMetrics
  analysis_duration_ms?: number
  status?: 'processing' | 'completed' | 'failed'
  error_message?: string
  ai_enhanced?: boolean
  custom_name?: string
  tags?: string[]
  notes?: string
}

// Data for updating an existing SEO analysis
export interface UpdateSEOAnalysisData {
  id: string
  is_favorite?: boolean
  custom_name?: string
  tags?: string[]
  notes?: string
  view_count?: number
  last_viewed_at?: string
  regeneration_count?: number
}

// Re-export types from the existing SEO analyzer types
export type {
  SEOBasicInfo,
  SEOContentAnalysis,
  SEOChecks,
  SEORecommendation,
  Achievement,
  SEOOpenGraph,
  SEOTwitterCard,
  SEOPreviewData,
  PerformanceMetrics,
  SEOAnalysisResult,
  SEOProgressData,
  AnalysisMode
} from '@/components/tools/seo-analyzer/types/seo'

// Service response types
export interface SEOAnalysisServiceResponse {
  data: SEOAnalysis[]
  total: number
  hasMore: boolean
}

export interface SEOAnalysisStats {
  totalAnalyses: number
  favoriteAnalyses: number
  averageScore: number
  analysisTypeBreakdown: Record<string, number>
  recentActivity: number
}
