import React, { useState, useEffect } from "react";
import { LanguageProvider } from "@/contexts/LanguageContext";
import Header from "../components/soluciones-negocio/Header";
import Hero from "../components/soluciones-negocio/Hero";
import BusinessSolutions from "../components/soluciones-negocio/BusinessSolutions";
import ComparisonSection from "../components/soluciones-negocio/ComparisonSection";
import AgentCapabilities from "../components/soluciones-negocio/AgentCapabilities";
import CTASection from "../components/soluciones-negocio/CTASection";
import Footer from "../components/soluciones-negocio/Footer";

const SolucionesNegocioContent: React.FC<{ isScrolled: boolean }> = ({ isScrolled }) => {
  return (
    <div className="min-h-screen bg-white">
      <Header isScrolled={isScrolled} />
      <Hero />
      <BusinessSolutions />
      <ComparisonSection />
      <AgentCapabilities />
      <CTASection />
      <Footer />
    </div>
  );
};

const SolucionesNegocio: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  // Manejar el scroll para cambiar el estilo del header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <LanguageProvider>
      <SolucionesNegocioContent isScrolled={isScrolled} />
    </LanguageProvider>
  );
};

export default SolucionesNegocio;
