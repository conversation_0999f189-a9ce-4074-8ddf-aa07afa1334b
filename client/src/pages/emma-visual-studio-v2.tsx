import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import DashboardLayoutWrapper from "@/components/layout/dashboard-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import EmmaVisualEditor from "@/components/visual-studio/EmmaVisualEditor";
import EmmaAssistantService, {
  ChatMessage as ApiChatMessage,
} from "@/services/emma-assistant-service";

// Importamos la imagen de Emma
import EmmaProfile from "@/assets/emma-profile.png";

import {
  Search,
  ArrowLeft,
  MessageCircle,
  Image as ImageIcon,
  Share2,
  Download,
  PenTool,
  Type,
  Wand2,
  Grid,
  Send,
  Plus,
  ChevronRight,
  ChevronDown,
  Heart,
  MessageSquare,
  Layers,
  Facebook,
  Instagram,
  Twitter,
  Linkedin,
  Mail,
  FileText,
  XCircle,
  Settings,
  GalleryHorizontalEnd,
  ArrowRight,
  Edit3,
  Square,
  Star as SparkleIcon,
  Italic,
  CalendarDays,
  Video,
} from "lucide-react";

// Función para el componente TikTok icon que no existe en lucide
const TiktokIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z" />
    <path d="M16 8v8a5 5 0 0 1-5 5v0a5 5 0 0 1-5-5v0-8" />
    <path d="M19 12V5c-1 0-4-1-7 0" />
  </svg>
);

// Tipos para los datos
type Platform =
  | "instagram"
  | "facebook"
  | "twitter"
  | "linkedin"
  | "tiktok"
  | "email"
  | "blog";
type ContentType =
  | "post"
  | "story"
  | "reel"
  | "carousel"
  | "profile"
  | "banner"
  | "email"
  | "blog"
  | "autonomous";



interface DesignProject {
  id: string;
  title: string;
  platform: Platform;
  type: ContentType;
  image: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChatMessage {
  id: string;
  sender: "user" | "assistant";
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

// Datos de prueba

const recentProjects: DesignProject[] = [
  {
    id: "1",
    title: "Campaña Verano 2025",
    platform: "instagram",
    type: "carousel",
    image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMGVhNWU5Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkNhbXBhw7FhIFZlcmFubzwvdGV4dD48L3N2Zz4=",
    createdAt: new Date("2025-04-01"),
    updatedAt: new Date("2025-04-02"),
  },
  {
    id: "2",
    title: "Lanzamiento Producto X",
    platform: "facebook",
    type: "post",
    image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjk3MzE2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPlByb2R1Y3RvIFg8L3RleHQ+PC9zdmc+",
    createdAt: new Date("2025-04-02"),
    updatedAt: new Date("2025-04-02"),
  },
  {
    id: "3",
    title: "Newsletter Abril",
    platform: "email",
    type: "email",
    image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjgwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjU5ZTBiIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzMiIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5ld3NsZXR0ZXI8L3RleHQ+PC9zdmc+",
    createdAt: new Date("2025-04-03"),
    updatedAt: new Date("2025-04-03"),
  },
];

const initialChatMessages: ChatMessage[] = [
  {
    id: "1",
    sender: "assistant",
    content:
      "¡Hola! Soy Emma, tu asistente de diseño. ¿En qué puedo ayudarte hoy?",
    timestamp: new Date(),
    suggestions: [
      "Crea un post para Instagram",
      "Necesito ideas para mi campaña",
      "Ayúdame a mejorar este diseño",
      "Genera texto para mi publicación",
    ],
  },
];

const platformIcons = {
  instagram: <Instagram className="w-5 h-5" />,
  facebook: <Facebook className="w-5 h-5" />,
  twitter: <Twitter className="w-5 h-5" />,
  linkedin: <Linkedin className="w-5 h-5" />,
  tiktok: <TiktokIcon />,
  email: <Mail className="w-5 h-5" />,
  blog: <FileText className="w-5 h-5" />,
};

const platformColors = {
  instagram: "bg-gradient-to-tr from-purple-600 to-pink-500",
  facebook: "bg-blue-600",
  twitter: "bg-sky-500",
  linkedin: "bg-blue-700",
  tiktok: "bg-black",
  email: "bg-amber-500",
  blog: "bg-emerald-600",
};

// Página principal de Emma Visual Studio
export default function EmmaVisualStudioV2Page() {
  return (
    <DashboardLayoutWrapper pageTitle="Emma Visual Studio">
      <EmmaVisualStudioContent />
    </DashboardLayoutWrapper>
  );
}

// Componente principal
function EmmaVisualStudioContent() {
  const [view, setView] = useState<"home" | "editor" | "wizard">("home");
  const [showChat, setShowChat] = useState(false);
  const [chatMessages, setChatMessages] =
    useState<ChatMessage[]>(initialChatMessages);
  const [inputMessage, setInputMessage] = useState("");
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(
    null,
  );
  const [selectedContentType, setSelectedContentType] =
    useState<ContentType | null>(null);
  const [wizardStep, setWizardStep] = useState(1);
  const [purpose, setPurpose] = useState("");
  const [showChatPanel, setShowChatPanel] = useState(false);
  const [fullscreenPreview, setFullscreenPreview] = useState<string | null>(
    null,
  );
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const { toast } = useToast();

  // Scroll al final del chat cuando hay nuevos mensajes
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Enviar un mensaje al chat
  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    // Agregar mensaje del usuario
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: "user",
      content: inputMessage,
      timestamp: new Date(),
    };

    setChatMessages((prev) => [...prev, userMessage]);
    setInputMessage("");

    try {
      // Preparar el historial para la API
      const historyForApi = chatMessages.map((msg) => ({
        id: msg.id,
        sender: msg.sender,
        content: msg.content,
        timestamp: msg.timestamp,
      }));

      // Llamar a la API de Emma
      const result = await EmmaAssistantService.sendMessage(
        userMessage.content,
        historyForApi,
      );

      // Crear la respuesta
      const response: ChatMessage = {
        id: (Date.now() + 100).toString(),
        sender: "assistant",
        content: result.response,
        timestamp: new Date(),
        suggestions: result.suggestions,
      };

      setChatMessages((prev) => [...prev, response]);
    } catch (error) {
      console.error("Error al comunicarse con Emma:", error);

      // Fallback en caso de error
      const errorResponse: ChatMessage = {
        id: (Date.now() + 100).toString(),
        sender: "assistant",
        content:
          "Lo siento, ha ocurrido un error al procesar tu solicitud. ¿Puedes intentarlo de nuevo?",
        timestamp: new Date(),
        suggestions: [
          "Crear un nuevo post",
          "Mostrar ejemplos similares",
          "Editar un proyecto existente",
        ],
      };

      setChatMessages((prev) => [...prev, errorResponse]);

      toast({
        title: "Error de comunicación",
        description:
          "No se pudo conectar con el servicio de Emma. Intentaremos nuevamente más tarde.",
        variant: "destructive",
      });
    }
  };

  // Usar una sugerencia del chat
  const useSuggestion = (suggestion: string) => {
    setInputMessage(suggestion);
  };

  // Iniciar el wizard para crear nuevo contenido
  const startWizard = (platform: Platform) => {
    setSelectedPlatform(platform);
    setView("wizard");
    setWizardStep(1);
  };

  // Seleccionar tipo de contenido en el wizard
  const selectContentType = (type: ContentType) => {
    setSelectedContentType(type);
    setWizardStep(2);
  };

  // Avanzar al editor desde el wizard
  const proceedToEditor = () => {
    setView("editor");
  };



  // Seleccionar un proyecto existente
  const selectProject = (project: DesignProject) => {
    setSelectedPlatform(project.platform);
    setSelectedContentType(project.type);
    setSelectedProject(project.id);
    setView("editor");

    toast({
      title: "Proyecto abierto",
      description: project.title,
    });
  };

  // Guardar imagen del editor
  const handleEditorSave = (dataUrl: string) => {
    setFullscreenPreview(dataUrl);

    toast({
      title: "¡Diseño guardado!",
      description: "Tu diseño ha sido guardado exitosamente.",
    });
  };

  // Renderizar la vista de inicio
  const renderHomeView = () => (
    <div className="h-full overflow-y-auto">
      {/* Emma AI Assistant - Siempre visible en la parte superior */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-500 to-purple-600 text-white p-4 rounded-xl mb-6 relative overflow-hidden">
        <div className="absolute right-0 top-0 h-full w-1/4 bg-gradient-to-l from-purple-500/30 to-transparent"></div>
        <div className="absolute left-0 bottom-0 h-1/2 w-full bg-gradient-to-t from-blue-900/20 to-transparent"></div>

        <div className="flex items-center">
          <Avatar className="h-12 w-12 border-2 border-white/50">
            <AvatarImage src={EmmaProfile} alt="Emma AI" />
            <AvatarFallback>E</AvatarFallback>
          </Avatar>

          <div className="ml-4 flex-1">
            <h3 className="font-bold text-lg">Emma, tu asistente de IA</h3>
            <p className="text-sm text-white/80">
              ¿Qué tipo de contenido quieres crear hoy?
            </p>
          </div>

          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              onClick={() => setShowChatPanel(true)}
            >
              <MessageCircle className="w-4 h-4 mr-1" /> Chatear con Emma
            </Button>

            {/* Botón de acceso directo al editor */}
            <Button
              size="sm"
              className="bg-white text-blue-600 hover:bg-white/90"
              onClick={() => {
                setSelectedPlatform("instagram");
                setSelectedContentType("post");
                setView("editor");
              }}
            >
              <PenTool className="w-4 h-4 mr-1" /> Ir al Editor Directamente
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              onClick={() => (window.location.href = "/video-generator")}
            >
              <Video className="w-3 h-3 mr-1" /> Generar Video
            </Button>
          </div>
        </div>


      </div>

      {/* Planificador de Contenido - DESTACADO */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold flex items-center">
            <span className="bg-blue-600 text-white px-2 py-1 rounded-md text-xs font-semibold mr-2">
              NUEVO
            </span>
            Planificador de Contenido
          </h2>
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-600"
            onClick={() => (window.location.href = "/content-planner")}
          >
            Ver calendario <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </div>

        {/* Definimos animación en el componente */}
        <Card className="overflow-hidden group hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-blue-100 to-indigo-100 border-2 border-blue-300 relative animate-pulse-border">
          <div className="p-6 flex flex-col sm:flex-row items-center gap-6 relative">
            {/* Insignia de "Destacado" */}
            <div className="absolute top-0 right-0 bg-blue-600 text-white px-3 py-1 rounded-bl-md text-sm font-semibold">
              Destacado
            </div>
            {/* Cinta de Novedad */}
            <div className="absolute top-5 -left-8 bg-red-500 text-white px-10 py-1 rotate-[-45deg] text-sm font-bold shadow-sm z-10">
              ¡NUEVO!
            </div>

            <div className="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0 shadow-md">
              <CalendarDays className="w-10 h-10 text-white" />
            </div>
            <div className="text-center sm:text-left flex-1">
              <h3 className="font-bold text-xl mb-2">
                Planificador de Contenido
              </h3>
              <p className="text-sm text-gray-700 mb-4">
                <span className="font-semibold">
                  ¡Organiza todas tus publicaciones!
                </span>{" "}
                Programa tus posts en redes sociales con nuestro calendario
                interactivo. Crea, edita y visualiza tu estrategia de contenido
                en un solo lugar.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                  Fácil de usar
                </Badge>
                <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">
                  Calendario visual
                </Badge>
                <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
                  Programación
                </Badge>
                <Badge className="bg-sky-100 text-sky-800 hover:bg-sky-200">
                  Multi-plataforma
                </Badge>
              </div>
              <Button
                onClick={() => (window.location.href = "/content-planner")}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 text-base"
                size="lg"
              >
                Ir al Planificador{" "}
                <ArrowRight className="w-5 h-5 ml-2 animate-pulse" />
              </Button>
            </div>
          </div>
        </Card>
      </div>



      {/* Proyectos recientes */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">Proyectos Recientes</h2>
          <Button variant="ghost" size="sm" className="text-blue-600">
            Ver todos <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {recentProjects.map((project) => (
            <Card
              key={project.id}
              className="overflow-hidden group hover:shadow-md transition-shadow duration-300"
            >
              <div className="relative">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    size="sm"
                    className="bg-white text-gray-800 hover:bg-white/90"
                    onClick={() => selectProject(project)}
                  >
                    <Edit3 className="w-4 h-4 mr-1" /> Editar
                  </Button>
                </div>
              </div>
              <CardContent className="p-3">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">{project.title}</h3>
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center ${platformColors[project.platform]}`}
                  >
                    {platformIcons[project.platform]}
                  </div>
                </div>
                <p className="text-sm text-gray-500">
                  Actualizado: {project.updatedAt.toLocaleDateString()}
                </p>
              </CardContent>
            </Card>
          ))}

          {/* Tarjeta para crear nuevo proyecto */}
          <Card className="overflow-hidden border-dashed border-2 hover:border-blue-500 transition-colors group">
            <CardContent className="p-0 h-full">
              <Button
                variant="ghost"
                className="w-full h-full min-h-[216px] flex flex-col items-center justify-center text-gray-500 group-hover:text-blue-600"
                onClick={() => startWizard("instagram")}
              >
                <Plus className="w-8 h-8 mb-2" />
                <span className="font-medium">Crear nuevo proyecto</span>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );

  // Renderizar el wizard
  const renderWizardView = () => (
    <div className="h-full">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() =>
            wizardStep === 1 ? setView("home") : setWizardStep(wizardStep - 1)
          }
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h2 className="text-2xl font-bold ml-2">
          {wizardStep === 1
            ? "Elige el tipo de contenido"
            : "Configura tu diseño"}
        </h2>
      </div>

      {wizardStep === 1 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {selectedPlatform === "instagram" && (
            <>
              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("post")}
              >
                <div className="bg-blue-100 p-8 flex items-center justify-center">
                  <Square className="w-20 h-20 text-blue-500" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Post</h3>
                  <p className="text-sm text-gray-500">
                    Post cuadrado para tu feed de Instagram
                  </p>
                </CardContent>
              </Card>

              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("story")}
              >
                <div className="bg-purple-100 p-8 flex items-center justify-center">
                  <div className="w-12 h-20 rounded-lg bg-purple-500" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Historia</h3>
                  <p className="text-sm text-gray-500">
                    Historia vertical para Instagram
                  </p>
                </CardContent>
              </Card>

              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("carousel")}
              >
                <div className="bg-amber-100 p-8 flex items-center justify-center">
                  <Layers className="w-20 h-20 text-amber-500" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Carrusel</h3>
                  <p className="text-sm text-gray-500">
                    Múltiples imágenes para deslizar
                  </p>
                </CardContent>
              </Card>

              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("reel")}
              >
                <div className="bg-pink-100 p-8 flex items-center justify-center">
                  <div className="w-12 h-20 rounded-lg bg-pink-500 flex items-center justify-center">
                    <ImageIcon className="w-8 h-8 text-white" />
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Reel</h3>
                  <p className="text-sm text-gray-500">
                    Cover para tus videos de Reels
                  </p>
                </CardContent>
              </Card>
            </>
          )}

          {selectedPlatform === "facebook" && (
            <>
              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("post")}
              >
                <div className="bg-blue-100 p-8 flex items-center justify-center">
                  <Square className="w-20 h-20 text-blue-500" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Post</h3>
                  <p className="text-sm text-gray-500">
                    Post para tu feed de Facebook
                  </p>
                </CardContent>
              </Card>

              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("banner")}
              >
                <div className="bg-indigo-100 p-8 flex items-center justify-center">
                  <div className="w-24 h-12 rounded-lg bg-indigo-500" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Banner</h3>
                  <p className="text-sm text-gray-500">
                    Banner para tu página de Facebook
                  </p>
                </CardContent>
              </Card>

              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("profile")}
              >
                <div className="bg-green-100 p-8 flex items-center justify-center">
                  <div className="w-20 h-20 rounded-full bg-green-500" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Perfil</h3>
                  <p className="text-sm text-gray-500">
                    Imagen de perfil para Facebook
                  </p>
                </CardContent>
              </Card>
            </>
          )}

          {selectedPlatform === "email" && (
            <>
              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("email")}
              >
                <div className="bg-amber-100 p-8 flex items-center justify-center">
                  <Mail className="w-20 h-20 text-amber-500" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Email</h3>
                  <p className="text-sm text-gray-500">
                    Plantilla para newsletters
                  </p>
                </CardContent>
              </Card>

              <Card
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => selectContentType("banner")}
              >
                <div className="bg-orange-100 p-8 flex items-center justify-center">
                  <div className="w-24 h-12 rounded-lg bg-orange-500" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-1">Banner Email</h3>
                  <p className="text-sm text-gray-500">
                    Banner para tus campañas de email
                  </p>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      )}

      {wizardStep === 2 && (
        <div>
          <div className="bg-slate-50 p-4 rounded-lg mb-6">
            <div className="flex items-center mb-4">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${platformColors[selectedPlatform || "instagram"]} mr-3`}
              >
                {platformIcons[selectedPlatform || "instagram"]}
              </div>
              <div>
                <p className="font-medium capitalize">{selectedPlatform}</p>
                <p className="text-sm text-gray-500 capitalize">
                  {selectedContentType}
                </p>
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                ¿Cuál es el propósito de este diseño?
              </label>
              <Input
                value={purpose}
                onChange={(e) => setPurpose(e.target.value)}
                placeholder="Ej. Promocionar un nuevo producto, anunciar un evento, etc."
              />
            </div>

            <Button className="w-full" onClick={proceedToEditor}>
              Crear diseño <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>


        </div>
      )}
    </div>
  );

  // Renderizar el editor
  const renderEditorView = () => (
    <div className="h-full">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => setView("home")}>
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h2 className="text-2xl font-bold ml-2">Editor</h2>

          {selectedPlatform && selectedContentType && (
            <div className="flex items-center ml-4">
              <div
                className={`w-6 h-6 rounded-full flex items-center justify-center ${platformColors[selectedPlatform]} mr-2`}
              >
                {platformIcons[selectedPlatform]}
              </div>
              <span className="text-sm font-medium capitalize">
                {selectedContentType} de {selectedPlatform}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowChatPanel(true)}
          >
            <MessageCircle className="w-4 h-4 mr-1" /> Asistente
          </Button>

          <Button variant="outline" size="sm">
            <Share2 className="w-4 h-4 mr-1" /> Compartir
          </Button>

          <Button size="sm">
            <Download className="w-4 h-4 mr-1" /> Descargar
          </Button>
        </div>
      </div>

      <div className="bg-slate-50 rounded-lg p-4 flex-1 flex flex-col items-center justify-center">
        <div className="w-full max-w-6xl h-[calc(100vh-200px)]">
          <EmmaVisualEditor
            width={800}
            height={600}
            backgroundColor="#ffffff"
            onSave={handleEditorSave}
          />
        </div>
      </div>
    </div>
  );

  // Panel de chat con Emma
  const renderChatPanel = () => (
    <Dialog open={showChatPanel} onOpenChange={setShowChatPanel}>
      <DialogContent className="sm:max-w-[400px] p-0 overflow-hidden max-h-[80vh]">
        <DialogHeader className="px-4 py-2 border-b">
          <DialogTitle className="flex items-center">
            <Avatar className="h-8 w-8 mr-2">
              <AvatarImage src={EmmaProfile} alt="Emma AI" />
              <AvatarFallback>E</AvatarFallback>
            </Avatar>
            <span>Emma Assistant</span>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="p-4 h-[50vh]" ref={chatContainerRef}>
          <div className="space-y-4">
            {chatMessages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    message.sender === "user"
                      ? "bg-blue-600 text-white rounded-br-none"
                      : "bg-gray-100 text-gray-800 rounded-bl-none"
                  }`}
                >
                  <p>{message.content}</p>

                  {message.sender === "assistant" &&
                    message.suggestions &&
                    message.suggestions.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {message.suggestions.map((suggestion, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            className="w-full justify-start p-1 h-auto text-xs bg-white/10 hover:bg-white/20 text-left"
                            onClick={() => useSuggestion(suggestion)}
                          >
                            {suggestion}
                          </Button>
                        ))}
                      </div>
                    )}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        <div className="p-3 border-t">
          <div className="flex items-center gap-2">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Escribe tu mensaje..."
              className="flex-1"
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
            />
            <Button
              size="icon"
              onClick={sendMessage}
              disabled={!inputMessage.trim()}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  // Diálogo de previsualización a pantalla completa
  const renderFullscreenPreview = () => (
    <Dialog
      open={!!fullscreenPreview}
      onOpenChange={() => setFullscreenPreview(null)}
    >
      <DialogContent className="sm:max-w-[800px] p-0 overflow-hidden">
        <div className="relative">
          {fullscreenPreview && (
            <img src={fullscreenPreview} alt="Preview" className="w-full" />
          )}
          <Button
            variant="outline"
            size="icon"
            className="absolute top-2 right-2 rounded-full bg-white/80 hover:bg-white"
            onClick={() => setFullscreenPreview(null)}
          >
            <XCircle className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-4 flex justify-between">
          <div>
            <h3 className="font-bold text-lg">Tu diseño</h3>
            <p className="text-sm text-gray-500">
              Creado con Emma Visual Studio
            </p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline">
              <Share2 className="h-4 w-4 mr-1" /> Compartir
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-1" /> Descargar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="h-full px-6 py-6">
      {view === "home" && renderHomeView()}
      {view === "wizard" && renderWizardView()}
      {view === "editor" && renderEditorView()}

      {renderChatPanel()}
      {renderFullscreenPreview()}
    </div>
  );
}
