import React, { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useLocation } from "wouter";
import PolotnoIntegrationService from "@/services/polotno-integration-service";

import { ArrowLeft, Save, Share2, Download, ImageIcon, X, Palette, ChevronDown, Settings } from "lucide-react";

import { Instagram, Twitter, Facebook, Linkedin } from "lucide-react";
import { Suspense, lazy } from "react";
import { EditorSimple } from "@/components/editor/EditorSimple";
import { CanvasEditor } from "@/components/editor/CanvasEditor";
import KonvaEditor from "@/components/editor/KonvaEditor";

// Lazy load PolotnoStudio for better performance
const PolotnoStudio = lazy(() => import("@/components/polotno/PolotnoStudio").then(module => ({
  default: module.PolotnoStudio
})));

// Función para el componente TikTok icon que no existe en lucide
const TiktokIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z" />
    <path d="M16 8v8a5 5 0 0 1-5 5v0a5 5 0 0 1-5-5v0-8" />
    <path d="M19 12V5c-1 0-4-1-7 0" />
  </svg>
);

// Página principal del editor visual
export default function VisualEditorPage() {
  return (
    <main className="min-h-screen bg-gray-50">
      <VisualEditorContent />
    </main>
  );
}

function VisualEditorContent() {
  const { toast } = useToast();
  const [postContent, setPostContent] = useState("");
  const [platform, setPlatform] = useState("custom"); // Cambiado de "instagram" a "custom"
  const [canvasSize, setCanvasSize] = useState({ width: 1080, height: 1080 }); // Tamaño estándar
  const [showSizeSelector, setShowSizeSelector] = useState(false); // Para mostrar selector de tamaño
  const [isSaving, setIsSaving] = useState(false);
  const [copied, setCopied] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [, setLocation] = useLocation();

  // Estados para gestionar la imagen desde la URL
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Estado para múltiples mockups
  const [mockupImages, setMockupImages] = useState<string[]>([]);

  // Debug: Log cuando cambia imageUrl
  useEffect(() => {
    console.log("🔄 imageUrl state cambió a:", imageUrl);
  }, [imageUrl]);

  // Cargar contenido, plataforma e imagen de la URL
  useEffect(() => {
    console.log("🔍 URL completa:", window.location.href)
    console.log("🔍 Search params:", window.location.search)

    const searchParams = new URLSearchParams(window.location.search);
    const contentParam = searchParams.get("content");
    const platformParam = searchParams.get("platform");
    const imageUrlParam = searchParams.get("imageUrl");
    const imageParam = searchParams.get("image"); // Soporte para parámetro 'image' del AdMiniEditor
    const imageKeyParam = searchParams.get("imageKey"); // Nueva: clave de localStorage
    const loadFromSessionParam = searchParams.get("loadFromSession"); // Nueva: cargar desde sessionStorage
    const tempImageIdParam = searchParams.get("tempImageId"); // Nueva: cargar desde backend temporal
    const pageDataParam = searchParams.get("pageData"); // Nueva: datos de página de Polotno
    const polotnoJSONParam = searchParams.get("polotnoJSON"); // Nueva: JSON válido para Polotno
    const loadPolotnoJSONParam = searchParams.get("loadPolotnoJSON"); // Nueva: cargar JSON desde sessionStorage
    const templateParam = searchParams.get("template");
    const widthParam = searchParams.get("width");
    const heightParam = searchParams.get("height");



    // Verificar si hay múltiples mockups
    const totalMockupsParam = searchParams.get("totalMockups");
    if (totalMockupsParam) {
      const totalMockups = parseInt(totalMockupsParam);
      const mockupImagesTemp: string[] = [];

      for (let i = 1; i <= totalMockups; i++) {
        const mockupImageUrl = searchParams.get(`mockupImageUrl${i}`);
        if (mockupImageUrl) {
          mockupImagesTemp.push(decodeURIComponent(mockupImageUrl));
        }
      }

      if (mockupImagesTemp.length > 0) {
        setMockupImages(mockupImagesTemp);
        console.log('🎨 Múltiples mockups detectados:', mockupImagesTemp);
      }
    }

    console.log("📋 Todos los parámetros:", {
      content: contentParam,
      platform: platformParam,
      imageUrl: imageUrlParam,
      image: imageParam,
      imageKey: imageKeyParam,
      pageData: pageDataParam ? "SÍ" : "NO",
      template: templateParam,
      width: widthParam,
      height: heightParam,
      totalMockups: totalMockupsParam,
      mockupImages: mockupImages.length > 0 ? mockupImages : "NINGUNA"
    });

    // NUEVO: Procesar JSON válido para Polotno (desde URL)
    if (polotnoJSONParam) {
      try {
        const polotnoData = JSON.parse(decodeURIComponent(polotnoJSONParam));
        console.log('🎨 JSON VÁLIDO PARA POLOTNO DETECTADO (desde URL):', {
          structure: 'CORRECTO',
          width: polotnoData.width,
          height: polotnoData.height,
          pages: polotnoData.pages?.length,
          children: polotnoData.pages?.[0]?.children?.length
        });

        // Guardar JSON para que PolotnoStudio lo use
        sessionStorage.setItem('POLOTNO_LOAD_JSON', JSON.stringify(polotnoData));

        // Extraer dimensiones
        if (polotnoData.width && polotnoData.height) {
          setCanvasSize({ width: polotnoData.width, height: polotnoData.height });
        }

        console.log('✅ JSON guardado en sessionStorage para Polotno');
      } catch (error) {
        console.error('❌ Error procesando JSON de Polotno:', error);
      }
    }

    // NUEVO: Cargar JSON desde sessionStorage (método directo)
    if (loadPolotnoJSONParam === 'true') {
      const polotnoJSON = sessionStorage.getItem('POLOTNO_LOAD_JSON');
      if (polotnoJSON) {
        try {
          const polotnoData = JSON.parse(polotnoJSON);
          console.log('🎨 JSON VÁLIDO PARA POLOTNO DETECTADO (desde sessionStorage):', {
            structure: 'CORRECTO',
            pages: polotnoData.pages?.length,
            children: polotnoData.pages?.[0]?.children?.length,
            hasBase64Image: polotnoData.pages?.[0]?.children?.[0]?.src?.startsWith('data:image/')
          });

          // Extraer dimensiones si existen
          if (polotnoData.pages?.[0]?.children?.[0]?.width && polotnoData.pages?.[0]?.children?.[0]?.height) {
            const width = polotnoData.pages[0].children[0].width;
            const height = polotnoData.pages[0].children[0].height;
            setCanvasSize({ width, height });
            console.log('📏 Dimensiones extraídas del JSON:', width, 'x', height);
          }

          console.log('✅ JSON listo para cargar en Polotno');
        } catch (error) {
          console.error('❌ Error procesando JSON desde sessionStorage:', error);
        }
      } else {
        console.error('❌ No se encontró JSON en sessionStorage');
      }
    }

    // Procesar el contenido del post
    if (contentParam) {
      setPostContent(decodeURIComponent(contentParam));
      console.log(
        "Contenido cargado desde URL:",
        decodeURIComponent(contentParam).substring(0, 50) + "...",
      );
    }

    // Procesar la plataforma
    if (
      platformParam &&
      ["instagram", "facebook", "twitter", "linkedin", "tiktok"].includes(
        platformParam,
      )
    ) {
      setPlatform(platformParam);
      console.log("Plataforma configurada:", platformParam);

      // Ajustar dimensiones del canvas según la plataforma
      // Nota: Reducimos el tamaño para mejor rendimiento y compatibilidad
      if (platformParam === "instagram") {
        setCanvasSize({ width: 800, height: 800 });
      } else if (platformParam === "twitter") {
        setCanvasSize({ width: 800, height: 450 });
      } else if (platformParam === "facebook") {
        setCanvasSize({ width: 800, height: 420 });
      } else if (platformParam === "linkedin") {
        setCanvasSize({ width: 800, height: 418 });
      } else if (platformParam === "tiktok") {
        setCanvasSize({ width: 600, height: 800 });
      } else {
        // Para "custom" o cualquier otra plataforma, usar tamaño estándar
        setCanvasSize({ width: 1080, height: 1080 });
      }
    }

    // Procesar pageData si existe (nueva integración con Polotno)
    if (pageDataParam) {
      try {
        const pageData = JSON.parse(decodeURIComponent(pageDataParam));
        console.log("📄 Datos de página de Polotno cargados:", pageData);

        // Configurar dimensiones del canvas
        if (pageData.width && pageData.height) {
          setCanvasSize({ width: pageData.width, height: pageData.height });
        }

        // Extraer imagen de fondo si existe
        const imageElement = pageData.elements?.find((el: any) => el.type === 'image');
        if (imageElement && imageElement.src) {
          setImageUrl(imageElement.src);
          setImageLoaded(true);
          console.log("🖼️ Imagen extraída de pageData");
        }

        // Extraer texto si existe
        const textElements = pageData.elements?.filter((el: any) => el.type === 'text') || [];
        if (textElements.length > 0) {
          const combinedText = textElements.map((el: any) => el.text).join('\n');
          setPostContent(combinedText);
          console.log("📝 Texto extraído de pageData:", combinedText.substring(0, 50) + "...");
        }

      } catch (error) {
        console.error("❌ Error procesando pageData:", error);
      }
    }

    // Procesar la imagen si existe (soporte para 'imageUrl', 'image' e 'imageKey')
    let finalImageUrl = null;
    let imageSource = '';

    // PRIMERO: Revisar si hay imagen en backend temporal
    if (tempImageIdParam) {
      console.log("🔍 VISUAL EDITOR: Cargando imagen desde backend temporal:", tempImageIdParam);

      fetch(`/api/v1/temp-images/${tempImageIdParam}`)
        .then(response => response.json())
        .then(result => {
          if (result.success && result.imageData) {
            console.log("✅ VISUAL EDITOR: Imagen cargada desde backend temporal");
            setImageUrl(result.imageData);
            setImageLoaded(true);
          } else {
            console.error("❌ VISUAL EDITOR: Error cargando imagen desde backend temporal");
          }
        })
        .catch(error => {
          console.error("❌ VISUAL EDITOR: Error en fetch de imagen temporal:", error);
        });
    } else if (loadFromSessionParam) {
      const storedImage = sessionStorage.getItem('POLOTNO_CURRENT_IMAGE');
      if (storedImage) {
        finalImageUrl = storedImage;
        imageSource = 'sessionStorage';
        console.log("🖼️ VISUAL EDITOR: Imagen cargada desde sessionStorage:", {
          size: storedImage.length
        });
        // Limpiar después de usar
        sessionStorage.removeItem('POLOTNO_CURRENT_IMAGE');
      } else {
        console.error("❌ VISUAL EDITOR: No se encontró imagen en sessionStorage");
      }
    } else if (imageKeyParam) {
      // Leer imagen desde sessionStorage usando la clave
      console.log("🔍 VISUAL EDITOR: Buscando imagen con clave:", imageKeyParam);
      console.log("🔍 VISUAL EDITOR: Claves disponibles en sessionStorage:", Object.keys(sessionStorage));

      const storedImage = sessionStorage.getItem(imageKeyParam);
      if (storedImage) {
        finalImageUrl = storedImage;
        imageSource = 'sessionStorage';
        console.log("✅ VISUAL EDITOR: Imagen encontrada en sessionStorage:", {
          key: imageKeyParam,
          size: storedImage.length,
          preview: storedImage.substring(0, 50) + '...'
        });
        // Limpiar sessionStorage después de usar la imagen
        sessionStorage.removeItem(imageKeyParam);
      } else {
        console.error("❌ VISUAL EDITOR: No se encontró imagen en sessionStorage con clave:", imageKeyParam);
        console.error("❌ VISUAL EDITOR: sessionStorage keys:", Object.keys(sessionStorage));
      }
    } else if (imageUrlParam || imageParam) {
      // Fallback a parámetros URL (para compatibilidad)
      const finalImageParam = imageUrlParam || imageParam;
      finalImageUrl = decodeURIComponent(finalImageParam);
      imageSource = imageUrlParam ? 'imageUrl' : 'image';
    }

    if (finalImageUrl) {
      console.log("🖼️ VISUAL EDITOR: Imagen detectada:", {
        source: imageSource,
        url: finalImageUrl.substring(0, 100) + '...'
      });
      console.log("🖼️ VISUAL EDITOR: Configurando imageUrl state...");
      setImageUrl(finalImageUrl);

      // Verificar que la imagen se puede cargar
      const testImg = new Image()
      testImg.onload = () => {
        console.log("✅ VISUAL EDITOR: Imagen verificada y lista para Editor Profesional")
        console.log("📏 VISUAL EDITOR: Dimensiones:", testImg.width, "x", testImg.height)
        setImageLoaded(true);
      }
      testImg.onerror = () => {
        console.error("❌ VISUAL EDITOR: Error verificando imagen")
        setImageLoaded(false);
      }
      testImg.src = finalImageUrl;
    } else {
      console.log("❌ VISUAL EDITOR: No se encontró imagen en parámetros URL ni localStorage");
      console.log("🔍 VISUAL EDITOR: Parámetros disponibles:", window.location.search);
      console.log("🔍 VISUAL EDITOR: Todos los parámetros:", Object.fromEntries(searchParams.entries()));
    }

    // Procesar plantilla si existe
    if (templateParam) {
      console.log("Plantilla ID detectada:", templateParam);
      toast({
        title: "Cargando plantilla",
        description: `Preparando plantilla: ${templateParam}`,
      });
    }

    // Procesar dimensiones personalizadas si existen
    if (widthParam && heightParam) {
      const width = parseInt(widthParam);
      const height = parseInt(heightParam);
      if (!isNaN(width) && !isNaN(height)) {
        setCanvasSize({ width, height });
        console.log("Dimensiones personalizadas:", width, "x", height);
      }
    }

    // Listener para recibir imagen via postMessage
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return;

      if ((event.data.type === 'LOAD_IMAGE' || event.data.type === 'LOAD_IMAGE_NOW') && event.data.imageData) {
        console.log('📥 VISUAL EDITOR: Imagen recibida via postMessage');
        console.log('📥 Tamaño de imagen:', event.data.imageData.length);
        setImageUrl(event.data.imageData);
        setImageLoaded(true);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // Manejar la vista previa generada por el editor
  const handlePreview = (dataUrl: string) => {
    if (dataUrl) {
      console.log("Vista previa generada correctamente");
      setPreviewImage(dataUrl);
      toast({
        title: "Vista previa generada",
        description: "Se ha generado la vista previa de tu diseño",
      });
    } else {
      console.error("Error: No se pudo generar la vista previa");
      toast({
        title: "Error en la vista previa",
        description: "No se pudo generar la vista previa, intenta nuevamente",
        variant: "destructive",
      });
    }
  };

  // Descargar el diseño
  const downloadDesign = () => {
    if (previewImage) {
      // Crear un elemento <a> temporal
      const downloadLink = document.createElement("a");
      downloadLink.href = previewImage;

      // Configurar nombre del archivo según la plataforma
      const timestamp = new Date()
        .toISOString()
        .replace(/:/g, "-")
        .substring(0, 19);
      downloadLink.download = `emma-design-${platform}-${timestamp}.png`;

      // Añadir al DOM, hacer click, y luego limpiar
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      toast({
        title: "Diseño descargado",
        description: "Tu diseño ha sido descargado correctamente",
      });
    } else {
      toast({
        title: "No hay vista previa",
        description: "Genera una vista previa antes de descargar",
        variant: "destructive",
      });
    }
  };

  // Compartir el diseño
  const shareDesign = () => {
    toast({
      title: "Compartir diseño",
      description: "Opciones para compartir tu diseño",
    });
  };

  // Guardar el diseño
  const saveDesign = () => {
    setIsSaving(true);

    setTimeout(() => {
      setIsSaving(false);
      toast({
        title: "Diseño guardado",
        description: "Tu diseño ha sido guardado correctamente",
      });
    }, 1500);
  };

  // Icono de la plataforma seleccionada
  const getPlatformIcon = () => {
    switch (platform) {
      case "instagram":
        return <Instagram className="w-5 h-5" />;
      case "facebook":
        return <Facebook className="w-5 h-5" />;
      case "twitter":
        return <Twitter className="w-5 h-5" />;
      case "linkedin":
        return <Linkedin className="w-5 h-5" />;
      case "tiktok":
        return <TiktokIcon />;
      case "custom":
        return <Palette className="w-5 h-5" />;
      default:
        return <Palette className="w-5 h-5" />; // Cambiado de Instagram a Palette
    }
  };

  // Tamaños predefinidos para selección rápida
  const presetSizes = [
    { name: "Cuadrado (1:1)", width: 1080, height: 1080 },
    { name: "Instagram Post", width: 1080, height: 1080 },
    { name: "Instagram Story", width: 1080, height: 1920 },
    { name: "Facebook Post", width: 1200, height: 630 },
    { name: "Twitter Post", width: 1200, height: 675 },
    { name: "LinkedIn Post", width: 1200, height: 627 },
    { name: "YouTube Thumbnail", width: 1280, height: 720 },
    { name: "Banner Web", width: 1920, height: 1080 },
    { name: "A4 Portrait", width: 2480, height: 3508 },
    { name: "A4 Landscape", width: 3508, height: 2480 },
  ];

  // Cambiar tamaño del canvas
  const changeCanvasSize = (width: number, height: number) => {
    setCanvasSize({ width, height });
    setShowSizeSelector(false);
    toast({
      title: "Tamaño actualizado",
      description: `Canvas cambiado a ${width}x${height}px`,
    });
  };

  // Configurar título de la ventana
  useEffect(() => {
    document.title = "Emma AI - Editor Visual";
  }, []);

  return (
    <div className="h-screen flex flex-col">
      {/* Barra de herramientas superior */}
      <div className="flex items-center justify-between border-b border-gray-200 p-4 bg-white">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // Redirigir a Emma Studio en lugar de la página principal
              setLocation("/emma-studio");
            }}
          >
            <ArrowLeft className="w-4 h-4 mr-2" /> Volver
          </Button>
          <Separator orientation="vertical" className="h-6 mx-4" />
          <div className="flex items-center gap-2">
            {platform === "custom" ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100">
                    {getPlatformIcon()}
                    <span className="ml-2 capitalize">{platform}</span>
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                  <div className="px-2 py-1.5 text-sm font-semibold text-gray-900">
                    Tamaños Predefinidos
                  </div>
                  <DropdownMenuSeparator />
                  {presetSizes.map((size, index) => (
                    <DropdownMenuItem
                      key={index}
                      onClick={() => changeCanvasSize(size.width, size.height)}
                      className="flex justify-between"
                    >
                      <span>{size.name}</span>
                      <span className="text-xs text-gray-500">
                        {size.width}×{size.height}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center border rounded-md px-3 py-1 bg-indigo-50 text-indigo-700">
                {getPlatformIcon()}
                <span className="ml-2 capitalize">{platform}</span>
              </div>
            )}
            <span className="text-sm text-gray-500">|</span>
            <div className="text-sm text-gray-500">
              {canvasSize.width} x {canvasSize.height}px
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" onClick={shareDesign}>
            <Share2 className="w-4 h-4 mr-1" /> Compartir
          </Button>
          <Button variant="outline" size="sm" onClick={downloadDesign}>
            <Download className="w-4 h-4 mr-1" /> Descargar
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={saveDesign}
            disabled={isSaving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Save className="w-4 h-4 mr-1" />{" "}
            {isSaving ? "Guardando..." : "Guardar"}
          </Button>
        </div>
      </div>

      {/* Editor Visual */}
      <div
        className="flex-1 overflow-hidden"
        style={{
          minHeight: "700px",
          height: "calc(100vh - 120px)",
          display: "flex",
        }}
      >
        {/* Usamos Polotno Studio para edición profesional con soporte para videos */}
        {(() => {
          console.log("🎨 VISUAL EDITOR: Renderizando PolotnoStudio con props:", {
            width: canvasSize.width,
            height: canvasSize.height,
            initialText: postContent,
            platform: platform,
            initialImageUrl: imageUrl ? `${imageUrl.substring(0, 50)}...` : undefined,
            imageUrlExists: !!imageUrl,
            imageUrlLength: imageUrl?.length || 0
          });
          return null;
        })()}
        <Suspense fallback={
          <div className="flex items-center justify-center w-full h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading Visual Editor...</p>
              <p className="text-sm text-gray-500 mt-2">Preparing your design canvas</p>
            </div>
          </div>
        }>
          <PolotnoStudio
            width={canvasSize.width}
            height={canvasSize.height}
            initialText={postContent}
            platform={platform}
            initialImageUrl={imageUrl || undefined}
            mockupImages={mockupImages.length > 0 ? mockupImages : undefined}
            onPreview={handlePreview}
          />
        </Suspense>

      </div>

      {/* Modal de previsualizacion */}
      <Dialog open={!!previewImage} onOpenChange={() => setPreviewImage(null)}>
        <DialogContent className="sm:max-w-2xl">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold" id="preview-dialog-title">
              Vista previa
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewImage(null)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          {previewImage && (
            <img
              src={previewImage}
              alt="Preview"
              className="w-full h-auto rounded-md"
            />
          )}
          <div className="mt-4 flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPreviewImage(null)}
            >
              Cerrar
            </Button>
            <Button size="sm" onClick={downloadDesign}>
              <Download className="w-4 h-4 mr-2" /> Descargar
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
