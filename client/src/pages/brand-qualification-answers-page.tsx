import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { MarcaService } from "@/services/marca-service";
import { Marca } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import BrandQualificationAnswers from "@/components/marca/brand-qualification-answers";

interface BrandQualificationAnswersPageProps {
  marcaId: string;
}

const BrandQualificationAnswersPage: React.FC<BrandQualificationAnswersPageProps> = ({ marcaId }) => {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [marca, setMarca] = useState<Marca | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMarca();
  }, [marcaId]);

  const loadMarca = async () => {
    try {
      setLoading(true);
      const marcaData = await MarcaService.getMarcaById(marcaId);
      setMarca(marcaData);
    } catch (error) {
      console.error('Error loading marca:', error);
      toast({
        title: "Error",
        description: "No se pudo cargar la información de la marca",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpdate = async (updatedMarca: Marca) => {
    try {
      // Update the marca using local storage (as requested - no database persistence)
      await MarcaService.updateMarca(updatedMarca);
      setMarca(updatedMarca);
      toast({
        title: "¡Éxito!",
        description: "Las respuestas de calificación han sido actualizadas",
      });
    } catch (error) {
      console.error('Error updating marca:', error);
      toast({
        title: "Error",
        description: "No se pudieron actualizar las respuestas",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <DashboardLayout pageTitle="Cargando...">
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Cargando respuestas de calificación...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!marca) {
    return (
      <DashboardLayout pageTitle="Error">
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Marca no encontrada</h2>
            <p className="text-gray-600 mb-4">No se pudo encontrar la información de la marca solicitada.</p>
            <Button onClick={() => navigate('/dashboard/marca')}>
              Volver a Marcas
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout pageTitle={`Respuestas - ${marca.brand_name}`}>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30">
        <div className="max-w-4xl mx-auto p-6">
          {/* Header */}
          <div className="mb-8">
            <Button 
              variant="ghost" 
              onClick={() => navigate("/dashboard/marca")}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver a Marcas
            </Button>
            
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
                <Eye className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {marca.brand_name}
                </h1>
                <p className="text-gray-600">
                  Respuestas de calificación de marca
                </p>
              </div>
            </div>
          </div>

          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <BrandQualificationAnswers 
              marca={marca} 
              onUpdate={handleUpdate}
            />
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BrandQualificationAnswersPage;
