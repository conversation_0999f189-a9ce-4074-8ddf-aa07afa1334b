import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { motion } from "framer-motion";
import { 
  ArrowLeft, 
  Building, 
  Globe, 
  Palette, 
  Users, 
  MessageSquare, 
  Target, 
  FileText, 
  Edit,
  Share2,
  Copy,
  Download,
  Eye,
  Sparkles
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { MarcaService } from "@/services/marca-service";
import { Marca } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";

interface MarcaDetailPageProps {
  marcaId: string;
}

const MarcaDetailPage: React.FC<MarcaDetailPageProps> = ({ marcaId }) => {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [marca, setMarca] = useState<Marca | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMarca();
  }, [marcaId]);

  const loadMarca = async () => {
    try {
      setLoading(true);
      const marcaData = await MarcaService.getMarcaById(marcaId);
      setMarca(marcaData);
    } catch (error) {
      console.error('Error loading marca:', error);
      toast({
        title: "Error",
        description: "No se pudo cargar la información de la marca",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigate(`/dashboard/marca/${marcaId}/editar`);
  };

  const handleDuplicate = async () => {
    try {
      await MarcaService.duplicateMarca(marcaId);
      toast({
        title: "¡Éxito!",
        description: "Marca duplicada correctamente"
      });
      navigate("/dashboard/marca");
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo duplicar la marca",
        variant: "destructive"
      });
    }
  };

  const handleShare = () => {
    // Copy URL to clipboard
    navigator.clipboard.writeText(window.location.href);
    toast({
      title: "¡Copiado!",
      description: "Enlace copiado al portapapeles"
    });
  };

  if (loading) {
    return (
      <DashboardLayout pageTitle="Cargando...">
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Cargando información de la marca...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!marca) {
    return (
      <DashboardLayout pageTitle="Marca no encontrada">
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Marca no encontrada</h2>
            <p className="text-gray-600 mb-4">La marca que buscas no existe o ha sido eliminada.</p>
            <Button onClick={() => navigate("/dashboard/marca")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver a Marcas
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout pageTitle={marca.brand_name}>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30">
        {/* Header */}
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => navigate("/dashboard/marca")}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Marcas
          </Button>
          
          {/* Brand Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl p-8 mb-8 text-white"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-6">
                <Avatar className="h-20 w-20 border-4 border-white/20">
                  <AvatarImage src={marca.logo_url} alt={marca.brand_name} />
                  <AvatarFallback 
                    className="text-2xl font-bold"
                    style={{ backgroundColor: marca.primary_color }}
                  >
                    {marca.brand_name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h1 className="text-4xl font-bold mb-2">{marca.brand_name}</h1>
                  <p className="text-blue-100 text-lg mb-2">{marca.industry}</p>
                  <div className="flex items-center gap-4 text-sm text-blue-100">
                    <Badge variant="secondary" className="bg-white/20 text-white border-0">
                      {marca.status === 'active' ? 'Activo' : marca.status === 'draft' ? 'Borrador' : 'Archivado'}
                    </Badge>
                    {marca.website && (
                      <a 
                        href={marca.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 hover:text-white transition-colors"
                      >
                        <Globe className="h-4 w-4" />
                        Sitio web
                      </a>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button variant="secondary" size="sm" onClick={handleShare}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Compartir
                </Button>
                <Button variant="secondary" size="sm" onClick={handleDuplicate}>
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicar
                </Button>
                <Button variant="secondary" size="sm" onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </Button>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Brand Description */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Descripción de la Marca
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{marca.description}</p>
              </CardContent>
            </Card>

            {/* Unique Value Proposition */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Propuesta de Valor Única
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{marca.unique_value}</p>
              </CardContent>
            </Card>

            {/* Content Guidelines */}
            {marca.examples && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Lineamientos de Contenido y Referencias
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{marca.examples}</p>
                </CardContent>
              </Card>
            )}

            {/* Competitors */}
            {marca.competitors && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Principales Competidores
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{marca.competitors}</p>
                </CardContent>
              </Card>
            )}

            {/* Documents */}
            {marca.documents && marca.documents.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Documentos de Referencia
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {marca.documents.map((doc, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{doc}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Details */}
          <div className="space-y-6">
            {/* Visual Identity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Identidad Visual
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Colores de Marca</p>
                  <div className="flex gap-3">
                    <div className="text-center">
                      <div 
                        className="w-12 h-12 rounded-lg border-2 border-gray-200 mb-1"
                        style={{ backgroundColor: marca.primary_color }}
                      ></div>
                      <p className="text-xs text-gray-500">Primario</p>
                      <p className="text-xs font-mono">{marca.primary_color}</p>
                    </div>
                    <div className="text-center">
                      <div 
                        className="w-12 h-12 rounded-lg border-2 border-gray-200 mb-1"
                        style={{ backgroundColor: marca.secondary_color }}
                      ></div>
                      <p className="text-xs text-gray-500">Secundario</p>
                      <p className="text-xs font-mono">{marca.secondary_color}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Target Audience & Tone */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Audiencia y Tono
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-1">Audiencia Objetivo</p>
                  <p className="text-gray-700">{marca.target_audience}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-1">Tono de Comunicación</p>
                  <p className="text-gray-700">{marca.tone}</p>
                </div>
                {marca.personality && marca.personality.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-2">Personalidad</p>
                    <div className="flex flex-wrap gap-1">
                      {marca.personality.map((trait, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {trait}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Estadísticas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Campañas</span>
                  <span className="font-medium">{marca.campaigns_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Assets</span>
                  <span className="font-medium">{marca.assets_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Creado</span>
                  <span className="font-medium text-xs">
                    {new Date(marca.created_at).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Actualizado</span>
                  <span className="font-medium text-xs">
                    {new Date(marca.updated_at).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default MarcaDetailPage;
