"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  <PERSON>rkles,
  User,
  Brain,
  MessageCircle,
  Globe,
  Crown,
  Zap,
  Target,
  TrendingUp,
  Users,
  ArrowRight,
  CheckCircle,
  Star,
} from "lucide-react";

export default function PremiumDemo() {
  const [activeDemo, setActiveDemo] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const premiumFeatures = [
    {
      id: "avatars",
      icon: User,
      title: "Avatares AI Realistas",
      description: "Genera imágenes realistas para cada buyer persona",
      benefits: [
        "Múltiples estilos profesionales",
        "Personalización por edad y etnia",
        "Galería de opciones",
        "Exportación en alta resolución"
      ],
      demoData: {
        title: "Generación de Avatar",
        subtitle: "María González - Directora de Marketing",
        preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNjM2NmYxIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkF2YXRhciBBSTwvdGV4dD48L3N2Zz4="
      }
    },
    {
      id: "behavior",
      icon: Brain,
      title: "Predicción de Comportamiento",
      description: "Predice patrones de compra y comportamiento del cliente",
      benefits: [
        "Probabilidad de compra: 78%",
        "Canal óptimo: LinkedIn + Email",
        "Mejor horario: 10:00-12:00",
        "Precio dispuesto: $5,000-$15,000"
      ],
      demoData: {
        title: "Análisis Predictivo",
        subtitle: "Comportamiento de compra esperado",
        metrics: [
          { label: "Probabilidad de compra", value: "78%", color: "green" },
          { label: "Tiempo de decisión", value: "3-6 meses", color: "blue" },
          { label: "Sensibilidad al precio", value: "Media", color: "yellow" },
          { label: "Influencia de equipo", value: "Alta", color: "purple" }
        ]
      }
    },
    {
      id: "conversation",
      icon: MessageCircle,
      title: "Simulador de Conversaciones",
      description: "Practica conversaciones realistas con buyer personas",
      benefits: [
        "Chat simulado basado en perfil",
        "Múltiples escenarios de venta",
        "Análisis de sentimiento",
        "Entrenamiento de objeciones"
      ],
      demoData: {
        title: "Simulación de Conversación",
        subtitle: "Entrenamiento de ventas interactivo",
        conversation: [
          { sender: "Vendedor", message: "Hola María, ¿cómo está tu equipo de marketing?" },
          { sender: "María", message: "Hola! Bien, aunque estamos buscando optimizar nuestros procesos..." },
          { sender: "Vendedor", message: "Entiendo. ¿Qué desafíos específicos están enfrentando?" },
          { sender: "María", message: "Principalmente la generación de leads cualificados y el ROI de campañas." }
        ]
      }
    },
    {
      id: "geographic",
      icon: Globe,
      title: "Análisis Geográfico y Cultural",
      description: "Adapta tu estrategia por región y cultura",
      benefits: [
        "Diferencias culturales por país",
        "Horarios óptimos por zona",
        "Canales preferidos regionales",
        "Compliance local automático"
      ],
      demoData: {
        title: "Análisis Regional",
        subtitle: "Adaptación cultural por mercado",
        regions: [
          { country: "España", score: 92, channels: ["Email", "LinkedIn"], timing: "9:00-12:00" },
          { country: "México", score: 85, channels: ["WhatsApp", "Email"], timing: "10:00-17:00" },
          { country: "Colombia", score: 78, channels: ["WhatsApp", "Teléfono"], timing: "8:00-16:00" }
        ]
      }
    }
  ];

  const pricingTiers = [
    {
      name: "Free",
      price: "$0",
      period: "/mes",
      features: [
        "3 buyer personas básicas",
        "Exportación PDF",
        "Soporte por email"
      ],
      cta: "Empezar Gratis",
      popular: false
    },
    {
      name: "Pro",
      price: "$29",
      period: "/mes",
      features: [
        "Personas ilimitadas",
        "Avatares AI realistas",
        "Predicción de comportamiento",
        "Análisis básico",
        "Soporte prioritario"
      ],
      cta: "Upgrade a Pro",
      popular: true
    },
    {
      name: "Enterprise",
      price: "$199",
      period: "/mes",
      features: [
        "Todo lo anterior",
        "Simulador de conversaciones",
        "Análisis geográfico completo",
        "API access",
        "Soporte dedicado",
        "White-label"
      ],
      cta: "Contactar Ventas",
      popular: false
    }
  ];

  const runDemo = async (featureId: string) => {
    setActiveDemo(featureId);
    setIsLoading(true);
    
    // Simular carga de demo
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-3">
              <Crown className="h-8 w-8 text-purple-600" />
              Funcionalidades Premium
              <Sparkles className="h-8 w-8 text-blue-600" />
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Lleva tu generación de buyer personas al siguiente nivel con IA avanzada
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {premiumFeatures.map((feature, index) => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-all cursor-pointer border-2 hover:border-purple-200"
                    onClick={() => runDemo(feature.id)}>
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-3 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full w-fit">
                    <feature.icon className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                  <Button className="w-full mt-4 bg-purple-600 hover:bg-purple-700">
                    {isLoading && activeDemo === feature.id ? (
                      <>
                        <Zap className="h-4 w-4 mr-2 animate-pulse" />
                        Cargando Demo...
                      </>
                    ) : (
                      <>
                        Ver Demo
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Demo Section */}
        {activeDemo && !isLoading && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mb-12"
          >
            <Card className="border-purple-200 shadow-xl">
              <CardHeader className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
                <CardTitle className="text-center text-xl">
                  🚀 Demo: {premiumFeatures.find(f => f.id === activeDemo)?.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                {activeDemo === "avatars" && (
                  <div className="text-center space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {["Profesional", "Casual", "Ejecutivo"].map((style, idx) => (
                        <div key={style} className="space-y-3">
                          <div className="w-32 h-32 mx-auto bg-gradient-to-br from-purple-200 to-blue-200 rounded-full flex items-center justify-center">
                            <User className="h-16 w-16 text-purple-600" />
                          </div>
                          <Badge variant="outline">{style}</Badge>
                        </div>
                      ))}
                    </div>
                    <p className="text-gray-600">
                      Avatares generados con IA para María González - Directora de Marketing
                    </p>
                  </div>
                )}

                {activeDemo === "behavior" && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {premiumFeatures.find(f => f.id === "behavior")?.demoData.metrics?.map((metric, idx) => (
                        <div key={idx} className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className={`text-2xl font-bold text-${metric.color}-600`}>
                            {metric.value}
                          </div>
                          <div className="text-sm text-gray-600">{metric.label}</div>
                        </div>
                      ))}
                    </div>
                    <Alert>
                      <Target className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Recomendación:</strong> Contactar los martes entre 10:00-12:00 vía LinkedIn con enfoque en ROI y casos de éxito.
                      </AlertDescription>
                    </Alert>
                  </div>
                )}

                {activeDemo === "conversation" && (
                  <div className="space-y-4">
                    <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                      {premiumFeatures.find(f => f.id === "conversation")?.demoData.conversation?.map((msg, idx) => (
                        <div key={idx} className={`mb-3 ${msg.sender === "Vendedor" ? "text-right" : "text-left"}`}>
                          <div className={`inline-block p-3 rounded-lg max-w-xs ${
                            msg.sender === "Vendedor" 
                              ? "bg-blue-500 text-white" 
                              : "bg-white border"
                          }`}>
                            <div className="font-semibold text-xs mb-1">{msg.sender}</div>
                            <div className="text-sm">{msg.message}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Badge variant="outline">😊 Interés: Alto</Badge>
                      <Badge variant="outline">🤝 Confianza: Media</Badge>
                      <Badge variant="outline">⚡ Urgencia: Baja</Badge>
                    </div>
                  </div>
                )}

                {activeDemo === "geographic" && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {premiumFeatures.find(f => f.id === "geographic")?.demoData.regions?.map((region, idx) => (
                        <div key={idx} className="p-4 border rounded-lg">
                          <div className="flex justify-between items-center mb-3">
                            <h4 className="font-semibold">{region.country}</h4>
                            <Badge variant={region.score > 85 ? "default" : "secondary"}>
                              {region.score}% match
                            </Badge>
                          </div>
                          <div className="space-y-2 text-sm">
                            <div><strong>Canales:</strong> {region.channels.join(", ")}</div>
                            <div><strong>Horario:</strong> {region.timing}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <Alert>
                      <Globe className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Recomendación:</strong> Comenzar expansión por España (92% match), seguido de México (85% match).
                      </AlertDescription>
                    </Alert>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Pricing Section */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Planes y Precios
          </h2>
          <p className="text-gray-600">
            Elige el plan que mejor se adapte a tus necesidades
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {pricingTiers.map((tier, index) => (
            <motion.div
              key={tier.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`h-full relative ${
                tier.popular 
                  ? "border-purple-500 shadow-lg scale-105" 
                  : "border-gray-200"
              }`}>
                {tier.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-purple-600 text-white px-4 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      Más Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center">
                  <CardTitle className="text-xl">{tier.name}</CardTitle>
                  <div className="text-3xl font-bold text-purple-600">
                    {tier.price}
                    <span className="text-lg text-gray-500">{tier.period}</span>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    {tier.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${
                      tier.popular 
                        ? "bg-purple-600 hover:bg-purple-700" 
                        : "bg-gray-600 hover:bg-gray-700"
                    }`}
                  >
                    {tier.cta}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4">
                ¿Listo para revolucionar tus buyer personas?
              </h3>
              <p className="text-purple-100 mb-6">
                Únete a más de 1,000 empresas que ya están usando nuestras funcionalidades premium
              </p>
              <div className="flex gap-4 justify-center">
                <Button size="lg" variant="secondary">
                  Empezar Prueba Gratuita
                </Button>
                <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-purple-600">
                  Agendar Demo
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
