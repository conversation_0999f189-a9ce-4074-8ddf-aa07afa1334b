import {
  FileText,
  Mail,
  MessageSquare,
  Megaphone,
  FileType,
  Target,
  Users,
  RefreshCw,
  Clock,
  Calendar,
  Globe,
  BarChart2,
  <PERSON><PERSON>hart,
  Layers,
  Tag,
  Share2,
  ChevronRight,
  ArrowRight,
  Search,
  LucideIcon,
  Sparkles,
  X,
  Check,
} from "lucide-react";
import { useState } from "react";
import React from "react";

import DashboardLayoutWrapper from "@/components/layout/dashboard-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import axios from "axios";

// Importamos nuestras estructuras de datos
import {
  contentCategories,
  renderIcon,
  getIconByName,
  getSubcategoriesByParent,
} from "@/data/simplified-categories";
import { getQuestionsForSubcategory } from "@/data/category-questions";

export default function AIContentHubPage() {
  // Estados para la UI
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTabId, setActiveTabId] = useState("content");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Estados para la selección de contenido
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<any>(null);
  const [showSubcategoryDialog, setShowSubcategoryDialog] = useState(false);

  // Estado para el formulario de la plantilla
  const [formValues, setFormValues] = useState<Record<string, any>>({});

  // Estados para la generación y resultados
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState("");
  const [showResultsDialog, setShowResultsDialog] = useState(false);

  // Filtrar categorías en base a la búsqueda
  const filteredCategories = contentCategories.filter((category) => {
    if (!searchQuery) return true;

    const matchesCategory =
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.description.toLowerCase().includes(searchQuery.toLowerCase());

    if (matchesCategory) return true;

    // También buscar en subcategorías
    const subs = getSubcategoriesByParent(category.id);
    return subs.some(
      (sub) =>
        sub.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        sub.description.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  });

  // Manejar cambios en los campos del formulario
  const handleInputChange = (id: string, value: any) => {
    setFormValues((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  // Abrir dialog de subcategoría
  const handleSelectSubcategory = (category: any, subcategory: any) => {
    setSelectedCategory(category);
    setSelectedSubcategory(subcategory);
    setFormValues({});
    setShowSubcategoryDialog(true);
  };

  // Generar contenido basado en las respuestas del formulario
  const handleGenerateContent = async () => {
    // Verificar campos requeridos
    const questions = getQuestionsForSubcategory(selectedSubcategory.id);
    const requiredQuestions = questions.filter((q) => q.required);
    const missingRequired = requiredQuestions.filter((q) => !formValues[q.id]);

    if (missingRequired.length > 0) {
      alert(
        "Por favor completa todos los campos requeridos antes de continuar.",
      );
      return;
    }

    setIsGenerating(true);
    setShowResultsDialog(true);
    setShowSubcategoryDialog(false);

    // Construir el prompt a partir de las respuestas
    let prompt = `Genera un asset de tipo '${selectedSubcategory.name}' para marketing.\n`;
    questions.forEach((q) => {
      if (formValues[q.id]) {
        prompt += `\n${q.label}: ${formValues[q.id]}`;
      }
    });

    try {
      const API_BASE = import.meta.env.VITE_API_BASE || "http://localhost:8001";
      const response = await axios.post(`${API_BASE}/api/v1/content/generate`, {
        prompt,
        mode: "copy", // Puedes parametrizar el modo según la subcategoría si lo deseas
        user_id: null, // Si tienes user_id, pásalo aquí
      });
      if (response.data && response.data.asset) {
        setGeneratedContent(response.data.asset);
      } else {
        setGeneratedContent(
          "No se pudo generar el contenido. Respuesta inesperada del backend.",
        );
      }
    } catch (error: any) {
      setGeneratedContent(
        error?.response?.data?.detail ||
          error?.message ||
          "Error al conectar con el backend.",
      );
    } finally {
      setIsGenerating(false);
    }
  };

  // Renderizado de preguntas para la plantilla seleccionada
  const renderTemplateQuestions = () => {
    if (!selectedSubcategory) return null;

    const questions = getQuestionsForSubcategory(selectedSubcategory.id);

    if (questions.length === 0) {
      return (
        <div className="py-4 text-center text-muted-foreground">
          No hay preguntas disponibles para esta plantilla.
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {questions.map((question) => (
          <div key={question.id} className="space-y-2">
            <Label className="text-base font-semibold">
              {question.label}
              {question.required && (
                <span className="text-destructive ml-1">*</span>
              )}
            </Label>

            {question.description && (
              <p className="text-sm text-muted-foreground mb-2">
                {question.description}
              </p>
            )}

            {question.type === "text" && (
              <Input
                placeholder={question.placeholder}
                value={formValues[question.id] || ""}
                onChange={(e) => handleInputChange(question.id, e.target.value)}
              />
            )}

            {question.type === "textarea" && (
              <Textarea
                placeholder={question.placeholder}
                value={formValues[question.id] || ""}
                onChange={(e) => handleInputChange(question.id, e.target.value)}
                className="min-h-[100px]"
              />
            )}

            {question.type === "radio" && question.options && (
              <RadioGroup
                value={formValues[question.id] || ""}
                onValueChange={(value) => handleInputChange(question.id, value)}
              >
                <div className="space-y-2">
                  {question.options.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-2"
                    >
                      <RadioGroupItem
                        value={option.value}
                        id={`${question.id}-${option.value}`}
                      />
                      <Label htmlFor={`${question.id}-${option.value}`}>
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Renderiza todas las categorías en un grid
  const renderCategories = () => {
    if (filteredCategories.length === 0) {
      return (
        <div className="col-span-full py-12 text-center">
          <p className="text-xl text-muted-foreground">
            No se encontraron resultados para "{searchQuery}"
          </p>
          <Button variant="link" onClick={() => setSearchQuery("")}>
            Borrar búsqueda
          </Button>
        </div>
      );
    }

    return (
      <div
        className={
          viewMode === "grid"
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
        }
      >
        {filteredCategories.map((category) => {
          // Usar un componente de icono directo en lugar de renderIcon
          const IconComponent = (): React.ReactNode => {
            // Usamos un icono por defecto si no hay coincidencia
            return <FileText className="h-5 w-5 text-primary" />;
          };

          const subcategories = getSubcategoriesByParent(category.id);

          return (
            <Card
              key={category.id}
              className={viewMode === "list" ? "p-2" : ""}
            >
              <CardHeader className={viewMode === "list" ? "p-3" : ""}>
                <div className="flex justify-between items-start">
                  <div className="flex space-x-3 items-center">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <IconComponent />
                    </div>
                    <CardTitle
                      className={viewMode === "list" ? "text-lg" : "text-xl"}
                    >
                      {category.name}
                    </CardTitle>
                  </div>
                  {category.badge && (
                    <Badge variant="outline" className="text-xs px-2 py-0 h-5">
                      {category.badge}
                    </Badge>
                  )}
                </div>
                {viewMode === "grid" && (
                  <CardDescription className="mt-2">
                    {category.description}
                  </CardDescription>
                )}
              </CardHeader>

              <CardContent className={viewMode === "list" ? "p-3 pt-0" : ""}>
                {viewMode === "grid" ? (
                  <div className="space-y-2">
                    {subcategories.map((subcategory) => (
                      <div
                        key={subcategory.id}
                        className="flex justify-between items-center p-2 border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors cursor-pointer"
                        onClick={() =>
                          handleSelectSubcategory(category, subcategory)
                        }
                      >
                        <div>
                          <p className="font-medium">{subcategory.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {subcategory.description}
                          </p>
                        </div>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {subcategories.map((subcategory) => (
                      <Button
                        key={subcategory.id}
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleSelectSubcategory(category, subcategory)
                        }
                        className="text-xs h-7"
                      >
                        {subcategory.name}
                      </Button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  };

  return (
    <DashboardLayoutWrapper pageTitle="AI Content Hub">
      <div className="container py-8 max-w-7xl">
        <div className="space-y-8">
          {/* Cabecera */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold flex items-center justify-center">
              <Sparkles className="h-6 w-6 mr-2 text-primary" />
              AI Content Hub
            </h1>
            <p className="text-muted-foreground max-w-3xl mx-auto">
              Explora nuestras plantillas de contenido impulsadas por IA y
              genera rápidamente diversos tipos de contenido optimizado para tus
              campañas de marketing.
            </p>
            <div className="relative max-w-2xl mx-auto mt-6">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar por tipo de contenido, formato o plataforma..."
                className="pl-10 h-12"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-2"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Tabs de categorías principales */}
          <Tabs
            defaultValue="content"
            className="w-full"
            onValueChange={setActiveTabId}
          >
            <div className="flex justify-between items-center">
              <TabsList className="grid w-full max-w-md grid-cols-2">
                <TabsTrigger value="content">Tipos de Contenido</TabsTrigger>
                <TabsTrigger value="marketing">
                  Función de Marketing
                </TabsTrigger>
              </TabsList>

              <div className="flex">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  className="rounded-l-md rounded-r-none"
                  onClick={() => setViewMode("grid")}
                >
                  Grid
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  className="rounded-r-md rounded-l-none"
                  onClick={() => setViewMode("list")}
                >
                  Lista
                </Button>
              </div>
            </div>

            <TabsContent value="content" className="mt-6">
              {renderCategories()}
            </TabsContent>

            <TabsContent value="marketing" className="mt-6">
              <div className="p-8 text-center border rounded-md bg-muted/50">
                <h3 className="text-xl font-semibold mb-2">Próximamente</h3>
                <p className="text-muted-foreground">
                  Estamos trabajando en plantillas específicas por función de
                  marketing. Pronto podrás generar contenido optimizado para
                  cada etapa del funnel.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Dialog para la subcategoría seleccionada */}
        <Dialog
          open={showSubcategoryDialog}
          onOpenChange={setShowSubcategoryDialog}
        >
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="text-2xl">
                {selectedSubcategory?.name}
                {selectedCategory && (
                  <Badge variant="outline" className="ml-2">
                    {selectedCategory.name}
                  </Badge>
                )}
              </DialogTitle>
              <DialogDescription>
                {selectedSubcategory?.description}
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <h3 className="text-lg font-medium mb-4">
                Personaliza tu contenido
              </h3>
              {renderTemplateQuestions()}
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowSubcategoryDialog(false)}
              >
                Cancelar
              </Button>
              <Button onClick={handleGenerateContent}>
                <Sparkles className="mr-2 h-4 w-4" />
                Generar Contenido
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog para mostrar resultados */}
        <Dialog open={showResultsDialog} onOpenChange={setShowResultsDialog}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="text-2xl flex items-center">
                {isGenerating ? (
                  <>Generando contenido...</>
                ) : (
                  <>
                    <Check className="h-5 w-5 mr-2 text-green-500" />
                    Contenido generado
                  </>
                )}
              </DialogTitle>
              <DialogDescription>
                {isGenerating
                  ? "Nuestro equipo de IA está creando tu contenido personalizado"
                  : "Tu contenido ha sido generado con éxito"}
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              {isGenerating ? (
                <div className="text-center py-12">
                  <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-muted-foreground">
                    Esto puede tardar unos segundos...
                  </p>
                </div>
              ) : (
                <div className="border rounded-md p-4 bg-muted/50">
                  <pre className="whitespace-pre-wrap font-mono text-sm">
                    {generatedContent}
                  </pre>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowResultsDialog(false)}
                disabled={isGenerating}
              >
                Cerrar
              </Button>
              {!isGenerating && <Button>Guardar Contenido</Button>}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayoutWrapper>
  );
}
