import React, { useState, useEffect } from "react";
import Header from "../components/profesionales-ia/Header";
import Hero from "../components/profesionales-ia/Hero";
import WhatIsSection from "../components/profesionales-ia/WhatIsSection";
import Benefits from "../components/profesionales-ia/Benefits";
import FeaturedAgents from "../components/profesionales-ia/FeaturedAgents";

import CTASection from "../components/profesionales-ia/CTASection";
import Footer from "../components/profesionales-ia/Footer";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { useLanguage } from "@/contexts/LanguageContext";

const ProfesionalesIAContent: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [textIndex, setTextIndex] = useState(0);
  const { t } = useLanguage();

  // Manejar el scroll para cambiar el estilo del header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Cambiar el texto rotativo cada 3 segundos
  useEffect(() => {
    const rotatingTexts = t('profesionales_ia.rotating_texts') as string[];
    const interval = setInterval(() => {
      setTextIndex((prevIndex) => (prevIndex + 1) % rotatingTexts.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [t]);

  return (
    <div className="min-h-screen bg-white">
      <Header isScrolled={isScrolled} />
      <Hero textIndex={textIndex} />
      <WhatIsSection />
      <Benefits />
      <FeaturedAgents />

      <CTASection />
      <Footer />
    </div>
  );
};

const ProfesionalesIA: React.FC = () => {
  return (
    <LanguageProvider>
      <ProfesionalesIAContent />
    </LanguageProvider>
  );
};

export default ProfesionalesIA;
