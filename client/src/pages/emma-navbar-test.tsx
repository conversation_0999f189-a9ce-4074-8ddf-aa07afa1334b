import React from "react";
import { Emma<PERSON>avB<PERSON> } from "@/components/ui/emma-navbar";

export default function EmmaNavBarTest() {
  return (
    <div className="min-h-screen bg-gray-100">
      <EmmaNavBar />
      
      {/* Content to test scrolling */}
      <div className="pt-32 px-4">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-2xl font-bold mb-4">Emma NavBar Mobile Test</h1>
          <p className="text-gray-600 mb-4">
            This page is designed to test the EmmaNavBar mobile positioning. 
            The navbar should remain fixed at the top of the screen while scrolling.
          </p>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <h2 className="font-semibold text-red-800 mb-2">🚨 Critical Fix:</h2>
            <p className="text-sm text-red-700">
              The EmmaNavBar was appearing at the bottom on mobile devices due to 
              the "fixed bottom-0 sm:top-0" CSS classes. This has been fixed to 
              always position at the top.
            </p>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <h2 className="font-semibold text-blue-800 mb-2">Test Instructions:</h2>
            <ol className="list-decimal list-inside text-sm text-blue-700 space-y-1">
              <li>Open this page on a mobile device or use browser dev tools mobile view</li>
              <li>Scroll down through the content</li>
              <li>Verify the navbar stays at the top of the screen (not bottom)</li>
              <li>Test navigation by tapping the icons</li>
              <li>Rotate the device to test orientation changes</li>
            </ol>
          </div>
        </div>

        {/* Generate content for scrolling */}
        {Array.from({ length: 15 }, (_, i) => (
          <div key={i} className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 mb-6">
            <h3 className="text-lg font-semibold mb-2">Content Block {i + 1}</h3>
            <p className="text-gray-600 mb-4">
              This is test content to create a scrollable page. The EmmaNavBar should remain 
              fixed at the top while you scroll through this content.
            </p>
            <div className="bg-gray-50 rounded p-3">
              <p className="text-sm text-gray-500">
                Scroll position test content. The Emma AI navbar should be visible 
                at the top of the screen at all times on mobile devices, not at the bottom.
              </p>
            </div>
            
            {i === 7 && (
              <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-sm text-yellow-800 font-medium">
                  ⚠️ Halfway checkpoint: If you can see the navbar at the top 
                  (not bottom) while reading this, the fix is working!
                </p>
              </div>
            )}
          </div>
        ))}

        <div className="max-w-md mx-auto bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-green-800 mb-2">✅ Test Complete</h3>
          <p className="text-green-700 mb-2">
            If you can see the EmmaNavBar at the top of the screen while viewing this 
            message, the mobile navbar fix is working correctly!
          </p>
          <div className="bg-green-100 rounded p-3 mt-3">
            <p className="text-sm text-green-600">
              <strong>Expected behavior:</strong> The navbar should be at the top with 
              the Emma logo and navigation icons, not at the bottom of the screen.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
