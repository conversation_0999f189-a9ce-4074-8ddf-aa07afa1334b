import React from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { useLocation } from 'wouter';

export default function EmailCustomerServicePage() {
  const [, setLocation] = useLocation();

  const handleNavigation = (section: string) => {
    switch (section) {
      case 'home':
        setLocation('/dashboard/agents-marketplace');
        break;
      case 'agents':
        setLocation('/dashboard/agents-marketplace');
        break;
      case 'what-are-agents':
        setLocation('/dashboard/que-son-agentes');
        break;
      default:
        console.log(`Navigating to ${section}`);
    }
  };

  return (
    <DashboardLayout pageTitle="📧 Email Customer Service AI">
      <div className="relative flex min-h-screen flex-col bg-white overflow-x-hidden" style={{fontFamily: "'Space Grotesk', 'Noto Sans', sans-serif"}}>
        <div className="layout-container flex flex-col h-full grow">
          {/* Header */}
          <header className="flex items-center justify-between border-b border-[#f1f2f3] px-10 py-3">
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-4 text-[#131416]">
                <div className="size-4">
                  <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor"></path></svg>
                </div>
                <h2 className="text-lg font-bold tracking-[-0.015em] bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">Emma AI</h2>
              </div>
              <nav className="flex gap-9 text-sm font-medium">
                <button className="text-[#0e141b] hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('home')}>Inicio</button>
                <button className="text-[#0e141b] hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('agents')}>Agentes</button>
                <button className="text-[#0e141b] hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('what-are-agents')}>¿Qué son los Agentes?</button>
              </nav>
            </div>
            <div className="flex gap-8">
              <label className="flex min-w-40 max-w-64 h-10">
                <div className="flex flex-1 items-center bg-[#f1f2f3] rounded-xl">
                  <div className="pl-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                      <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z" />
                    </svg>
                  </div>
                  <input type="text" placeholder="Buscar agentes..." className="w-full px-4 bg-[#f1f2f3] rounded-xl focus:outline-none text-base placeholder-[#6b7680]" />
                </div>
              </label>
              <button
                className="h-10 px-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-full font-bold text-sm tracking-[0.015em] hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300"
                onClick={() => setLocation('/dashboard/agents-marketplace')}
              >
                Ver Agentes
              </button>
            </div>
          </header>

          {/* Main */}
          <main className="flex justify-center px-4 md:px-8 lg:px-40 py-5">
            <div className="flex flex-col max-w-[1200px] w-full">

              {/* Hero Header Grande */}
              <section className="relative overflow-hidden rounded-3xl mb-12 min-h-[500px]">
                <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef]/20 via-[#3018ef]/10 to-[#dd3a5a]/20"></div>
                <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

                <div className="relative p-8 md:p-16 flex items-center min-h-[500px]">
                  <div className="grid md:grid-cols-2 gap-12 items-center w-full">

                    {/* Lado Izquierdo - Info del Agente */}
                    <div>
                      <div className="inline-flex items-center gap-3 bg-white/90 backdrop-blur-md px-6 py-3 rounded-full border border-white/30 mb-6 shadow-lg">
                        <span className="text-2xl">📧</span>
                        <span className="text-lg font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">Email Customer Service AI</span>
                        <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-bold">ACTIVO</span>
                      </div>

                      <h1 className="text-4xl md:text-5xl font-black text-gray-900 mb-6 leading-tight">
                        Tu Asistente Virtual de
                        <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent"> Atención al Cliente</span>
                      </h1>

                      <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                        Responde emails automáticamente, organiza tu bandeja de entrada y mantiene a tus clientes satisfechos 24/7.
                        Todo profesional, todo inteligente.
                      </p>

                      {/* Stats Rápidas */}
                      <div className="grid grid-cols-3 gap-4 mb-8">
                        <div className="bg-white/80 backdrop-blur-md p-4 rounded-xl border border-white/20 text-center">
                          <div className="text-2xl font-black text-[#3018ef]">1000+</div>
                          <div className="text-xs text-gray-600">Emails/día</div>
                        </div>
                        <div className="bg-white/80 backdrop-blur-md p-4 rounded-xl border border-white/20 text-center">
                          <div className="text-2xl font-black text-[#dd3a5a]">30s</div>
                          <div className="text-xs text-gray-600">Tiempo respuesta</div>
                        </div>
                        <div className="bg-white/80 backdrop-blur-md p-4 rounded-xl border border-white/20 text-center">
                          <div className="text-2xl font-black text-green-600">97%</div>
                          <div className="text-xs text-gray-600">Satisfacción</div>
                        </div>
                      </div>

                      <button className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 transform">
                        Contratar Email Customer Service AI 📧
                      </button>
                    </div>

                    {/* Lado Derecho - Espacio para Imagen del Agente */}
                    <div className="relative">
                      <div className="bg-white/20 backdrop-blur-md rounded-3xl border border-white/30 p-8 h-[400px] flex items-center justify-center overflow-hidden">
                        <div className="relative w-full h-full">
                          <img
                            src="/agents/Roberto.png"
                            alt="Email Customer Service AI Agent"
                            className="w-full h-full object-cover rounded-2xl shadow-2xl"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                        </div>
                      </div>

                      {/* Elementos decorativos flotantes */}
                      <div className="absolute -top-4 -right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
                      <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-[#dd3a5a]/20 rounded-full blur-xl"></div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Before/After Comparison - Más Compacto */}
              <section className="mb-12">
                <div className="text-center mb-8">
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                    El Antes y Después de
                    <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent"> Email Customer Service AI</span>
                  </h2>
                  <p className="text-gray-600">Compara la atención manual vs la automatización inteligente</p>
                </div>

                <div className="grid md:grid-cols-2 gap-8 mb-8">
                  {/* Before - Sin Email Customer Service AI */}
                  <div className="relative group">
                    <div className="absolute inset-0 bg-red-500/10 rounded-2xl blur-xl opacity-50 group-hover:opacity-70 transition-opacity"></div>
                    <div className="relative bg-white/60 backdrop-blur-md p-8 rounded-2xl border border-red-200/50 hover:border-red-300/50 transition-all duration-300">
                      <div className="text-center mb-6">
                        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-3xl">😰</span>
                        </div>
                        <h3 className="text-2xl font-bold text-red-800 mb-2">Sin Email Customer Service AI</h3>
                        <p className="text-red-600 font-semibold">Atención manual tradicional</p>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center text-red-700">
                          <span className="w-2 h-2 bg-red-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Responder emails uno por uno manualmente</span>
                        </div>
                        <div className="flex items-center text-red-700">
                          <span className="w-2 h-2 bg-red-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Tiempo de respuesta de 2-24 horas</span>
                        </div>
                        <div className="flex items-center text-red-700">
                          <span className="w-2 h-2 bg-red-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Bandeja de entrada desorganizada</span>
                        </div>
                        <div className="flex items-center text-red-700">
                          <span className="w-2 h-2 bg-red-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Respuestas inconsistentes entre agentes</span>
                        </div>
                        <div className="flex items-center text-red-700">
                          <span className="w-2 h-2 bg-red-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Costo: $2,500/mes en salarios</span>
                        </div>
                      </div>
                      <div className="mt-6 p-4 bg-red-50 rounded-xl border border-red-200">
                        <p className="text-red-800 font-bold text-center">⏰ Resultado: Clientes insatisfechos</p>
                      </div>
                    </div>
                  </div>

                  {/* After - Con Email Customer Service AI */}
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-2xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity"></div>
                    <div className="relative bg-white/80 backdrop-blur-md p-8 rounded-2xl border border-[#3018ef]/20 hover:border-[#3018ef]/40 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2">
                      <div className="text-center mb-6">
                        <div className="w-16 h-16 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-3xl">🚀</span>
                        </div>
                        <h3 className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2">Con Email Customer Service AI</h3>
                        <p className="text-[#3018ef] font-semibold">Automatización inteligente</p>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center text-green-700">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Respuestas automáticas en 30 segundos</span>
                        </div>
                        <div className="flex items-center text-green-700">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Atención 24/7 sin interrupciones</span>
                        </div>
                        <div className="flex items-center text-green-700">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Organización inteligente por prioridad</span>
                        </div>
                        <div className="flex items-center text-green-700">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Respuestas consistentes y profesionales</span>
                        </div>
                        <div className="flex items-center text-green-700">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                          <span className="text-sm">Costo: $149/mes todo incluido</span>
                        </div>
                      </div>
                      <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-200">
                        <p className="text-green-800 font-bold text-center">✨ Resultado: 97% satisfacción del cliente</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* CTA Principal */}
                <div className="text-center mt-8">
                  <button className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-12 py-4 rounded-2xl font-bold text-xl hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 transform">
                    Contratar Email Customer Service AI Ahora 📧
                  </button>
                  <p className="text-gray-500 mt-4">Setup en 10 minutos • Respuestas desde el primer email</p>
                </div>
              </section>

              {/* Timeline Visual del Proceso - Más Compacto */}
              <section className="mb-12">
                <div className="text-center mb-8">
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                    ¿Cómo Funciona
                    <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent"> Email Customer Service AI</span>?
                  </h2>
                  <p className="text-gray-600">Proceso automatizado en 4 pasos simples</p>
                </div>

                <div className="relative max-w-4xl mx-auto">
                  {/* Timeline Line */}
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-gradient-to-b from-[#3018ef] to-[#dd3a5a] rounded-full opacity-40"></div>

                  <div className="space-y-8">
                    {/* Paso 1 */}
                    <div className="relative flex items-center">
                      <div className="w-1/2 pr-6 text-right">
                        <div className="bg-white/80 backdrop-blur-md p-4 rounded-xl border border-[#3018ef]/20 hover:border-[#3018ef]/40 transition-all duration-300 hover:shadow-lg">
                          <div className="flex items-center justify-end mb-2">
                            <div className="text-right mr-3">
                              <h3 className="text-lg font-bold text-gray-900">Análisis de Email</h3>
                              <p className="text-gray-600 text-sm">Examina cada consulta</p>
                            </div>
                            <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                              <span className="text-white text-lg">📧</span>
                            </div>
                          </div>
                          <p className="text-gray-700 text-xs">
                            Identifica el tipo de consulta, urgencia y sentimiento del cliente automáticamente.
                          </p>
                        </div>
                      </div>
                      <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full border-2 border-white shadow-md z-10"></div>
                      <div className="w-1/2 pl-6">
                        <div className="text-[#3018ef] font-bold">Paso 1</div>
                      </div>
                    </div>

                    {/* Paso 2 */}
                    <div className="relative flex items-center">
                      <div className="w-1/2 pr-6 text-right">
                        <div className="text-[#3018ef] font-bold">Paso 2</div>
                      </div>
                      <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full border-2 border-white shadow-md z-10"></div>
                      <div className="w-1/2 pl-6">
                        <div className="bg-white/80 backdrop-blur-md p-4 rounded-xl border border-[#dd3a5a]/20 hover:border-[#dd3a5a]/40 transition-all duration-300 hover:shadow-lg">
                          <div className="flex items-center mb-2">
                            <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center mr-3">
                              <span className="text-white text-lg">🔍</span>
                            </div>
                            <div>
                              <h3 className="text-lg font-bold text-gray-900">Búsqueda en Base de Conocimiento</h3>
                              <p className="text-gray-600 text-sm">Consulta información actualizada</p>
                            </div>
                          </div>
                          <p className="text-gray-700 text-xs">
                            Busca en la base de datos de tu marca, productos y políticas para generar respuestas precisas.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Paso 3 */}
                    <div className="relative flex items-center">
                      <div className="w-1/2 pr-6 text-right">
                        <div className="bg-white/80 backdrop-blur-md p-4 rounded-xl border border-[#3018ef]/20 hover:border-[#3018ef]/40 transition-all duration-300 hover:shadow-lg">
                          <div className="flex items-center justify-end mb-2">
                            <div className="text-right mr-3">
                              <h3 className="text-lg font-bold text-gray-900">Respuesta Personalizada</h3>
                              <p className="text-gray-600 text-sm">Genera respuesta profesional</p>
                            </div>
                            <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                              <span className="text-white text-lg">✍️</span>
                            </div>
                          </div>
                          <p className="text-gray-700 text-xs">
                            Crea respuestas personalizadas manteniendo el tono de tu marca y resolviendo la consulta específica.
                          </p>
                        </div>
                      </div>
                      <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full border-2 border-white shadow-md z-10"></div>
                      <div className="w-1/2 pl-6">
                        <div className="text-[#3018ef] font-bold">Paso 3</div>
                      </div>
                    </div>

                    {/* Paso 4 */}
                    <div className="relative flex items-center">
                      <div className="w-1/2 pr-6 text-right">
                        <div className="text-[#3018ef] font-bold">Paso 4</div>
                      </div>
                      <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full border-2 border-white shadow-md z-10"></div>
                      <div className="w-1/2 pl-6">
                        <div className="bg-white/80 backdrop-blur-md p-4 rounded-xl border border-[#dd3a5a]/20 hover:border-[#dd3a5a]/40 transition-all duration-300 hover:shadow-lg">
                          <div className="flex items-center mb-2">
                            <div className="w-10 h-10 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center mr-3">
                              <span className="text-white text-lg">📋</span>
                            </div>
                            <div>
                              <h3 className="text-lg font-bold text-gray-900">Organización y Seguimiento</h3>
                              <p className="text-gray-600 text-sm">Gestión inteligente de casos</p>
                            </div>
                          </div>
                          <p className="text-gray-700 text-xs">
                            Organiza emails por categorías, prioridades y realiza seguimiento automático de casos pendientes.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* About Section */}
              <section className="px-4 pt-5">
                <h2 className="text-[22px] font-bold tracking-[-0.015em] pb-3">Acerca de Email Customer Service AI</h2>
                <p className="text-base pb-3">
                  Email Customer Service AI es tu asistente virtual especializado en transformar tu bandeja de entrada en un centro de atención al cliente eficiente y profesional. Este agente inteligente analiza cada email entrante, identifica el tipo de consulta y proporciona respuestas personalizadas y precisas sobre tu marca, productos y servicios. Desde consultas sobre precios hasta problemas técnicos básicos, este asistente mantiene a tus clientes satisfechos mientras libera tiempo valioso de tu equipo.
                </p>
              </section>

              {/* Key Features */}
              <section className="px-4 pt-5">
                <h2 className="text-[22px] font-bold tracking-[-0.015em] pb-3">Características Principales</h2>
                <div className="grid grid-cols-[repeat(auto-fit,minmax(200px,1fr))] gap-4">
                  <div className="p-4 border border-[#dee0e3] rounded-lg">
                    <h3 className="font-bold text-base mb-2">📧 Respuesta Automática</h3>
                    <p className="text-sm text-[#6b7680]">Responde automáticamente a consultas de clientes con respuestas personalizadas y profesionales.</p>
                  </div>
                  <div className="p-4 border border-[#dee0e3] rounded-lg">
                    <h3 className="font-bold text-base mb-2">📋 Organización Inteligente</h3>
                    <p className="text-sm text-[#6b7680]">Clasifica y organiza emails por prioridad, tipo de consulta y urgencia automáticamente.</p>
                  </div>
                  <div className="p-4 border border-[#dee0e3] rounded-lg">
                    <h3 className="font-bold text-base mb-2">🧠 Base de Conocimiento</h3>
                    <p className="text-sm text-[#6b7680]">Utiliza información actualizada de tu marca y productos para respuestas precisas.</p>
                  </div>
                  <div className="p-4 border border-[#dee0e3] rounded-lg">
                    <h3 className="font-bold text-base mb-2">🔄 Seguimiento Automático</h3>
                    <p className="text-sm text-[#6b7680]">Realiza seguimiento de casos pendientes y escala consultas complejas al equipo humano.</p>
                  </div>
                </div>
              </section>

              {/* Pricing */}
              <section className="px-4 pt-5">
                <h2 className="text-[22px] font-bold tracking-[-0.015em] pb-3">Precios</h2>
                <details className="rounded-xl border border-[#dee0e3] px-[15px] py-[7px]" open>
                  <summary className="cursor-pointer py-2 flex justify-between items-center">
                    <p className="text-sm font-medium">Ver Detalles de Precios</p>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256"><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z" /></svg>
                  </summary>
                  <div className="text-sm text-[#6b7680] pb-2 space-y-2">
                    <p><strong>Plan Básico:</strong> $149/mes - Hasta 500 emails/día</p>
                    <p><strong>Plan Pro:</strong> $299/mes - Hasta 2,000 emails/día</p>
                    <p><strong>Plan Enterprise:</strong> $599/mes - Emails ilimitados + CRM integrado</p>
                  </div>
                </details>
              </section>

              {/* Testimonials */}
              <section className="px-4 pt-5">
                <h2 className="text-[22px] font-bold tracking-[-0.015em] pb-3">Testimonios</h2>
                <div className="flex flex-col gap-4">
                  <div className="p-4 border border-[#dee0e3] rounded-lg">
                    <p className="text-sm text-[#6b7680] mb-2">"Email Customer Service AI transformó nuestra atención al cliente. Ahora respondemos en segundos y nuestros clientes están más satisfechos que nunca."</p>
                    <p className="text-xs font-bold">- Ana Martínez, Directora de Atención al Cliente</p>
                  </div>
                  <div className="p-4 border border-[#dee0e3] rounded-lg">
                    <p className="text-sm text-[#6b7680] mb-2">"Liberamos 30 horas semanales de nuestro equipo y aumentamos la satisfacción del cliente al 97%. Una inversión que se paga sola."</p>
                    <p className="text-xs font-bold">- Roberto Silva, CEO de TechSupport Pro</p>
                  </div>
                </div>
              </section>
            </div>
          </main>
        </div>
      </div>
    </DashboardLayout>
  );
}
