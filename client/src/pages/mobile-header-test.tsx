import React from "react";
import Header from "@/components/landing/header";

export default function MobileHeaderTest() {
  return (
    <div className="min-h-screen bg-gray-100">
      <Header />
      
      {/* Content to test scrolling */}
      <div className="pt-32 px-4">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-2xl font-bold mb-4">Mobile Header Test</h1>
          <p className="text-gray-600 mb-4">
            This page is designed to test the mobile header positioning. 
            The header should remain fixed at the top of the screen while scrolling.
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <h2 className="font-semibold text-blue-800 mb-2">Test Instructions:</h2>
            <ol className="list-decimal list-inside text-sm text-blue-700 space-y-1">
              <li>Open this page on a mobile device or use browser dev tools mobile view</li>
              <li>Scroll down through the content</li>
              <li>Verify the header stays at the top of the screen</li>
              <li>Test the mobile menu by tapping the hamburger button</li>
              <li>Rotate the device to test orientation changes</li>
            </ol>
          </div>
        </div>

        {/* Generate content for scrolling */}
        {Array.from({ length: 20 }, (_, i) => (
          <div key={i} className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 mb-6">
            <h3 className="text-lg font-semibold mb-2">Content Block {i + 1}</h3>
            <p className="text-gray-600 mb-4">
              This is test content to create a scrollable page. The header should remain 
              fixed at the top while you scroll through this content.
            </p>
            <div className="bg-gray-50 rounded p-3">
              <p className="text-sm text-gray-500">
                Scroll position test content. The Emma AI header should be visible 
                at the top of the screen at all times on mobile devices.
              </p>
            </div>
          </div>
        ))}

        <div className="max-w-md mx-auto bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-green-800 mb-2">✅ Test Complete</h3>
          <p className="text-green-700">
            If you can see the header at the top of the screen while viewing this 
            message, the mobile header fix is working correctly!
          </p>
        </div>
      </div>
    </div>
  );
}
