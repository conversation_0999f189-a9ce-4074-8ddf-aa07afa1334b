import React from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { useLocation } from 'wouter';

export default function AgentsMarketplacePage() {
  const [, setLocation] = useLocation();

  const handleAgentClick = (agentPath: string) => {
    setLocation(agentPath);
  };

  const handleNavigation = (section: string) => {
    if (section === 'home') {
      setLocation('/dashboard');
    } else if (section === 'agents') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else if (section === 'services') {
      setLocation('/dashboard/herramientas-marketing');
    } else if (section === 'resources') {
      setLocation('/dashboard/emma-ai');
    }
  };

  return (
    <DashboardLayout pageTitle="Marketplace de Agentes IA">
      <div className="relative flex size-full min-h-screen flex-col bg-slate-50 group/design-root overflow-x-hidden" style={{fontFamily: '"Space Grotesk", "Noto Sans", sans-serif'}}>
        <div className="layout-container flex h-full grow flex-col">
          {/* Header */}
          <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e7edf3] px-10 py-3">
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-4 text-[#0e141b]">
                <div className="size-4">
                  <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor"></path></svg>
                </div>
                <h2 className="text-[#0e141b] text-lg font-bold leading-tight tracking-[-0.015em] bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">Emma AI</h2>
              </div>
              <div className="flex items-center gap-9">
                <button className="text-[#0e141b] text-sm font-medium leading-normal cursor-pointer hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('home')}>Inicio</button>
                <button className="text-[#0e141b] text-sm font-medium leading-normal cursor-pointer hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('agents')}>Agentes</button>
                <button className="text-[#0e141b] text-sm font-medium leading-normal cursor-pointer hover:text-[#3018ef] transition-colors" onClick={() => setLocation('/dashboard/que-son-agentes')}>¿Qué son los Agentes?</button>
              </div>
            </div>
            <div className="flex flex-1 justify-end gap-8">
              <label className="flex flex-col min-w-40 !h-10 max-w-64">
                <div className="flex w-full flex-1 items-stretch rounded-lg h-full">
                  <div className="text-[#4e7397] flex border-none bg-[#e7edf3] items-center justify-center pl-4 rounded-l-lg border-r-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
                    </svg>
                  </div>
                  <input
                    placeholder="Buscar"
                    className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#0e141b] focus:outline-0 focus:ring-0 border-none bg-[#e7edf3] focus:border-none h-full placeholder:text-[#4e7397] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                  />
                </div>
              </label>
              <button
                className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white text-sm font-bold leading-normal tracking-[0.015em] hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300"
                onClick={() => setLocation('/dashboard/agents/hunter-pro')}
              >
                <span className="truncate">Comenzar</span>
              </button>
            </div>
          </header>

          {/* Hero Section */}
          <div className="@container">
            <div className="@[480px]:p-4">
              <div
                className="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-lg items-center justify-center p-4"
                style={{backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("/Herramientas.png")', backgroundPosition: 'center center'}}
              >
                <div className="flex flex-col gap-2 text-center">
                  <h1 className="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]">
                    Revoluciona tu Marketing con Agentes IA
                  </h1>
                  <h2 className="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal">
                    Explora una nueva frontera del marketing con nuestros agentes IA de vanguardia, diseñados para elevar tus estrategias y generar un crecimiento sin precedentes.
                  </h2>
                </div>
                <button
                  className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em]"
                  onClick={() => setLocation('/dashboard/agents/hunter-pro')}
                >
                  <span className="truncate">Descubrir Agentes</span>
                </button>
              </div>
            </div>
          </div>

          {/* Contenido Principal */}
          <div className="px-40 flex flex-1 justify-center py-5">
            <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
              {/* Barra de Búsqueda Principal */}
              <div className="px-4 py-3">
                <label className="flex flex-col min-w-40 h-12 w-full">
                  <div className="flex w-full flex-1 items-stretch rounded-lg h-full">
                    <div className="text-[#4e7397] flex border-none bg-[#e7edf3] items-center justify-center pl-4 rounded-l-lg border-r-0">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                        <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
                      </svg>
                    </div>
                    <input
                      placeholder="Buscar Agentes IA"
                      className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#0e141b] focus:outline-0 focus:ring-0 border-none bg-[#e7edf3] focus:border-none h-full placeholder:text-[#4e7397] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                    />
                  </div>
                </label>
              </div>

              {/* Agentes Destacados */}
              <h2 className="text-[#0e141b] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">🌟 Agentes Destacados</h2>
              <div className="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
                <div className="flex items-stretch p-4 gap-3">
                  {/* Hunter Pro Card */}
                  <div className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60 cursor-pointer" onClick={() => handleAgentClick('/dashboard/agents/hunter-pro')}>
                    <div
                      className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col relative overflow-hidden"
                      style={{backgroundImage: 'url("/agents/María.png")'}}
                    >
                      <div className="absolute top-2 left-2">
                        <div className="bg-[#3018ef] text-white px-3 py-1 rounded-full text-xs font-bold">
                          MÁS CONTRATADA
                        </div>
                      </div>
                    </div>
                    <div>
                      <p className="text-[#0e141b] text-base font-medium leading-normal">María González</p>
                      <p className="text-[#3018ef] text-xs font-medium leading-normal mb-1">Especialista en Prospección Digital</p>
                      <p className="text-[#4e7397] text-sm font-normal leading-normal">Encuentra correos empresariales y ejecuta campañas de outreach que generan respuestas.</p>
                    </div>
                  </div>
                  {/* Calendar Bot Card */}
                  <div className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60 cursor-pointer" onClick={() => handleAgentClick('/dashboard/agents/calendar-bot')}>
                    <div
                      className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col relative overflow-hidden"
                      style={{backgroundImage: 'url("/agents/Diego.png")'}}
                    >
                      <div className="absolute top-2 left-2">
                        <div className="bg-[#dd3a5a] text-white px-3 py-1 rounded-full text-xs font-bold">
                          AUTOMATIZACIÓN
                        </div>
                      </div>
                    </div>
                    <div>
                      <p className="text-[#0e141b] text-base font-medium leading-normal">Diego Morales</p>
                      <p className="text-[#3018ef] text-xs font-medium leading-normal mb-1">Coordinador de Citas y Reuniones</p>
                      <p className="text-[#4e7397] text-sm font-normal leading-normal">Gestiona tu calendario y agenda reuniones comerciales automáticamente.</p>
                    </div>
                  </div>
                  {/* Community Manager AI Card - Now using Sales Support agent */}
                  <div className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60 cursor-pointer" onClick={() => handleAgentClick('/dashboard/agents/community-manager-ai')}>
                    <div
                      className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col relative overflow-hidden"
                      style={{backgroundImage: 'url("/agents/Andrés.png")'}}
                    >
                      <div className="absolute top-2 left-2">
                        <div className="bg-[#3018ef] text-white px-3 py-1 rounded-full text-xs font-bold">
                          SOCIAL MEDIA
                        </div>
                      </div>
                    </div>
                    <div>
                      <p className="text-[#0e141b] text-base font-medium leading-normal">Andrés Herrera</p>
                      <p className="text-[#3018ef] text-xs font-medium leading-normal mb-1">Community Manager Digital</p>
                      <p className="text-[#4e7397] text-sm font-normal leading-normal">Gestiona tus redes sociales y brinda soporte a tu comunidad 24/7.</p>
                    </div>
                  </div>
                  {/* Email Customer Service AI Card */}
                  <div className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60 cursor-pointer" onClick={() => handleAgentClick('/dashboard/agents/email-customer-service')}>
                    <div
                      className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col relative overflow-hidden"
                      style={{backgroundImage: 'url("/agents/Roberto.png")'}}
                    >
                      <div className="absolute top-2 left-2">
                        <div className="bg-[#dd3a5a] text-white px-3 py-1 rounded-full text-xs font-bold">
                          EMAIL
                        </div>
                      </div>
                    </div>
                    <div>
                      <p className="text-[#0e141b] text-base font-medium leading-normal">Roberto Silva</p>
                      <p className="text-[#3018ef] text-xs font-medium leading-normal mb-1">Especialista en Atención al Cliente</p>
                      <p className="text-[#4e7397] text-sm font-normal leading-normal">Responde emails de clientes y organiza la bandeja de entrada por prioridades.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Catálogo Completo - Botón */}
              <div className="px-4 pt-5 pb-3">
                <div className="flex items-center justify-between">
                  <h2 className="text-[#0e141b] text-[22px] font-bold leading-tight tracking-[-0.015em]">👥 Equipo Completo</h2>
                  <button
                    className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-6 py-2 rounded-full font-bold text-sm tracking-[0.015em] hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300"
                    onClick={() => setLocation('/dashboard/agents-catalog')}
                  >
                    Ver Todos los Empleados →
                  </button>
                </div>
                <p className="text-[#4e7397] text-sm mt-2">Conoce a todo nuestro equipo de especialistas digitales disponibles para contratar</p>
              </div>
              <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
                {/* Lead Agent Card */}
                <div className="flex flex-col gap-3 pb-3 cursor-pointer" onClick={() => handleAgentClick('/dashboard/agents/lead-agent')}>
                  <div
                    className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg relative overflow-hidden"
                    style={{backgroundImage: 'url("/agents/Miguel.png")'}}
                  >
                    <div className="absolute top-2 left-2">
                      <div className="bg-orange-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                        WHATSAPP
                      </div>
                    </div>
                  </div>
                  <div>
                    <p className="text-[#0e141b] text-base font-medium leading-normal">Miguel Torres</p>
                    <p className="text-[#3018ef] text-xs font-medium leading-normal mb-1">Ejecutivo de Ventas WhatsApp</p>
                    <p className="text-[#4e7397] text-sm font-normal leading-normal">Atiende leads por WhatsApp y pre-califica oportunidades de venta.</p>
                  </div>
                </div>
                {/* Sales Support Card - Now using Calendar Bot agent */}
                <div className="flex flex-col gap-3 pb-3 cursor-pointer" onClick={() => handleAgentClick('/dashboard/agents/sales-support')}>
                  <div
                    className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg relative overflow-hidden"
                    style={{backgroundImage: 'url("/agents/Laura.png")'}}
                  >
                    <div className="absolute top-2 left-2">
                      <div className="bg-indigo-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                        LLAMADAS
                      </div>
                    </div>
                  </div>
                  <div>
                    <p className="text-[#0e141b] text-base font-medium leading-normal">Laura Vásquez</p>
                    <p className="text-[#3018ef] text-xs font-medium leading-normal mb-1">Representante de Ventas Telefónicas</p>
                    <p className="text-[#4e7397] text-sm font-normal leading-normal">Maneja llamadas entrantes y ejecuta campañas de cold calling.</p>
                  </div>
                </div>
                {/* Email Customer Service AI Card */}
                <div className="flex flex-col gap-3 pb-3 cursor-pointer" onClick={() => handleAgentClick('/dashboard/agents/email-customer-service')}>
                  <div
                    className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg relative overflow-hidden"
                    style={{backgroundImage: 'url("/agents/Roberto.png")'}}
                  >
                    <div className="absolute top-2 left-2">
                      <div className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                        EMAIL
                      </div>
                    </div>
                  </div>
                  <div>
                    <p className="text-[#0e141b] text-base font-medium leading-normal">Roberto Silva</p>
                    <p className="text-[#3018ef] text-xs font-medium leading-normal mb-1">Especialista en Atención al Cliente</p>
                    <p className="text-[#4e7397] text-sm font-normal leading-normal">Responde emails automáticamente y organiza tu bandeja de entrada.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <footer className="flex justify-center">
            <div className="flex max-w-[960px] flex-1 flex-col">
              <footer className="flex flex-col gap-6 px-5 py-10 text-center @container">
                <div className="flex flex-wrap items-center justify-center gap-6 @[480px]:flex-row @[480px]:justify-around">
                  <a className="text-[#4e7397] text-base font-normal leading-normal min-w-40" href="#" onClick={() => setLocation('/dashboard/que-son-agentes')}>¿Qué son los Agentes?</a>
                  <a className="text-[#4e7397] text-base font-normal leading-normal min-w-40" href="#" onClick={() => setLocation('/dashboard')}>Términos de Servicio</a>
                  <a className="text-[#4e7397] text-base font-normal leading-normal min-w-40" href="#" onClick={() => setLocation('/dashboard')}>Política de Privacidad</a>
                  <a className="text-[#4e7397] text-base font-normal leading-normal min-w-40" href="#" onClick={() => setLocation('/dashboard')}>Contacto</a>
                </div>
                <p className="text-[#4e7397] text-base font-normal leading-normal">© 2024 Emma AI. Todos los derechos reservados.</p>
              </footer>
            </div>
          </footer>
        </div>
      </div>
    </DashboardLayout>
  );
}
