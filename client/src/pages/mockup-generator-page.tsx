import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Upload,
  Package,
  Sparkles,
  Download,
  Eye,
  Loader2,
  Camera,
  User,
  Palette,
  Settings,
  Grid3X3,
  Zap,
  Star,
  ChevronRight
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import DashboardLayout from '@/components/layout/dashboard-layout';
import {
  getMockupContexts,
  generateMockup,
  getContextSuggestions,
  validateImageFile,
  createImagePreview,
  cleanupImagePreview,
  downloadMockup,
  generatePortrait,
  type MockupContext,
  type EnhancedMockupContext,
  type MockupGenerationResult,
  type MockupVariation,
  type ContextSuggestionsResponse,
  type PortraitGenerationOptions,
  type PortraitGenerationResult
} from '@/services/mockup-service';
import { MockupMiniEditor } from '@/components/tools/mockup-generator/MockupMiniEditor';
import { PromptEnhancer } from '@/components/tools/mockup-generator/PromptEnhancer';
import { openMockupInPolotno, createMockupProject } from '@/services/mockup-polotno-integration';

// Mode types for the dual-mode system
type GenerationMode = 'product' | 'portrait';



const MockupGeneratorPage: React.FC = () => {
  // Mode state
  const [mode, setMode] = useState<GenerationMode>('product');

  // Product mode states
  const [productImage, setProductImage] = useState<File | null>(null);
  const [productDescription, setProductDescription] = useState('');
  const [selectedContext, setSelectedContext] = useState<string>('');
  const [availableContexts, setAvailableContexts] = useState<Record<string, MockupContext>>({});
  const [suggestedContexts, setSuggestedContexts] = useState<Record<string, EnhancedMockupContext>>({});
  const [suggestions, setSuggestions] = useState<ContextSuggestionsResponse | null>(null);

  // Portrait mode states
  const [portraitPrompt, setPortraitPrompt] = useState('');

  // Shared states
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [generatedMockup, setGeneratedMockup] = useState<MockupGenerationResult | null>(null);
  const [generatedPortrait, setGeneratedPortrait] = useState<PortraitGenerationResult | null>(null);
  const [selectedVariation, setSelectedVariation] = useState<number>(0);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [generationProgress, setGenerationProgress] = useState<string>('');

  // New feature states
  const [editingMockup, setEditingMockup] = useState<any>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);


  const { toast } = useToast();

  // Predefined contexts (fallback if API fails)
  const defaultContexts: Record<string, MockupContext> = {
    hands: {
      name: 'En Manos de Usuario',
      description: 'Producto siendo usado por una persona'
    },
    desk: {
      name: 'En Escritorio',
      description: 'Producto en un ambiente de trabajo profesional'
    },
    lifestyle: {
      name: 'Estilo de Vida',
      description: 'Producto en contexto de uso cotidiano'
    },
    outdoor: {
      name: 'Ambiente Exterior',
      description: 'Producto en contexto al aire libre'
    },
    studio: {
      name: 'Estudio Profesional',
      description: 'Producto en estudio con iluminación profesional'
    },
    social: {
      name: 'Contexto Social',
      description: 'Producto siendo compartido o usado en grupo'
    }
  };

  React.useEffect(() => {
    // Load available contexts from API
    loadContexts();
  }, []);

  // Cleanup preview URL on unmount
  React.useEffect(() => {
    return () => {
      if (previewUrl) {
        cleanupImagePreview(previewUrl);
      }
    };
  }, [previewUrl]);

  // Load suggestions when product description changes
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (productDescription.trim().length > 10) {
        loadSuggestions(productDescription);
      }
    }, 1000); // Debounce for 1 second

    return () => clearTimeout(timeoutId);
  }, [productDescription]);

  const loadContexts = async () => {
    try {
      const result = await getMockupContexts();
      if (result.success && result.contexts) {
        setAvailableContexts(result.contexts);
      } else {
        setAvailableContexts(defaultContexts);
      }
    } catch (error) {
      console.error('Error loading contexts:', error);
      setAvailableContexts(defaultContexts);
    }
  };

  const loadSuggestions = async (description: string) => {
    if (!description.trim()) {
      setSuggestedContexts({});
      setSuggestions(null);
      return;
    }

    setIsLoadingSuggestions(true);
    try {
      const result = await getContextSuggestions(description);
      if (result.success) {
        setSuggestedContexts(result.suggested_contexts || {});
        setSuggestions(result);

        // Auto-select top recommendation if available
        if (result.analysis?.top_recommendation && !selectedContext) {
          setSelectedContext(result.analysis.top_recommendation);
        }
      }
    } catch (error) {
      console.error('Error loading suggestions:', error);
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validation = validateImageFile(file);
      if (validation.valid) {
        // Clean up previous preview
        if (previewUrl) {
          cleanupImagePreview(previewUrl);
        }

        setProductImage(file);
        // Create preview URL
        const url = createImagePreview(file);
        setPreviewUrl(url);
      } else {
        toast({
          title: "Error",
          description: validation.error || "Archivo de imagen inválido",
          variant: "destructive"
        });
      }
    }
  };

  const handleGenerateMockup = async () => {
    if (!productDescription.trim()) {
      toast({
        title: "Error",
        description: "Por favor describe tu producto o el mockup que quieres crear",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setGeneratedMockup(null);
    setGenerationProgress('Preparing your product mockup...');

    try {
      // Si no hay contexto seleccionado, usar el mejor sugerido o 'lifestyle' por defecto
      const contextToUse = selectedContext ||
                          (suggestions?.analysis?.top_recommendation) ||
                          'lifestyle';

      setGenerationProgress('Generating professional mockup with Emma AI...');
      const result = await generateMockup({
        productImage: productImage || undefined,
        context: contextToUse,
        productDescription,
        size: '1024x1024',
        variations: 4
      });

      if (result.success && result.variations && result.variations.length > 0) {
        setGeneratedMockup(result);
        setSelectedVariation(0); // Select first variation by default
        toast({
          title: "¡Mockups generados!",
          description: `${result.total_generated} variaciones profesionales creadas con tecnología Emma AI`,
        });
      } else {
        throw new Error(result.error || 'Error generando mockup');
      }
    } catch (error) {
      console.error('Error generating mockup:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error generando mockup",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress('');
    }
  };

  const handleDownloadMockup = (variationIndex?: number) => {
    const variation = generatedMockup?.variations?.[variationIndex ?? selectedVariation];
    if (variation?.image_url) {
      const filename = `mockup-${selectedContext}-variation-${(variationIndex ?? selectedVariation) + 1}-${Date.now()}.png`;
      downloadMockup(variation.image_url, filename);
    }
  };

  const handleGeneratePortrait = async () => {
    if (!portraitPrompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a portrait description",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setGeneratedPortrait(null);
    setGenerationProgress('Creating realistic portrait with AI...');

    try {
      const result = await generatePortrait({
        prompt: portraitPrompt,
        gender: 'any',
        age: 'adult',
        ethnicity: 'any',
        style: 'professional',
        lighting: 'studio',
        size: '1024x1024',
        variations: 4
      });

      if (result.success && result.variations && result.variations.length > 0) {
        setGeneratedPortrait(result);
        setSelectedVariation(0); // Select first variation by default
        toast({
          title: "Portrait Generated!",
          description: `${result.total_generated} realistic portraits created with Emma AI technology`,
        });
      } else {
        throw new Error(result.error || 'Error generating portrait');
      }
    } catch (error) {
      console.error('Portrait generation error:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate portrait. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress('');
    }
  };

  // New feature handlers
  const handleEditMockup = (mockup: any) => {
    console.log('🎨 Opening mockup editor with:', mockup);
    setEditingMockup(mockup);
    setIsEditorOpen(true);
  };

  const handleSaveEditedMockup = (editedMockup: any) => {
    // Update the mockup in the results
    if (generatedMockup?.variations) {
      const updatedVariations = generatedMockup.variations.map(variation =>
        variation.id === editedMockup.metadata?.originalId ? editedMockup : variation
      );
      setGeneratedMockup({
        ...generatedMockup,
        variations: updatedVariations
      });
    }

    if (generatedPortrait?.variations) {
      const updatedVariations = generatedPortrait.variations.map(variation =>
        variation.id === editedMockup.metadata?.originalId ? editedMockup : variation
      );
      setGeneratedPortrait({
        ...generatedPortrait,
        variations: updatedVariations
      });
    }

    setIsEditorOpen(false);
    setEditingMockup(null);
  };

  const handleOpenInPolotno = async (mockup: any) => {
    try {
      const result = await openMockupInPolotno({
        mockupId: mockup.id || `mockup-${Date.now()}`,
        imageUrl: mockup.image_url,
        mockupType: mode,
        platform: 'mockup-generator',
        metadata: mockup.metadata
      });

      if (result.success) {
        window.open(result.editorUrl, '_blank', 'noopener,noreferrer');
        toast({
          title: "¡Editor abierto!",
          description: "El mockup se ha abierto en el editor profesional",
        });
      } else {
        throw new Error(result.error || 'Error abriendo editor');
      }
    } catch (error) {
      console.error('Error opening in Polotno:', error);
      toast({
        title: "Error",
        description: "No se pudo abrir el editor profesional",
        variant: "destructive"
      });
    }
  };

  const handleCreateProject = async () => {
    const mockups = mode === 'product' ? generatedMockup?.variations : generatedPortrait?.variations;
    if (!mockups || mockups.length === 0) return;

    try {
      const projectName = `${mode}-mockups-${Date.now()}`;
      const result = await createMockupProject(mockups, projectName, mode);

      if (result.success) {
        window.open(result.editorUrl, '_blank', 'noopener,noreferrer');
        toast({
          title: "¡Proyecto creado!",
          description: `Proyecto con ${mockups.length} mockups creado en el editor`,
        });
      } else {
        throw new Error(result.error || 'Error creando proyecto');
      }
    } catch (error) {
      console.error('Error creating project:', error);
      toast({
        title: "Error",
        description: "No se pudo crear el proyecto",
        variant: "destructive"
      });
    }
  };

  const handleDownloadAll = () => {
    if (generatedMockup?.variations) {
      generatedMockup.variations.forEach((variation, index) => {
        if (variation.image_url) {
          setTimeout(() => {
            const filename = `mockup-${selectedContext}-variation-${index + 1}-${Date.now()}.png`;
            downloadMockup(variation.image_url!, filename);
          }, index * 500); // Stagger downloads
        }
      });
    }
  };

  return (
    <DashboardLayout pageTitle="Emma Studio">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        {/* Photography Studio Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative overflow-hidden bg-white border-b border-slate-200"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 opacity-50" />
          <div className="relative max-w-7xl mx-auto px-6 py-12">
            <div className="text-center">
              <div className="flex items-center justify-center gap-4 mb-6">
                <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl shadow-lg">
                  <Camera className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent tracking-tight">
                  Emma Studio
                </h1>
                <Badge variant="secondary" className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0">
                  Professional
                </Badge>
              </div>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-medium">
                Estudio de fotografía profesional con IA para crear mockups de productos impresionantes y retratos realistas
              </p>
            </div>
          </div>
        </motion.div>

        {/* Mode Selection */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Tabs value={mode} onValueChange={(value) => setMode(value as GenerationMode)} className="w-full">
              <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 mb-8 bg-slate-100 p-1 rounded-xl">
                <TabsTrigger
                  value="product"
                  className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg py-3 font-semibold"
                >
                  <Package className="h-4 w-4" />
                  Mockups de Productos
                </TabsTrigger>
                <TabsTrigger
                  value="portrait"
                  className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg py-3 font-semibold"
                >
                  <User className="h-4 w-4" />
                  Modo Retrato
                </TabsTrigger>
              </TabsList>

              {/* Product Mode Content */}
              <TabsContent value="product" className="space-y-8">
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                  {/* Left Panel - Configuration */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="xl:col-span-2 space-y-6"
                  >
                    {/* Product Image Upload */}
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-3 text-slate-800 font-bold">
                          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                            <Upload className="h-5 w-5 text-white" />
                          </div>
                          Imagen del Producto (Opcional)
                        </CardTitle>
                        <CardDescription className="text-slate-600 font-medium">
                          Sube una imagen de tu producto para que aparezca integrada en el mockup, o déjalo vacío para mockups genéricos
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="relative border-2 border-dashed border-slate-300 rounded-xl p-8 text-center hover:border-blue-400 transition-all duration-300 bg-gradient-to-br from-slate-50 to-white group">
                            <input
                              type="file"
                              accept="image/*"
                              onChange={handleImageUpload}
                              className="hidden"
                              id="product-image"
                            />
                            <label htmlFor="product-image" className="cursor-pointer block">
                              {previewUrl ? (
                                <div className="space-y-4">
                                  <div className="relative">
                                    <img
                                      src={previewUrl}
                                      alt="Product preview"
                                      className="max-w-full max-h-48 mx-auto rounded-xl shadow-lg border border-slate-200"
                                    />
                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-xl flex items-center justify-center">
                                      <div className="opacity-0 group-hover:opacity-100 transition-opacity bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2">
                                        <p className="text-sm font-medium text-slate-700">Click to change</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <div className="space-y-4">
                                  <div className="p-4 bg-gradient-to-br from-blue-50 to-purple-50 rounded-full w-20 h-20 mx-auto flex items-center justify-center group-hover:scale-105 transition-transform">
                                    <Upload className="h-8 w-8 text-blue-600" />
                                  </div>
                                  <div>
                                    <p className="text-lg font-bold text-slate-700 mb-1">
                                      Arrastra tu imagen aquí (opcional)
                                    </p>
                                    <p className="text-sm text-slate-500 font-medium">
                                      o haz clic para seleccionar • PNG, JPG hasta 10MB
                                    </p>
                                    <p className="text-xs text-blue-600 font-medium mt-2">
                                      💡 Sin imagen = mockup genérico | Con imagen = tu producto integrado
                                    </p>
                                  </div>
                                </div>
                              )}
                            </label>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Product Description */}
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-3 text-slate-800 font-bold">
                          <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                            <Palette className="h-5 w-5 text-white" />
                          </div>
                          Descripción del Producto
                        </CardTitle>
                        <CardDescription className="text-slate-600 font-medium">
                          Describe tu producto o el tipo de mockup que quieres crear
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {/* Direct Prompt Enhancer */}
                        <PromptEnhancer
                          initialPrompt={productDescription}
                          type="product"
                          onPromptChange={setProductDescription}
                        />

                        <div className="mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <div className="flex items-start gap-2">
                            <div className="text-blue-600 mt-0.5">💡</div>
                            <div className="text-xs text-blue-700 font-medium">
                              <p className="font-semibold mb-1">Dos modos disponibles:</p>
                              <p>• <strong>Sin imagen:</strong> Mockups genéricos basados en tu descripción</p>
                              <p>• <strong>Con imagen:</strong> Tu producto específico integrado en el mockup</p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Generate Button for Product Mode */}
                    <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-600 to-purple-600">
                      <CardContent className="p-6">
                        <Button
                          onClick={handleGenerateMockup}
                          disabled={!productDescription.trim() || isGenerating}
                          className="w-full h-14 text-lg font-semibold bg-white text-blue-600 hover:bg-slate-50 border-0 shadow-lg"
                          size="lg"
                        >
                          {isGenerating ? (
                            <>
                              <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                              Creando Mockup Profesional...
                            </>
                          ) : (
                            <>
                              <Zap className="mr-3 h-6 w-6" />
                              Generar Mockup del Producto
                            </>
                          )}
                        </Button>
                        {!productDescription.trim() && (
                          <p className="text-center text-white/80 text-sm mt-3 font-medium">
                            Describe tu producto o mockup para comenzar
                          </p>
                        )}
                        {productDescription.trim() && !productImage && (
                          <p className="text-center text-white/80 text-sm mt-3 font-medium">
                            ✨ Modo genérico activado - sin imagen de referencia
                          </p>
                        )}
                        {productDescription.trim() && productImage && (
                          <p className="text-center text-white/80 text-sm mt-3 font-medium">
                            🎯 Modo específico activado - con tu producto
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>

                  {/* Right Panel - Results */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="space-y-6"
                  >
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm min-h-[600px]">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-3 text-slate-800 font-bold">
                          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                            <Eye className="h-5 w-5 text-white" />
                          </div>
                          Mockup Generado
                        </CardTitle>
                        <CardDescription className="text-slate-600 font-medium">
                          Tu mockup profesional aparecerá aquí
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-1">
                        {isGenerating ? (
                          <div className="flex items-center justify-center h-96 bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl">
                            <div className="text-center">
                              <div className="relative">
                                <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                                  <Camera className="h-8 w-8 text-white animate-pulse" />
                                </div>
                                <div className="absolute inset-0 w-20 h-20 border-4 border-blue-200 rounded-full animate-spin mx-auto"></div>
                              </div>
                              <p className="text-lg font-bold text-slate-700 mb-2">Creando Mockup Profesional</p>
                              <p className="text-slate-500 font-medium">{generationProgress || 'Usando tecnología Emma AI...'}</p>
                            </div>
                          </div>
                        ) : generatedMockup && generatedMockup.variations && generatedMockup.variations.length > 0 ? (
                          <div className="space-y-6">
                            {/* Main Image Display */}
                            <div className="relative">
                              <img
                                src={generatedMockup.variations[selectedVariation]?.image_url}
                                alt="Generated mockup"
                                className="w-full rounded-xl shadow-lg border border-slate-200"
                              />
                              <div className="absolute top-4 right-4">
                                <Badge className="bg-green-500 text-white border-0">
                                  <Star className="h-3 w-3 mr-1" />
                                  Professional
                                </Badge>
                              </div>
                            </div>

                            {/* Variation Selector */}
                            {generatedMockup.variations.length > 1 && (
                              <div className="space-y-3">
                                <h4 className="font-bold text-slate-700 flex items-center gap-2">
                                  <Grid3X3 className="h-4 w-4" />
                                  Variaciones ({generatedMockup.variations.length})
                                </h4>
                                <div className="grid grid-cols-2 gap-3">
                                  {generatedMockup.variations.map((variation, index) => (
                                    <button
                                      key={index}
                                      onClick={() => setSelectedVariation(index)}
                                      className={`relative rounded-lg overflow-hidden border-2 transition-all ${
                                        selectedVariation === index
                                          ? 'border-blue-500 ring-2 ring-blue-200'
                                          : 'border-slate-200 hover:border-slate-300'
                                      }`}
                                    >
                                      <img
                                        src={variation.image_url}
                                        alt={`Variation ${index + 1}`}
                                        className="w-full h-24 object-cover"
                                      />
                                      {selectedVariation === index && (
                                        <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                                          <div className="bg-blue-500 text-white rounded-full p-1">
                                            <Eye className="h-3 w-3" />
                                          </div>
                                        </div>
                                      )}
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Action Buttons */}
                            <div className="space-y-3">
                              {/* Edit Button */}
                              <Button
                                onClick={() => handleEditMockup({
                                  id: `mockup-${selectedVariation}`,
                                  image_url: generatedMockup.variations[selectedVariation]?.image_url,
                                  prompt: productDescription,
                                  metadata: generatedMockup.variations[selectedVariation]?.metadata
                                })}
                                variant="outline"
                                className="w-full border-blue-200 text-blue-700 hover:bg-blue-50 h-11 font-semibold"
                              >
                                <Palette className="mr-2 h-4 w-4" />
                                Editar con IA
                              </Button>

                              {/* Open in Polotno Button */}
                              <Button
                                onClick={() => handleOpenInPolotno({
                                  id: `mockup-${selectedVariation}`,
                                  image_url: generatedMockup.variations[selectedVariation]?.image_url,
                                  metadata: generatedMockup.variations[selectedVariation]?.metadata
                                })}
                                variant="outline"
                                className="w-full border-purple-200 text-purple-700 hover:bg-purple-50 h-11 font-semibold"
                              >
                                <Settings className="mr-2 h-4 w-4" />
                                Abrir en Editor Profesional
                              </Button>

                              {/* Create Project Button */}
                              <Button
                                onClick={handleCreateProject}
                                variant="outline"
                                className="w-full border-orange-200 text-orange-700 hover:bg-orange-50 h-11 font-semibold"
                              >
                                <Grid3X3 className="mr-2 h-4 w-4" />
                                Crear Proyecto con Todas las Variaciones
                              </Button>

                              {/* Download Button */}
                              <Button
                                onClick={() => downloadMockup(generatedMockup.variations[selectedVariation]?.image_url || '', 'mockup')}
                                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0 h-12 font-bold"
                              >
                                <Download className="mr-2 h-5 w-5" />
                                Descargar Mockup de Alta Calidad
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-center h-96 bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl border-2 border-dashed border-slate-300">
                            <div className="text-center">
                              <div className="p-4 bg-gradient-to-br from-blue-50 to-purple-50 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                                <Camera className="h-8 w-8 text-blue-600" />
                              </div>
                              <p className="text-lg font-bold text-slate-700 mb-2">Listo para Crear</p>
                              <p className="text-slate-500 font-medium">Sube la imagen de tu producto y haz clic en generar</p>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                </div>
              </TabsContent>

              {/* Portrait Mode Content */}
              <TabsContent value="portrait" className="space-y-8">
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                  {/* Left Panel - Portrait Configuration */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="xl:col-span-2 space-y-6"
                  >
                    {/* Portrait Description */}
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-3 text-slate-800 font-bold">
                          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                            <User className="h-5 w-5 text-white" />
                          </div>
                          Descripción del Retrato
                        </CardTitle>
                        <CardDescription className="text-slate-600 font-medium">
                          Describe el retrato que quieres generar
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {/* Direct Portrait Prompt Enhancer */}
                        <PromptEnhancer
                          initialPrompt={portraitPrompt}
                          type="portrait"
                          onPromptChange={setPortraitPrompt}
                        />

                        <div className="mt-3 flex items-center gap-2 text-xs text-slate-500">
                          <Sparkles className="h-3 w-3" />
                          <span className="font-medium">La IA creará retratos fotorrealistas basados en tu descripción</span>
                        </div>
                      </CardContent>
                    </Card>



                    {/* Generate Button for Portrait Mode */}
                    <Card className="border-0 shadow-lg bg-gradient-to-r from-purple-600 to-pink-600">
                      <CardContent className="p-6">
                        <Button
                          onClick={handleGeneratePortrait}
                          disabled={!portraitPrompt.trim() || isGenerating}
                          className="w-full h-14 text-lg font-bold bg-white text-purple-600 hover:bg-slate-50 border-0 shadow-lg"
                          size="lg"
                        >
                          {isGenerating ? (
                            <>
                              <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                              Creando Retrato Realista...
                            </>
                          ) : (
                            <>
                              <User className="mr-3 h-6 w-6" />
                              Generar Retrato
                            </>
                          )}
                        </Button>
                        {!portraitPrompt.trim() && (
                          <p className="text-center text-white/80 text-sm mt-3 font-medium">
                            Ingresa una descripción del retrato para comenzar
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>

                  {/* Right Panel - Portrait Results */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="space-y-6"
                  >
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm min-h-[600px]">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-3 text-slate-800 font-bold">
                          <div className="p-2 bg-gradient-to-r from-pink-500 to-rose-500 rounded-lg">
                            <Eye className="h-5 w-5 text-white" />
                          </div>
                          Retrato Generado
                        </CardTitle>
                        <CardDescription className="text-slate-600 font-medium">
                          Tu retrato realista aparecerá aquí
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-1">
                        {isGenerating && mode === "portrait" ? (
                          <div className="flex items-center justify-center h-96 bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl">
                            <div className="text-center">
                              <div className="relative">
                                <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                                  <User className="h-8 w-8 text-white animate-pulse" />
                                </div>
                                <div className="absolute inset-0 w-20 h-20 border-4 border-purple-200 rounded-full animate-spin mx-auto"></div>
                              </div>
                              <p className="text-lg font-bold text-slate-700 mb-2">Creando Retrato Realista</p>
                              <p className="text-slate-500 font-medium">{generationProgress || 'Usando tecnología Emma AI...'}</p>
                            </div>
                          </div>
                        ) : generatedPortrait && generatedPortrait.variations && generatedPortrait.variations.length > 0 ? (
                          <div className="space-y-6">
                            {/* Main Image Display */}
                            <div className="relative">
                              <img
                                src={generatedPortrait.variations[selectedVariation]?.image_url}
                                alt="Generated portrait"
                                className="w-full rounded-xl shadow-lg border border-slate-200"
                              />
                              <div className="absolute top-4 right-4">
                                <Badge className="bg-purple-500 text-white border-0">
                                  <Star className="h-3 w-3 mr-1" />
                                  Realistic
                                </Badge>
                              </div>
                            </div>

                            {/* Variation Selector */}
                            {generatedPortrait.variations.length > 1 && (
                              <div className="space-y-3">
                                <h4 className="font-bold text-slate-700 flex items-center gap-2">
                                  <Grid3X3 className="h-4 w-4" />
                                  Variaciones ({generatedPortrait.variations.length})
                                </h4>
                                <div className="grid grid-cols-2 gap-3">
                                  {generatedPortrait.variations.map((variation, index) => (
                                    <button
                                      key={index}
                                      onClick={() => setSelectedVariation(index)}
                                      className={`relative rounded-lg overflow-hidden border-2 transition-all ${
                                        selectedVariation === index
                                          ? 'border-purple-500 ring-2 ring-purple-200'
                                          : 'border-slate-200 hover:border-slate-300'
                                      }`}
                                    >
                                      <img
                                        src={variation.image_url}
                                        alt={`Portrait variation ${index + 1}`}
                                        className="w-full h-24 object-cover"
                                      />
                                      {selectedVariation === index && (
                                        <div className="absolute inset-0 bg-purple-500/20 flex items-center justify-center">
                                          <div className="bg-purple-500 text-white rounded-full p-1">
                                            <Eye className="h-3 w-3" />
                                          </div>
                                        </div>
                                      )}
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Action Buttons for Portrait */}
                            <div className="space-y-3">
                              {/* Edit Button */}
                              <Button
                                onClick={() => handleEditMockup({
                                  id: `portrait-${selectedVariation}`,
                                  image_url: generatedPortrait.variations[selectedVariation]?.image_url,
                                  prompt: portraitPrompt,
                                  metadata: generatedPortrait.variations[selectedVariation]?.metadata
                                })}
                                variant="outline"
                                className="w-full border-blue-200 text-blue-700 hover:bg-blue-50 h-11 font-semibold"
                              >
                                <Palette className="mr-2 h-4 w-4" />
                                Editar con IA
                              </Button>

                              {/* Open in Polotno Button */}
                              <Button
                                onClick={() => handleOpenInPolotno({
                                  id: `portrait-${selectedVariation}`,
                                  image_url: generatedPortrait.variations[selectedVariation]?.image_url,
                                  metadata: generatedPortrait.variations[selectedVariation]?.metadata
                                })}
                                variant="outline"
                                className="w-full border-purple-200 text-purple-700 hover:bg-purple-50 h-11 font-semibold"
                              >
                                <Settings className="mr-2 h-4 w-4" />
                                Abrir en Editor Profesional
                              </Button>

                              {/* Create Project Button */}
                              <Button
                                onClick={handleCreateProject}
                                variant="outline"
                                className="w-full border-orange-200 text-orange-700 hover:bg-orange-50 h-11 font-semibold"
                              >
                                <Grid3X3 className="mr-2 h-4 w-4" />
                                Crear Proyecto con Todas las Variaciones
                              </Button>

                              {/* Download Button */}
                              <Button
                                onClick={() => downloadMockup(generatedPortrait.variations[selectedVariation]?.image_url || '', 'portrait')}
                                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 h-12 font-bold"
                              >
                                <Download className="mr-2 h-5 w-5" />
                                Descargar Retrato de Alta Calidad
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-center h-96 bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl border-2 border-dashed border-slate-300">
                            <div className="text-center">
                              <div className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                                <User className="h-8 w-8 text-purple-600" />
                              </div>
                              <p className="text-lg font-bold text-slate-700 mb-2">Listo para Crear</p>
                              <p className="text-slate-500 font-medium">Ingresa la descripción de tu retrato y haz clic en generar</p>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                </div>
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>

      {/* Mini Editor Modal */}
      {isEditorOpen && editingMockup && (
        <MockupMiniEditor
          mockup={editingMockup}
          isOpen={isEditorOpen}
          onClose={() => {
            setIsEditorOpen(false);
            setEditingMockup(null);
          }}
          onSave={handleSaveEditedMockup}
        />
      )}
    </DashboardLayout>
  );
};

export default MockupGeneratorPage;
