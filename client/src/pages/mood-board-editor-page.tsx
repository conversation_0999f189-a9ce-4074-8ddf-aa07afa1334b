import { withAuth } from "@/lib/with-auth"
import MoodBoardEditor from "@/components/tools/mood-board-editor"

interface MoodBoardEditorPageProps {
  boardId: string
}

function MoodBoardEditorPageContent({ boardId }: MoodBoardEditorPageProps) {
  return <MoodBoardEditor boardId={boardId} />
}

function MoodBoardEditorPage({ boardId }: MoodBoardEditorPageProps) {
  // No usamos DashboardLayoutWrapper porque queremos pantalla completa
  return <MoodBoardEditorPageContent boardId={boardId} />
}

// Enable authentication for moodboard editor functionality
export default withAuth(MoodBoardEditorPage)
