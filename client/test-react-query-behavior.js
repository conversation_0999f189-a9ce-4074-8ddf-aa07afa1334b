/**
 * Test React Query Behavior
 * Specifically test React Query cache invalidation and refetching
 */

console.log('🔍 Testing React Query Behavior');

async function testReactQueryBehavior() {
  console.log('🚀 Starting React Query behavior test...');
  
  try {
    // Step 1: Get user
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      console.log('❌ User not authenticated');
      return false;
    }
    console.log('✅ User authenticated:', user.email);
    
    // Step 2: Find the query client
    let queryClient = null;
    
    // Try different methods to get the query client
    if (window.queryClient) {
      queryClient = window.queryClient;
      console.log('✅ Found queryClient on window');
    } else if (window.__REACT_QUERY_DEVTOOLS__) {
      queryClient = window.__REACT_QUERY_DEVTOOLS__.queryClient;
      console.log('✅ Found queryClient from DevTools');
    } else {
      // Try to extract from React DevTools if available
      if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('🔍 React DevTools available, searching for queryClient...');
        
        // This is a hack to try to find the query client
        const reactFiber = window.__REACT_DEVTOOLS_GLOBAL_HOOK__.renderers;
        if (reactFiber && reactFiber.size > 0) {
          console.log('🔍 Found React renderers:', reactFiber.size);
        }
      }
      
      // Last resort: try to find it in the DOM
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        if (script.textContent && script.textContent.includes('queryClient')) {
          console.log('🔍 Found queryClient reference in script');
          break;
        }
      }
    }
    
    if (!queryClient) {
      console.log('❌ Could not find queryClient - this is the main issue!');
      console.log('🔍 Available window properties:', Object.keys(window).filter(k => k.includes('query') || k.includes('react')));
      return false;
    }
    
    console.log('✅ QueryClient found');
    
    // Step 3: Examine the query cache
    console.log('📊 Examining query cache...');
    const cache = queryClient.getQueryCache();
    const allQueries = cache.getAll();
    
    console.log('📊 Total queries in cache:', allQueries.length);
    
    // Find design analysis queries
    const designQueries = allQueries.filter(q => 
      Array.isArray(q.queryKey) && q.queryKey[0] === 'design-analyses'
    );
    
    console.log('📊 Design analysis queries:', designQueries.length);
    designQueries.forEach((query, index) => {
      console.log(`  ${index + 1}. Key:`, query.queryKey);
      console.log(`     Status:`, query.state.status);
      console.log(`     Data:`, query.state.data?.length || 'no data');
      console.log(`     Last updated:`, new Date(query.state.dataUpdatedAt));
    });
    
    // Step 4: Check if the correct query exists
    const userQuery = cache.find(['design-analyses', user.id]);
    if (userQuery) {
      console.log('✅ Found user-specific query');
      console.log('📊 Query details:', {
        status: userQuery.state.status,
        isFetching: userQuery.state.isFetching,
        isStale: userQuery.state.isStale,
        dataLength: userQuery.state.data?.length || 0,
        lastUpdated: new Date(userQuery.state.dataUpdatedAt)
      });
    } else {
      console.log('❌ User-specific query not found');
      console.log('🔍 Available query keys:', allQueries.map(q => q.queryKey));
    }
    
    // Step 5: Test manual invalidation
    console.log('🔄 Testing manual invalidation...');
    
    // Monitor invalidation
    const originalInvalidate = queryClient.invalidateQueries.bind(queryClient);
    let invalidationCalled = false;
    
    queryClient.invalidateQueries = function(filters) {
      invalidationCalled = true;
      console.log('🔄 invalidateQueries called with:', filters);
      
      const matchedQueries = cache.findAll(filters);
      console.log('🔄 Matched queries:', matchedQueries.length);
      matchedQueries.forEach(q => {
        console.log('  - Key:', q.queryKey, 'Status:', q.state.status);
      });
      
      const result = originalInvalidate(filters);
      
      // Check state after invalidation
      setTimeout(() => {
        const updatedQuery = cache.find(['design-analyses', user.id]);
        if (updatedQuery) {
          console.log('📊 Query state after invalidation:', {
            status: updatedQuery.state.status,
            isFetching: updatedQuery.state.isFetching,
            isStale: updatedQuery.state.isStale
          });
        }
      }, 100);
      
      return result;
    };
    
    // Trigger invalidation
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    if (!invalidationCalled) {
      console.log('❌ Invalidation was not called');
    }
    
    // Step 6: Wait and check if refetch happens
    console.log('⏳ Waiting for refetch...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const finalQuery = cache.find(['design-analyses', user.id]);
    if (finalQuery) {
      console.log('📊 Final query state:', {
        status: finalQuery.state.status,
        isFetching: finalQuery.state.isFetching,
        dataLength: finalQuery.state.data?.length || 0,
        lastUpdated: new Date(finalQuery.state.dataUpdatedAt)
      });
    }
    
    // Step 7: Test direct service call vs query data
    console.log('🔄 Comparing service data vs query data...');
    
    const serviceData = await window.designAnalysisService.getUserAnalyses(user.id, 10);
    const queryData = finalQuery?.state.data || [];
    
    console.log('📊 Service data count:', serviceData.length);
    console.log('📊 Query data count:', queryData.length);
    
    if (serviceData.length !== queryData.length) {
      console.log('⚠️ Data mismatch between service and query!');
      console.log('🔍 Service IDs:', serviceData.map(a => a.id).slice(0, 3));
      console.log('🔍 Query IDs:', queryData.map(a => a.id).slice(0, 3));
    } else {
      console.log('✅ Service and query data match');
    }
    
    // Step 8: Test a complete save and invalidate cycle
    console.log('🧪 Testing complete save and invalidate cycle...');
    
    const testData = {
      user_id: user.id,
      original_filename: 'react-query-test.png',
      file_size: 1024,
      file_type: 'image/png',
      file_url: null,
      overall_score: 90,
      complexity_scores: { visual: 88, cognitive: 90, structural: 92 },
      analysis_areas: [{
        name: 'Layout',
        score: 90,
        description: 'React Query test',
        recommendations: ['Test recommendation']
      }],
      recommendations: ['Test recommendation'],
      ai_analysis_summary: 'React Query test',
      gemini_analysis: 'React Query test',
      agent_message: 'React Query test',
      visuai_insights: 'React Query test',
      tags: ['react-query-test']
    };
    
    // Save
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testData, null);
    if (!savedAnalysis) {
      console.log('❌ Failed to save test analysis');
      return false;
    }
    console.log('✅ Test analysis saved:', savedAnalysis.id);
    
    // Invalidate
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    // Wait for refetch
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if the new analysis appears in the query
    const updatedQuery = cache.find(['design-analyses', user.id]);
    const updatedData = updatedQuery?.state.data || [];
    const foundInQuery = updatedData.find(a => a.id === savedAnalysis.id);
    
    if (foundInQuery) {
      console.log('✅ New analysis found in query data');
    } else {
      console.log('❌ New analysis NOT found in query data');
      console.log('🔍 Query data length:', updatedData.length);
      console.log('🔍 Looking for ID:', savedAnalysis.id);
      console.log('🔍 Available IDs:', updatedData.map(a => a.id).slice(0, 5));
    }
    
    // Clean up
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    console.log('🧹 Test analysis cleaned up');
    
    // Restore original function
    queryClient.invalidateQueries = originalInvalidate;
    
    console.log('✅ React Query behavior test completed');
    return true;
    
  } catch (error) {
    console.error('❌ React Query behavior test failed:', error);
    return false;
  }
}

// Helper to check if React Query is properly set up
function checkReactQuerySetup() {
  console.log('🔍 Checking React Query setup...');
  
  // Check for React Query in window
  const reactQueryKeys = Object.keys(window).filter(k => 
    k.toLowerCase().includes('query') || k.toLowerCase().includes('tanstack')
  );
  console.log('🔍 React Query related keys on window:', reactQueryKeys);
  
  // Check for React Query in document
  const scripts = Array.from(document.querySelectorAll('script')).filter(script => 
    script.src.includes('query') || script.textContent?.includes('QueryClient')
  );
  console.log('🔍 React Query related scripts:', scripts.length);
  
  // Check for React DevTools
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('✅ React DevTools available');
  } else {
    console.log('❌ React DevTools not available');
  }
  
  // Check for TanStack Query DevTools
  if (window.__REACT_QUERY_DEVTOOLS__) {
    console.log('✅ React Query DevTools available');
  } else {
    console.log('❌ React Query DevTools not available');
  }
}

// Run the tests
console.log('🚀 Starting React Query investigation...');

checkReactQuerySetup();

testReactQueryBehavior().then(success => {
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('🎉 REACT QUERY TEST: COMPLETED');
  } else {
    console.log('💥 REACT QUERY TEST: FAILED');
  }
  console.log('='.repeat(50));
}).catch(error => {
  console.error('💥 React Query test crashed:', error);
});

// Export for manual use
window.testReactQueryBehavior = testReactQueryBehavior;
window.checkReactQuerySetup = checkReactQuerySetup;
console.log('🔧 Functions available: window.testReactQueryBehavior(), window.checkReactQuerySetup()');
