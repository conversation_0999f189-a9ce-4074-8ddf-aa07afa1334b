// Debug script for SEO Analyzer History tab issues
// Run this in the browser console to diagnose the problem

console.log('🔍 Debugging SEO Analyzer History Tab');
console.log('====================================');

async function debugSEOHistory() {
  console.log('\n🔐 Step 1: Checking Authentication...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ User not authenticated');
      console.log('💡 This is likely the main issue - analyses only save when user is authenticated');
      console.log('🔑 Please sign in to test the History functionality');
      return false;
    }
    
    console.log('✅ User authenticated:', {
      userId: user.id,
      email: user.email
    });
    
    return user;
  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    return false;
  }
}

async function testServiceLayer(userId) {
  console.log('\n🔧 Step 2: Testing Service Layer...');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Test getRecentAnalyses
    console.log('🔄 Testing getRecentAnalyses...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    console.log('📊 Recent analyses result:', {
      count: recentAnalyses.length,
      analyses: recentAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        created_at: a.created_at
      }))
    });
    
    // Test getFavoriteAnalyses
    console.log('🔄 Testing getFavoriteAnalyses...');
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    console.log('⭐ Favorite analyses result:', {
      count: favoriteAnalyses.length,
      analyses: favoriteAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        is_favorite: a.is_favorite
      }))
    });
    
    // Test getUserAnalyses directly
    console.log('🔄 Testing getUserAnalyses directly...');
    const userAnalyses = await seoAnalysisService.getUserAnalyses(userId);
    console.log('👤 User analyses result:', {
      count: userAnalyses.length,
      analyses: userAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        created_at: a.created_at
      }))
    });
    
    return { recentAnalyses, favoriteAnalyses, userAnalyses };
  } catch (error) {
    console.error('❌ Service layer test failed:', error);
    console.log('Error details:', {
      message: error.message,
      stack: error.stack
    });
    return null;
  }
}

async function testDatabaseDirect(userId) {
  console.log('\n💾 Step 3: Testing Database Direct Access...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test direct query to seo_analyses table
    console.log('🔄 Testing direct database query...');
    const { data, error } = await supabase
      .from('seo_analyses')
      .select('id, created_at, user_id, url, overall_score, is_favorite')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (error) {
      console.error('❌ Database query failed:', {
        error: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return null;
    }
    
    console.log('✅ Database query successful:', {
      count: data?.length || 0,
      records: data || []
    });
    
    return data;
  } catch (error) {
    console.error('❌ Database direct access failed:', error);
    return null;
  }
}

async function testSaveAnalysis(userId) {
  console.log('\n💾 Step 4: Testing Save Analysis...');

  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');

    const testAnalysisData = {
      user_id: userId,
      url: 'https://example.com/history-debug-test',
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 75,
      basic_info: {
        title: 'History Debug Test',
        title_length: 18,
        meta_description: 'Testing history functionality',
        meta_description_length: 29,
        h1_tags: ['History Debug Test'],
        h1_count: 1
      },
      content_analysis: {
        word_count: 350,
        images: { total: 2, without_alt: 0 },
        links: { total: 5, internal: 3, external: 2 }
      },
      seo_checks: {
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Test',
          issue: 'This is a test recommendation',
          importance: 'low',
          recommendation: 'This is for debugging the history functionality'
        }
      ],
      achievements: [
        {
          category: 'Test',
          achievement: 'History Debug Test',
          description: 'Testing the history functionality',
          icon: '🔍',
          impact: 'positive'
        }
      ],
      status: 'completed'
    };

    console.log('🔄 Attempting to save test analysis...');
    const savedAnalysis = await seoAnalysisService.saveAnalysis(testAnalysisData);
    console.log('✅ Analysis saved successfully:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      score: savedAnalysis.overall_score,
      created_at: savedAnalysis.created_at
    });

    // Wait a moment for the cache to update
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test if it appears in recent analyses
    console.log('🔄 Checking if analysis appears in recent analyses...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    const foundInRecent = recentAnalyses.find(a => a.id === savedAnalysis.id);

    if (foundInRecent) {
      console.log('✅ Analysis found in recent analyses');
    } else {
      console.error('❌ Analysis NOT found in recent analyses');
      console.log('🔍 Recent analyses found:', recentAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        created_at: a.created_at
      })));
    }

    return savedAnalysis;
  } catch (error) {
    console.error('❌ Save analysis test failed:', error);
    console.log('Error details:', {
      message: error.message,
      stack: error.stack
    });
    return null;
  }
}

async function testFullWorkflow() {
  console.log('\n🔄 Step 5: Testing Full Analysis Workflow...');

  try {
    // Test the complete workflow: API call + auto-save
    console.log('🔄 Running a real SEO analysis...');

    const analysisResponse = await fetch('/api/seo/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://example.com/workflow-test',
        mode: 'page'
      })
    });

    if (!analysisResponse.ok) {
      console.error('❌ Analysis API call failed:', analysisResponse.status);
      return false;
    }

    const analysisResult = await analysisResponse.json();
    console.log('✅ Analysis API call successful');

    // Check if user is authenticated for auto-save
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();

    if (!user) {
      console.log('⚠️ User not authenticated - analysis won\'t auto-save');
      console.log('💡 Sign in to test the complete workflow with auto-save');
      return true; // API works, just no auto-save
    }

    console.log('✅ User authenticated - analysis should auto-save');

    // Wait a moment for auto-save to complete
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if the analysis was saved
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();

    const foundAnalysis = recentAnalyses.find(a =>
      a.url === 'https://example.com/workflow-test' &&
      new Date(a.created_at).getTime() > Date.now() - 60000 // Within last minute
    );

    if (foundAnalysis) {
      console.log('✅ Analysis auto-saved successfully:', {
        id: foundAnalysis.id,
        url: foundAnalysis.url,
        score: foundAnalysis.overall_score
      });
    } else {
      console.error('❌ Analysis was not auto-saved');
      console.log('🔍 Recent analyses:', recentAnalyses.map(a => ({
        url: a.url,
        created_at: a.created_at
      })));
    }

    return true;
  } catch (error) {
    console.error('❌ Full workflow test failed:', error);
    return false;
  }
}

async function testHistoryHook() {
  console.log('\n🪝 Step 5: Testing History Hook...');
  
  try {
    // Check if the hook is available
    const { useSEOAnalysisHistory } = await import('/src/components/tools/seo-analyzer/hooks/useSEOAnalysisHistory.ts');
    console.log('✅ useSEOAnalysisHistory hook imported successfully');
    
    // Note: We can't actually test the hook here since we're not in a React component
    console.log('💡 Hook is available - testing would need to be done in the actual component');
    
    return true;
  } catch (error) {
    console.error('❌ History hook test failed:', error);
    return false;
  }
}

async function checkHistoryComponent() {
  console.log('\n🎨 Step 6: Checking History Component...');
  
  try {
    // Check if we're on the SEO Analyzer page
    const seoAnalyzerElement = document.querySelector('[data-testid="seo-analyzer"]') || 
                              document.querySelector('.seo-analyzer') ||
                              document.querySelector('#seo-analyzer');
    
    if (!seoAnalyzerElement) {
      console.log('⚠️ SEO Analyzer component not found in current page');
      console.log('💡 Navigate to the SEO Analyzer tool to test the History tab');
      return false;
    }
    
    console.log('✅ SEO Analyzer component found');
    
    // Check for History tab
    const historyTab = document.querySelector('[data-value="history"]') ||
                      document.querySelector('button:contains("Historial")') ||
                      document.querySelector('[role="tab"]:contains("Historial")');
    
    if (historyTab) {
      console.log('✅ History tab found in DOM');
    } else {
      console.log('⚠️ History tab not found - may not be rendered yet');
    }
    
    return true;
  } catch (error) {
    console.error('❌ History component check failed:', error);
    return false;
  }
}

// Main debug function
async function runHistoryDebug() {
  console.log('🚀 Starting SEO History Debug...\n');
  
  const user = await debugSEOHistory();
  if (!user) {
    console.log('\n❌ AUTHENTICATION ISSUE DETECTED');
    console.log('🔑 The main issue is that no user is authenticated');
    console.log('💡 Analyses only save when a user is signed in');
    console.log('📝 To fix: Sign in to your account and try running an analysis');
    return;
  }
  
  const serviceResults = await testServiceLayer(user.id);
  const dbResults = await testDatabaseDirect(user.id);
  const hookTest = await testHistoryHook();
  const componentTest = await checkHistoryComponent();
  const workflowTest = await testFullWorkflow();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 DEBUG RESULTS SUMMARY');
  console.log('='.repeat(60));
  
  if (serviceResults && dbResults !== null) {
    console.log('✅ Authentication: Working');
    console.log('✅ Service Layer: Working');
    console.log('✅ Database Access: Working');
    console.log(`📊 Total analyses in database: ${dbResults.length}`);
    
    if (dbResults.length === 0) {
      console.log('\n💡 ISSUE IDENTIFIED: No analyses in database');
      console.log('🔧 SOLUTION: Run a new SEO analysis to test saving');
      
      // Test saving
      const savedAnalysis = await testSaveAnalysis(user.id);
      if (savedAnalysis) {
        console.log('✅ Save functionality working - try refreshing the History tab');
      }
    } else {
      console.log('✅ Analyses found in database - History tab should display them');
      console.log('💡 If History tab is empty, there may be a frontend rendering issue');
    }
  } else {
    console.log('❌ Issues detected in service layer or database access');
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Ensure you are signed in');
  console.log('2. Run a new SEO analysis');
  console.log('3. Check the History tab after analysis completes');
  console.log('4. If still empty, check browser console for React errors');
}

// Auto-run the debug
runHistoryDebug();

// Export for manual testing
window.debugSEOHistory = {
  runHistoryDebug,
  debugSEOHistory,
  testServiceLayer,
  testDatabaseDirect,
  testSaveAnalysis,
  testHistoryHook,
  checkHistoryComponent,
  testFullWorkflow
};

console.log('\n💡 Manual debugging available:');
console.log('- window.debugSEOHistory.runHistoryDebug()');
console.log('- window.debugSEOHistory.testSaveAnalysis(userId)');
console.log('- window.debugSEOHistory.testServiceLayer(userId)');
