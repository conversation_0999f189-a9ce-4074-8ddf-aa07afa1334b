// Test script to verify the React Query fix for SEO Analyzer
// Run this after the enabled condition fix

console.log('🔧 Testing SEO Query Fix');
console.log('========================');

async function testQueryFix() {
  console.log('\n🔐 Step 1: Check Authentication Status');
  
  try {
    const { useAuth } = await import('/src/hooks/use-auth.ts');
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Get auth data directly
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Not authenticated - please sign in first');
      return false;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      isAnonymous: user.id === 'anonymous'
    });
    
    // Test the enabled condition logic
    const isEnabledOld = true && !!user?.id; // Old condition (isAuthenticated && !!user?.id)
    const isEnabledNew = !!user?.id && user.id !== 'anonymous'; // New condition
    
    console.log('🔍 Query enabled conditions:');
    console.log('  Old condition (isAuthenticated && !!user?.id):', isEnabledOld);
    console.log('  New condition (!!user?.id && user.id !== "anonymous"):', isEnabledNew);
    
    if (isEnabledNew) {
      console.log('✅ New condition should enable queries');
    } else {
      console.log('❌ New condition would still disable queries');
      return false;
    }
    
    return { user, isEnabledNew };
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return false;
  }
}

async function testServiceDirectly(user) {
  console.log('\n🔧 Step 2: Test Service Layer Directly');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('🔄 Testing getRecentAnalyses...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    console.log('✅ getRecentAnalyses result:', {
      count: recentAnalyses.length,
      analyses: recentAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        created_at: a.created_at
      }))
    });
    
    console.log('🔄 Testing getFavoriteAnalyses...');
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    console.log('✅ getFavoriteAnalyses result:', {
      count: favoriteAnalyses.length,
      analyses: favoriteAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        is_favorite: a.is_favorite
      }))
    });
    
    return { recentAnalyses, favoriteAnalyses };
  } catch (error) {
    console.error('❌ Service test failed:', error);
    return null;
  }
}

async function createTestAnalysisIfNeeded(user) {
  console.log('\n💾 Step 3: Create Test Analysis (if needed)');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Check if we already have analyses
    const existing = await seoAnalysisService.getRecentAnalyses();
    if (existing.length > 0) {
      console.log('✅ Existing analyses found, skipping creation');
      return existing[0];
    }
    
    console.log('🔄 No analyses found, creating test analysis...');
    
    const testData = {
      user_id: user.id,
      url: `https://example.com/query-fix-test-${Date.now()}`,
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 82,
      basic_info: { 
        title: 'Query Fix Test', 
        title_length: 15,
        meta_description: 'Testing the React Query fix',
        meta_description_length: 28,
        h1_tags: ['Query Fix Test'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 400,
        images: { total: 2, without_alt: 0 },
        links: { total: 6, internal: 4, external: 2 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Test',
          issue: 'This is a test recommendation',
          importance: 'low',
          recommendation: 'Testing the query fix functionality'
        }
      ],
      achievements: [
        {
          category: 'Technical',
          achievement: 'Query Fix Test',
          description: 'Testing React Query enabled condition fix',
          icon: '🔧',
          impact: 'positive'
        }
      ],
      status: 'completed'
    };

    const savedAnalysis = await seoAnalysisService.saveAnalysis(testData);
    console.log('✅ Test analysis created:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      score: savedAnalysis.overall_score
    });
    
    return savedAnalysis;
  } catch (error) {
    console.error('❌ Create test analysis failed:', error);
    return null;
  }
}

async function testQueryAfterFix(user) {
  console.log('\n🔄 Step 4: Test Queries After Fix');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Wait a moment for any cache updates
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('🔄 Re-testing getRecentAnalyses after fix...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    console.log('📊 Recent analyses after fix:', {
      count: recentAnalyses.length,
      analyses: recentAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        created_at: new Date(a.created_at).toLocaleString()
      }))
    });
    
    console.log('🔄 Re-testing getFavoriteAnalyses after fix...');
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    console.log('⭐ Favorite analyses after fix:', {
      count: favoriteAnalyses.length,
      analyses: favoriteAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        is_favorite: a.is_favorite
      }))
    });
    
    return { recentAnalyses, favoriteAnalyses };
  } catch (error) {
    console.error('❌ Query test after fix failed:', error);
    return null;
  }
}

async function runQueryFixTest() {
  console.log('🚀 Starting SEO Query Fix Test...\n');
  
  // Step 1: Authentication
  const authResult = await testQueryFix();
  if (!authResult) {
    console.log('\n❌ Authentication failed - cannot proceed');
    return;
  }
  
  const { user } = authResult;
  
  // Step 2: Test service directly
  const serviceResult = await testServiceDirectly(user);
  
  // Step 3: Create test analysis if needed
  const testAnalysis = await createTestAnalysisIfNeeded(user);
  
  // Step 4: Test queries after fix
  const finalResult = await testQueryAfterFix(user);
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 QUERY FIX TEST RESULTS');
  console.log('='.repeat(50));
  
  if (finalResult && finalResult.recentAnalyses.length > 0) {
    console.log('🎉 SUCCESS: Query fix working!');
    console.log('✅ Service layer functional');
    console.log('✅ Analyses being retrieved');
    console.log(`📊 Found ${finalResult.recentAnalyses.length} recent analyses`);
    console.log(`⭐ Found ${finalResult.favoriteAnalyses.length} favorite analyses`);
    console.log('\n💡 The History tab should now display analyses');
    console.log('🔗 Navigate to SEO Analyzer and check the History tab');
  } else {
    console.log('❌ ISSUE PERSISTS: Queries still not working');
    console.log('🔧 The enabled condition fix may not be the root cause');
    console.log('💡 Consider implementing the headline analyzer pattern');
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Navigate to the SEO Analyzer tool');
  console.log('2. Click on the "Historial" tab');
  console.log('3. Check if analyses are now displayed');
  console.log('4. If still empty, the issue may be in React Query cache or component rendering');
}

// Auto-run the test
runQueryFixTest();

// Export for manual use
window.testSEOQueryFix = {
  runQueryFixTest,
  testQueryFix,
  testServiceDirectly,
  createTestAnalysisIfNeeded,
  testQueryAfterFix
};

console.log('\n💡 Manual testing available:');
console.log('- window.testSEOQueryFix.runQueryFixTest()');
console.log('- window.testSEOQueryFix.createTestAnalysisIfNeeded(user)');
