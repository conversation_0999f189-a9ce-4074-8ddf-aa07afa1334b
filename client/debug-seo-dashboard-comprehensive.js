// Comprehensive SEO Dashboard Debug Script
// Run this in the browser console on the SEO Analyzer page

console.log('🔍 Starting comprehensive SEO Dashboard debugging...');

async function debugSEODashboard() {
  console.log('\n=== 🔐 AUTHENTICATION STATE DEBUG ===');
  
  try {
    // Import required modules
    const { supabase } = await import('/src/lib/supabase.ts');
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // 1. Check Supabase auth state
    console.log('\n1️⃣ Checking Supabase Auth State...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ Supabase auth error:', authError);
      return;
    }
    
    if (!user) {
      console.error('❌ No user found in Supabase auth');
      return;
    }
    
    console.log('✅ Supabase user found:', {
      id: user.id,
      email: user.email,
      created_at: user.created_at
    });
    
    // 2. Check React Auth Hook state (simulate)
    console.log('\n2️⃣ Simulating React Auth Hook State...');
    const authLoading = false; // Simulate auth loading complete
    const isAuthenticated = !!user && user.id !== 'anonymous';
    const queryEnabled = !authLoading && !!user?.id && isAuthenticated;
    
    console.log('🔍 Auth hook simulation:', {
      user: !!user,
      userId: user?.id,
      authLoading,
      isAuthenticated,
      queryEnabled,
      userIdType: typeof user?.id,
      isAnonymous: user?.id === 'anonymous'
    });
    
    if (!queryEnabled) {
      console.error('❌ Query would be DISABLED due to auth conditions');
      return;
    }
    
    console.log('✅ Query would be ENABLED');
    
    // 3. Test direct database query
    console.log('\n3️⃣ Testing Direct Database Query...');
    
    try {
      const directQuery = await supabase
        .from('seo_analyses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });
      
      console.log('📊 Direct Supabase query result:', {
        error: directQuery.error,
        dataLength: directQuery.data?.length || 0,
        data: directQuery.data
      });
      
      if (directQuery.error) {
        console.error('❌ Direct query error:', directQuery.error);
      }
      
    } catch (error) {
      console.error('❌ Direct query exception:', error);
    }
    
    // 4. Test seoAnalysisService.getUserAnalyses
    console.log('\n4️⃣ Testing seoAnalysisService.getUserAnalyses...');
    
    try {
      const serviceResult = await seoAnalysisService.getUserAnalyses(user.id, {
        limit: 100,
        orderBy: 'created_at',
        orderDirection: 'desc'
      });
      
      console.log('📊 Service result:', {
        type: typeof serviceResult,
        isArray: Array.isArray(serviceResult),
        length: serviceResult?.length || 0,
        firstItem: serviceResult?.[0] || null
      });
      
      if (serviceResult && serviceResult.length > 0) {
        console.log('✅ Service returned data:', serviceResult.map(item => ({
          id: item.id,
          url: item.url?.substring(0, 50) + '...',
          status: item.status,
          created_at: item.created_at
        })));
      } else {
        console.warn('⚠️ Service returned empty array or null');
      }
      
    } catch (error) {
      console.error('❌ Service error:', error);
    }
    
    // 5. Check React Query cache
    console.log('\n5️⃣ Checking React Query Cache...');
    
    try {
      // Try to access React Query client from window
      const queryClient = window.__REACT_QUERY_CLIENT__ || 
                         window.queryClient || 
                         document.querySelector('[data-react-query-client]')?.__reactInternalInstance?.memoizedProps?.client;
      
      if (queryClient) {
        const cacheData = queryClient.getQueryData(['seo-analyses', user.id]);
        console.log('📊 React Query cache data:', {
          exists: !!cacheData,
          type: typeof cacheData,
          isArray: Array.isArray(cacheData),
          length: cacheData?.length || 0,
          data: cacheData
        });
      } else {
        console.warn('⚠️ Could not access React Query client');
      }
      
    } catch (error) {
      console.warn('⚠️ Could not check React Query cache:', error);
    }
    
    // 6. Check for any saved analyses in the database
    console.log('\n6️⃣ Checking Database for Any SEO Analyses...');
    
    try {
      const allAnalyses = await supabase
        .from('seo_analyses')
        .select('id, user_id, url, status, created_at')
        .order('created_at', { ascending: false })
        .limit(10);
      
      console.log('📊 All analyses in database (last 10):', {
        error: allAnalyses.error,
        count: allAnalyses.data?.length || 0,
        data: allAnalyses.data
      });
      
      if (allAnalyses.data && allAnalyses.data.length > 0) {
        const userAnalyses = allAnalyses.data.filter(a => a.user_id === user.id);
        console.log('📊 User-specific analyses:', {
          count: userAnalyses.length,
          data: userAnalyses
        });
      }
      
    } catch (error) {
      console.error('❌ Database check error:', error);
    }
    
    // 7. Test saving a new analysis
    console.log('\n7️⃣ Testing Analysis Save (if needed)...');
    
    const shouldTestSave = confirm('Do you want to test saving a new SEO analysis? This will create test data.');
    
    if (shouldTestSave) {
      try {
        const testAnalysis = {
          user_id: user.id,
          url: 'https://test-debug.com',
          analysis_mode: 'basic',
          tool_type: 'seo_analyzer',
          analysis_version: '1.0',
          overall_score: 85,
          basic_info: { title: 'Test Debug Page' },
          content_analysis: { word_count: 500 },
          seo_checks: { meta_description: true },
          recommendations: ['Test recommendation'],
          achievements: ['Test achievement'],
          open_graph: {},
          twitter_card: {},
          preview_data: {},
          performance_metrics: {},
          analysis_duration_ms: 1000,
          status: 'completed',
          ai_enhanced: false,
          is_favorite: false
        };
        
        const savedAnalysis = await seoAnalysisService.saveAnalysis(testAnalysis);
        console.log('✅ Test analysis saved:', savedAnalysis.id);
        
        // Re-fetch to verify
        const updatedAnalyses = await seoAnalysisService.getUserAnalyses(user.id);
        console.log('📊 Updated analyses count:', updatedAnalyses.length);
        
      } catch (error) {
        console.error('❌ Test save error:', error);
      }
    }
    
    console.log('\n=== 🏁 DEBUG COMPLETE ===');
    
  } catch (error) {
    console.error('💥 Debug script error:', error);
  }
}

// Run the debug
debugSEODashboard();
