// Test script to verify the new headline analyzer pattern implementation
// Run this after implementing the single query pattern

console.log('🔄 Testing SEO Headline Analyzer Pattern');
console.log('========================================');

async function testNewPattern() {
  console.log('\n🔐 Step 1: Authentication Check');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Not authenticated - please sign in first');
      return false;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email
    });
    
    return user;
  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    return false;
  }
}

async function testSingleQuery(user) {
  console.log('\n📊 Step 2: Testing Single Query Pattern');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('🔄 Testing getUserAnalyses (single query)...');
    const allAnalyses = await seoAnalysisService.getUserAnalyses(user.id, {
      limit: 100,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    console.log('✅ Single query result:', {
      totalCount: allAnalyses.length,
      analyses: allAnalyses.map(a => ({
        id: a.id,
        url: a.url,
        score: a.overall_score,
        is_favorite: a.is_favorite,
        created_at: a.created_at
      }))
    });
    
    // Test computed values (like the hook does)
    const favoriteAnalyses = allAnalyses.filter(analysis => analysis.is_favorite);
    const nonFavorites = allAnalyses.filter(analysis => !analysis.is_favorite);
    const recentAnalyses = nonFavorites.slice(0, 10);
    
    console.log('📈 Computed values:', {
      totalAnalyses: allAnalyses.length,
      favoriteCount: favoriteAnalyses.length,
      recentCount: recentAnalyses.length
    });
    
    return { allAnalyses, favoriteAnalyses, recentAnalyses };
  } catch (error) {
    console.error('❌ Single query test failed:', error);
    return null;
  }
}

async function createTestAnalysisForPattern(user) {
  console.log('\n💾 Step 3: Creating Test Analysis');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    const testData = {
      user_id: user.id,
      url: `https://example.com/pattern-test-${Date.now()}`,
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 88,
      basic_info: { 
        title: 'Pattern Test Analysis', 
        title_length: 21,
        meta_description: 'Testing the headline analyzer pattern implementation',
        meta_description_length: 50,
        h1_tags: ['Pattern Test'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 550,
        images: { total: 4, without_alt: 0 },
        links: { total: 10, internal: 7, external: 3 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Pattern Test',
          issue: 'Testing the new pattern implementation',
          importance: 'low',
          recommendation: 'Verify that the headline analyzer pattern works correctly'
        }
      ],
      achievements: [
        {
          category: 'Pattern',
          achievement: 'New Pattern Test',
          description: 'Testing the headline analyzer pattern implementation',
          icon: '🔄',
          impact: 'positive'
        }
      ],
      status: 'completed'
    };

    console.log('🔄 Saving test analysis...');
    const savedAnalysis = await seoAnalysisService.saveAnalysis(testData);
    console.log('✅ Test analysis saved:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      score: savedAnalysis.overall_score
    });
    
    return savedAnalysis;
  } catch (error) {
    console.error('❌ Test analysis creation failed:', error);
    return null;
  }
}

async function verifyPatternAfterSave(user, savedAnalysis) {
  console.log('\n🔍 Step 4: Verifying Pattern After Save');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Wait a moment for any cache updates
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('🔄 Re-fetching all analyses...');
    const allAnalyses = await seoAnalysisService.getUserAnalyses(user.id, {
      limit: 100,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    // Check if our test analysis is included
    const foundAnalysis = allAnalyses.find(a => a.id === savedAnalysis.id);
    
    if (foundAnalysis) {
      console.log('✅ Test analysis found in all analyses');
    } else {
      console.error('❌ Test analysis NOT found in all analyses');
    }
    
    // Test computed values again
    const favoriteAnalyses = allAnalyses.filter(analysis => analysis.is_favorite);
    const nonFavorites = allAnalyses.filter(analysis => !analysis.is_favorite);
    const recentAnalyses = nonFavorites.slice(0, 10);
    
    console.log('📊 Updated computed values:', {
      totalAnalyses: allAnalyses.length,
      favoriteCount: favoriteAnalyses.length,
      recentCount: recentAnalyses.length,
      testAnalysisInRecent: recentAnalyses.some(a => a.id === savedAnalysis.id)
    });
    
    // Test the separate service methods (should still work)
    console.log('🔄 Testing getRecentAnalyses method...');
    const recentFromService = await seoAnalysisService.getRecentAnalyses();
    
    console.log('🔄 Testing getFavoriteAnalyses method...');
    const favoritesFromService = await seoAnalysisService.getFavoriteAnalyses();
    
    console.log('📈 Service method results:', {
      recentFromService: recentFromService.length,
      favoritesFromService: favoritesFromService.length,
      testAnalysisInServiceRecent: recentFromService.some(a => a.id === savedAnalysis.id)
    });
    
    return {
      allAnalyses,
      favoriteAnalyses,
      recentAnalyses,
      recentFromService,
      favoritesFromService,
      foundAnalysis: !!foundAnalysis
    };
  } catch (error) {
    console.error('❌ Pattern verification failed:', error);
    return null;
  }
}

async function testFavoriteToggle(user, savedAnalysis) {
  console.log('\n⭐ Step 5: Testing Favorite Toggle');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('🔄 Toggling favorite status...');
    const updatedAnalysis = await seoAnalysisService.toggleFavorite(savedAnalysis.id);
    console.log('✅ Favorite toggled:', {
      id: updatedAnalysis.id,
      is_favorite: updatedAnalysis.is_favorite
    });
    
    // Wait and verify
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const allAnalyses = await seoAnalysisService.getUserAnalyses(user.id, {
      limit: 100,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    const favoriteAnalyses = allAnalyses.filter(analysis => analysis.is_favorite);
    const foundInFavorites = favoriteAnalyses.find(a => a.id === savedAnalysis.id);
    
    console.log('📊 After favorite toggle:', {
      totalFavorites: favoriteAnalyses.length,
      testAnalysisIsFavorite: !!foundInFavorites
    });
    
    return updatedAnalysis;
  } catch (error) {
    console.error('❌ Favorite toggle test failed:', error);
    return null;
  }
}

async function runPatternTest() {
  console.log('🚀 Starting SEO Headline Pattern Test...\n');
  
  // Step 1: Authentication
  const user = await testNewPattern();
  if (!user) {
    console.log('\n❌ Authentication failed - cannot proceed');
    return;
  }
  
  // Step 2: Test single query
  const queryResult = await testSingleQuery(user);
  if (!queryResult) {
    console.log('\n❌ Single query test failed');
    return;
  }
  
  // Step 3: Create test analysis
  const savedAnalysis = await createTestAnalysisForPattern(user);
  if (!savedAnalysis) {
    console.log('\n❌ Test analysis creation failed');
    return;
  }
  
  // Step 4: Verify pattern after save
  const verifyResult = await verifyPatternAfterSave(user, savedAnalysis);
  if (!verifyResult) {
    console.log('\n❌ Pattern verification failed');
    return;
  }
  
  // Step 5: Test favorite toggle
  const toggleResult = await testFavoriteToggle(user, savedAnalysis);
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 HEADLINE PATTERN TEST RESULTS');
  console.log('='.repeat(60));
  
  if (verifyResult.foundAnalysis && toggleResult) {
    console.log('🎉 SUCCESS: Headline analyzer pattern working!');
    console.log('✅ Single query pattern implemented');
    console.log('✅ Computed values working correctly');
    console.log('✅ Analysis saving and retrieval functional');
    console.log('✅ Favorite toggle working');
    console.log('\n💡 The History tab should now display analyses correctly');
    console.log('🔗 Navigate to SEO Analyzer and check the History tab');
  } else {
    console.log('❌ ISSUES DETECTED');
    console.log('🔧 Check the errors above for specific problems');
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Navigate to the SEO Analyzer tool');
  console.log('2. Click on the "Historial" tab');
  console.log('3. Verify that analyses are now displayed');
  console.log('4. Test creating a new analysis to verify real-time updates');
  
  // Clean up test data
  try {
    console.log('\n🧹 Cleaning up test data...');
    await seoAnalysisService.deleteAnalysis(savedAnalysis.id);
    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.log('⚠️ Could not clean up test data:', error.message);
  }
}

// Auto-run the test
runPatternTest();

// Export for manual use
window.testSEOHeadlinePattern = {
  runPatternTest,
  testNewPattern,
  testSingleQuery,
  createTestAnalysisForPattern,
  verifyPatternAfterSave,
  testFavoriteToggle
};

console.log('\n💡 Manual testing available:');
console.log('- window.testSEOHeadlinePattern.runPatternTest()');
console.log('- window.testSEOHeadlinePattern.testSingleQuery(user)');
