// SEO Analyzer Database Integration Diagnostic Script
// Run this in the browser console to diagnose and fix issues

console.log('🔍 SEO Analyzer Database Integration Diagnostic');
console.log('================================================');

async function runDiagnostics() {
  const results = {
    supabaseConnection: false,
    authentication: false,
    serviceLayer: false,
    reactHook: false,
    componentIntegration: false,
    databaseSchema: false
  };

  // Test 1: Supabase Connection
  console.log('\n📡 Test 1: Supabase Connection');
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    const { data, error } = await supabase
      .from('seo_analyses')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
      console.log('💡 Solution: Check Supabase configuration in .env file');
    } else {
      console.log('✅ Supabase connection successful');
      results.supabaseConnection = true;
    }
  } catch (error) {
    console.error('❌ Failed to import Supabase:', error);
    console.log('💡 Solution: Check if Supabase is properly configured');
  }

  // Test 2: Authentication
  console.log('\n🔐 Test 2: Authentication');
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.warn('⚠️ User not authenticated');
      console.log('💡 Solution: Sign in to test database operations');
    } else {
      console.log('✅ User authenticated:', { userId: user.id, email: user.email });
      results.authentication = true;
    }
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
  }

  // Test 3: Service Layer
  console.log('\n🔧 Test 3: Service Layer');
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Test service methods exist
    const methods = ['getRecentAnalyses', 'getFavoriteAnalyses', 'saveAnalysis', 'updateAnalysis', 'deleteAnalysis'];
    const missingMethods = methods.filter(method => typeof seoAnalysisService[method] !== 'function');
    
    if (missingMethods.length > 0) {
      console.error('❌ Missing service methods:', missingMethods);
      console.log('💡 Solution: Check seoAnalysisService implementation');
    } else {
      console.log('✅ Service layer methods available');
      
      // Test basic service call
      try {
        const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
        console.log('✅ Service call successful, found', recentAnalyses.length, 'analyses');
        results.serviceLayer = true;
      } catch (serviceError) {
        console.error('❌ Service call failed:', serviceError.message);
        console.log('💡 Solution: Check authentication and database permissions');
      }
    }
  } catch (error) {
    console.error('❌ Failed to import service:', error);
    console.log('💡 Solution: Check if seoAnalysisService is properly exported');
  }

  // Test 4: React Hook
  console.log('\n🪝 Test 4: React Hook');
  try {
    const { useSEOAnalysisHistory } = await import('/src/components/tools/seo-analyzer/hooks/useSEOAnalysisHistory.ts');
    console.log('✅ React hook imported successfully');
    results.reactHook = true;
  } catch (error) {
    console.error('❌ Failed to import React hook:', error);
    console.log('💡 Solution: Check hook implementation and exports');
  }

  // Test 5: Component Integration
  console.log('\n⚛️ Test 5: Component Integration');
  try {
    // Check if SEO Analyzer component is mounted
    const seoAnalyzerElement = document.querySelector('[data-testid="seo-analyzer"]') || 
                              document.querySelector('.seo-analyzer') ||
                              document.querySelector('div:contains("SEO Analyzer")');
    
    if (seoAnalyzerElement) {
      console.log('✅ SEO Analyzer component found in DOM');
      results.componentIntegration = true;
    } else {
      console.warn('⚠️ SEO Analyzer component not found in DOM');
      console.log('💡 Solution: Navigate to SEO Analyzer tool page');
    }
  } catch (error) {
    console.error('❌ Component integration test failed:', error);
  }

  // Test 6: Database Schema
  console.log('\n🗄️ Test 6: Database Schema');
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test table structure
    const { data, error } = await supabase
      .from('seo_analyses')
      .select('id, created_at, user_id, url, analysis_mode, overall_score')
      .limit(1);
    
    if (error) {
      console.error('❌ Database schema test failed:', error.message);
      if (error.code === '42P01') {
        console.log('💡 Solution: Table seo_analyses does not exist. Run database migration.');
      } else if (error.code === '42703') {
        console.log('💡 Solution: Missing columns in seo_analyses table. Update schema.');
      } else {
        console.log('💡 Solution: Check RLS policies and permissions');
      }
    } else {
      console.log('✅ Database schema is correct');
      results.databaseSchema = true;
    }
  } catch (error) {
    console.error('❌ Database schema test failed:', error);
  }

  // Summary
  console.log('\n📊 DIAGNOSTIC SUMMARY');
  console.log('=====================');
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}`);
  });

  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! SEO Analyzer integration should work correctly.');
  } else {
    console.log('\n🔧 Some tests failed. Follow the solutions above to fix the issues.');
  }

  return results;
}

// Auto-run diagnostics
runDiagnostics().catch(console.error);

// Export for manual testing
window.seoAnalyzerDiagnostics = {
  runDiagnostics,
  
  // Quick test functions
  async testSaveAnalysis() {
    console.log('🧪 Testing Save Analysis...');
    try {
      const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
      const { supabase } = await import('/src/lib/supabase.ts');
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('❌ User not authenticated');
        return;
      }

      const testData = {
        user_id: user.id,
        url: 'https://example.com',
        analysis_mode: 'page',
        tool_type: 'seo_analyzer',
        analysis_version: '1.0',
        overall_score: 85,
        basic_info: { title: 'Test Page', title_length: 9 },
        content_analysis: { word_count: 500 },
        seo_checks: { has_title: true },
        recommendations: [],
        status: 'completed'
      };

      const result = await seoAnalysisService.saveAnalysis(testData);
      console.log('✅ Save analysis test successful:', result.id);
      
      // Clean up test data
      await seoAnalysisService.deleteAnalysis(result.id);
      console.log('✅ Test data cleaned up');
      
    } catch (error) {
      console.error('❌ Save analysis test failed:', error);
    }
  },

  async testDatabaseConnection() {
    console.log('🧪 Testing Database Connection...');
    try {
      const { supabase } = await import('/src/lib/supabase.ts');
      
      const { data, error } = await supabase
        .from('seo_analyses')
        .select('count');
      
      if (error) {
        console.error('❌ Database connection failed:', error);
      } else {
        console.log('✅ Database connection successful');
      }
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
    }
  }
};

console.log('\n💡 Manual testing available:');
console.log('- window.seoAnalyzerDiagnostics.testSaveAnalysis()');
console.log('- window.seoAnalyzerDiagnostics.testDatabaseConnection()');
console.log('- window.seoAnalyzerDiagnostics.runDiagnostics()');
