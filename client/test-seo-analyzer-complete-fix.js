// Complete test script to verify SEO Analyzer end-to-end functionality
// Run this in the browser console to test the complete fix

console.log('🔧 Testing SEO Analyzer Complete Fix');
console.log('===================================');

async function testCompleteFlow() {
  console.log('\n🚀 Step 1: Testing API Connectivity...');
  
  try {
    // Test the test endpoint first
    const testResponse = await fetch('/api/seo/test');
    const testData = await testResponse.json();
    
    if (testResponse.ok && testData.status === 'success') {
      console.log('✅ API connectivity test passed');
    } else {
      console.error('❌ API connectivity test failed:', testData);
      return false;
    }
  } catch (error) {
    console.error('❌ API connectivity test error:', error);
    return false;
  }
  
  console.log('\n📊 Step 2: Testing SEO Analysis...');
  
  try {
    // Test actual SEO analysis
    const analysisResponse = await fetch('/api/seo/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://example.com',
        mode: 'page'
      })
    });
    
    if (!analysisResponse.ok) {
      console.error('❌ SEO analysis failed with status:', analysisResponse.status);
      const errorData = await analysisResponse.json();
      console.error('Error details:', errorData);
      return false;
    }
    
    const analysisData = await analysisResponse.json();
    
    if (analysisData.status === 'success') {
      console.log('✅ SEO analysis successful');
      console.log('📈 Overall score calculation available:', !!analysisData.seo_checks);
      console.log('🏆 Achievements found:', analysisData.achievements?.length || 0);
      console.log('💡 Recommendations found:', analysisData.recommendations?.length || 0);
      console.log('⚡ Processing time:', analysisData.processing_time?.toFixed(2) + 's');
    } else {
      console.error('❌ SEO analysis returned error status:', analysisData);
      return false;
    }
  } catch (error) {
    console.error('❌ SEO analysis error:', error);
    return false;
  }
  
  console.log('\n🔐 Step 3: Testing Authentication...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.warn('⚠️ User not authenticated - database tests will be skipped');
      console.log('💡 Please sign in to test database integration');
      return true; // API is working, just no auth
    }
    
    console.log('✅ User authenticated:', {
      userId: user.id,
      email: user.email
    });
    
    return await testDatabaseIntegration(user.id);
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return false;
  }
}

async function testDatabaseIntegration(userId) {
  console.log('\n💾 Step 4: Testing Database Integration...');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    // Test getting recent analyses
    console.log('🔄 Testing getRecentAnalyses...');
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    console.log('✅ Recent analyses retrieved:', {
      count: recentAnalyses.length
    });
    
    // Test getting favorite analyses
    console.log('🔄 Testing getFavoriteAnalyses...');
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    console.log('✅ Favorite analyses retrieved:', {
      count: favoriteAnalyses.length
    });
    
    // Test saving an analysis
    console.log('🔄 Testing saveAnalysis...');
    const testAnalysisData = {
      user_id: userId,
      url: 'https://example.com/test-complete-fix',
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 88,
      basic_info: { 
        title: 'Complete Fix Test', 
        title_length: 17,
        meta_description: 'Testing complete SEO analyzer fix',
        meta_description_length: 34,
        h1_tags: ['Complete Fix Test'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 450,
        images: { total: 3, without_alt: 0 },
        links: { total: 8, internal: 5, external: 3 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true,
        is_https: true
      },
      recommendations: [
        {
          category: 'Test',
          issue: 'This is a test recommendation',
          importance: 'low',
          recommendation: 'This is just for testing the complete fix'
        }
      ],
      achievements: [
        {
          category: 'Test',
          achievement: 'Complete Fix Working',
          description: 'The SEO analyzer complete fix is working correctly',
          icon: '🎉',
          impact: 'positive'
        }
      ],
      status: 'completed'
    };

    const savedAnalysis = await seoAnalysisService.saveAnalysis(testAnalysisData);
    console.log('✅ Analysis saved successfully:', {
      id: savedAnalysis.id,
      url: savedAnalysis.url,
      score: savedAnalysis.overall_score
    });
    
    // Test updating analysis (toggle favorite)
    console.log('🔄 Testing updateAnalysis...');
    const updatedAnalysis = await seoAnalysisService.updateAnalysis(savedAnalysis.id, {
      is_favorite: true,
      custom_name: 'Complete Fix Test - Favorited'
    });
    console.log('✅ Analysis updated successfully:', {
      id: updatedAnalysis.id,
      is_favorite: updatedAnalysis.is_favorite,
      custom_name: updatedAnalysis.custom_name
    });
    
    // Clean up test data
    console.log('🧹 Cleaning up test data...');
    await seoAnalysisService.deleteAnalysis(savedAnalysis.id);
    console.log('✅ Test data cleaned up');
    
    return true;
  } catch (error) {
    console.error('❌ Database integration test failed:', error);
    console.log('Error details:', {
      message: error.message,
      stack: error.stack
    });
    return false;
  }
}

async function testFrontendComponents() {
  console.log('\n🎨 Step 5: Testing Frontend Components...');
  
  try {
    // Check if SEO Analyzer component is available
    const seoAnalyzerElement = document.querySelector('[data-testid="seo-analyzer"]') || 
                              document.querySelector('.seo-analyzer') ||
                              document.querySelector('#seo-analyzer');
    
    if (seoAnalyzerElement) {
      console.log('✅ SEO Analyzer component found in DOM');
    } else {
      console.log('⚠️ SEO Analyzer component not found in current page');
      console.log('💡 Navigate to the SEO Analyzer tool to test the UI');
    }
    
    // Test if the hooks are available
    try {
      const { useSEOAnalysis } = await import('/src/components/tools/seo-analyzer/hooks/useSEOAnalysis.ts');
      console.log('✅ useSEOAnalysis hook available');
    } catch (error) {
      console.log('⚠️ useSEOAnalysis hook import failed:', error.message);
    }
    
    try {
      const { useSEOAnalysisHistory } = await import('/src/components/tools/seo-analyzer/hooks/useSEOAnalysisHistory.ts');
      console.log('✅ useSEOAnalysisHistory hook available');
    } catch (error) {
      console.log('⚠️ useSEOAnalysisHistory hook import failed:', error.message);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Frontend component test failed:', error);
    return false;
  }
}

// Run all tests
async function runCompleteTest() {
  console.log('🚀 Starting Complete SEO Analyzer Fix Test...\n');
  
  const apiTestResult = await testCompleteFlow();
  const frontendTestResult = await testFrontendComponents();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 COMPLETE TEST RESULTS');
  console.log('='.repeat(60));
  
  if (apiTestResult && frontendTestResult) {
    console.log('🎉 SUCCESS: SEO Analyzer is completely fixed!');
    console.log('✅ API connectivity working');
    console.log('✅ SEO analysis functional');
    console.log('✅ Database integration working');
    console.log('✅ Frontend components available');
    console.log('\n💡 The SEO Analyzer should now work end-to-end!');
    console.log('🔗 Try navigating to the SEO Analyzer and running an analysis');
  } else {
    console.log('❌ PARTIAL SUCCESS: Some components may still have issues');
    console.log('🔧 Check the errors above for details');
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Navigate to the SEO Analyzer tool');
  console.log('2. Enter a URL (e.g., https://example.com)');
  console.log('3. Click "Analizar" to run an analysis');
  console.log('4. Check that results display correctly');
  console.log('5. Verify that the analysis saves to History tab');
  console.log('6. Test the Favorites functionality');
}

// Auto-run the complete test
runCompleteTest();

// Export for manual testing
window.testSEOAnalyzerComplete = {
  runCompleteTest,
  testCompleteFlow,
  testDatabaseIntegration,
  testFrontendComponents
};

console.log('\n💡 Manual testing available:');
console.log('- window.testSEOAnalyzerComplete.runCompleteTest()');
console.log('- window.testSEOAnalyzerComplete.testCompleteFlow()');
console.log('- window.testSEOAnalyzerComplete.testDatabaseIntegration(userId)');
