/**
 * Debug Auto-Save UI Issue
 * Comprehensive debugging script to identify why UI isn't updating after auto-save
 */

console.log('🔍 DEBUG: Auto-Save UI Issue Investigation');

// Global debug state
window.debugAutoSave = {
  logs: [],
  queryInvalidations: [],
  queryRefetches: [],
  uiUpdates: []
};

function log(message, data = null) {
  const timestamp = new Date().toISOString();
  const logEntry = { timestamp, message, data };
  window.debugAutoSave.logs.push(logEntry);
  console.log(`[${timestamp}] ${message}`, data || '');
}

async function debugAutoSaveUIIssue() {
  log('🚀 Starting comprehensive auto-save UI debugging');
  
  try {
    // Step 1: Check authentication and basic setup
    log('1️⃣ AUTHENTICATION & SETUP CHECK');
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error || !user) {
      log('❌ User not authenticated', error);
      return false;
    }
    log('✅ User authenticated', { email: user.email, id: user.id });
    
    // Step 2: Check if services are available
    log('2️⃣ SERVICE AVAILABILITY CHECK');
    if (!window.designAnalysisService) {
      log('❌ designAnalysisService not available');
      return false;
    }
    log('✅ designAnalysisService available');
    
    // Step 3: Get initial state
    log('3️⃣ INITIAL STATE CHECK');
    const initialAnalyses = await window.designAnalysisService.getUserAnalyses(user.id, 10);
    log('📊 Initial analyses count', { count: initialAnalyses.length });
    
    // Step 4: Check React Query setup
    log('4️⃣ REACT QUERY SETUP CHECK');
    
    // Try to find the query client
    let queryClient = null;
    
    // Method 1: Global window
    if (window.queryClient) {
      queryClient = window.queryClient;
      log('✅ Found queryClient on window');
    }
    
    // Method 2: React DevTools
    if (!queryClient && window.__REACT_QUERY_DEVTOOLS__) {
      queryClient = window.__REACT_QUERY_DEVTOOLS__.queryClient;
      log('✅ Found queryClient from DevTools');
    }
    
    // Method 3: Try to extract from React components
    if (!queryClient) {
      const reactElements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
      log('🔍 Searching for queryClient in React elements', { elementsFound: reactElements.length });
      
      // Try to find it in the React fiber tree (this is hacky but might work)
      for (const element of reactElements) {
        if (element._reactInternalFiber || element._reactInternalInstance) {
          log('🔍 Found React fiber, attempting to extract queryClient');
          break;
        }
      }
    }
    
    if (!queryClient) {
      log('❌ Could not find queryClient - this is a major issue');
      return false;
    }
    
    log('✅ QueryClient found', { type: typeof queryClient });
    
    // Step 5: Check current queries in cache
    log('5️⃣ QUERY CACHE INSPECTION');
    const cache = queryClient.getQueryCache();
    const allQueries = cache.getAll();
    
    log('📊 Total queries in cache', { count: allQueries.length });
    
    const designAnalysisQueries = allQueries.filter(q => 
      q.queryKey[0] === 'design-analyses'
    );
    
    log('📊 Design analysis queries', { 
      count: designAnalysisQueries.length,
      keys: designAnalysisQueries.map(q => q.queryKey)
    });
    
    // Check if the correct query exists
    const userSpecificQuery = allQueries.find(q => 
      q.queryKey[0] === 'design-analyses' && q.queryKey[1] === user.id
    );
    
    if (userSpecificQuery) {
      log('✅ Found user-specific design-analyses query', {
        key: userSpecificQuery.queryKey,
        state: userSpecificQuery.state.status,
        dataUpdatedAt: userSpecificQuery.state.dataUpdatedAt,
        data: userSpecificQuery.state.data?.length || 'no data'
      });
    } else {
      log('❌ User-specific design-analyses query NOT found');
    }
    
    // Step 6: Hook up query invalidation monitoring
    log('6️⃣ SETTING UP QUERY MONITORING');
    
    const originalInvalidateQueries = queryClient.invalidateQueries.bind(queryClient);
    queryClient.invalidateQueries = function(filters) {
      const invalidationEvent = {
        timestamp: new Date().toISOString(),
        filters,
        matchedQueries: cache.findAll(filters).length
      };
      window.debugAutoSave.queryInvalidations.push(invalidationEvent);
      log('🔄 Query invalidation triggered', invalidationEvent);
      return originalInvalidateQueries(filters);
    };
    
    // Step 7: Test auto-save process
    log('7️⃣ TESTING AUTO-SAVE PROCESS');
    
    const testAnalysisData = {
      user_id: user.id,
      original_filename: 'debug-test.png',
      file_size: 1024,
      file_type: 'image/png',
      file_url: null,
      overall_score: 85,
      complexity_scores: { visual: 80, cognitive: 85, structural: 90 },
      analysis_areas: [{
        name: 'Layout',
        score: 85,
        description: 'Debug test analysis',
        recommendations: ['Debug recommendation']
      }],
      recommendations: ['Debug overall recommendation'],
      ai_analysis_summary: 'Debug test summary',
      gemini_analysis: 'Debug test analysis',
      agent_message: 'Debug test message',
      visuai_insights: 'Debug test insights',
      tags: ['debug', 'ui-test']
    };
    
    log('💾 Saving test analysis...');
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testAnalysisData, null);
    
    if (!savedAnalysis) {
      log('❌ Failed to save test analysis');
      return false;
    }
    
    log('✅ Test analysis saved', { id: savedAnalysis.id });
    
    // Step 8: Manually trigger invalidation (like auto-save does)
    log('8️⃣ MANUAL QUERY INVALIDATION');
    
    log('🔄 Invalidating design-analyses query...');
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    log('🔄 Invalidating design-analysis-stats query...');
    await queryClient.invalidateQueries({ queryKey: ['design-analysis-stats', user.id] });
    
    // Step 9: Wait and check if queries refetch
    log('9️⃣ WAITING FOR QUERY REFETCH');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if the query was refetched
    const updatedQuery = cache.find(['design-analyses', user.id]);
    if (updatedQuery) {
      log('📊 Query state after invalidation', {
        status: updatedQuery.state.status,
        isFetching: updatedQuery.state.isFetching,
        dataUpdatedAt: updatedQuery.state.dataUpdatedAt,
        data: updatedQuery.state.data?.length || 'no data'
      });
    }
    
    // Step 10: Check if data was actually updated
    log('🔟 DATA UPDATE VERIFICATION');
    
    const updatedAnalyses = await window.designAnalysisService.getUserAnalyses(user.id, 10);
    log('📊 Updated analyses count', { count: updatedAnalyses.length });
    
    const foundTestAnalysis = updatedAnalyses.find(a => a.id === savedAnalysis.id);
    if (foundTestAnalysis) {
      log('✅ Test analysis found in database');
    } else {
      log('❌ Test analysis NOT found in database');
    }
    
    // Step 11: Check UI state
    log('1️⃣1️⃣ UI STATE CHECK');
    
    // Check if we're on the history tab
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      log('🔄 Switching to history tab...');
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Look for analysis cards in the UI
    const analysisCards = document.querySelectorAll('[data-testid*="analysis"], [class*="analysis"], .analysis-card');
    log('📊 Analysis cards in UI', { count: analysisCards.length });
    
    // Look for our specific test analysis
    const testAnalysisInUI = Array.from(document.querySelectorAll('*')).find(el => 
      el.textContent && el.textContent.includes('debug-test.png')
    );
    
    if (testAnalysisInUI) {
      log('✅ Test analysis found in UI');
    } else {
      log('❌ Test analysis NOT found in UI');
      
      // Debug: What's actually in the UI?
      const textContent = document.body.textContent;
      if (textContent.includes('No hay análisis') || textContent.includes('Sin análisis')) {
        log('⚠️ UI shows "no analyses" message');
      }
      
      // Check for loading states
      const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"], .animate-spin');
      if (loadingElements.length > 0) {
        log('⚠️ UI shows loading state', { count: loadingElements.length });
      }
    }
    
    // Step 12: Check React component state
    log('1️⃣2️⃣ REACT COMPONENT STATE CHECK');
    
    // Try to access the component's state (this is hacky but might give us insights)
    const reactElements = document.querySelectorAll('[data-reactroot] *');
    let componentStateFound = false;
    
    for (const element of reactElements) {
      if (element._reactInternalFiber || element._reactInternalInstance) {
        log('🔍 Found React component with state');
        componentStateFound = true;
        break;
      }
    }
    
    if (!componentStateFound) {
      log('⚠️ Could not access React component state');
    }
    
    // Step 13: Clean up
    log('1️⃣3️⃣ CLEANUP');
    
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    log('🧹 Test analysis deleted');
    
    // Final invalidation
    await queryClient.invalidateQueries({ queryKey: ['design-analyses', user.id] });
    
    log('✅ Debug investigation completed');
    
    // Restore original invalidateQueries
    queryClient.invalidateQueries = originalInvalidateQueries;
    
    return true;
    
  } catch (error) {
    log('❌ Debug investigation failed', { error: error.message, stack: error.stack });
    return false;
  }
}

// Helper function to show debug summary
function showDebugSummary() {
  console.log('\n' + '='.repeat(60));
  console.log('📋 DEBUG SUMMARY');
  console.log('='.repeat(60));
  
  console.log('\n🔄 Query Invalidations:', window.debugAutoSave.queryInvalidations.length);
  window.debugAutoSave.queryInvalidations.forEach((inv, i) => {
    console.log(`  ${i + 1}. ${inv.timestamp} - Matched ${inv.matchedQueries} queries`, inv.filters);
  });
  
  console.log('\n📝 All Logs:');
  window.debugAutoSave.logs.forEach(log => {
    console.log(`  ${log.timestamp} - ${log.message}`, log.data || '');
  });
  
  console.log('\n' + '='.repeat(60));
}

// Run the debug investigation
debugAutoSaveUIIssue().then(success => {
  showDebugSummary();
  
  if (success) {
    console.log('🎉 DEBUG INVESTIGATION COMPLETED');
  } else {
    console.log('💥 DEBUG INVESTIGATION FAILED');
  }
}).catch(error => {
  console.error('💥 Debug investigation crashed:', error);
  showDebugSummary();
});

// Export for manual use
window.debugAutoSaveUIIssue = debugAutoSaveUIIssue;
window.showDebugSummary = showDebugSummary;
console.log('🔧 Debug functions available: window.debugAutoSaveUIIssue(), window.showDebugSummary()');
