// Quick Authentication State Debug
// Run this in the browser console on the SEO Analyzer page

console.log('🔍 Quick Authentication State Check...');

async function quickAuthCheck() {
  try {
    // Import Supabase
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Check auth state
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Not authenticated:', error);
      return;
    }
    
    // Simulate the authentication logic used in components
    const authLoading = false; // Simulate auth loading complete
    const isAuthenticated = !!user && user.id !== 'anonymous';
    const queryEnabled = !authLoading && !!user?.id && isAuthenticated;
    
    console.log('✅ Authentication State:', {
      userExists: !!user,
      userId: user.id,
      userEmail: user.email,
      isAnonymous: user.id === 'anonymous',
      authLoading,
      isAuthenticated,
      queryEnabled
    });
    
    if (queryEnabled) {
      console.log('✅ Queries should be ENABLED');
      
      // Test a quick database query
      const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
      
      try {
        const analyses = await seoAnalysisService.getUserAnalyses(user.id, { limit: 5 });
        console.log('📊 Current analyses count:', analyses.length);
        
        if (analyses.length === 0) {
          console.log('ℹ️ No analyses found - this is why Dashboard shows empty state');
          console.log('💡 Try running an SEO analysis to create some data');
        } else {
          console.log('📋 Found analyses:', analyses.map(a => ({
            id: a.id,
            url: a.url,
            created_at: a.created_at
          })));
        }
      } catch (serviceError) {
        console.error('❌ Service error:', serviceError);
      }
    } else {
      console.error('❌ Queries would be DISABLED');
    }
    
  } catch (error) {
    console.error('💥 Debug error:', error);
  }
}

quickAuthCheck();
