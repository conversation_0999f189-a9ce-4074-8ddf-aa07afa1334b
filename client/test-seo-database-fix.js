// Test script to verify SEO Analyzer database fix
// Run this in the browser console to test the database connection

console.log('🔧 Testing SEO Analyzer Database Fix');
console.log('====================================');

async function testSEODatabaseFix() {
  console.log('\n📡 Step 1: Testing Database Connection...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test basic connection to api.seo_analyses table
    const { data, error } = await supabase
      .from('seo_analyses')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error);
      console.log('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return false;
    }
    
    console.log('✅ Database connection successful - api.seo_analyses table accessible');
    return true;
  } catch (error) {
    console.error('❌ Failed to test database connection:', error);
    return false;
  }
}

async function testAuthentication() {
  console.log('\n🔐 Step 2: Testing Authentication...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.warn('⚠️ User not authenticated - some tests will be skipped');
      console.log('💡 Please sign in to test database operations');
      return null;
    }
    
    console.log('✅ User authenticated:', {
      userId: user.id,
      email: user.email
    });
    
    return user;
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return null;
  }
}

async function testSEOAnalysisService() {
  console.log('\n🔧 Step 3: Testing SEO Analysis Service...');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    console.log('✅ SEO Analysis Service imported successfully');
    
    // Test getting recent analyses (should work even with empty data)
    const recentAnalyses = await seoAnalysisService.getRecentAnalyses();
    console.log('✅ Recent analyses retrieved:', {
      count: recentAnalyses.length
    });
    
    // Test getting favorite analyses
    const favoriteAnalyses = await seoAnalysisService.getFavoriteAnalyses();
    console.log('✅ Favorite analyses retrieved:', {
      count: favoriteAnalyses.length
    });
    
    return true;
  } catch (error) {
    console.error('❌ SEO Analysis Service test failed:', error);
    console.log('Error details:', {
      message: error.message,
      stack: error.stack
    });
    return false;
  }
}

async function testDirectQuery(userId) {
  console.log('\n📊 Step 4: Testing Direct Database Query...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test with explicit column selection to avoid schema issues
    const { data, error } = await supabase
      .from('seo_analyses')
      .select(`
        id,
        created_at,
        user_id,
        url,
        analysis_mode,
        tool_type,
        overall_score,
        is_favorite
      `)
      .eq('user_id', userId)
      .limit(5);
    
    if (error) {
      console.error('❌ Direct query failed:', {
        error: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return false;
    }
    
    console.log('✅ Direct query successful:', {
      recordsFound: data?.length || 0,
      sampleRecord: data?.[0] || 'No records'
    });
    
    return true;
  } catch (error) {
    console.error('❌ Direct query error:', error);
    return false;
  }
}

async function testSaveAnalysis(userId) {
  console.log('\n💾 Step 5: Testing Save Analysis...');
  
  try {
    const { seoAnalysisService } = await import('/src/services/seoAnalysisService.ts');
    
    const testData = {
      user_id: userId,
      url: 'https://example.com/test',
      analysis_mode: 'page',
      tool_type: 'seo_analyzer',
      analysis_version: '1.0',
      overall_score: 85,
      basic_info: { 
        title: 'Test Page', 
        title_length: 9,
        meta_description: 'Test description',
        meta_description_length: 16,
        h1_tags: ['Test H1'],
        h1_count: 1
      },
      content_analysis: { 
        word_count: 500,
        images: { total: 2, without_alt: 0 },
        links: { total: 5, internal: 3, external: 2 }
      },
      seo_checks: { 
        has_title: true,
        has_meta_description: true,
        has_h1: true
      },
      recommendations: [],
      status: 'completed'
    };

    console.log('🔄 Attempting to save test analysis...');
    const result = await seoAnalysisService.saveAnalysis(testData);
    console.log('✅ Save analysis test successful:', {
      id: result.id,
      url: result.url,
      score: result.overall_score
    });
    
    // Clean up test data
    console.log('🧹 Cleaning up test data...');
    await seoAnalysisService.deleteAnalysis(result.id);
    console.log('✅ Test data cleaned up');
    
    return true;
  } catch (error) {
    console.error('❌ Save analysis test failed:', error);
    console.log('Error details:', {
      message: error.message,
      stack: error.stack
    });
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting SEO Analyzer Database Fix Tests...\n');
  
  const dbTestResult = await testSEODatabaseFix();
  if (!dbTestResult) {
    console.log('\n❌ Database connection failed - stopping tests');
    console.log('💡 The table should now be in the api schema. Try refreshing the page.');
    return;
  }
  
  const user = await testAuthentication();
  const serviceTestResult = await testSEOAnalysisService();
  
  let directQueryResult = true;
  let saveTestResult = true;
  
  if (user) {
    directQueryResult = await testDirectQuery(user.id);
    if (directQueryResult) {
      saveTestResult = await testSaveAnalysis(user.id);
    }
  } else {
    console.log('\n⚠️ Skipping user-specific tests (not authenticated)');
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL RESULT');
  console.log('='.repeat(60));
  
  if (dbTestResult && serviceTestResult && directQueryResult && saveTestResult) {
    console.log('🎉 SUCCESS: SEO Analyzer database integration is now working!');
    console.log('✅ Database connection established');
    console.log('✅ Service layer functional');
    console.log('✅ Direct queries working');
    console.log('✅ Save/delete operations working');
    console.log('\n💡 You can now use the SEO Analyzer with full database integration!');
  } else {
    console.log('❌ PARTIAL SUCCESS: Some tests failed');
    console.log('🔧 Please check the errors above');
  }
}

// Auto-run the tests
runAllTests();

// Export for manual testing
window.testSEODatabaseFix = {
  runAllTests,
  testSEODatabaseFix,
  testAuthentication,
  testSEOAnalysisService,
  testDirectQuery,
  testSaveAnalysis
};

console.log('\n💡 Manual testing available:');
console.log('- window.testSEODatabaseFix.runAllTests()');
console.log('- window.testSEODatabaseFix.testSEODatabaseFix()');
console.log('- window.testSEODatabaseFix.testSEOAnalysisService()');
