/**
 * Test script to verify auto-save functionality is working properly
 * Run this in the browser console on the Design Complexity Analyzer page
 */

console.log('🧪 Testing Auto-Save Functionality Fix');

// Test function to simulate analysis completion and auto-save
async function testAutoSave() {
  console.log('🔍 Checking if user is authenticated...');
  
  // Check if user is authenticated
  const user = window.supabase?.auth?.getUser ? await window.supabase.auth.getUser() : null;
  if (!user?.data?.user) {
    console.log('❌ User not authenticated. Please log in first.');
    return;
  }
  
  console.log('✅ User authenticated:', user.data.user.email);
  
  // Check if designAnalysisService is available
  if (!window.designAnalysisService) {
    console.log('❌ designAnalysisService not available');
    return;
  }
  
  console.log('✅ designAnalysisService available');
  
  // Create test analysis data
  const testAnalysisData = {
    user_id: user.data.user.id,
    original_filename: 'test-auto-save.png',
    file_size: 12345,
    file_type: 'image/png',
    file_url: null,
    overall_score: 85,
    complexity_scores: {
      visual: 80,
      cognitive: 85,
      structural: 90
    },
    analysis_areas: [
      {
        name: 'Layout',
        score: 85,
        description: 'Test layout analysis',
        recommendations: ['Test recommendation 1']
      }
    ],
    recommendations: ['Test overall recommendation'],
    ai_analysis_summary: 'Test AI analysis summary',
    gemini_analysis: 'Test Gemini analysis',
    agent_message: 'Test agent message',
    visuai_insights: 'Test visual insights',
    tags: ['test', 'auto-save-fix']
  };
  
  console.log('📊 Test analysis data prepared:', testAnalysisData);
  
  try {
    console.log('💾 Testing saveAnalysis function...');
    
    // Test the saveAnalysis function directly
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testAnalysisData, null);
    
    if (savedAnalysis && savedAnalysis.id) {
      console.log('✅ Auto-save test PASSED!');
      console.log('📝 Saved analysis ID:', savedAnalysis.id);
      console.log('📅 Created at:', savedAnalysis.created_at);
      
      // Clean up test data
      console.log('🧹 Cleaning up test data...');
      await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('✅ Test data cleaned up');
      
      return true;
    } else {
      console.log('❌ Auto-save test FAILED - no analysis returned');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Auto-save test FAILED with error:', error);
    return false;
  }
}

// Test the auto-save functionality
testAutoSave().then(success => {
  if (success) {
    console.log('🎉 AUTO-SAVE FIX VERIFICATION: SUCCESS');
    console.log('✅ The auto-save functionality is working correctly');
  } else {
    console.log('💥 AUTO-SAVE FIX VERIFICATION: FAILED');
    console.log('❌ The auto-save functionality still has issues');
  }
}).catch(error => {
  console.error('💥 Test execution failed:', error);
});

// Export for manual testing
window.testAutoSave = testAutoSave;
console.log('🔧 Test function available as window.testAutoSave()');
