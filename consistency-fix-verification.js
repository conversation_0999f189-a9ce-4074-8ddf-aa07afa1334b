/**
 * Consistency Fix Verification Script
 * Tests the implemented solution for consistent mood board image persistence
 */

console.log('🔧 Mood Board Consistency Fix Verification');
console.log('==========================================\n');

class ConsistencyFixVerification {
  constructor() {
    this.workingBoardId = '2659ce54-f3ea-4926-bbe9-a605cff2c50d';
    this.nonWorkingBoardId = 'f7bbb990-3f6c-413f-b721-f0fdc4a52a62';
    this.currentBoardId = this.getCurrentBoardId();
    this.testResults = [];
    this.assetStoreLogs = [];
    this.originalConsoleLog = console.log;
  }

  async runVerification() {
    console.log('🚀 Starting consistency fix verification...\n');
    
    try {
      this.setupLogging();
      await this.test1_AssetStoreInitialization();
      await this.test2_AssetStoreType();
      await this.test3_ImageUploadConsistency();
      await this.test4_CrossBoardComparison();
      await this.test5_PersistenceVerification();
      
      this.displayVerificationResults();
    } catch (error) {
      console.error('❌ Verification failed:', error);
    } finally {
      this.restoreLogging();
    }
  }

  setupLogging() {
    const self = this;
    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('MoodBoard') || message.includes('Asset Store') || message.includes('Tldraw')) {
        self.assetStoreLogs.push({
          timestamp: new Date().toISOString(),
          message: message,
          boardId: self.currentBoardId
        });
      }
      self.originalConsoleLog.apply(console, args);
    };
  }

  async test1_AssetStoreInitialization() {
    console.log('📋 Test 1: Asset Store Initialization');
    
    try {
      // Wait for component mounting
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const initLogs = this.assetStoreLogs.filter(log => 
        log.message.includes('Creating') || 
        log.message.includes('fallback') ||
        log.message.includes('Tldraw mounted')
      );
      
      if (initLogs.length > 0) {
        console.log('✅ Asset store initialization detected');
        
        // Check for asset store type
        const authAssetStore = initLogs.find(log => log.message.includes('authenticated asset store'));
        const fallbackAssetStore = initLogs.find(log => log.message.includes('fallback asset store'));
        const mountLog = initLogs.find(log => log.message.includes('Tldraw mounted'));
        
        if (authAssetStore) {
          console.log('✅ Authenticated asset store created');
          this.testResults.push({ test: 'Asset Store Type', result: 'AUTHENTICATED' });
        } else if (fallbackAssetStore) {
          console.log('⚠️ Fallback asset store used');
          this.testResults.push({ test: 'Asset Store Type', result: 'FALLBACK' });
        }
        
        if (mountLog) {
          console.log('✅ Tldraw mounted with asset store');
          this.testResults.push({ test: 'Tldraw Mounting', result: 'SUCCESS' });
        }
        
        this.testResults.push({ test: 'Asset Store Initialization', result: 'SUCCESS' });
      } else {
        console.log('❌ No asset store initialization detected');
        this.testResults.push({ test: 'Asset Store Initialization', result: 'FAIL' });
      }

    } catch (error) {
      console.error('❌ Error in test 1:', error);
      this.testResults.push({ test: 'Asset Store Initialization', result: 'ERROR' });
    }
  }

  async test2_AssetStoreType() {
    console.log('\n📋 Test 2: Asset Store Type Detection');
    
    try {
      const mountLogs = this.assetStoreLogs.filter(log => 
        log.message.includes('assetStoreType')
      );
      
      if (mountLogs.length > 0) {
        const latestMountLog = mountLogs[mountLogs.length - 1];
        console.log('✅ Asset store type information found');
        console.log(`   📊 ${latestMountLog.message}`);
        
        if (latestMountLog.message.includes('AUTHENTICATED')) {
          this.testResults.push({ test: 'Asset Store Type Detection', result: 'AUTHENTICATED' });
        } else if (latestMountLog.message.includes('FALLBACK')) {
          this.testResults.push({ test: 'Asset Store Type Detection', result: 'FALLBACK' });
        }
      } else {
        console.log('⚠️ No asset store type information found');
        this.testResults.push({ test: 'Asset Store Type Detection', result: 'UNKNOWN' });
      }

    } catch (error) {
      console.error('❌ Error in test 2:', error);
      this.testResults.push({ test: 'Asset Store Type Detection', result: 'ERROR' });
    }
  }

  async test3_ImageUploadConsistency() {
    console.log('\n📋 Test 3: Image Upload Consistency');
    
    try {
      // Create test image
      const testFile = await this.createTestImage();
      console.log(`✅ Test image created: ${testFile.name}`);
      
      // Test upload
      const canvas = document.querySelector('canvas');
      if (canvas) {
        console.log('🔄 Testing image upload...');
        
        const beforeUploadLogs = this.assetStoreLogs.length;
        
        // Simulate drop
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(testFile);
        
        const dropEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          dataTransfer: dataTransfer
        });
        
        canvas.dispatchEvent(dropEvent);
        
        // Wait for processing
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const afterUploadLogs = this.assetStoreLogs.length;
        const newLogs = this.assetStoreLogs.slice(beforeUploadLogs);
        
        // Check for upload activity
        const uploadLogs = newLogs.filter(log => 
          log.message.includes('Upload') || 
          log.message.includes('asset')
        );
        
        if (uploadLogs.length > 0) {
          console.log('✅ Upload activity detected');
          uploadLogs.forEach(log => {
            console.log(`   📤 ${log.message}`);
          });
          this.testResults.push({ test: 'Image Upload Consistency', result: 'SUCCESS' });
        } else {
          console.log('⚠️ No upload activity detected');
          this.testResults.push({ test: 'Image Upload Consistency', result: 'NO_ACTIVITY' });
        }
        
      } else {
        console.log('❌ Canvas not found');
        this.testResults.push({ test: 'Image Upload Consistency', result: 'NO_CANVAS' });
      }

    } catch (error) {
      console.error('❌ Error in test 3:', error);
      this.testResults.push({ test: 'Image Upload Consistency', result: 'ERROR' });
    }
  }

  async test4_CrossBoardComparison() {
    console.log('\n📋 Test 4: Cross-Board Comparison');
    
    try {
      const boardType = this.currentBoardId === this.workingBoardId ? 'WORKING' :
                       this.currentBoardId === this.nonWorkingBoardId ? 'NON_WORKING' : 'OTHER';
      
      console.log(`ℹ️ Current board type: ${boardType}`);
      console.log(`ℹ️ Board ID: ${this.currentBoardId}`);
      
      // Check if asset store is consistently available
      const hasAssetStore = this.assetStoreLogs.some(log => 
        log.message.includes('asset store') || 
        log.message.includes('Asset Store')
      );
      
      if (hasAssetStore) {
        console.log('✅ Asset store available on this board');
        this.testResults.push({ test: 'Cross-Board Asset Store', result: 'AVAILABLE', details: boardType });
      } else {
        console.log('❌ Asset store not available on this board');
        this.testResults.push({ test: 'Cross-Board Asset Store', result: 'UNAVAILABLE', details: boardType });
      }

    } catch (error) {
      console.error('❌ Error in test 4:', error);
      this.testResults.push({ test: 'Cross-Board Comparison', result: 'ERROR' });
    }
  }

  async test5_PersistenceVerification() {
    console.log('\n📋 Test 5: Persistence Verification');
    
    try {
      // Check current mood board data
      const response = await fetch(`/api/moodboard/${this.currentBoardId}`);
      if (response.ok) {
        const data = await response.json();
        const moodboard = data.data;
        
        if (moodboard?.tldraw_data) {
          const imageShapes = this.findImageShapes(moodboard.tldraw_data);
          console.log(`ℹ️ Found ${imageShapes.length} image shapes in mood board`);
          
          let validUrls = 0;
          let nullUrls = 0;
          
          imageShapes.forEach((shape, index) => {
            const src = shape.props?.src;
            if (src && src !== 'null' && src !== null) {
              validUrls++;
              console.log(`✅ Image ${index + 1}: Valid URL`);
            } else {
              nullUrls++;
              console.log(`❌ Image ${index + 1}: NULL URL`);
            }
          });
          
          if (nullUrls === 0 && validUrls > 0) {
            console.log('🎉 All images have valid URLs - persistence working!');
            this.testResults.push({ test: 'Persistence Verification', result: 'SUCCESS' });
          } else if (nullUrls > 0) {
            console.log(`⚠️ Found ${nullUrls} images with null URLs`);
            this.testResults.push({ test: 'Persistence Verification', result: 'PARTIAL', details: `${nullUrls} null URLs` });
          } else {
            console.log('ℹ️ No images to verify');
            this.testResults.push({ test: 'Persistence Verification', result: 'NO_IMAGES' });
          }
          
        } else {
          console.log('ℹ️ No tldraw data to verify');
          this.testResults.push({ test: 'Persistence Verification', result: 'NO_DATA' });
        }
      } else {
        console.log('❌ Failed to fetch mood board data');
        this.testResults.push({ test: 'Persistence Verification', result: 'FETCH_FAILED' });
      }

    } catch (error) {
      console.error('❌ Error in test 5:', error);
      this.testResults.push({ test: 'Persistence Verification', result: 'ERROR' });
    }
  }

  // Helper methods
  getCurrentBoardId() {
    const url = window.location.href;
    const parts = url.split('/');
    const editorIndex = parts.findIndex(part => part === 'editor');
    return editorIndex !== -1 && editorIndex + 1 < parts.length ? parts[editorIndex + 1] : null;
  }

  async createTestImage() {
    const canvas = document.createElement('canvas');
    canvas.width = 120;
    canvas.height = 120;
    const ctx = canvas.getContext('2d');
    
    // Create verification pattern
    ctx.fillStyle = '#27ae60';
    ctx.fillRect(0, 0, 60, 60);
    ctx.fillStyle = '#e74c3c';
    ctx.fillRect(60, 0, 60, 60);
    ctx.fillStyle = '#3498db';
    ctx.fillRect(0, 60, 60, 60);
    ctx.fillStyle = '#f39c12';
    ctx.fillRect(60, 60, 60, 60);
    
    // Add text
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('FIX', 60, 65);
    
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    return new File([blob], 'consistency-fix-test.png', { type: 'image/png' });
  }

  findImageShapes(tldrawData) {
    const imageShapes = [];
    if (tldrawData?.store) {
      for (const [key, shape] of Object.entries(tldrawData.store)) {
        if (shape && shape.type === 'image') {
          imageShapes.push(shape);
        }
      }
    }
    return imageShapes;
  }

  displayVerificationResults() {
    console.log('\n🎯 CONSISTENCY FIX VERIFICATION RESULTS');
    console.log('=======================================');
    
    this.testResults.forEach(result => {
      const emoji = result.result === 'SUCCESS' || result.result === 'AUTHENTICATED' ? '✅' :
                   result.result === 'FAIL' || result.result === 'ERROR' ? '❌' :
                   result.result === 'FALLBACK' ? '🔄' : '⚠️';
      
      console.log(`${emoji} ${result.test}: ${result.result}`);
      if (result.details) {
        console.log(`   Details: ${result.details}`);
      }
    });
    
    console.log(`\n📊 Total Asset Store Logs: ${this.assetStoreLogs.length}`);
    
    // Provide fix assessment
    const hasAssetStore = this.testResults.some(r => r.test === 'Asset Store Initialization' && r.result === 'SUCCESS');
    const hasConsistentBehavior = this.testResults.some(r => r.test === 'Cross-Board Asset Store' && r.result === 'AVAILABLE');
    
    console.log('\n🔧 FIX ASSESSMENT:');
    
    if (hasAssetStore && hasConsistentBehavior) {
      console.log('🎉 SUCCESS: Consistency fix is working correctly!');
      console.log('✅ Asset store is now consistently available');
      console.log('✅ Image persistence should work reliably across all mood boards');
    } else if (hasAssetStore) {
      console.log('🔄 PARTIAL: Asset store is working but needs more testing');
      console.log('💡 Test on both working and non-working mood boards');
    } else {
      console.log('❌ ISSUE: Asset store still not working consistently');
      console.log('💡 Additional debugging may be needed');
    }
    
    console.log('\n📋 SUMMARY OF FIXES IMPLEMENTED:');
    console.log('1. ✅ Always provide an asset store (authenticated or fallback)');
    console.log('2. ✅ Handle authentication timing issues');
    console.log('3. ✅ Prevent Tldraw mounting without asset store');
    console.log('4. ✅ Enhanced logging for debugging');
    console.log('5. ✅ Fallback asset store for edge cases');
  }

  restoreLogging() {
    console.log = this.originalConsoleLog;
  }
}

// Auto-run verification
if (window.location.href.includes('mood-board/editor')) {
  const verification = new ConsistencyFixVerification();
  verification.runVerification();
} else {
  console.log('ℹ️ Please run this verification on a mood board editor page');
}

// Export for manual testing
window.ConsistencyFixVerification = ConsistencyFixVerification;
