#!/bin/bash

echo "Testing Focus Group API with Authentication..."

# Test health endpoint first
echo "1. Testing health endpoint..."
curl -s -X GET http://localhost:8001/api/focus-group/health

echo -e "\n\n2. Testing text autocorrect endpoint..."
curl -s -X POST http://localhost:8001/api/text-autocorrect \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Este es un texto que necesita mejoras",
    "context": "Marketing content"
  }'

echo -e "\n\n3. Testing focus group simulation (requires auth)..."
echo "Note: This will return 401 without proper authentication token"
curl -s -X POST http://localhost:8001/api/simulate-focus-group \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Nuevo smartphone con cámara de 108MP y batería de 5000mAh",
    "product_category": "Tecnología",
    "context": "Lanzamiento de producto dirigido a jóvenes profesionales de 25-35 años",
    "questions": [
      "¿Qué opinas sobre la calidad de la cámara?",
      "¿Es importante la duración de la batería para ti?",
      "¿Pagarías un precio premium por estas características?"
    ],
    "num_participants": 3,
    "discussion_rounds": 2
  }'

echo -e "\n\n4. Testing recent simulations endpoint (requires auth)..."
curl -s -X GET http://localhost:8001/api/focus-group/recent

echo -e "\n\nTest completed."
