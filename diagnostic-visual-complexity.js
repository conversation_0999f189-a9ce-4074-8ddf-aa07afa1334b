/**
 * Diagnóstico Completo del Analizador de Complejidad Visual
 * Este script identifica exactamente dónde están los problemas de guardado de historial y URLs de imágenes
 */

console.log('🔍 DIAGNÓSTICO COMPLETO - Analizador de Complejidad Visual');
console.log('='.repeat(60));

// Función para crear una imagen de prueba
function createTestImageFile() {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');
    
    // Crear un diseño de prueba
    ctx.fillStyle = '#3498db';
    ctx.fillRect(0, 0, 400, 300);
    
    ctx.fillStyle = '#ffffff';
    ctx.font = '24px Arial';
    ctx.fillText('PRUEBA DIAGNÓSTICO', 100, 150);
    
    canvas.toBlob((blob) => {
      const file = new File([blob], 'test-diagnostic.png', { type: 'image/png' });
      resolve(file);
    }, 'image/png');
  });
}

// Función principal de diagnóstico
async function runDiagnostic() {
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    errors: [],
    summary: {}
  };

  try {
    console.log('\n📋 PASO 1: Verificación de Servicios');
    console.log('-'.repeat(40));

    // Test 1: Verificar autenticación
    console.log('🔐 Verificando autenticación...');
    const { supabase } = await import('./client/src/lib/supabase.js');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      results.errors.push('Usuario no autenticado');
      console.error('❌ Usuario no autenticado:', authError);
      return results;
    }
    
    console.log('✅ Usuario autenticado:', user.id);
    results.tests.authentication = { success: true, userId: user.id };

    // Test 2: Verificar servicios
    console.log('🔧 Verificando servicios...');
    const designAnalysisService = await import('./client/src/services/designAnalysisService.js');
    results.tests.services = { success: true, designAnalysisService: !!designAnalysisService };

    console.log('\n📋 PASO 2: Prueba de Análisis Completo');
    console.log('-'.repeat(40));

    // Test 3: Crear imagen de prueba
    console.log('🖼️ Creando imagen de prueba...');
    const testFile = await createTestImageFile();
    console.log('✅ Imagen de prueba creada:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });

    // Test 4: Análisis backend
    console.log('🧠 Ejecutando análisis en backend...');
    const formData = new FormData();
    formData.append('design', testFile);

    const analysisResponse = await fetch('/api/analyze-design', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
      },
      body: formData
    });

    if (!analysisResponse.ok) {
      throw new Error(`Backend analysis failed: ${analysisResponse.status} ${analysisResponse.statusText}`);
    }

    const analysisData = await analysisResponse.json();
    console.log('✅ Análisis backend completado:', {
      success: analysisData.success,
      hasAnalysisId: !!analysisData.analysis_id,
      savedToDatabase: analysisData.saved_to_database,
      hasFileUrl: !!analysisData.file_url,
      fileUrl: analysisData.file_url ? analysisData.file_url.substring(0, 50) + '...' : 'NULL'
    });

    results.tests.backendAnalysis = {
      success: analysisData.success,
      analysisId: analysisData.analysis_id,
      savedToDatabase: analysisData.saved_to_database,
      hasFileUrl: !!analysisData.file_url,
      fileUrl: analysisData.file_url
    };

    console.log('\n📋 PASO 3: Verificación de Base de Datos');
    console.log('-'.repeat(40));

    // Test 5: Verificar en base de datos
    if (analysisData.analysis_id) {
      console.log('🔍 Verificando registro en base de datos...');
      
      const { data: dbRecord, error: dbError } = await supabase
        .schema('api')
        .from('design_analyses')
        .select('id, original_filename, file_url, overall_score, created_at, user_id')
        .eq('id', analysisData.analysis_id)
        .single();

      if (dbError) {
        console.error('❌ Error consultando base de datos:', dbError);
        results.errors.push(`Database query error: ${dbError.message}`);
      } else {
        console.log('✅ Registro encontrado en base de datos:', {
          id: dbRecord.id,
          filename: dbRecord.original_filename,
          hasFileUrl: !!dbRecord.file_url,
          fileUrl: dbRecord.file_url ? dbRecord.file_url.substring(0, 50) + '...' : 'NULL',
          score: dbRecord.overall_score,
          userId: dbRecord.user_id
        });

        results.tests.databaseRecord = {
          success: true,
          record: dbRecord,
          hasFileUrl: !!dbRecord.file_url
        };

        // Test 6: Verificar acceso a imagen
        if (dbRecord.file_url) {
          console.log('🖼️ Probando acceso a imagen...');
          
          try {
            const imageResponse = await fetch(`/api/image/${dbRecord.file_url}`, {
              headers: {
                'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
              }
            });

            if (imageResponse.ok) {
              console.log('✅ Imagen accesible desde backend');
              results.tests.imageAccess = { success: true, status: imageResponse.status };
            } else {
              console.error('❌ Error accediendo a imagen:', imageResponse.status, imageResponse.statusText);
              results.tests.imageAccess = { success: false, status: imageResponse.status, error: imageResponse.statusText };
            }
          } catch (imageError) {
            console.error('❌ Error en solicitud de imagen:', imageError);
            results.tests.imageAccess = { success: false, error: imageError.message };
          }
        } else {
          console.warn('⚠️ No hay file_url para probar acceso a imagen');
          results.tests.imageAccess = { success: false, error: 'No file_url in database' };
        }
      }
    }

    console.log('\n📋 PASO 4: Verificación de Historial');
    console.log('-'.repeat(40));

    // Test 7: Verificar historial
    console.log('📚 Verificando historial de análisis...');
    
    const { data: historyRecords, error: historyError } = await supabase
      .schema('api')
      .from('design_analyses')
      .select('id, created_at, original_filename, file_url, overall_score')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5);

    if (historyError) {
      console.error('❌ Error consultando historial:', historyError);
      results.errors.push(`History query error: ${historyError.message}`);
    } else {
      console.log(`✅ Historial encontrado: ${historyRecords.length} registros`);
      
      const recordsWithImages = historyRecords.filter(r => r.file_url);
      const recordsWithoutImages = historyRecords.filter(r => !r.file_url);
      
      console.log(`📊 Estadísticas del historial:`);
      console.log(`   - Total de registros: ${historyRecords.length}`);
      console.log(`   - Con imágenes: ${recordsWithImages.length}`);
      console.log(`   - Sin imágenes: ${recordsWithoutImages.length}`);

      results.tests.history = {
        success: true,
        totalRecords: historyRecords.length,
        recordsWithImages: recordsWithImages.length,
        recordsWithoutImages: recordsWithoutImages.length,
        records: historyRecords.map(r => ({
          id: r.id,
          filename: r.original_filename,
          hasFileUrl: !!r.file_url,
          score: r.overall_score,
          created: r.created_at
        }))
      };
    }

    // Limpiar el análisis de prueba
    if (analysisData.analysis_id) {
      console.log('🧹 Limpiando análisis de prueba...');
      try {
        await designAnalysisService.default.deleteAnalysis(analysisData.analysis_id);
        console.log('✅ Análisis de prueba eliminado');
      } catch (cleanupError) {
        console.warn('⚠️ No se pudo eliminar el análisis de prueba:', cleanupError);
      }
    }

  } catch (error) {
    console.error('💥 Error en diagnóstico:', error);
    results.errors.push(error.message);
  }

  // Generar resumen
  console.log('\n📋 RESUMEN DEL DIAGNÓSTICO');
  console.log('='.repeat(60));

  const issues = [];
  
  if (!results.tests.backendAnalysis?.savedToDatabase) {
    issues.push('❌ Backend no está guardando en base de datos');
  }
  
  if (!results.tests.backendAnalysis?.hasFileUrl) {
    issues.push('❌ Backend no está generando file_url');
  }
  
  if (!results.tests.databaseRecord?.hasFileUrl) {
    issues.push('❌ Base de datos no contiene file_url');
  }
  
  if (!results.tests.imageAccess?.success) {
    issues.push('❌ Imágenes no son accesibles');
  }
  
  if (results.tests.history?.recordsWithoutImages > 0) {
    issues.push(`⚠️ ${results.tests.history.recordsWithoutImages} registros sin imágenes en historial`);
  }

  if (issues.length === 0) {
    console.log('✅ ¡Todo funciona correctamente!');
  } else {
    console.log('🚨 PROBLEMAS IDENTIFICADOS:');
    issues.forEach(issue => console.log(`   ${issue}`));
  }

  results.summary = {
    totalIssues: issues.length,
    issues: issues,
    status: issues.length === 0 ? 'HEALTHY' : 'NEEDS_ATTENTION'
  };

  console.log('\n📊 Resultados completos guardados en window.diagnosticResults');
  window.diagnosticResults = results;
  
  return results;
}

// Ejecutar diagnóstico
runDiagnostic().then(results => {
  console.log('\n🎯 DIAGNÓSTICO COMPLETADO');
  console.log('Revisa window.diagnosticResults para detalles completos');
}).catch(error => {
  console.error('💥 Error ejecutando diagnóstico:', error);
});
