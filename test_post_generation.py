#!/usr/bin/env python3
"""
Test script to verify the post generation fixes work correctly.
Tests simple topics like 'dogs', 'sleeping well', 'fitness' to ensure
the system generates relevant content instead of generic abstractions.
"""

import requests
import json
import sys

def test_post_generation(topic, business_name="Test Business"):
    """Test post generation with a specific topic."""
    
    print(f"\n🧪 Testing post generation for topic: '{topic}'")
    print("=" * 60)
    
    # Prepare the request payload
    payload = {
        "brandInfo": {
            "businessName": business_name,
            "brandColor": "#3018ef",
            "voice": "Profesional y amigable",
            "topics": [topic],
            "ctas": ["¡Descubre más!"],
            "industry": "general",
            "target_audience": "general audience"
        },
        "designConfig": {
            "selectedTheme": "Balance",
            "platform": "Instagram",
            "contentType": "instagram_posts"
        },
        "generationConfig": {
            "count": 1,
            "template": "Balance",
            "analysisComplete": False
        }
    }
    
    try:
        # Make the request
        response = requests.post(
            "http://localhost:8001/api/v1/posts/generate-batch",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("success") and data.get("posts"):
                post = data["posts"][0]
                
                print(f"✅ SUCCESS! Generated post for '{topic}':")
                print(f"📝 Content: {post.get('text', 'No content')}")
                print(f"🎯 Hook: {post.get('metadata', {}).get('visual_hook', 'No hook')}")
                print(f"🖼️ Image URL: {post.get('image_url', 'No image')}")
                print(f"🎨 Visual Concept: {post.get('metadata', {}).get('creative_concept', 'No concept')}")
                
                # Check if content is topic-relevant
                topic_lower = topic.lower()
                content_lower = post.get('text', '').lower()
                hook_lower = post.get('metadata', {}).get('visual_hook', '').lower()
                
                topic_mentioned = any(word in content_lower or word in hook_lower 
                                    for word in topic_lower.split())
                
                if topic_mentioned:
                    print(f"✅ TOPIC RELEVANCE: Content mentions topic '{topic}'")
                else:
                    print(f"❌ TOPIC RELEVANCE: Content doesn't seem related to '{topic}'")
                    print(f"   Content: {post.get('text', '')[:100]}...")
                
                return True
            else:
                print(f"❌ FAILED: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def main():
    """Run tests for different topics."""
    
    print("🚀 Testing Emma Studio Post Generation Fixes")
    print("Testing if the system generates topic-specific content instead of generic abstractions")
    
    # Test cases: simple topics that should generate relevant content
    test_cases = [
        "perros",
        "dormir bien", 
        "fitness",
        "comida saludable",
        "mi marca es de suplementos para perros, se llama Wouf"
    ]
    
    results = []
    
    for topic in test_cases:
        success = test_post_generation(topic)
        results.append((topic, success))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for topic, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {topic}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The post generation fixes are working correctly.")
    else:
        print("⚠️ Some tests failed. The system may still have issues.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
