/**
 * Batch Upload Solution Test
 * Comprehensive testing for the upload queue management system
 */

console.log('🧪 Batch Upload Solution Test');
console.log('=============================\n');

class BatchUploadSolutionTest {
  constructor() {
    this.testResults = [];
    this.uploadLogs = [];
    this.originalConsoleLog = console.log;
    this.testImageSizes = [1, 3, 5, 8, 10]; // Different batch sizes to test
  }

  async runComprehensiveTest() {
    console.log('🚀 Starting comprehensive batch upload solution test...\n');
    
    try {
      this.setupLogging();
      await this.test1_SmallBatchUploads();
      await this.test2_ExactLimitUploads();
      await this.test3_OverflowBatchUploads();
      await this.test4_ProgressFeedbackTest();
      await this.test5_DatabasePersistenceTest();
      await this.test6_EdgeCaseTests();
      
      this.displayComprehensiveResults();
    } catch (error) {
      console.error('❌ Comprehensive test failed:', error);
    } finally {
      this.restoreLogging();
    }
  }

  setupLogging() {
    const self = this;
    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('Upload') || message.includes('Queue') || message.includes('Asset Store')) {
        self.uploadLogs.push({
          timestamp: new Date().toISOString(),
          message: message,
          type: 'log'
        });
      }
      self.originalConsoleLog.apply(console, args);
    };
  }

  async test1_SmallBatchUploads() {
    console.log('📋 Test 1: Small Batch Uploads (≤3 images)');
    
    try {
      for (const batchSize of [1, 2, 3]) {
        console.log(`\n🔄 Testing ${batchSize} image batch...`);
        
        const testImages = await this.createTestImages(batchSize, `small-batch-${batchSize}`);
        const beforeLogs = this.uploadLogs.length;
        
        // Simulate simultaneous upload
        await this.simulateBatchUpload(testImages);
        
        // Wait for processing
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const afterLogs = this.uploadLogs.length;
        const uploadActivity = afterLogs - beforeLogs;
        
        // Check for queue management logs
        const queueLogs = this.uploadLogs.slice(beforeLogs).filter(log => 
          log.message.includes('Queue') || log.message.includes('slots used')
        );
        
        this.testResults.push({
          test: `Small Batch ${batchSize} Images`,
          result: uploadActivity > 0 ? 'SUCCESS' : 'NO_ACTIVITY',
          details: {
            uploadActivity,
            queueManagement: queueLogs.length > 0,
            batchSize
          }
        });
        
        console.log(`   📊 Upload activity: ${uploadActivity} logs, Queue management: ${queueLogs.length > 0 ? 'Yes' : 'No'}`);
      }

    } catch (error) {
      console.error('❌ Error in test 1:', error);
      this.testResults.push({ test: 'Small Batch Uploads', result: 'ERROR', details: error.message });
    }
  }

  async test2_ExactLimitUploads() {
    console.log('\n📋 Test 2: Exact Limit Uploads (3 images)');
    
    try {
      const testImages = await this.createTestImages(3, 'exact-limit');
      const beforeLogs = this.uploadLogs.length;
      
      await this.simulateBatchUpload(testImages);
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      const afterLogs = this.uploadLogs.length;
      const newLogs = this.uploadLogs.slice(beforeLogs);
      
      // Check for concurrent processing
      const concurrentLogs = newLogs.filter(log => 
        log.message.includes('3/3 slots used') || 
        log.message.includes('Processing 3 uploads')
      );
      
      this.testResults.push({
        test: 'Exact Limit (3 Images)',
        result: concurrentLogs.length > 0 ? 'SUCCESS' : 'PARTIAL',
        details: {
          totalActivity: afterLogs - beforeLogs,
          concurrentProcessing: concurrentLogs.length > 0
        }
      });

    } catch (error) {
      console.error('❌ Error in test 2:', error);
      this.testResults.push({ test: 'Exact Limit Uploads', result: 'ERROR', details: error.message });
    }
  }

  async test3_OverflowBatchUploads() {
    console.log('\n📋 Test 3: Overflow Batch Uploads (>3 images)');
    
    try {
      for (const batchSize of [5, 8, 10]) {
        console.log(`\n🔄 Testing ${batchSize} image overflow batch...`);
        
        const testImages = await this.createTestImages(batchSize, `overflow-${batchSize}`);
        const beforeLogs = this.uploadLogs.length;
        
        await this.simulateBatchUpload(testImages);
        
        // Wait longer for queue processing
        await new Promise(resolve => setTimeout(resolve, 6000));
        
        const afterLogs = this.uploadLogs.length;
        const newLogs = this.uploadLogs.slice(beforeLogs);
        
        // Check for queue management
        const queueLogs = newLogs.filter(log => 
          log.message.includes('Added upload to queue') ||
          log.message.includes('queueLength')
        );
        
        // Check for sequential processing
        const processingLogs = newLogs.filter(log => 
          log.message.includes('Processing') && log.message.includes('uploads')
        );
        
        this.testResults.push({
          test: `Overflow Batch ${batchSize} Images`,
          result: queueLogs.length >= batchSize ? 'SUCCESS' : 'PARTIAL',
          details: {
            batchSize,
            queuedUploads: queueLogs.length,
            processingBatches: processingLogs.length,
            totalActivity: afterLogs - beforeLogs
          }
        });
        
        console.log(`   📊 Queued: ${queueLogs.length}/${batchSize}, Processing batches: ${processingLogs.length}`);
      }

    } catch (error) {
      console.error('❌ Error in test 3:', error);
      this.testResults.push({ test: 'Overflow Batch Uploads', result: 'ERROR', details: error.message });
    }
  }

  async test4_ProgressFeedbackTest() {
    console.log('\n📋 Test 4: Progress Feedback Test');
    
    try {
      // Check if upload progress indicator is visible
      const progressIndicator = document.querySelector('[class*="upload-progress"], [class*="UploadProgress"]');
      const hasProgressUI = !!progressIndicator;
      
      console.log(`   📊 Progress UI visible: ${hasProgressUI ? 'Yes' : 'No'}`);
      
      // Test with a medium batch to trigger progress UI
      const testImages = await this.createTestImages(5, 'progress-test');
      await this.simulateBatchUpload(testImages);
      
      // Wait and check again
      await new Promise(resolve => setTimeout(resolve, 2000));
      const progressIndicatorAfter = document.querySelector('[class*="upload-progress"], [class*="UploadProgress"]');
      const hasProgressUIAfter = !!progressIndicatorAfter;
      
      this.testResults.push({
        test: 'Progress Feedback UI',
        result: hasProgressUIAfter ? 'SUCCESS' : 'NOT_VISIBLE',
        details: {
          initiallyVisible: hasProgressUI,
          visibleAfterUpload: hasProgressUIAfter
        }
      });

    } catch (error) {
      console.error('❌ Error in test 4:', error);
      this.testResults.push({ test: 'Progress Feedback Test', result: 'ERROR', details: error.message });
    }
  }

  async test5_DatabasePersistenceTest() {
    console.log('\n📋 Test 5: Database Persistence Test');
    
    try {
      // Get current mood board ID
      const currentUrl = window.location.href;
      const boardId = currentUrl.split('/').pop();
      
      if (boardId && boardId !== 'new') {
        // Fetch current mood board data
        const response = await fetch(`/api/moodboard/${boardId}`);
        if (response.ok) {
          const data = await response.json();
          const moodboard = data.data;
          
          if (moodboard?.tldraw_data) {
            const imageShapes = this.findImageShapes(moodboard.tldraw_data);
            
            let validUrls = 0;
            let nullUrls = 0;
            let dataUrls = 0;
            let httpUrls = 0;
            
            imageShapes.forEach(shape => {
              const src = shape.props?.src;
              if (!src || src === 'null' || src === null) {
                nullUrls++;
              } else if (src.startsWith('data:')) {
                dataUrls++;
              } else if (src.startsWith('http')) {
                httpUrls++;
                validUrls++;
              } else {
                validUrls++;
              }
            });
            
            console.log(`   📊 Images: ${imageShapes.length} total, ${validUrls} valid URLs, ${nullUrls} null, ${dataUrls} data URLs, ${httpUrls} HTTP URLs`);
            
            this.testResults.push({
              test: 'Database Persistence',
              result: nullUrls === 0 ? 'SUCCESS' : 'PARTIAL',
              details: {
                totalImages: imageShapes.length,
                validUrls,
                nullUrls,
                dataUrls,
                httpUrls
              }
            });
          } else {
            this.testResults.push({ test: 'Database Persistence', result: 'NO_DATA' });
          }
        } else {
          this.testResults.push({ test: 'Database Persistence', result: 'FETCH_FAILED' });
        }
      } else {
        this.testResults.push({ test: 'Database Persistence', result: 'NEW_BOARD' });
      }

    } catch (error) {
      console.error('❌ Error in test 5:', error);
      this.testResults.push({ test: 'Database Persistence Test', result: 'ERROR', details: error.message });
    }
  }

  async test6_EdgeCaseTests() {
    console.log('\n📋 Test 6: Edge Case Tests');
    
    try {
      // Test very large batch
      console.log('   🔄 Testing very large batch (15 images)...');
      const largeTestImages = await this.createTestImages(15, 'edge-large');
      const beforeLogs = this.uploadLogs.length;
      
      await this.simulateBatchUpload(largeTestImages);
      await new Promise(resolve => setTimeout(resolve, 8000));
      
      const afterLogs = this.uploadLogs.length;
      const largeActivity = afterLogs - beforeLogs;
      
      // Test rapid successive uploads
      console.log('   🔄 Testing rapid successive uploads...');
      const rapidImages1 = await this.createTestImages(2, 'rapid-1');
      const rapidImages2 = await this.createTestImages(2, 'rapid-2');
      
      await this.simulateBatchUpload(rapidImages1);
      await new Promise(resolve => setTimeout(resolve, 500)); // Short delay
      await this.simulateBatchUpload(rapidImages2);
      
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      this.testResults.push({
        test: 'Edge Cases',
        result: largeActivity > 0 ? 'SUCCESS' : 'PARTIAL',
        details: {
          largeBatchActivity: largeActivity,
          rapidSuccessiveUploads: 'tested'
        }
      });

    } catch (error) {
      console.error('❌ Error in test 6:', error);
      this.testResults.push({ test: 'Edge Case Tests', result: 'ERROR', details: error.message });
    }
  }

  // Helper methods
  async createTestImages(count, prefix = 'test') {
    const images = [];
    
    for (let i = 0; i < count; i++) {
      const canvas = document.createElement('canvas');
      canvas.width = 80;
      canvas.height = 80;
      const ctx = canvas.getContext('2d');
      
      // Create unique pattern
      const hue = (i * 360 / count) % 360;
      ctx.fillStyle = `hsl(${hue}, 80%, 60%)`;
      ctx.fillRect(0, 0, 80, 80);
      
      // Add identifier
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(`${prefix}-${i + 1}`, 40, 45);
      
      const blob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'image/png');
      });
      
      images.push(new File([blob], `${prefix}-${i + 1}.png`, { type: 'image/png' }));
    }
    
    return images;
  }

  async simulateBatchUpload(images) {
    const canvas = document.querySelector('canvas');
    if (!canvas) {
      throw new Error('Canvas not found');
    }
    
    // Create a single drop event with multiple files
    const dataTransfer = new DataTransfer();
    images.forEach(image => {
      dataTransfer.items.add(image);
    });
    
    const dropEvent = new DragEvent('drop', {
      bubbles: true,
      cancelable: true,
      dataTransfer: dataTransfer
    });
    
    canvas.dispatchEvent(dropEvent);
  }

  findImageShapes(tldrawData) {
    const imageShapes = [];
    if (tldrawData?.store) {
      for (const [key, shape] of Object.entries(tldrawData.store)) {
        if (shape && shape.type === 'image') {
          imageShapes.push(shape);
        }
      }
    }
    return imageShapes;
  }

  displayComprehensiveResults() {
    console.log('\n🎯 BATCH UPLOAD SOLUTION TEST RESULTS');
    console.log('=====================================');
    
    this.restoreLogging();
    
    // Display all results
    this.testResults.forEach(result => {
      const emoji = result.result === 'SUCCESS' ? '✅' :
                   result.result === 'PARTIAL' ? '⚠️' :
                   result.result === 'ERROR' ? '❌' : '🔄';
      
      console.log(`${emoji} ${result.test}: ${result.result}`);
      
      if (result.details) {
        if (typeof result.details === 'object') {
          console.log('   📊 Details:', result.details);
        } else {
          console.log(`   📊 Details: ${result.details}`);
        }
      }
    });
    
    // Summary statistics
    const successCount = this.testResults.filter(r => r.result === 'SUCCESS').length;
    const totalTests = this.testResults.length;
    
    console.log(`\n📊 Overall Success Rate: ${successCount}/${totalTests} (${Math.round(successCount/totalTests*100)}%)`);
    console.log(`📊 Total Upload Logs: ${this.uploadLogs.length}`);
    
    // Provide assessment
    console.log('\n🔍 SOLUTION ASSESSMENT:');
    
    if (successCount >= totalTests * 0.8) {
      console.log('🎉 EXCELLENT: Batch upload solution is working very well!');
      console.log('✅ Queue management is functioning correctly');
      console.log('✅ Overflow handling is working');
      console.log('✅ Progress feedback is implemented');
    } else if (successCount >= totalTests * 0.6) {
      console.log('🔄 GOOD: Batch upload solution is mostly working');
      console.log('💡 Some areas may need fine-tuning');
    } else {
      console.log('⚠️ NEEDS WORK: Batch upload solution needs improvement');
      console.log('💡 Review queue management and error handling');
    }
    
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. Test with real image uploads to verify database persistence');
    console.log('2. Monitor queue performance under heavy load');
    console.log('3. Verify progress UI updates correctly');
    console.log('4. Test error recovery and retry mechanisms');
  }

  restoreLogging() {
    console.log = this.originalConsoleLog;
  }
}

// Auto-run the test
if (window.location.href.includes('mood-board/editor')) {
  const batchTest = new BatchUploadSolutionTest();
  batchTest.runComprehensiveTest();
} else {
  console.log('ℹ️ Please run this test on a mood board editor page');
}

// Export for manual testing
window.BatchUploadSolutionTest = BatchUploadSolutionTest;
