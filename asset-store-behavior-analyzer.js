/**
 * Asset Store Behavior Analyzer
 * Specifically designed to identify why the TLAssetStore works inconsistently
 */

console.log('🔬 Asset Store Behavior Analyzer');
console.log('=================================\n');

class AssetStoreBehaviorAnalyzer {
  constructor() {
    this.logs = [];
    this.errors = [];
    this.assetStoreInstance = null;
    this.tldrawEditor = null;
    this.originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn
    };
  }

  async runAnalysis() {
    console.log('🚀 Starting asset store behavior analysis...\n');
    
    try {
      this.setupLogging();
      await this.step1_DetectAssetStoreInstance();
      await this.step2_AnalyzeAssetStoreConfiguration();
      await this.step3_TestAssetStoreAPI();
      await this.step4_MonitorTldrawIntegration();
      await this.step5_IdentifyInconsistencyFactors();
      
      this.displayAnalysisResults();
    } catch (error) {
      console.error('❌ Analysis failed:', error);
    } finally {
      this.restoreLogging();
    }
  }

  setupLogging() {
    const self = this;
    
    // Intercept console methods to capture asset store activity
    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('MoodBoard') || message.includes('Asset Store') || message.includes('Tldraw')) {
        self.logs.push({
          type: 'log',
          timestamp: new Date().toISOString(),
          message: message,
          args: args
        });
      }
      self.originalConsole.log.apply(console, args);
    };

    console.error = function(...args) {
      const message = args.join(' ');
      self.errors.push({
        type: 'error',
        timestamp: new Date().toISOString(),
        message: message,
        args: args
      });
      self.originalConsole.error.apply(console, args);
    };

    console.warn = function(...args) {
      const message = args.join(' ');
      if (message.includes('MoodBoard') || message.includes('Asset Store')) {
        self.logs.push({
          type: 'warn',
          timestamp: new Date().toISOString(),
          message: message,
          args: args
        });
      }
      self.originalConsole.warn.apply(console, args);
    };

    console.log('✅ Logging interception enabled');
  }

  async step1_DetectAssetStoreInstance() {
    console.log('📋 Step 1: Detecting Asset Store Instance');
    
    try {
      // Wait for component to mount
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Look for asset store creation logs
      const assetStoreCreationLogs = this.logs.filter(log => 
        log.message.includes('Creating asset store') || 
        log.message.includes('asset store for user')
      );
      
      if (assetStoreCreationLogs.length > 0) {
        console.log('✅ Asset store creation detected');
        assetStoreCreationLogs.forEach(log => {
          console.log(`   📡 ${log.message}`);
        });
      } else {
        console.log('⚠️ No asset store creation logs found');
        console.log('💡 This could indicate the asset store is not being instantiated');
      }
      
      // Check for Tldraw mounting logs
      const tldrawMountLogs = this.logs.filter(log => 
        log.message.includes('Tldraw mounted') || 
        log.message.includes('asset store')
      );
      
      if (tldrawMountLogs.length > 0) {
        console.log('✅ Tldraw mounting with asset store detected');
        tldrawMountLogs.forEach(log => {
          console.log(`   🎨 ${log.message}`);
        });
      } else {
        console.log('⚠️ No Tldraw mounting logs found');
      }

    } catch (error) {
      console.error('❌ Error in step 1:', error);
    }
  }

  async step2_AnalyzeAssetStoreConfiguration() {
    console.log('\n📋 Step 2: Analyzing Asset Store Configuration');
    
    try {
      // Check if Tldraw component has assets prop
      const tldrawCanvas = document.querySelector('canvas');
      if (tldrawCanvas) {
        console.log('✅ Tldraw canvas found');
        
        // Try to access the Tldraw component's props through React DevTools or DOM
        const tldrawContainer = tldrawCanvas.closest('[data-testid], .tldraw__editor');
        if (tldrawContainer) {
          console.log('✅ Tldraw container found');
          
          // Check for React fiber to access props (if available)
          const reactFiber = tldrawContainer._reactInternalFiber || 
                            tldrawContainer._reactInternalInstance ||
                            Object.keys(tldrawContainer).find(key => key.startsWith('__reactInternalInstance'));
          
          if (reactFiber) {
            console.log('✅ React fiber detected - component props accessible');
          } else {
            console.log('⚠️ React fiber not accessible - cannot inspect props directly');
          }
        }
      } else {
        console.log('❌ Tldraw canvas not found');
      }
      
      // Check for user authentication
      const userLogs = this.logs.filter(log => 
        log.message.includes('user') && log.message.includes('ID')
      );
      
      if (userLogs.length > 0) {
        console.log('✅ User authentication detected');
      } else {
        console.log('⚠️ No user authentication logs found');
        console.log('💡 Asset store requires user ID for initialization');
      }

    } catch (error) {
      console.error('❌ Error in step 2:', error);
    }
  }

  async step3_TestAssetStoreAPI() {
    console.log('\n📋 Step 3: Testing Asset Store API');
    
    try {
      // Create a test file
      const testFile = await this.createTestFile();
      console.log(`✅ Test file created: ${testFile.name} (${testFile.size} bytes)`);
      
      // Try to simulate asset store upload
      console.log('🔄 Simulating asset store upload...');
      
      // Monitor for upload-related logs
      const beforeUploadLogCount = this.logs.length;
      
      // Simulate drag and drop to trigger asset store
      const canvas = document.querySelector('canvas');
      if (canvas) {
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(testFile);
        
        const dropEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          dataTransfer: dataTransfer
        });
        
        canvas.dispatchEvent(dropEvent);
        
        // Wait for processing
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const afterUploadLogCount = this.logs.length;
        const newLogs = this.logs.slice(beforeUploadLogCount);
        
        console.log(`📊 Upload triggered ${newLogs.length} new logs`);
        
        // Analyze upload logs
        const uploadLogs = newLogs.filter(log => 
          log.message.includes('Uploading asset') || 
          log.message.includes('Upload successful') ||
          log.message.includes('Upload failed')
        );
        
        if (uploadLogs.length > 0) {
          console.log('✅ Asset store upload activity detected:');
          uploadLogs.forEach(log => {
            console.log(`   📤 ${log.message}`);
          });
        } else {
          console.log('⚠️ No asset store upload activity detected');
          console.log('💡 This indicates the asset store upload method is not being called');
        }
        
        // Check for resolve logs
        const resolveLogs = newLogs.filter(log => 
          log.message.includes('Resolving asset') || 
          log.message.includes('Asset resolved')
        );
        
        if (resolveLogs.length > 0) {
          console.log('✅ Asset store resolve activity detected:');
          resolveLogs.forEach(log => {
            console.log(`   🔍 ${log.message}`);
          });
        }
        
      } else {
        console.log('❌ Canvas not found for upload test');
      }

    } catch (error) {
      console.error('❌ Error in step 3:', error);
    }
  }

  async step4_MonitorTldrawIntegration() {
    console.log('\n📋 Step 4: Monitoring Tldraw Integration');
    
    try {
      // Check for Tldraw-specific logs
      const tldrawLogs = this.logs.filter(log => 
        log.message.toLowerCase().includes('tldraw') ||
        log.message.includes('editor') ||
        log.message.includes('shape')
      );
      
      console.log(`📊 Tldraw-related logs: ${tldrawLogs.length}`);
      
      if (tldrawLogs.length > 0) {
        console.log('✅ Tldraw integration activity detected');
        tldrawLogs.slice(-5).forEach(log => { // Show last 5 logs
          console.log(`   🎨 ${log.message}`);
        });
      } else {
        console.log('⚠️ No Tldraw integration logs found');
      }
      
      // Check for any errors
      if (this.errors.length > 0) {
        console.log(`⚠️ ${this.errors.length} errors detected:`);
        this.errors.forEach(error => {
          console.log(`   ❌ ${error.message}`);
        });
      } else {
        console.log('✅ No errors detected');
      }

    } catch (error) {
      console.error('❌ Error in step 4:', error);
    }
  }

  async step5_IdentifyInconsistencyFactors() {
    console.log('\n📋 Step 5: Identifying Inconsistency Factors');
    
    try {
      const currentUrl = window.location.href;
      const boardId = currentUrl.split('/').pop();
      
      const factors = {
        boardId: boardId,
        isWorkingBoard: boardId === '2659ce54-f3ea-4926-bbe9-a605cff2c50d',
        isNonWorkingBoard: boardId === 'f7bbb990-3f6c-413f-b721-f0fdc4a52a62',
        hasAssetStoreLogs: this.logs.filter(log => log.message.includes('Asset Store')).length > 0,
        hasUploadLogs: this.logs.filter(log => log.message.includes('Uploading')).length > 0,
        hasErrorLogs: this.errors.length > 0,
        totalLogs: this.logs.length,
        userAgent: navigator.userAgent.substring(0, 50) + '...',
        timestamp: new Date().toISOString()
      };
      
      console.log('📊 Inconsistency Factors:');
      console.table(factors);
      
      // Provide specific insights
      if (factors.isWorkingBoard && factors.hasAssetStoreLogs) {
        console.log('✅ Working board with asset store activity - expected behavior');
      } else if (factors.isNonWorkingBoard && !factors.hasAssetStoreLogs) {
        console.log('❌ Non-working board with no asset store activity - confirms issue');
      } else if (factors.isNonWorkingBoard && factors.hasAssetStoreLogs) {
        console.log('🔄 Non-working board now has asset store activity - potential fix');
      }

    } catch (error) {
      console.error('❌ Error in step 5:', error);
    }
  }

  async createTestFile() {
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    
    // Create a simple test pattern
    ctx.fillStyle = '#e74c3c';
    ctx.fillRect(0, 0, 50, 50);
    ctx.fillStyle = '#3498db';
    ctx.fillRect(50, 0, 50, 50);
    ctx.fillStyle = '#2ecc71';
    ctx.fillRect(0, 50, 50, 50);
    ctx.fillStyle = '#f39c12';
    ctx.fillRect(50, 50, 50, 50);
    
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    return new File([blob], 'behavior-test.png', { type: 'image/png' });
  }

  displayAnalysisResults() {
    console.log('\n🎯 ASSET STORE BEHAVIOR ANALYSIS RESULTS');
    console.log('=========================================');
    
    console.log(`📊 Total Logs Captured: ${this.logs.length}`);
    console.log(`📊 Total Errors: ${this.errors.length}`);
    
    // Categorize logs
    const assetStoreLogs = this.logs.filter(log => log.message.includes('Asset Store'));
    const uploadLogs = this.logs.filter(log => log.message.includes('Upload'));
    const tldrawLogs = this.logs.filter(log => log.message.includes('Tldraw'));
    
    console.log(`📊 Asset Store Logs: ${assetStoreLogs.length}`);
    console.log(`📊 Upload Logs: ${uploadLogs.length}`);
    console.log(`📊 Tldraw Logs: ${tldrawLogs.length}`);
    
    // Provide diagnosis
    console.log('\n🔍 DIAGNOSIS:');
    
    if (assetStoreLogs.length === 0) {
      console.log('❌ CRITICAL: No asset store activity detected');
      console.log('💡 Possible causes:');
      console.log('   - Asset store not being instantiated');
      console.log('   - User authentication issues');
      console.log('   - Component mounting problems');
    } else if (uploadLogs.length === 0) {
      console.log('⚠️ WARNING: Asset store exists but upload not triggered');
      console.log('💡 Possible causes:');
      console.log('   - Asset store not properly connected to Tldraw');
      console.log('   - Drop event not reaching asset store');
      console.log('   - Asset store upload method not being called');
    } else {
      console.log('✅ SUCCESS: Asset store is working correctly');
    }
    
    if (this.errors.length > 0) {
      console.log('\n🚨 ERRORS DETECTED:');
      this.errors.forEach(error => {
        console.log(`   ❌ ${error.message}`);
      });
    }
    
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. Test on both working and non-working mood boards');
    console.log('2. Compare asset store initialization between boards');
    console.log('3. Check user authentication consistency');
    console.log('4. Verify Tldraw component props configuration');
  }

  restoreLogging() {
    console.log = this.originalConsole.log;
    console.error = this.originalConsole.error;
    console.warn = this.originalConsole.warn;
  }
}

// Auto-run the analysis
if (window.location.href.includes('mood-board/editor')) {
  const analyzer = new AssetStoreBehaviorAnalyzer();
  analyzer.runAnalysis();
} else {
  console.log('ℹ️ Please run this analysis on a mood board editor page');
}

// Export for manual testing
window.AssetStoreBehaviorAnalyzer = AssetStoreBehaviorAnalyzer;
