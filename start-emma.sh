#!/bin/bash
# Emma Studio - Script de inicio robusto
# Soluciona problemas de procesos colgados y conexiones perdidas

echo "🚀 EMMA STUDIO - INICIO ROBUSTO"
echo "================================"

# Función para limpiar procesos
cleanup_processes() {
    echo "🧹 Limpiando procesos existentes..."
    
    # Matar procesos en puertos específicos
    lsof -ti:8001 | xargs kill -9 2>/dev/null || true
    lsof -ti:3002 | xargs kill -9 2>/dev/null || true
    
    # Matar procesos por nombre
    pkill -f "uvicorn" 2>/dev/null || true
    pkill -f "vite" 2>/dev/null || true
    pkill -f "node.*vite" 2>/dev/null || true
    
    echo "✅ Procesos limpiados"
}

# Función para verificar si un puerto está libre
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "❌ Puerto $port está ocupado"
        return 1
    else
        echo "✅ Puerto $port está libre"
        return 0
    fi
}

# Función para esperar que un servicio esté listo
wait_for_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Esperando que $name esté listo..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            echo "✅ $name está listo!"
            return 0
        fi
        
        echo "   Intento $attempt/$max_attempts..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $name no respondió después de $max_attempts intentos"
    return 1
}

# Limpiar procesos existentes
cleanup_processes

# Esperar un momento para que los puertos se liberen
sleep 3

# Verificar que los puertos estén libres
if ! check_port 8001; then
    echo "❌ No se pudo liberar el puerto 8001. Saliendo..."
    exit 1
fi

if ! check_port 3002; then
    echo "❌ No se pudo liberar el puerto 3002. Saliendo..."
    exit 1
fi

# Cambiar al directorio del proyecto
cd "$(dirname "$0")"
PROJECT_ROOT=$(pwd)

echo "📁 Directorio del proyecto: $PROJECT_ROOT"

# Verificar que existan los directorios necesarios
if [ ! -d "backend" ]; then
    echo "❌ Directorio 'backend' no encontrado"
    exit 1
fi

if [ ! -d "client" ]; then
    echo "❌ Directorio 'client' no encontrado"
    exit 1
fi

# Iniciar backend
echo ""
echo "🐍 INICIANDO BACKEND..."
echo "======================"

cd "$PROJECT_ROOT/backend"

# Verificar que app.main existe
if [ ! -f "app/main.py" ]; then
    echo "❌ app/main.py no encontrado"
    exit 1
fi

# Limpiar puertos si están ocupados
echo "🧹 Limpiando puertos..."
lsof -ti :3002 | xargs kill -9 2>/dev/null || true
lsof -ti :8001 | xargs kill -9 2>/dev/null || true

# Iniciar backend en background
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001 > backend.log 2>&1 &
BACKEND_PID=$!

echo "🚀 Backend iniciado (PID: $BACKEND_PID)"
echo "📝 Logs en: $PROJECT_ROOT/backend/backend.log"

# Esperar que el backend esté listo
if wait_for_service "http://localhost:8001/api/v1/ad-creator-agent/health" "Backend"; then
    echo "✅ Backend funcionando correctamente"
else
    echo "❌ Backend falló al iniciar. Revisando logs..."
    tail -20 backend.log
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# Iniciar frontend
echo ""
echo "⚛️  INICIANDO FRONTEND..."
echo "======================="

cd "$PROJECT_ROOT/client"

# Verificar que package.json existe
if [ ! -f "package.json" ]; then
    echo "❌ package.json no encontrado"
    exit 1
fi

# Iniciar frontend en background
npm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!

echo "🚀 Frontend iniciado (PID: $FRONTEND_PID)"
echo "📝 Logs en: $PROJECT_ROOT/client/frontend.log"

# Esperar que el frontend esté listo
if wait_for_service "http://localhost:3002" "Frontend"; then
    echo "✅ Frontend funcionando correctamente"
else
    echo "❌ Frontend falló al iniciar. Revisando logs..."
    tail -20 frontend.log
    kill $FRONTEND_PID 2>/dev/null || true
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# Todo listo
echo ""
echo "🎉 EMMA STUDIO INICIADO CORRECTAMENTE"
echo "===================================="
echo "🌐 Frontend: http://localhost:3002"
echo "🔧 Backend:  http://localhost:8001"
echo "📊 Health:   http://localhost:8001/api/v1/ad-creator-agent/health"
echo ""
echo "📝 Para ver logs:"
echo "   Backend:  tail -f $PROJECT_ROOT/backend/backend.log"
echo "   Frontend: tail -f $PROJECT_ROOT/client/frontend.log"
echo ""
echo "🛑 Para detener: Ctrl+C o ejecutar: kill $BACKEND_PID $FRONTEND_PID"
echo ""

# Crear archivo con PIDs para fácil cleanup
echo "$BACKEND_PID" > "$PROJECT_ROOT/.backend.pid"
echo "$FRONTEND_PID" > "$PROJECT_ROOT/.frontend.pid"

# Función de cleanup al salir
cleanup_on_exit() {
    echo ""
    echo "🛑 Deteniendo Emma Studio..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    rm -f "$PROJECT_ROOT/.backend.pid" "$PROJECT_ROOT/.frontend.pid"
    echo "✅ Emma Studio detenido"
    exit 0
}

# Capturar señales para cleanup
trap cleanup_on_exit SIGINT SIGTERM

# Mantener el script corriendo y monitorear procesos
echo "🔍 Monitoreando procesos... (Ctrl+C para detener)"

while true; do
    # Verificar que ambos procesos sigan corriendo
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo "❌ Backend se detuvo inesperadamente"
        tail -10 "$PROJECT_ROOT/backend/backend.log"
        cleanup_on_exit
    fi
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "❌ Frontend se detuvo inesperadamente"
        tail -10 "$PROJECT_ROOT/client/frontend.log"
        cleanup_on_exit
    fi
    
    sleep 10
done
