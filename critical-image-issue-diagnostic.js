#!/usr/bin/env node

/**
 * CRITICAL IMAGE ISSUE DIAGNOSTIC
 * Comprehensive investigation of image loading failures in Emma Studio
 */

console.log('🚨 CRITICAL IMAGE ISSUE DIAGNOSTIC');
console.log('==================================');
console.log('Investigating image loading failures across all tools...\n');

const BACKEND_URL = "http://localhost:8001";
const FRONTEND_URL = "http://localhost:3002";

class CriticalImageIssueDiagnostic {
  constructor() {
    this.issues = [];
    this.solutions = [];
    this.testResults = {};
  }

  async runFullDiagnostic() {
    console.log('🔍 Starting critical image issue investigation...\n');

    try {
      // Test 1: Backend-Frontend connectivity
      await this.testBackendFrontendConnectivity();
      
      // Test 2: Image endpoint availability
      await this.testImageEndpointAvailability();
      
      // Test 3: Supabase direct access
      await this.testSupabaseDirectAccess();
      
      // Test 4: Authentication flow
      await this.testAuthenticationFlow();
      
      // Test 5: Image URL generation methods
      await this.testImageUrlGenerationMethods();
      
      // Test 6: CORS and proxy configuration
      await this.testCorsAndProxy();
      
      // Generate critical issue report
      this.generateCriticalIssueReport();
      
    } catch (error) {
      console.error('❌ Critical diagnostic failed:', error);
      this.issues.push(`Diagnostic failed: ${error.message}`);
    }
  }

  async testBackendFrontendConnectivity() {
    console.log('🔗 Test 1: Backend-Frontend Connectivity');
    console.log('----------------------------------------');

    try {
      // Test backend health
      const healthResponse = await fetch(`${BACKEND_URL}/api/health`);
      if (healthResponse.ok) {
        console.log('✅ Backend health: OPERATIONAL');
        this.testResults.backendHealth = true;
      } else {
        console.log('❌ Backend health: FAILED');
        this.testResults.backendHealth = false;
        this.issues.push('Backend not responding to health checks');
      }

      // Test proxy configuration by checking if frontend can reach backend
      try {
        const proxyTestResponse = await fetch('/api/health');
        if (proxyTestResponse.ok) {
          console.log('✅ Frontend proxy: WORKING');
          this.testResults.proxyWorking = true;
        } else {
          console.log('❌ Frontend proxy: FAILED');
          this.testResults.proxyWorking = false;
          this.issues.push('Frontend proxy not working - requests not reaching backend');
        }
      } catch (proxyError) {
        console.log('❌ Frontend proxy: ERROR -', proxyError.message);
        this.testResults.proxyWorking = false;
        this.issues.push(`Frontend proxy error: ${proxyError.message}`);
      }

    } catch (error) {
      console.log('❌ Backend-Frontend connectivity test failed:', error.message);
      this.issues.push(`Backend-Frontend connectivity failed: ${error.message}`);
    }

    console.log('');
  }

  async testImageEndpointAvailability() {
    console.log('🖼️ Test 2: Image Endpoint Availability');
    console.log('--------------------------------------');

    const imageEndpoints = [
      '/api/image/test-path',
      '/api/image-url/test-path',
      '/api/test-image-access'
    ];

    for (const endpoint of imageEndpoints) {
      try {
        const response = await fetch(`${BACKEND_URL}${endpoint}`);
        
        if (response.status === 401) {
          console.log(`✅ ${endpoint}: REQUIRES AUTH (Correct)`);
        } else if (response.status === 404) {
          console.log(`❌ ${endpoint}: NOT FOUND`);
          this.issues.push(`Image endpoint ${endpoint} not found - may not be registered`);
        } else {
          console.log(`⚠️ ${endpoint}: Unexpected response (${response.status})`);
        }
      } catch (error) {
        console.log(`❌ ${endpoint}: ERROR (${error.message})`);
        this.issues.push(`Image endpoint ${endpoint} error: ${error.message}`);
      }
    }

    console.log('');
  }

  async testSupabaseDirectAccess() {
    console.log('🗄️ Test 3: Supabase Direct Access');
    console.log('----------------------------------');

    const SUPABASE_URL = "https://pthewpjbegkgomvyhkin.supabase.co";
    const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aGV3cGpiZWdrZ29tdnloa2luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MjM1NDMsImV4cCI6MjA2NDI5OTU0M30.bskxkyZ9meYb2cpZZGmS_FAS2Wyjs4j_lOPnJqh1s0k";

    try {
      // Test Supabase Storage API
      const storageResponse = await fetch(`${SUPABASE_URL}/storage/v1/bucket/design-analysis-images`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      });

      if (storageResponse.ok) {
        console.log('✅ Supabase Storage API: ACCESSIBLE');
        this.testResults.supabaseStorageApi = true;
      } else if (storageResponse.status === 401) {
        console.log('⚠️ Supabase Storage API: REQUIRES AUTH (Expected)');
        this.testResults.supabaseStorageApi = 'auth_required';
      } else {
        console.log(`❌ Supabase Storage API: FAILED (${storageResponse.status})`);
        this.testResults.supabaseStorageApi = false;
        this.issues.push(`Supabase Storage API failed: ${storageResponse.status}`);
      }

      // Test authenticated endpoint format
      const testFilePath = "test-user/test-file.png";
      const authenticatedUrl = `${SUPABASE_URL}/storage/v1/object/authenticated/design-analysis-images/${testFilePath}`;
      
      const authResponse = await fetch(authenticatedUrl, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      });

      console.log(`🔐 Authenticated endpoint test: ${authResponse.status} (${authResponse.status === 401 ? 'Expected - needs user auth' : 'Unexpected'})`);

    } catch (error) {
      console.log('❌ Supabase direct access test failed:', error.message);
      this.issues.push(`Supabase direct access failed: ${error.message}`);
    }

    console.log('');
  }

  async testAuthenticationFlow() {
    console.log('🔐 Test 4: Authentication Flow');
    console.log('------------------------------');

    try {
      // Test if there's an active session endpoint
      const authTestEndpoints = [
        '/api/auth/user',
        '/api/auth/session',
        '/api/user/profile'
      ];

      for (const endpoint of authTestEndpoints) {
        try {
          const response = await fetch(`${BACKEND_URL}${endpoint}`);
          console.log(`🧪 ${endpoint}: ${response.status} (${response.status === 401 ? 'Requires auth' : response.status === 404 ? 'Not found' : 'Available'})`);
        } catch (error) {
          console.log(`❌ ${endpoint}: ERROR (${error.message})`);
        }
      }

    } catch (error) {
      console.log('❌ Authentication flow test failed:', error.message);
      this.issues.push(`Authentication flow test failed: ${error.message}`);
    }

    console.log('');
  }

  async testImageUrlGenerationMethods() {
    console.log('🔗 Test 5: Image URL Generation Methods');
    console.log('--------------------------------------');

    console.log('📋 Current frontend methods being used:');
    console.log('  1. Supabase authenticated endpoint (direct)');
    console.log('  2. Supabase SDK download + blob URL');
    console.log('  3. Supabase signed URL');
    console.log('');

    console.log('🔍 Potential issues with current methods:');
    console.log('  ❌ Method 1: Requires user JWT token, may fail with RLS');
    console.log('  ❌ Method 2: Creates temporary blob URLs, memory intensive');
    console.log('  ❌ Method 3: Requires proper permissions, may fail');
    console.log('');

    console.log('✅ Recommended solution: Backend image proxy');
    console.log('  - Use /api/image/{file_path} endpoint');
    console.log('  - Backend handles authentication with service role');
    console.log('  - Frontend gets clean image URLs');

    this.solutions.push('Implement backend image proxy for reliable image serving');

    console.log('');
  }

  async testCorsAndProxy() {
    console.log('🌐 Test 6: CORS and Proxy Configuration');
    console.log('---------------------------------------');

    try {
      // Test CORS headers
      const corsTestResponse = await fetch(`${BACKEND_URL}/api/health`);
      const corsHeaders = {
        'access-control-allow-origin': corsTestResponse.headers.get('access-control-allow-origin'),
        'access-control-allow-methods': corsTestResponse.headers.get('access-control-allow-methods'),
        'access-control-allow-headers': corsTestResponse.headers.get('access-control-allow-headers')
      };

      console.log('🔍 CORS Headers:', corsHeaders);

      if (corsHeaders['access-control-allow-origin']) {
        console.log('✅ CORS: Configured');
        this.testResults.corsConfigured = true;
      } else {
        console.log('⚠️ CORS: Not configured or restrictive');
        this.testResults.corsConfigured = false;
      }

    } catch (error) {
      console.log('❌ CORS and proxy test failed:', error.message);
      this.issues.push(`CORS and proxy test failed: ${error.message}`);
    }

    console.log('');
  }

  generateCriticalIssueReport() {
    console.log('🚨 CRITICAL IMAGE ISSUE REPORT');
    console.log('==============================');
    
    console.log('\n❌ IDENTIFIED ISSUES:');
    if (this.issues.length === 0) {
      console.log('  No critical issues found');
    } else {
      this.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }
    
    console.log('\n🔍 ROOT CAUSE ANALYSIS:');
    this.analyzeRootCause();
    
    console.log('\n🔧 IMMEDIATE SOLUTIONS:');
    this.generateImmediateSolutions();
    
    console.log('\n📋 ACTION PLAN:');
    this.generateActionPlan();
    
    console.log('\n🏁 DIAGNOSTIC COMPLETE');
    console.log('======================');
  }

  analyzeRootCause() {
    console.log('Based on the diagnostic results, the most likely root causes are:');
    console.log('');
    
    if (!this.testResults.backendHealth) {
      console.log('🔴 CRITICAL: Backend not operational');
      console.log('   - Images cannot be served without backend');
      console.log('   - All image functionality will fail');
    }
    
    if (!this.testResults.proxyWorking) {
      console.log('🔴 CRITICAL: Frontend-Backend proxy not working');
      console.log('   - Frontend cannot communicate with backend');
      console.log('   - API calls are failing');
    }
    
    if (this.issues.some(issue => issue.includes('endpoint') && issue.includes('not found'))) {
      console.log('🟡 MAJOR: Image endpoints not properly registered');
      console.log('   - New image retrieval endpoints may not be active');
      console.log('   - Frontend falling back to direct Supabase access');
    }
    
    console.log('🟡 DESIGN ISSUE: Frontend using direct Supabase access');
    console.log('   - Supabase authenticated endpoints have limitations');
    console.log('   - RLS policies may block access');
    console.log('   - JWT token issues can cause failures');
  }

  generateImmediateSolutions() {
    console.log('1. 🔧 Verify backend is running and accessible');
    console.log('   - Check: curl http://localhost:8001/api/health');
    console.log('   - Restart backend if needed');
    console.log('');
    
    console.log('2. 🔧 Test frontend-backend connectivity');
    console.log('   - Open browser dev tools on http://localhost:3002');
    console.log('   - Check Network tab for failed API calls');
    console.log('   - Verify proxy is working');
    console.log('');
    
    console.log('3. 🔧 Implement backend image proxy');
    console.log('   - Use /api/image/{file_path} endpoint');
    console.log('   - Update frontend to use backend URLs instead of Supabase direct');
    console.log('   - Test with actual user authentication');
    console.log('');
    
    console.log('4. 🔧 Update frontend image loading logic');
    console.log('   - Replace Supabase authenticated endpoints');
    console.log('   - Use backend image proxy as primary method');
    console.log('   - Keep Supabase SDK as fallback only');
  }

  generateActionPlan() {
    console.log('IMMEDIATE (Next 15 minutes):');
    console.log('  1. Verify backend is running on port 8001');
    console.log('  2. Test image endpoints with authentication');
    console.log('  3. Check browser console for specific errors');
    console.log('');
    
    console.log('SHORT TERM (Next hour):');
    console.log('  1. Update frontend to use backend image proxy');
    console.log('  2. Test with real user session');
    console.log('  3. Verify images load in Visual Complexity Analyzer');
    console.log('  4. Test MoodBoard image display');
    console.log('');
    
    console.log('VERIFICATION:');
    console.log('  1. Images should load in analysis history');
    console.log('  2. MoodBoard should show actual images (no yellow squares)');
    console.log('  3. All image functionality should work as before');
  }
}

// Run the critical diagnostic
const diagnostic = new CriticalImageIssueDiagnostic();
diagnostic.runFullDiagnostic();
