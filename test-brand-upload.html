<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Brand Creation Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-center;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .preview-container {
            margin-top: 20px;
            text-align: center;
        }
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .success-message {
            color: #28a745;
            margin-top: 10px;
            font-weight: bold;
        }
        .error-message {
            color: #dc3545;
            margin-top: 10px;
            font-weight: bold;
        }
        .test-instructions {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-results {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .test-results h4 {
            color: #155724;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-instructions">
            <h3>🧪 Brand Creation Image Upload Test</h3>
            <p><strong>Test Status:</strong> ✅ Image upload functionality has been implemented and fixed!</p>
            <ul>
                <li><strong>Fixed Issues:</strong>
                    <ul>
                        <li>Added missing file input element</li>
                        <li>Implemented image upload handler with validation</li>
                        <li>Added image preview functionality</li>
                        <li>Connected button click to file input</li>
                        <li>Added proper memory cleanup for blob URLs</li>
                    </ul>
                </li>
                <li><strong>Test Steps:</strong>
                    <ol>
                        <li>Navigate to <a href="http://localhost:3002/dashboard/marca/crear" target="_blank">Brand Creation Page</a></li>
                        <li>Click "Siguiente" to go to Step 2 (Visual Identity)</li>
                        <li>Click on the logo upload area or "Seleccionar archivo" button</li>
                        <li>Select an image file (PNG, JPG, WebP up to 30MB)</li>
                        <li>Verify the image preview appears immediately</li>
                        <li>Verify success message shows "Logo cargado exitosamente"</li>
                    </ol>
                </li>
            </ul>
        </div>

        <div class="test-results">
            <h4>✅ Implementation Summary</h4>
            <p><strong>Files Modified:</strong></p>
            <ul>
                <li><code>client/src/pages/crear-marca-page.tsx</code> - Added complete image upload functionality</li>
            </ul>
            
            <p><strong>Features Added:</strong></p>
            <ul>
                <li>✅ File input with image validation</li>
                <li>✅ Image preview using URL.createObjectURL()</li>
                <li>✅ Click handler for upload area</li>
                <li>✅ Success/error feedback</li>
                <li>✅ Memory cleanup for blob URLs</li>
                <li>✅ File size and type validation (30MB limit, PNG/JPG/WebP)</li>
            </ul>

            <p><strong>Code Changes:</strong></p>
            <ul>
                <li>Added <code>useRef</code> for file input reference</li>
                <li>Added <code>logoPreview</code> state for image preview</li>
                <li>Added <code>handleLogoUpload</code> function with validation</li>
                <li>Added <code>triggerLogoUpload</code> function to open file dialog</li>
                <li>Added <code>useEffect</code> for cleanup on unmount</li>
                <li>Updated Visual Identity section with functional upload area</li>
            </ul>
        </div>

        <h2>🖼️ Test the Upload Functionality</h2>
        <p>Use this test area to verify the same logic works:</p>
        
        <div class="upload-area" id="uploadArea">
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
            <div id="uploadContent">
                <div style="font-size: 48px; margin-bottom: 16px;">📷</div>
                <h3>Test Logo Upload</h3>
                <p>Click here to test the upload functionality</p>
                <button type="button" style="padding: 8px 16px; border: 1px solid #ccc; border-radius: 4px; background: white; cursor: pointer;">Select File</button>
                <p style="font-size: 12px; color: #666; margin-top: 8px;">PNG, JPG, WebP up to 30MB</p>
            </div>
        </div>

        <div class="preview-container" id="previewContainer" style="display: none;">
            <img id="previewImage" class="preview-image" alt="Preview">
            <div class="success-message">✅ Logo cargado exitosamente!</div>
            <button type="button" onclick="resetUpload()" style="margin-top: 10px; padding: 8px 16px; border: 1px solid #ccc; border-radius: 4px; background: white; cursor: pointer;">Cambiar logo</button>
        </div>

        <div id="errorMessage" class="error-message" style="display: none;"></div>
    </div>

    <script>
        // Same validation logic as implemented in the app
        function validateImageFile(file) {
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                return {
                    valid: false,
                    error: 'Tipo de archivo no válido. Solo se permiten JPG, PNG y WebP.'
                };
            }

            const maxSize = 30 * 1024 * 1024; // 30MB
            if (file.size > maxSize) {
                return {
                    valid: false,
                    error: 'El archivo es demasiado grande. Máximo 30MB.'
                };
            }

            return { valid: true };
        }

        function handleFileUpload(file) {
            const validation = validateImageFile(file);
            
            if (!validation.valid) {
                document.getElementById('errorMessage').textContent = validation.error;
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('previewContainer').style.display = 'none';
                return;
            }

            // Clean up previous preview
            const previewImage = document.getElementById('previewImage');
            if (previewImage.src && previewImage.src.startsWith('blob:')) {
                URL.revokeObjectURL(previewImage.src);
            }

            // Create preview using URL.createObjectURL (same as in the app)
            const previewUrl = URL.createObjectURL(file);
            previewImage.src = previewUrl;
            
            // Show preview, hide upload area
            document.getElementById('uploadContent').style.display = 'none';
            document.getElementById('previewContainer').style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
        }

        function resetUpload() {
            const previewImage = document.getElementById('previewImage');
            if (previewImage.src && previewImage.src.startsWith('blob:')) {
                URL.revokeObjectURL(previewImage.src);
            }
            
            document.getElementById('uploadContent').style.display = 'block';
            document.getElementById('previewContainer').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('fileInput').value = '';
        }

        // Event listeners
        document.getElementById('uploadArea').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        document.getElementById('fileInput').addEventListener('change', (e) => {
            const file = e.target.files?.[0];
            if (file) handleFileUpload(file);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            const previewImage = document.getElementById('previewImage');
            if (previewImage.src && previewImage.src.startsWith('blob:')) {
                URL.revokeObjectURL(previewImage.src);
            }
        });
    </script>
</body>
</html>
