# Configuración de Firebase
VITE_FIREBASE_API_KEY=your-firebase-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
VITE_FIREBASE_APP_ID=your-app-id
VITE_FIREBASE_MEASUREMENT_ID=your-measurement-id

# API Keys para servicios de IA
GEMINI_API_KEY=your-gemini-api-key
VITE_GEMINI_API_KEY=${GEMINI_API_KEY}
STABILITY_API_KEY=your-stability-api-key
VITE_STABILITY_API_KEY=${STABILITY_API_KEY}
ELEVENLABS_API_KEY=your-elevenlabs-api-key
VITE_ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
OPENAI_API_KEY=your-openai-api-key
VITE_OPENAI_API_KEY=${OPENAI_API_KEY}

# Configuración de JWT
JWT_SECRET=your-secure-jwt-secret-at-least-32-characters-long

# Configuración de Polotno Studio (Editor Visual Profesional)
VITE_POLOTNO_API_KEY=your-polotno-api-key

# Configuración de la base de datos
DATABASE_URL=postgres://username:password@localhost:5432/emma_ai

# Variables para Docker
BUILD_STAGE=dev        # Opciones: dev, prod
FRONT_TARGET=dev       # Opciones: dev, runner
MOUNT_SUFFIX=:z        # Para SELinux: :z, para otros sistemas: vacío

# Otras configuraciones
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3002