/**
 * Test script to verify Tldraw asset store integration
 * This script will check if the asset store is properly connected to Tldraw
 */

console.log('🎨 Tldraw Asset Store Integration Test');
console.log('=====================================\n');

class TldrawAssetIntegrationTest {
  constructor() {
    this.originalConsoleLog = console.log;
    this.assetStoreLogs = [];
    this.tldrawEditor = null;
  }

  async runIntegrationTest() {
    console.log('🚀 Starting Tldraw asset store integration test...\n');
    
    try {
      await this.step1_CheckTldrawEditor();
      await this.step2_MonitorAssetStoreCalls();
      await this.step3_SimulateImageDrop();
      await this.step4_CheckAssetStoreUsage();
      await this.step5_VerifyImagePersistence();
      
      this.displayResults();
    } catch (error) {
      console.error('❌ Integration test failed:', error);
    }
  }

  async step1_CheckTldrawEditor() {
    console.log('📋 Step 1: Checking Tldraw Editor Access');
    
    try {
      // Try to find the Tldraw editor instance
      const tldrawCanvas = document.querySelector('canvas');
      if (tldrawCanvas) {
        console.log('✅ Tldraw canvas found');
        
        // Try to access the editor through the global scope or DOM
        // Tldraw editor is usually accessible through the component ref
        console.log('ℹ️ Looking for editor instance...');
        
        // Check if there's a way to access the editor
        if (window.tldrawEditor) {
          this.tldrawEditor = window.tldrawEditor;
          console.log('✅ Editor instance found in global scope');
        } else {
          console.log('⚠️ Editor instance not in global scope (expected)');
          console.log('ℹ️ Editor is likely accessible only within the component');
        }
      } else {
        console.log('❌ Tldraw canvas not found');
      }
    } catch (error) {
      console.error('❌ Error checking Tldraw editor:', error);
    }
  }

  async step2_MonitorAssetStoreCalls() {
    console.log('\n📋 Step 2: Monitoring Asset Store Calls');
    
    try {
      // Set up console monitoring for asset store logs
      console.log = (...args) => {
        const message = args.join(' ');
        if (message.includes('MoodBoard Asset Store')) {
          this.assetStoreLogs.push({
            timestamp: new Date().toISOString(),
            message: message,
            args: args
          });
          console.log('📡 Asset Store Log Captured:', message);
        }
        this.originalConsoleLog.apply(console, args);
      };
      
      console.log('✅ Asset store monitoring enabled');
      console.log('ℹ️ Monitoring for asset store activity...');
      
    } catch (error) {
      console.error('❌ Error setting up asset store monitoring:', error);
    }
  }

  async step3_SimulateImageDrop() {
    console.log('\n📋 Step 3: Simulating Image Drop');
    
    try {
      // Create a test image file
      const testFile = await this.createTestImageFile();
      console.log(`✅ Test image created: ${testFile.name} (${testFile.size} bytes)`);
      
      // Try to simulate a drag and drop event
      const canvas = document.querySelector('canvas');
      if (canvas) {
        console.log('🔄 Simulating drag and drop on canvas...');
        
        // Create a drag and drop event
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(testFile);
        
        const dropEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          dataTransfer: dataTransfer
        });
        
        // Dispatch the event
        canvas.dispatchEvent(dropEvent);
        
        console.log('✅ Drop event dispatched');
        
        // Wait for potential asset store activity
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } else {
        console.log('❌ Canvas not found for drop simulation');
      }
      
    } catch (error) {
      console.error('❌ Error simulating image drop:', error);
    }
  }

  async step4_CheckAssetStoreUsage() {
    console.log('\n📋 Step 4: Checking Asset Store Usage');
    
    try {
      console.log(`ℹ️ Captured ${this.assetStoreLogs.length} asset store logs`);
      
      if (this.assetStoreLogs.length > 0) {
        console.log('✅ Asset store activity detected:');
        this.assetStoreLogs.forEach((log, index) => {
          console.log(`   ${index + 1}. [${log.timestamp}] ${log.message}`);
        });
      } else {
        console.log('⚠️ No asset store activity detected');
        console.log('💡 This could indicate:');
        console.log('   - Asset store is not being called');
        console.log('   - Images are not being processed through the asset store');
        console.log('   - The drop simulation didn\'t trigger the expected behavior');
      }
      
    } catch (error) {
      console.error('❌ Error checking asset store usage:', error);
    }
  }

  async step5_VerifyImagePersistence() {
    console.log('\n📋 Step 5: Verifying Image Persistence');
    
    try {
      // Check current mood board data for image persistence issues
      const currentUrl = window.location.href;
      const boardId = currentUrl.split('/').pop();
      
      if (boardId && boardId !== 'new') {
        console.log(`ℹ️ Checking mood board ${boardId} for image persistence...`);
        
        const response = await fetch(`/api/moodboard/${boardId}`);
        if (response.ok) {
          const data = await response.json();
          const moodboard = data.data;
          
          if (moodboard && moodboard.tldraw_data) {
            const imageShapes = this.findImageShapes(moodboard.tldraw_data);
            console.log(`ℹ️ Found ${imageShapes.length} image shapes in mood board`);
            
            let nullSrcCount = 0;
            let validSrcCount = 0;
            
            imageShapes.forEach((shape, index) => {
              const src = shape.props?.src;
              if (!src || src === 'null' || src === null) {
                nullSrcCount++;
                console.log(`❌ Image ${index + 1}: NULL or missing src`);
              } else {
                validSrcCount++;
                const srcType = this.identifySourceType(src);
                console.log(`✅ Image ${index + 1}: ${srcType} (${src.substring(0, 50)}...)`);
              }
            });
            
            console.log(`📊 Image Source Summary: ${validSrcCount} valid, ${nullSrcCount} null`);
            
            if (nullSrcCount > 0) {
              console.log('🚨 CRITICAL ISSUE: Found images with null sources!');
              console.log('💡 This confirms the image persistence bug');
            }
            
          } else {
            console.log('⚠️ No tldraw data found in mood board');
          }
        } else {
          console.log('❌ Failed to fetch mood board data');
        }
      } else {
        console.log('ℹ️ This is a new mood board, no persistence data to check');
      }
      
    } catch (error) {
      console.error('❌ Error verifying image persistence:', error);
    }
  }

  // Helper methods
  async createTestImageFile() {
    const canvas = document.createElement('canvas');
    canvas.width = 150;
    canvas.height = 150;
    const ctx = canvas.getContext('2d');
    
    // Draw a test pattern
    ctx.fillStyle = '#ff6b6b';
    ctx.fillRect(0, 0, 75, 75);
    ctx.fillStyle = '#4ecdc4';
    ctx.fillRect(75, 0, 75, 75);
    ctx.fillStyle = '#45b7d1';
    ctx.fillRect(0, 75, 75, 75);
    ctx.fillStyle = '#f9ca24';
    ctx.fillRect(75, 75, 75, 75);
    
    // Add text
    ctx.fillStyle = '#2c2c2c';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('TEST', 75, 80);
    
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    return new File([blob], 'tldraw-integration-test.png', { type: 'image/png' });
  }

  findImageShapes(tldrawData) {
    const imageShapes = [];
    
    if (tldrawData && tldrawData.store) {
      for (const [shapeId, shape] of Object.entries(tldrawData.store)) {
        if (shape && shape.type === 'image') {
          imageShapes.push(shape);
        }
      }
    }
    
    return imageShapes;
  }

  identifySourceType(src) {
    if (src.startsWith('data:')) return 'Data URL';
    if (src.startsWith('blob:')) return 'Blob URL';
    if (src.startsWith('http://') || src.startsWith('https://')) return 'HTTP URL';
    if (src.includes('supabase')) return 'Supabase URL';
    return 'Unknown';
  }

  displayResults() {
    console.log('\n🎯 TLDRAW ASSET INTEGRATION TEST RESULTS');
    console.log('=========================================');
    
    // Restore original console.log
    console.log = this.originalConsoleLog;
    
    console.log(`📊 Asset Store Logs Captured: ${this.assetStoreLogs.length}`);
    
    if (this.assetStoreLogs.length > 0) {
      console.log('✅ Asset store is being called');
      console.log('ℹ️ This indicates the integration is working');
    } else {
      console.log('❌ Asset store is NOT being called');
      console.log('🚨 This indicates a problem with the integration');
    }
    
    console.log('\n🔍 Diagnostic Summary:');
    console.log('- Tldraw canvas: ✅ Found');
    console.log('- Asset store monitoring: ✅ Enabled');
    console.log('- Drop simulation: ✅ Executed');
    console.log(`- Asset store activity: ${this.assetStoreLogs.length > 0 ? '✅' : '❌'} ${this.assetStoreLogs.length} logs`);
    
    console.log('\n💡 Troubleshooting Guide:');
    if (this.assetStoreLogs.length === 0) {
      console.log('🔧 Possible Issues:');
      console.log('1. Asset store not properly configured in Tldraw component');
      console.log('2. Asset store upload method not being called');
      console.log('3. Tldraw not using the custom asset store');
      console.log('4. Event simulation not triggering asset processing');
      
      console.log('\n🔍 Next Steps:');
      console.log('1. Check if assets prop is correctly passed to Tldraw');
      console.log('2. Verify asset store instantiation in component');
      console.log('3. Test with real drag and drop instead of simulation');
      console.log('4. Check Tldraw documentation for asset store requirements');
    } else {
      console.log('✅ Asset store integration appears to be working');
      console.log('🔍 Check the captured logs for upload success/failure details');
    }
  }
}

// Auto-run the test if we're on the mood board editor page
if (window.location.href.includes('mood-board/editor')) {
  const integrationTest = new TldrawAssetIntegrationTest();
  integrationTest.runIntegrationTest();
} else {
  console.log('ℹ️ Please run this test on a mood board editor page');
  console.log('ℹ️ Navigate to: /dashboard/herramientas/mood-board/editor/[board-id]');
}
