#!/usr/bin/env python3
"""
Test script to verify Polotno integration works correctly.
Tests that generated posts include:
1. Clean background images (no burned-in text)
2. Proper Polotno JSON structure with editable text layers
3. Separate image and text layers
"""

import requests
import json
import sys

def test_polotno_integration(topic="perros"):
    """Test post generation with Polotno integration."""
    
    print(f"\n🧪 Testing Polotno integration for topic: '{topic}'")
    print("=" * 60)
    
    # Prepare the request payload
    payload = {
        "brandInfo": {
            "businessName": "Test Pet Store",
            "brandColor": "#3018ef",
            "voice": "Profesional y amigable",
            "topics": [topic],
            "ctas": ["¡Descubre más!"],
            "industry": "pet care",
            "target_audience": "pet owners"
        },
        "designConfig": {
            "selectedTheme": "Balance",
            "platform": "Instagram",
            "contentType": "instagram_posts"
        },
        "generationConfig": {
            "count": 1,
            "template": "Balance",
            "analysisComplete": False
        }
    }
    
    try:
        # Make the request
        response = requests.post(
            "http://localhost:8001/api/v1/posts/generate-batch",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("success") and data.get("posts"):
                post = data["posts"][0]
                
                print(f"✅ SUCCESS! Generated post for '{topic}':")
                print(f"📝 Content: {post.get('text', 'No content')[:100]}...")
                print(f"🖼️ Image URL: {post.get('image_url', 'No image')}")
                
                # Check Polotno JSON structure
                polotno_json = post.get('polotno_json')
                if polotno_json:
                    print(f"🎨 Polotno JSON: ✅ Present")
                    
                    # Validate Polotno structure
                    validation_results = validate_polotno_structure(polotno_json, post)
                    
                    for check, result in validation_results.items():
                        status = "✅" if result else "❌"
                        print(f"   {status} {check}")
                    
                    # Show structure summary
                    children = polotno_json.get('children', [])
                    print(f"   📊 Total layers: {len(children)}")
                    
                    for i, layer in enumerate(children):
                        layer_type = layer.get('type', 'unknown')
                        if layer_type == 'image':
                            print(f"   🖼️ Layer {i+1}: Image (background)")
                        elif layer_type == 'text':
                            text_preview = layer.get('text', '')[:30]
                            print(f"   📝 Layer {i+1}: Text - '{text_preview}...'")
                    
                    return all(validation_results.values())
                else:
                    print(f"❌ POLOTNO JSON: Missing")
                    return False
            else:
                print(f"❌ FAILED: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def validate_polotno_structure(polotno_json, post):
    """Validate that the Polotno JSON structure is correct."""
    
    results = {}
    
    # Check basic structure
    results["Has width/height"] = bool(
        polotno_json.get('width') and polotno_json.get('height')
    )
    
    results["Has children array"] = isinstance(
        polotno_json.get('children'), list
    )
    
    children = polotno_json.get('children', [])
    
    # Check for image layer
    image_layers = [c for c in children if c.get('type') == 'image']
    results["Has image layer"] = len(image_layers) > 0
    
    if image_layers:
        img_layer = image_layers[0]
        results["Image has src"] = bool(img_layer.get('src'))
        results["Image positioned correctly"] = (
            img_layer.get('x') == 0 and img_layer.get('y') == 0
        )
    
    # Check for text layers
    text_layers = [c for c in children if c.get('type') == 'text']
    results["Has text layers"] = len(text_layers) > 0
    
    if text_layers:
        for text_layer in text_layers:
            # Check required text properties
            has_text = bool(text_layer.get('text'))
            has_position = all(key in text_layer for key in ['x', 'y'])
            has_styling = all(key in text_layer for key in ['fontSize', 'fontFamily', 'fill'])
            
            results[f"Text layer complete"] = has_text and has_position and has_styling
    
    # Check metadata
    metadata = post.get('metadata', {})
    results["Clean background flag"] = metadata.get('clean_background', False)
    results["No text overlay"] = not metadata.get('text_overlay_applied', True)
    results["Polotno ready"] = metadata.get('polotno_ready', False)
    
    return results

def main():
    """Run Polotno integration tests."""
    
    print("🚀 Testing Emma Studio Polotno Integration")
    print("Testing clean backgrounds and editable text layers")
    
    # Test cases
    test_cases = [
        "perros",
        "fitness", 
        "comida saludable"
    ]
    
    results = []
    
    for topic in test_cases:
        success = test_polotno_integration(topic)
        results.append((topic, success))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 POLOTNO INTEGRATION TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for topic, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {topic}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Polotno integration is working correctly.")
        print("✅ Clean background images generated")
        print("✅ Editable text layers created")
        print("✅ Proper JSON structure for Polotno")
    else:
        print("⚠️ Some tests failed. Polotno integration needs fixes.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
