#!/usr/bin/env python3
"""
<PERSON>ript to fix Pydantic V1 validators to V2 field_validators
"""

import re

def fix_validators():
    file_path = "app/schemas/buyer_persona.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace @validator with @field_validator and add @classmethod
    # Pattern: @validator('field_name') followed by def method_name(cls, v):
    pattern = r'(@validator\([^)]+\))\s*\n(\s*)def\s+(\w+)\(cls,\s*v([^)]*)\):'
    replacement = r'@field_validator\1\n\2@classmethod\n\2def \3(cls, v\4):'
    
    # First, fix the decorator name
    content = content.replace('@validator(', '@field_validator(')
    
    # Then add @classmethod where missing
    lines = content.split('\n')
    new_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        if '@field_validator(' in line:
            new_lines.append(line)
            # Check if next line is @classmethod
            if i + 1 < len(lines) and '@classmethod' not in lines[i + 1]:
                # Get indentation from current line
                indent = len(line) - len(line.lstrip())
                new_lines.append(' ' * indent + '@classmethod')
        else:
            new_lines.append(line)
        i += 1
    
    content = '\n'.join(new_lines)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print("Fixed all validators!")

if __name__ == "__main__":
    fix_validators()
