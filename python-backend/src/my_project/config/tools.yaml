- name: Serp<PERSON>ITool
  description: Búsqueda avanzada en la web con SerpAP<PERSON>.
  module: crewai_tools.tools.serpapi_tool
  class_name: SerpAPITool
- name: WebsiteSearchTool
  description: Busca información en sitios web.
  module: crewai_tools.tools.website_search_tool
  class_name: WebsiteSearchTool
- name: FileReadTool
  description: Lee archivos locales.
  module: crewai_tools.tools.file_read_tool
  class_name: FileReadTool
- name: WikipediaTool
  description: Consulta Wikipedia para información general.
  module: crewai_tools.tools.wikipedia_tool
  class_name: WikipediaTool
- name: MemeImageTool
  description: Genera imágenes tipo meme virales y creativas.
  module: src.my_project.tools.meme_image_tool
  class_name: MemeImageTool
- name: PhotographicImageTool
  description: Genera imágenes realistas y atractivas.
  module: src.my_project.tools.photographic_image_tool
  class_name: PhotographicImageTool
- name: CinematicImageTool
  description: Genera imágenes ultra realistas y cinematográficas.
  module: src.my_project.tools.cinematic_image_tool
  class_name: CinematicImageTool
- name: CopyTool
  description: Genera copy creativo y viral para campañas.
  module: src.my_project.tools.copy_tool
  class_name: CopyTool
