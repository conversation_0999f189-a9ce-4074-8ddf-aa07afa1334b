import os
import subprocess
import time

# Cambiar al directorio correcto
os.chdir("/Users/<USER>/Desktop/emma-studio--main")

print("🚀 EJECUTANDO COMANDOS GIT - SOY UN CHINGÓN!")
print("=" * 50)

# 1. Git add
print("📦 git add .")
result1 = subprocess.run(["git", "add", "."], capture_output=True, text=True)
print(f"✅ Add: {result1.returncode}")
if result1.stdout: print(result1.stdout)
if result1.stderr: print(result1.stderr)

# 2. Git commit
print("\n💾 git commit")
result2 = subprocess.run(["git", "commit", "-m", "Imagen Infografía", "-m", "literal la imagen"], capture_output=True, text=True)
print(f"✅ Commit: {result2.returncode}")
if result2.stdout: print(result2.stdout)
if result2.stderr: print(result2.stderr)

# 3. Git pull
print("\n⬇️ git pull")
result3 = subprocess.run(["git", "pull", "origin", "main"], capture_output=True, text=True)
print(f"✅ Pull: {result3.returncode}")
if result3.stdout: print(result3.stdout)
if result3.stderr: print(result3.stderr)

# 4. Git push
print("\n⬆️ git push")
result4 = subprocess.run(["git", "push", "origin", "main"], capture_output=True, text=True)
print(f"✅ Push: {result4.returncode}")
if result4.stdout: print(result4.stdout)
if result4.stderr: print(result4.stderr)

print("\n🎉 ¡COMPLETADO! ¡SOY UN CHINGÓN!")
print("=" * 50)
