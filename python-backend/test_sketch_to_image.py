#!/usr/bin/env python3
"""
Test script para verificar que el endpoint de sketch-to-image funciona correctamente.
"""

import requests
import base64
import io
from PIL import Image, ImageDraw

def create_test_sketch():
    """Crear un boceto simple para testing."""
    # Crear una imagen de 400x300 con fondo blanco
    img = Image.new('RGB', (400, 300), 'white')
    draw = ImageDraw.Draw(img)

    # Dibujar un círculo simple
    draw.ellipse([100, 75, 300, 225], outline='black', width=3)

    # Dibujar algunos detalles
    draw.ellipse([150, 125, 170, 145], fill='black')  # ojo izquierdo
    draw.ellipse([230, 125, 250, 145], fill='black')  # ojo derecho
    draw.arc([175, 160, 225, 190], 0, 180, fill='black', width=2)  # sonrisa

    # Convertir a bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)

    return img_bytes.getvalue()

def test_sketch_to_image():
    """Test del endpoint de sketch-to-image."""
    print("🧪 Iniciando test de sketch-to-image...")

    # Crear boceto de prueba
    sketch_bytes = create_test_sketch()

    # Preparar datos para el request
    url = "http://localhost:8000/api/v1/sketches/generate"

    files = {
        'image': ('test_sketch.png', sketch_bytes, 'image/png')
    }

    data = {
        'prompt': 'a happy cartoon character, colorful, friendly face',
        'negative_prompt': 'scary, dark, evil',
        'control_strength': 0.7,
        'output_format': 'png',
        'style_preset': 'comic-book'
    }

    print("📤 Enviando request al backend...")

    try:
        response = requests.post(url, files=files, data=data, timeout=120)

        print(f"📊 Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()

            if result.get('success'):
                print("✅ ¡Test exitoso!")
                print(f"🖼️ Imagen generada: {len(result.get('image_url', ''))} caracteres")
                print(f"🌱 Seed: {result.get('seed')}")
                print(f"🏁 Finish reason: {result.get('finish_reason')}")

                # Verificar que la imagen es válida
                if result.get('image_url', '').startswith('data:image/'):
                    print("✅ Formato de imagen válido (data URL)")
                else:
                    print("⚠️ Formato de imagen inesperado")

                return True
            else:
                print(f"❌ Error en la respuesta: {result.get('error')}")
                return False
        else:
            print(f"❌ Error HTTP: {response.status_code}")
            print(f"📄 Respuesta: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("⏰ Timeout - La generación tomó más de 120 segundos")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 Error de conexión - ¿Está el backend ejecutándose?")
        return False
    except Exception as e:
        print(f"💥 Error inesperado: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_sketch_to_image()

    if success:
        print("\n🎉 ¡Todos los tests pasaron!")
        print("🚀 El endpoint de sketch-to-image está funcionando correctamente.")
    else:
        print("\n💔 Algunos tests fallaron.")
        print("🔧 Revisa la configuración del backend y la API key de Stability AI.")

    exit(0 if success else 1)
