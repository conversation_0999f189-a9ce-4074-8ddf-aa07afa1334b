# Emma Studio - Python Backend

## 📁 Estructura Reorganizada

Este directorio contiene **TODOS los archivos Python y backend** de Emma Studio que fueron movidos desde la raíz del proyecto para resolver problemas de deployment en Cloudflare Pages.

## 🎯 **¿Por qué se movieron estos archivos?**

**Problema**: Cloudflare Pages detectaba automáticamente archivos Python en la raíz (`pyproject.toml`, `poetry.lock`, etc.) y trataba de configurar un entorno Python, causando errores de build como:
```
python-build: definition not found: nodejs-18
Failed: build command exited with code: 1
```

**Solución**: Mover todos los archivos Python a este directorio `python-backend/` para que Cloudflare Pages solo detecte el frontend (React/Node.js).

## 📦 **Contenido de este directorio:**

### **🐍 Archivos Python Core:**
- `poetry.lock` - Dependencias Python (Poetry)
- `pytest.ini` - Configuración de tests
- `runtime.txt` - Versión de Python para deployment
- `Procfile` - Configuración para Heroku/Railway
- `uv.lock` - Lock file de uv package manager

### **🤖 Agentes y Servicios:**
- `agents/` - Sistema de agentes de IA de Emma
- `backend/` - API FastAPI principal
- `ComfyUI/` - Herramientas de procesamiento de imágenes
- `server/` - Servidor adicional
- `src/` - Código fuente Python

### **🔧 Integraciones:**
- `emma-integration/` - Integración con Emma AI
- `video-service/` - Servicio de procesamiento de video
- `agent_protocol/` - Protocolo de comunicación entre agentes

### **📊 Herramientas SEO/Marketing:**
- `extracted_seo_agent/` - Agente especializado en SEO
- `google adk-samples main python-agents_brand-search-optimization/` - Optimización de búsqueda de marca
- `marketing-agency/` - Herramientas de agencia de marketing

## 🚀 **Deployment del Backend:**

### **Opción 1: Railway (Recomendada)**
```bash
# 1. Crear cuenta en Railway
# 2. Conectar GitHub repo
# 3. Configurar build command:
cd python-backend/backend && python -m uvicorn main:app --host 0.0.0.0 --port $PORT
```

### **Opción 2: Render**
```bash
# Build command:
cd python-backend/backend && pip install -r requirements.txt
# Start command:
cd python-backend/backend && python -m uvicorn main:app --host 0.0.0.0 --port $PORT
```

## 🔗 **Arquitectura Completa:**

```
📁 emma-studio/
├── 🟦 client/ (Frontend React) → Cloudflare Pages
├── 🐍 python-backend/ (Backend Python) → Railway/Render
├── 🌐 wrangler.toml (Cloudflare config)
└── 📝 README.md
```

## ⚙️ **Variables de Entorno Necesarias:**

```bash
# Para el backend Python:
GOOGLE_API_KEY=tu_key_aqui
IDEOGRAM_API_KEY=tu_key_aqui
SERPER_API_KEY=tu_key_aqui
DATABASE_URL=postgresql://...
PORT=8000
```

## 🎯 **Resultado:**

- ✅ **Frontend**: Deploy limpio en Cloudflare Pages (solo Node.js)
- ✅ **Backend**: Deploy separado en Railway/Render (Python completo)
- ✅ **Sin conflictos**: Cada parte usa su tecnología apropiada
- ✅ **Escalabilidad**: Cada servicio se escala independientemente

---

**📝 Nota**: Esta reorganización NO afecta la funcionalidad de Emma Studio, solo mejora la arquitectura de deployment separando frontend y backend correctamente.
