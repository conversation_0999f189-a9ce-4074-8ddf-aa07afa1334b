#!/usr/bin/env python3
"""
Test Real Intelligence System for Emma Studio
Verifies that all SEO analysis, SAIO optimization, and AI content generation work correctly
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.services.seo_analysis_engine import seo_analysis_engine
from backend.app.services.saio_intelligence_engine import saio_intelligence_engine
from backend.app.services.keyword_analysis_engine import keyword_analysis_engine
from backend.app.services.readability_calculator import readability_calculator

def test_seo_analysis():
    """Test real SEO analysis engine"""
    print("🔍 Testing Real SEO Analysis Engine...")
    
    test_content = """
    <h1>Guía Completa de SEO para 2024</h1>
    <p>El SEO es fundamental para el éxito online. Esta guía te enseñará las mejores prácticas de SEO.</p>
    <h2>¿Qué es SEO?</h2>
    <p>SEO significa Search Engine Optimization. Es el proceso de optimizar tu sitio web para motores de búsqueda.</p>
    <ul>
        <li>Investigación de palabras clave</li>
        <li>Optimización on-page</li>
        <li>Link building</li>
    </ul>
    <h2>Estrategias de SEO</h2>
    <p>Las estrategias de SEO incluyen optimización técnica, contenido de calidad y autoridad de dominio.</p>
    """
    
    keywords = ["SEO", "optimización", "motores de búsqueda", "palabras clave"]
    
    try:
        result = seo_analysis_engine.analyze_content(test_content, keywords)
        
        print(f"✅ SEO Score: {result.overall_score:.1f}/100")
        print(f"✅ Word Count: {result.word_count}")
        print(f"✅ Readability Score: {result.readability_score:.1f}")
        print(f"✅ Keyword Densities: {result.keyword_density}")
        print(f"✅ Suggestions: {len(result.suggestions)} recommendations")
        
        if result.overall_score > 0:
            print("🎉 SEO Analysis Engine: WORKING!")
            return True
        else:
            print("❌ SEO Analysis Engine: Failed")
            return False
            
    except Exception as e:
        print(f"❌ SEO Analysis Engine Error: {e}")
        return False

def test_saio_intelligence():
    """Test SAIO/GEO intelligence engine"""
    print("\n🤖 Testing SAIO Intelligence Engine...")
    
    test_content = """
    <h1>¿Cómo funciona la inteligencia artificial?</h1>
    <p>La inteligencia artificial es una tecnología revolucionaria que está transformando el mundo.</p>
    
    <h2>Preguntas Frecuentes sobre IA</h2>
    <h3>¿Qué es la inteligencia artificial?</h3>
    <p>La IA es la capacidad de las máquinas para realizar tareas que normalmente requieren inteligencia humana.</p>
    
    <h3>¿Cómo se entrena un modelo de IA?</h3>
    <p>Los modelos de IA se entrenan usando grandes cantidades de datos y algoritmos de aprendizaje automático.</p>
    
    <h2>Beneficios de la IA</h2>
    <ul>
        <li>Automatización de procesos</li>
        <li>Análisis de datos avanzado</li>
        <li>Personalización de experiencias</li>
    </ul>
    
    <img src="ai-diagram.jpg" alt="Diagrama de inteligencia artificial" />
    """
    
    metadata = {
        "publish_date": "2024-06-17T10:00:00Z",
        "author": "Emma AI Expert"
    }
    
    try:
        result = saio_intelligence_engine.analyze_saio_optimization(test_content, metadata)
        
        print(f"✅ SAIO Score: {result.saio_score:.1f}/100")
        print(f"✅ AI Readiness: {result.ai_readiness:.1f}/100")
        print(f"✅ Q&A Score: {result.qa_optimization['score']:.1f}/100")
        print(f"✅ List Score: {result.list_optimization['score']:.1f}/100")
        print(f"✅ E-E-A-T Score: {result.eat_compliance['score']:.1f}/100")
        print(f"✅ Freshness Score: {result.freshness_score:.1f}/100")
        print(f"✅ Multimedia Score: {result.multimedia_score:.1f}/100")
        print(f"✅ Recommendations: {len(result.recommendations)}")
        
        if result.saio_score > 0:
            print("🎉 SAIO Intelligence Engine: WORKING!")
            return True
        else:
            print("❌ SAIO Intelligence Engine: Failed")
            return False
            
    except Exception as e:
        print(f"❌ SAIO Intelligence Engine Error: {e}")
        return False

def test_keyword_analysis():
    """Test keyword analysis engine"""
    print("\n🔑 Testing Keyword Analysis Engine...")
    
    test_content = """
    Marketing digital es esencial para empresas modernas. El marketing digital incluye SEO, 
    redes sociales, email marketing y publicidad online. Las estrategias de marketing digital 
    ayudan a las empresas a alcanzar sus objetivos de negocio. El marketing de contenidos 
    es una parte fundamental del marketing digital moderno.
    """
    
    keywords = ["marketing digital", "SEO", "estrategias", "contenidos"]
    
    try:
        result = keyword_analysis_engine.analyze_keywords(test_content, keywords)
        
        print(f"✅ Primary Keywords: {len(result.primary_keywords)}")
        print(f"✅ Secondary Keywords: {len(result.secondary_keywords)}")
        print(f"✅ Semantic Keywords: {len(result.semantic_keywords)}")
        print(f"✅ Competitive Score: {result.competitive_score:.1f}/100")
        print(f"✅ Optimization Suggestions: {len(result.optimization_suggestions)}")
        
        if result.competitive_score > 0:
            print("🎉 Keyword Analysis Engine: WORKING!")
            return True
        else:
            print("❌ Keyword Analysis Engine: Failed")
            return False
            
    except Exception as e:
        print(f"❌ Keyword Analysis Engine Error: {e}")
        return False

def test_readability_calculator():
    """Test readability calculator"""
    print("\n📖 Testing Readability Calculator...")
    
    test_content = """
    El marketing digital es una disciplina que utiliza canales digitales para promocionar 
    productos y servicios. Incluye estrategias como SEO, redes sociales y email marketing. 
    Las empresas modernas necesitan una presencia digital sólida para competir efectivamente. 
    La medición de resultados es fundamental para optimizar las campañas de marketing digital.
    """
    
    try:
        result = readability_calculator.calculate_readability(test_content, 'es')
        
        print(f"✅ Flesch Reading Ease: {result.flesch_reading_ease:.1f}")
        print(f"✅ Flesch-Kincaid Grade: {result.flesch_kincaid_grade:.1f}")
        print(f"✅ SMOG Index: {result.smog_index:.1f}")
        print(f"✅ Overall Score: {result.overall_score:.1f}/100")
        print(f"✅ Reading Level: {result.reading_level}")
        print(f"✅ Target Audience: {result.target_audience}")
        print(f"✅ Suggestions: {len(result.suggestions)}")
        
        if result.overall_score > 0:
            print("🎉 Readability Calculator: WORKING!")
            return True
        else:
            print("❌ Readability Calculator: Failed")
            return False
            
    except Exception as e:
        print(f"❌ Readability Calculator Error: {e}")
        return False

def main():
    """Run all intelligence tests"""
    print("🚀 Emma Studio Real Intelligence System Test")
    print("=" * 50)
    
    results = []
    
    # Test all engines
    results.append(test_seo_analysis())
    results.append(test_saio_intelligence())
    results.append(test_keyword_analysis())
    results.append(test_readability_calculator())
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Tests Passed: {passed}/{total}")
    print(f"📈 Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL INTELLIGENCE ENGINES ARE WORKING!")
        print("🔥 Emma Studio Real Intelligence System: ACTIVATED!")
        print("\n🎯 Features Available:")
        print("   • Real SEO Analysis with proven algorithms")
        print("   • SAIO/GEO optimization for AI search engines")
        print("   • TF-IDF keyword analysis and competitive scoring")
        print("   • Multi-algorithm readability calculation")
        print("   • Actionable optimization suggestions")
        print("\n✨ Ready for production SaaS deployment!")
    else:
        print(f"\n⚠️  {total-passed} engines need attention")
        print("🔧 Check the error messages above for details")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
