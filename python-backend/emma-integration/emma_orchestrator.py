"""
Emma Orchestrator - Integración entre Emma Studio y AgenticSeek
Este módulo actúa como el cerebro principal que coordina todos los agentes especializados
"""

import os
import sys
import asyncio
import configparser
from typing import Dict, List, Any, Optional
from pathlib import Path

# Agregar el path de agenticseek al sistema
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'agenticseek'))

from agenticseek.sources.agents import (
    Agent, CoderAgent, CasualAgent, FileAgent,
    PlannerAgent, BrowserAgent
)
from agenticseek.sources.llm_provider import Provider
from agenticseek.sources.router import AgentRouter
from agenticseek.sources.browser import Browser, create_driver, CloudBrowser, create_cloud_browser
from agenticseek.sources.logger import Logger


class EmmaOrchestrator:
    """
    Emma Orchestrator - El cerebro principal que coordina todos los agentes
    Reemplaza al sistema de routing básico con inteligencia específica para marketing
    """

    def __init__(self, config_path: str = None):
        """
        Inicializa Emma como el orquestador principal
        """
        self.config_path = config_path or os.path.join(
            os.path.dirname(__file__), '..', 'agenticseek', 'config.ini'
        )
        self.config = configparser.ConfigParser()
        self.config.read(self.config_path)

        self.logger = Logger("emma_orchestrator.log")
        self.provider = None
        self.browser = None
        self.agents = {}
        self.router = None

        # Estado de Emma
        self.current_task = None
        self.task_history = []
        self.active_agents = []

        self._initialize_system()

    def _initialize_system(self):
        """Inicializa todos los componentes del sistema"""
        try:
            # Configurar provider (Gemini)
            self._setup_provider()

            # Configurar browser
            self._setup_browser()

            # Inicializar agentes especializados
            self._initialize_agents()

            # Configurar router inteligente
            self._setup_router()

            self.logger.info("Emma Orchestrator inicializado correctamente")

        except Exception as e:
            self.logger.error(f"Error inicializando Emma Orchestrator: {e}")
            raise

    def _setup_provider(self):
        """Configura el proveedor LLM (Gemini)"""
        try:
            self.provider = Provider(
                provider_name=self.config.get('MAIN', 'provider_name'),
                model_name=self.config.get('MAIN', 'provider_model'),
                server_address=self.config.get('MAIN', 'provider_server_address'),
                is_local=self.config.getboolean('MAIN', 'is_local')
            )
            self.logger.info(f"Provider configurado: {self.provider.get_model_name()}")
        except Exception as e:
            self.logger.error(f"Error configurando provider: {e}")
            raise

    def _setup_browser(self):
        """Configura el navegador para agentes web"""
        try:
            # Intentar usar navegador cloud primero
            use_cloud_browser = self.config.getboolean('BROWSER', 'use_cloud_browser')
            if use_cloud_browser:
                browserless_api_key = self.config.get('BROWSER', 'browserless_api_key')
                self.browser = create_cloud_browser(browserless_api_key)
                self.logger.info("CloudBrowser configurado correctamente")
            else:
                # Fallback a navegador local
                headless = self.config.getboolean('BROWSER', 'headless_browser')
                stealth = self.config.getboolean('BROWSER', 'stealth_mode')
                driver = create_driver(headless=headless, stealth_mode=stealth)
                self.browser = Browser(driver)
                self.logger.info("Browser local configurado correctamente")
        except Exception as e:
            self.logger.error(f"Error configurando browser: {e}")
            # Intentar fallback a cloud browser si local falla
            try:
                browserless_api_key = self.config.get('BROWSER', 'browserless_api_key')
                self.browser = create_cloud_browser(browserless_api_key)
                self.logger.info("Fallback a CloudBrowser exitoso")
            except Exception as fallback_error:
                self.logger.error(f"Fallback browser falló: {fallback_error}")
                # Browser es opcional, continuar sin él
                self.browser = None

    def _initialize_agents(self):
        """Inicializa todos los agentes especializados"""
        agent_name = self.config.get('MAIN', 'agent_name')
        personality = "jarvis" if self.config.getboolean('MAIN', 'jarvis_personality') else "base"

        # Agentes base de AgenticSeek
        self.agents = {
            'planner': PlannerAgent(
                agent_name,
                f"agenticseek/prompts/{personality}/planner_agent.txt",
                self.provider,
                verbose=True,
                browser=self.browser
            ),
            'browser': BrowserAgent(
                agent_name,
                f"agenticseek/prompts/{personality}/browser_agent.txt",
                self.provider,
                verbose=True,
                browser=self.browser
            ),
            'coder': CoderAgent(
                agent_name,
                f"agenticseek/prompts/{personality}/coder_agent.txt",
                self.provider,
                verbose=True
            ),
            'file': FileAgent(
                agent_name,
                f"agenticseek/prompts/{personality}/file_agent.txt",
                self.provider,
                verbose=True
            ),
            'casual': CasualAgent(
                agent_name,
                f"agenticseek/prompts/{personality}/casual_agent.txt",
                self.provider,
                verbose=True
            )
        }

        self.logger.info(f"Agentes inicializados: {list(self.agents.keys())}")

    def _setup_router(self):
        """Configura el router inteligente"""
        try:
            languages = self.config.get('MAIN', 'languages').split()
            agent_list = list(self.agents.values())

            self.router = AgentRouter(agent_list, supported_language=languages)
            self.logger.info("Router inteligente configurado")
        except Exception as e:
            self.logger.error(f"Error configurando router: {e}")
            # Continuar sin router, usar lógica manual
            self.router = None

    async def process_request(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Procesa una solicitud del usuario usando Emma como orquestador

        Args:
            user_input: La solicitud del usuario
            context: Contexto adicional (historial, preferencias, etc.)

        Returns:
            Dict con la respuesta y metadatos
        """
        try:
            self.logger.info(f"Emma procesando: {user_input[:100]}...")

            # Analizar la solicitud y determinar estrategia
            strategy = await self._analyze_request(user_input, context)

            # Ejecutar la estrategia
            result = await self._execute_strategy(strategy, user_input, context)

            # Guardar en historial
            self.task_history.append({
                'input': user_input,
                'strategy': strategy,
                'result': result,
                'timestamp': asyncio.get_event_loop().time()
            })

            return result

        except Exception as e:
            self.logger.error(f"Error procesando solicitud: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': "Lo siento, hubo un error procesando tu solicitud."
            }

    async def _analyze_request(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Analiza la solicitud del usuario y determina la mejor estrategia
        """
        # Usar el router si está disponible
        if self.router:
            try:
                selected_agent = self.router.route(user_input)
                return {
                    'type': 'single_agent',
                    'agent': selected_agent.type,
                    'confidence': 0.8
                }
            except Exception as e:
                self.logger.warning(f"Router falló, usando lógica manual: {e}")

        # Lógica manual de routing
        user_lower = user_input.lower()

        # Detectar tareas de marketing
        marketing_keywords = ['marketing', 'campaña', 'anuncio', 'seo', 'contenido', 'social', 'email']
        if any(keyword in user_lower for keyword in marketing_keywords):
            return {
                'type': 'marketing_workflow',
                'complexity': 'high',
                'agents_needed': ['planner', 'browser', 'coder']
            }

        # Detectar tareas de código
        code_keywords = ['código', 'programar', 'script', 'función', 'debug']
        if any(keyword in user_lower for keyword in code_keywords):
            return {
                'type': 'single_agent',
                'agent': 'coder',
                'confidence': 0.9
            }

        # Detectar tareas de navegación web
        web_keywords = ['buscar', 'web', 'navegar', 'encontrar', 'información']
        if any(keyword in user_lower for keyword in web_keywords):
            return {
                'type': 'single_agent',
                'agent': 'browser',
                'confidence': 0.8
            }

        # Detectar tareas de archivos
        file_keywords = ['archivo', 'guardar', 'leer', 'crear', 'documento']
        if any(keyword in user_lower for keyword in file_keywords):
            return {
                'type': 'single_agent',
                'agent': 'file',
                'confidence': 0.7
            }

        # Por defecto, usar conversación casual
        return {
            'type': 'single_agent',
            'agent': 'casual',
            'confidence': 0.5
        }

    async def _execute_strategy(self, strategy: Dict[str, Any], user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Ejecuta la estrategia determinada
        """
        if strategy['type'] == 'single_agent':
            return await self._execute_single_agent(strategy['agent'], user_input, context)
        elif strategy['type'] == 'marketing_workflow':
            return await self._execute_marketing_workflow(user_input, context)
        else:
            return await self._execute_single_agent('casual', user_input, context)

    async def _execute_single_agent(self, agent_type: str, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Ejecuta una tarea con un solo agente
        """
        try:
            agent = self.agents.get(agent_type)
            if not agent:
                raise ValueError(f"Agente {agent_type} no encontrado")

            # Ejecutar el agente
            result = await asyncio.to_thread(agent.run, user_input)

            return {
                'success': True,
                'agent_used': agent_type,
                'response': result.get('response', ''),
                'metadata': {
                    'execution_time': result.get('execution_time', 0),
                    'status': result.get('status', 'completed')
                }
            }

        except Exception as e:
            self.logger.error(f"Error ejecutando agente {agent_type}: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': f"Error ejecutando {agent_type}: {str(e)}"
            }

    async def _execute_marketing_workflow(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Ejecuta un workflow complejo de marketing usando múltiples agentes
        """
        try:
            # Usar el PlannerAgent para dividir la tarea
            planner = self.agents['planner']
            plan_result = await asyncio.to_thread(planner.run, user_input)

            # Ejecutar cada subtarea del plan
            workflow_results = []

            # Por ahora, retornar el resultado del planner
            # TODO: Implementar ejecución de subtareas

            return {
                'success': True,
                'workflow_type': 'marketing',
                'response': plan_result.get('response', ''),
                'plan': plan_result.get('plan', []),
                'metadata': {
                    'agents_used': ['planner'],
                    'workflow_results': workflow_results
                }
            }

        except Exception as e:
            self.logger.error(f"Error ejecutando workflow de marketing: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': f"Error en workflow de marketing: {str(e)}"
            }

    def get_status(self) -> Dict[str, Any]:
        """
        Retorna el estado actual de Emma
        """
        return {
            'active': True,
            'current_task': self.current_task,
            'agents_available': list(self.agents.keys()),
            'active_agents': self.active_agents,
            'task_history_count': len(self.task_history),
            'provider': self.provider.get_model_name() if self.provider else None,
            'browser_available': self.browser is not None
        }

    def cleanup(self):
        """
        Limpia recursos al cerrar
        """
        try:
            if self.browser:
                self.browser.close()
            self.logger.info("Emma Orchestrator cerrado correctamente")
        except Exception as e:
            self.logger.error(f"Error cerrando Emma Orchestrator: {e}")


# Instancia global de Emma
emma_orchestrator = None

def get_emma_orchestrator() -> EmmaOrchestrator:
    """
    Retorna la instancia global de Emma Orchestrator
    """
    global emma_orchestrator
    if emma_orchestrator is None:
        emma_orchestrator = EmmaOrchestrator()
    return emma_orchestrator
