/* --- Global Styles & Variables --- */
:root {
    --primary-color: #8B4513; /* <PERSON> - Suggests chocolate/baking */
    --secondary-color: #D2691E; /* Chocolate Brown */
    --accent-color: #FFD700; /* Gold - For highlights/buttons */
    --light-bg-color: #FFF8F0; /* Very light cream/off-white */
    --text-color: #4A4A4A; /* Dark Gray */
    --heading-color: #333;
    --white-color: #FFFFFF;
    --border-color: #E0E0E0;

    --font-primary: 'Georgia', serif; /* Elegant serif for headings */
    --font-secondary: 'Helvetica Neue', Arial, sans-serif; /* Clean sans-serif for body */

    --container-width: 1140px;
    --header-height: 70px;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--white-color);
}

.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    color: var(--heading-color);
    margin-bottom: 1rem;
    line-height: 1.3;
}

h1 { font-size: 2.8rem; }
h2 { font-size: 2.2rem; }
h3 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover, a:focus {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* --- Utility Classes --- */
.section-padding {
    padding: 60px 0;
}

.bg-light {
    background-color: var(--light-bg-color);
}

.text-center {
    text-align: center;
}

.top-margin {
    margin-top: 2rem;
}

/* --- Header & Navigation --- */
.site-header {
    background-color: var(--white-color);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    height: var(--header-height);
    display: flex;
    align-items: center;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.brand-logo {
    font-family: var(--font-primary);
    font-size: 1.6rem;
    font-weight: bold;
    color: var(--primary-color);
}

.main-nav ul {
    list-style: none;
    display: flex;
    gap: 25px; /* Spacing between nav items */
}

.main-nav a {
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    color: var(--text-color);
    padding: 5px 0;
    position: relative;
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.main-nav a.active {
    color: var(--primary-color);
}

/* Optional Cart Icon Style */
.cart-icon {
    font-weight: bold;
}

/* Mobile Navigation Toggle */
.nav-toggle {
    display: none; /* Hidden by default */
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    position: relative; /* Needed for absolute positioning of spans */
    z-index: 1010; /* Ensure it's above nav menu */
}

.hamburger,
.hamburger::before,
.hamburger::after {
    content: '';
    display: block;
    background-color: var(--heading-color);
    height: 3px;
    width: 25px;
    border-radius: 3px;
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.hamburger::before {
    position: absolute;
    bottom: 7px; /* Adjust as needed */
}

.hamburger::after {
    position: absolute;
    top: 7px; /* Adjust as needed */
}

/* --- Footer --- */
.site-footer {
    background-color: var(--heading-color);
    color: var(--white-color);
    padding: 30px 0;
    text-align: center;
    font-size: 0.9rem;
}

.site-footer p {
    margin-bottom: 0;
}

/* Optional Social Links */
.social-links {
    list-style: none;
    padding: 0;
    margin-top: 10px;
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-links a {
    color: var(--white-color);
    font-size: 1.2rem;
}
.social-links a:hover {
    color: var(--accent-color);
}


/* --- Buttons --- */
.cta-button, .button-secondary {
    display: inline-block;
    padding: 12px 25px;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.2s ease;
    cursor: pointer;
    border: none;
    font-size: 1rem;
    margin: 5px; /* Add space around buttons */
}

.cta-button {
    background-color: var(--primary-color);
    color: var(--white-color);
    border: 1px solid var(--primary-color);
}

.cta-button:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
    transform: translateY(-2px);
}

.button-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.button-secondary:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
     transform: translateY(-2px);
}

/* --- Hero Section --- */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('https://source.unsplash.com/random/1600x800/?cake,bakery,celebration') no-repeat center center/cover;
    /* TODO: Replace placeholder background image */
    height: 70vh; /* Adjust height as needed */
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white-color);
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 0.5rem;
    color: var(--white-color);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-content .subtitle {
    font-size: 1.4rem;
    margin-bottom: 2rem;
    font-weight: 300;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

/* --- Featured Cakes / Cake Grid --- */
.cake-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Responsive grid */
    gap: 30px;
}

.cake-card {
    background-color: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column; /* Ensure content stacks vertically */
    padding: 15px;
    text-align: center;
}

.cake-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.cake-card img {
    width: 100%;
    height: 200px; /* Fixed height for consistency */
    object-fit: cover; /* Crop images nicely */
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
    border-radius: 4px 4px 0 0; /* Round top corners if padding is removed */
}

.cake-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.cake-card p {
    font-size: 0.9rem;
    flex-grow: 1; /* Allow description to take available space */
    margin-bottom: 1rem;
}

.cake-card .price {
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    display: block;
}

/* Specific adjustments for shop grid */
.shop-grid .cake-card {
    /* Add specific styles if needed, e.g., more padding */
}

.shop-grid .add-to-cart-btn {
     margin-top: auto; /* Push button to bottom if card heights vary */
}

.disclaimer {
    font-size: 0.9rem;
    color: #777;
    margin-top: 2rem;
}


/* --- About Snippet & About Page --- */
.about-snippet {
    text-align: center;
}

.profile-pic-small {
    border-radius: 50%;
    width: 120px;
    height: 120px;
    object-fit: cover;
    margin: 0 auto 1.5rem auto;
    border: 3px solid var(--border-color);
}

.about-container {
    display: grid;
    grid-template-columns: 1fr 2fr; /* Image | Text */
    gap: 40px;
    align-items: center;
}

.about-image img {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.about-text h2 {
    margin-top: 0; /* Remove top margin if it's the first element */
}

/* --- Page Header --- */
.page-header {
    background-color: var(--light-bg-color);
    text-align: center;
    padding: 40px 0;
}

.page-header h1 {
    margin-bottom: 0.5rem;
}
.page-header p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 0;
}

/* --- Contact Page --- */
.contact-container {
    display: grid;
    grid-template-columns: 2fr 1fr; /* Form | Details */
    gap: 50px;
}

.contact-form .form-group {
    margin-bottom: 1.5rem;
}

.contact-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: var(--heading-color);
}

.contact-form input[type="text"],
.contact-form input[type="email"],
.contact-form input[type="date"],
.contact-form textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    font-family: var(--font-secondary);
}

.contact-form textarea {
    resize: vertical; /* Allow vertical resizing */
    min-height: 120px;
}

.contact-form button[type="submit"] {
    width: auto;
    padding: 12px 30px;
}

.contact-details h2 {
    margin-top: 0;
}

.contact-details p {
    margin-bottom: 1.2rem;
    line-height: 1.7;
}
.contact-details strong {
     color: var(--heading-color);
}

.map-placeholder img {
    margin-top: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.form-status {
    margin-top: 1rem;
    font-weight: bold;
}


/* --- Responsiveness --- */
@media (max-width: 992px) {
    .container {
        max-width: 960px;
    }
    h1 { font-size: 2.5rem; }
    .hero-content h1 { font-size: 3rem; }
    .about-container { grid-template-columns: 1fr; } /* Stack image and text */
    .about-image { text-align: center; margin-bottom: 2rem;}
    .contact-container { grid-template-columns: 1fr; gap: 30px; }\
    .contact-details { margin-top: 2rem; }
}

@media (max-width: 768px) {
    :root { --header-height: 60px; }
    .container { padding: 0 15px; }
    h1 { font-size: 2.2rem; }
    h2 { font-size: 1.8rem; }
    .hero { height: 60vh; }
    .hero-content h1 { font-size: 2.5rem; }
    .hero-content .subtitle { font-size: 1.2rem; }
    .cake-grid { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }

    /* Mobile Navigation */
    .nav-toggle {
        display: block; /* Show the hamburger */
    }

    .main-nav {
        position: relative; /* Contain the absolute menu */
    }

    .main-nav ul {
        position: absolute;
        top: calc(var(--header-height) - 1px); /* Position below header border */
        right: 0;
        background-color: var(--white-color);
        flex-direction: column;
        width: 250px; /* Adjust width */
        border: 1px solid var(--border-color);
        border-top: none;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        padding: 15px 0;
        gap: 0;
        transform: scaleY(0); /* Start hidden */
        transform-origin: top;
        transition: transform 0.3s ease;
        opacity: 0; /* Start transparent */
        visibility: hidden; /* Hide completely when closed */
    }

    .main-nav.is-open ul {
        transform: scaleY(1); /* Show menu */
        opacity: 1;
        visibility: visible;
    }

    .main-nav li {
        width: 100%;
    }

    .main-nav a {
        display: block;
        padding: 12px 20px;
        width: 100%;
        text-align: left;
    }
     .main-nav a::after {
        display: none; /* Hide underline on mobile */
    }
    .main-nav a:hover, .main-nav a.active {
        background-color: var(--light-bg-color);
        color: var(--primary-color);
    }

    /* Adjust hamburger icon when menu is open */
    .nav-toggle[aria-expanded="true"] .hamburger {
        background-color: transparent; /* Hide middle bar */
    }
    .nav-toggle[aria-expanded="true"] .hamburger::before {
        transform: translateY(7px) rotate(45deg);
    }
    .nav-toggle[aria-expanded="true"] .hamburger::after {
        transform: translateY(-7px) rotate(-45deg);
    }
}

@media (max-width: 576px) {
     h1 { font-size: 2rem; }
     .hero { height: 55vh; }
     .hero-content h1 { font-size: 2rem; }
     .hero-content .subtitle { font-size: 1.1rem; }
     .section-padding { padding: 40px 0; }
     .cake-grid { grid-template-columns: 1fr; } /* Single column on very small screens */
     .cta-button, .button-secondary { width: 100%; margin: 5px 0; } /* Full width buttons */
     .cta-section .button-secondary { margin-top: 10px; }
}