import asyncio
import os
from typing import Dict, Any

from qdrant_client import QdrantClient
from qdrant_client.models import VectorParams, Distance
import websockets
import openai

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

class IntegrationTest:
    def __init__(self):
        # Qdrant Configuration
        self.qdrant_url = os.getenv('QDRANT_SERVER_URL', 'http://localhost:6333')
        self.qdrant_collection = os.getenv('QDRANT_COLLECTION_NAME', 'agent_context')
        
        # WebSocket Configuration
        self.websocket_url = os.getenv('WEBSOCKET_SERVER_URL', 'ws://localhost:3001')
        
        # OpenAI Configuration
        openai.api_key = os.getenv('OPENAI_API_KEY')

    async def test_qdrant_connection(self):
        print("🔍 Testing Qdrant Vector Database Connection")
        try:
            client = QdrantClient(url=self.qdrant_url)
            
            # Create collection if not exists
            client.create_collection(
                collection_name=self.qdrant_collection,
                vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
            )
            
            # Test vector embedding
            embedding = openai.Embedding.create(
                input="Test embedding for Qdrant integration",
                model="text-embedding-ada-002"
            )['data'][0]['embedding']
            
            # Upsert test point
            client.upsert(
                collection_name=self.qdrant_collection,
                points=[{
                    'id': 'test_point_1',
                    'vector': embedding,
                    'payload': {'text': 'Integration test payload'}
                }]
            )
            
            print("✅ Qdrant Connection and Vector Storage: Successful")
        except Exception as e:
            print(f"❌ Qdrant Test Failed: {e}")

    async def test_websocket_connection(self):
        print("🔍 Testing WebSocket Connection")
        try:
            async with websockets.connect(self.websocket_url) as websocket:
                # Send a test message
                await websocket.send(json.dumps({
                    'type': 'test_connection',
                    'message': 'Integration test connection'
                }))
                
                # Wait for response
                response = await websocket.recv()
                print(f"✅ WebSocket Connection: Successful. Response: {response}")
        except Exception as e:
            print(f"❌ WebSocket Test Failed: {e}")

    async def run_tests(self):
        print("🚀 Starting Integration Tests")
        await self.test_qdrant_connection()
        await self.test_websocket_connection()
        print("🏁 Integration Tests Completed")

async def main():
    test = IntegrationTest()
    await test.run_tests()

if __name__ == '__main__':
    asyncio.run(main())
