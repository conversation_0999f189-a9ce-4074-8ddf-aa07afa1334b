# DEPRECATED TypeScript Backend

⚠️ **WARNING: This TypeScript backend is deprecated and should not be used.** ⚠️

The project has been standardized on the Python FastAPI backend located in the `backend/app/` directory.

## Migration Notice

All functionality from this TypeScript backend has been or is being migrated to the Python FastAPI backend.
Please use the Python backend for all new development and API integrations.

## Running the Application

To run the application, use the Python FastAPI backend:

```bash
cd backend
poetry install
poetry run uvicorn app.main:app --reload
```

## API Endpoints

All API endpoints are now available through the Python backend at:
- Base URL: `http://localhost:8000`
- API prefix: `/api/v1`
- Documentation: `http://localhost:8000/docs`

For more information, see the documentation in the `backend/` directory.
