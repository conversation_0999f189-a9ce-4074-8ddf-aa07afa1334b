/**
 * Agent Protocol Server Entry Point
 * Initializes and runs the WebSocket server for agent communication
 */

import { AgentProtocolServer } from './agent-protocol-server';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get configuration from environment variables
const PORT = parseInt(process.env.AGENT_PROTOCOL_PORT || '8080', 10);
const PING_INTERVAL = parseInt(process.env.AGENT_PING_INTERVAL || '30000', 10);
const WS_PATH = process.env.AGENT_WS_PATH || '/agent-protocol';

async function main() {
  // Create and configure the server
  const server = new AgentProtocolServer({
    port: PORT,
    pingInterval: PING_INTERVAL,
    path: WS_PATH,
    allowCors: true
  });

  // Start the server
  try {
    await server.start();
    console.log('Agent Protocol Server started successfully');
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('Shutting down Agent Protocol Server...');
      await server.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('Shutting down Agent Protocol Server...');
      await server.stop();
      process.exit(0);
    });
    
    // Log stats periodically
    setInterval(() => {
      const stats = server.getStats();
      const agents = server.getConnectedAgents();
      
      console.log(`
=== Agent Protocol Server Stats ===
Active connections: ${stats.activeConnections}
Total connections: ${stats.totalConnections}
Messages sent: ${stats.messagesSent}
Messages received: ${stats.messagesReceived}
Uptime: ${Math.floor((Date.now() - stats.startTime) / 1000)}s
Connected agents: ${agents.length}
====================================
      `);
    }, 60000); // Log every minute
    
  } catch (error) {
    console.error('Failed to start Agent Protocol Server:', error);
    process.exit(1);
  }
}

// Run the server
main().catch(console.error);

// Export the server class for testing and programmatic usage
export { AgentProtocolServer };
