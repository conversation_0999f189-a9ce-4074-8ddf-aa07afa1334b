/**
 * Agent Protocol WebSocket Server
 * 
 * This server handles WebSocket connections for agent communication in the Vibe Marketing platform.
 * It manages agent connections, registration/deregistration, and message routing between agents.
 */

import * as http from 'http';
import WebSocket from 'ws';
import { WebSocketServer } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import express from 'express';

// Type Definitions
export interface AgentConnection {
  id: string;
  socket: WebSocket;
  agentId: string;
  name: string;
  role: string;
  capabilities: string[];
  lastSeen: number;
  isAlive: boolean;
}

export interface ServerStats {
  totalConnections: number;
  activeConnections: number;
  messagesSent: number;
  messagesReceived: number;
  startTime: number;
}

export interface ServerOptions {
  port?: number;
  pingInterval?: number;
  path?: string;
  allowCors?: boolean;
}

export interface AgentMessage {
  id: string;
  sender: any;
  recipient: any;
  messageType: string;
  content: any;
  timestamp: number;
  correlationId?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Agent Protocol WebSocket Server Implementation
 */
export class AgentProtocolServer {
  private server: http.Server;
  private wss: WebSocket.Server;
  private app: express.Application;
  private connections: Map<string, AgentConnection> = new Map();
  private agentToConnectionMap: Map<string, string> = new Map();
  private stats: ServerStats;
  private options: ServerOptions;

  constructor(options: ServerOptions = {}) {
    this.options = {
      port: 8080,
      pingInterval: 30000, // 30 seconds
      path: '/agent-protocol',
      allowCors: true,
      ...options
    };

    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      messagesSent: 0,
      messagesReceived: 0,
      startTime: Date.now()
    };

    this.app = express();
    this.setupExpress();
    this.server = http.createServer(this.app);
    this.wss = new WebSocketServer({ 
      server: this.server,
      path: this.options.path
    });

    this.setupWebSocketServer();
  }

  /**
   * Configure Express application
   */
  private setupExpress(): void {
    // Set up CORS if enabled
    if (this.options.allowCors) {
      this.app.use((req, res, next) => {
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
        next();
      });
    }

    // Basic health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        uptime: Math.floor((Date.now() - this.stats.startTime) / 1000),
        stats: this.stats
      });
    });

    // Endpoint to get active agents
    this.app.get('/agents', (req, res) => {
      const agents = Array.from(this.connections.values()).map(conn => ({
        id: conn.agentId,
        name: conn.name,
        role: conn.role,
        capabilities: conn.capabilities,
        lastSeen: conn.lastSeen
      }));

      res.json({
        count: agents.length,
        agents
      });
    });
  }

  /**
   * Configure WebSocket server
   */
  private setupWebSocketServer(): void {
    this.wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
      const connectionId = uuidv4();
      
      // Initialize the connection with temporary info
      // The complete agent info will be sent in the registration message
      const connection: AgentConnection = {
        id: connectionId,
        socket: ws,
        agentId: '',
        name: 'Unknown',
        role: 'Unknown',
        capabilities: [],
        lastSeen: Date.now(),
        isAlive: true
      };
      
      this.connections.set(connectionId, connection);
      this.stats.totalConnections++;
      this.stats.activeConnections++;
      
      console.log(`[AgentProtocolServer] New connection: ${connectionId}`);
      
      // Set up ping-pong heartbeat
      ws.on('pong', () => {
        const conn = this.connections.get(connectionId);
        if (conn) {
          conn.isAlive = true;
          conn.lastSeen = Date.now();
        }
      });
      
      // Handle incoming messages
      ws.on('message', (data: WebSocket.Data) => {
        try {
          const message = JSON.parse(data.toString()) as AgentMessage;
          this.stats.messagesReceived++;
          
          // Update connection's last seen timestamp
          const conn = this.connections.get(connectionId);
          if (conn) {
            conn.lastSeen = Date.now();
          }
          
          // Handle agent registration
          if (
            message.messageType === 'NOTIFICATION' && 
            message.content?.type === 'agent_registration'
          ) {
            this.handleAgentRegistration(connectionId, message);
            return;
          }
          
          // Route the message to the appropriate recipient
          this.routeMessage(message);
        } catch (error) {
          console.error('[AgentProtocolServer] Error processing message:', error);
          // Send error back to the client
          this.sendToConnection(connectionId, {
            id: uuidv4(),
            sender: 'server',
            recipient: connectionId,
            messageType: 'ERROR',
            content: {
              error: 'Error processing message',
              details: (error as Error).message
            },
            timestamp: Date.now()
          });
        }
      });
      
      // Handle disconnection
      ws.on('close', () => {
        console.log(`[AgentProtocolServer] Connection closed: ${connectionId}`);
        this.handleDisconnection(connectionId);
      });
      
      // Handle errors
      ws.on('error', (error) => {
        console.error(`[AgentProtocolServer] Connection error: ${connectionId}`, error);
        this.handleDisconnection(connectionId);
      });
      
      // Send welcome message
      this.sendToConnection(connectionId, {
        id: uuidv4(),
        sender: 'server',
        recipient: connectionId,
        messageType: 'NOTIFICATION',
        content: {
          type: 'welcome',
          message: 'Connected to Agent Protocol Server',
          serverId: uuidv4(),
          timestamp: Date.now()
        },
        timestamp: Date.now()
      });
    });
    
    // Set up periodic ping to detect disconnected clients
    setInterval(() => {
      this.connections.forEach((conn, id) => {
        if (!conn.isAlive) {
          console.log(`[AgentProtocolServer] Connection timed out: ${id}`);
          conn.socket.terminate();
          this.handleDisconnection(id);
          return;
        }
        
        conn.isAlive = false;
        conn.socket.ping();
      });
    }, this.options.pingInterval);
  }

  /**
   * Start the WebSocket server
   */
  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server.listen(this.options.port, () => {
          console.log(`Agent Protocol Server listening on port ${this.options.port}`);
          resolve();
        });

        this.server.on('error', (error) => {
          console.error('Agent Protocol Server start error:', error);
          reject(error);
        });
      } catch (error) {
        console.error('Failed to start Agent Protocol Server:', error);
        reject(error);
      }
    });
  }

  /**
   * Stop the WebSocket server
   */
  async stop(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Close WebSocket server
        this.wss.close((err) => {
          if (err) {
            console.error('Error closing WebSocket server:', err);
            reject(err);
            return;
          }

          // Close HTTP server
          this.server.close((serverErr) => {
            if (serverErr) {
              console.error('Error closing HTTP server:', serverErr);
              reject(serverErr);
              return;
            }

            console.log('Agent Protocol Server stopped');
            resolve();
          });
        });
      } catch (error) {
        console.error('Failed to stop Agent Protocol Server:', error);
        reject(error);
      }
    });
  }
  
  /**
   * Handle agent registration message
   */
  private handleAgentRegistration(connectionId: string, message: AgentMessage): void {
    const conn = this.connections.get(connectionId);
    if (!conn) return;
    
    // Extract agent info from the registration message
    const agentId = typeof message.sender === 'string' 
      ? message.sender 
      : message.sender.id;
      
    const agentName = typeof message.sender === 'string'
      ? 'Unknown'
      : message.sender.name;
      
    const agentRole = typeof message.sender === 'string'
      ? 'Unknown'
      : message.sender.role;
      
    const capabilities = typeof message.sender === 'string'
      ? []
      : message.sender.capabilities || [];
      
    // Remove any previous connection with the same agent ID
    const previousConnId = this.agentToConnectionMap.get(agentId);
    if (previousConnId && previousConnId !== connectionId) {
      const previousConn = this.connections.get(previousConnId);
      if (previousConn) {
        console.log(`[AgentProtocolServer] Replacing existing connection for agent ${agentId}`);
        previousConn.socket.terminate();
        this.connections.delete(previousConnId);
        this.stats.activeConnections--;
      }
    }
    
    // Update the connection with agent info
    conn.agentId = agentId;
    conn.name = agentName;
    conn.role = agentRole;
    conn.capabilities = capabilities;
    
    // Update agent to connection mapping
    this.agentToConnectionMap.set(agentId, connectionId);
    
    console.log(`[AgentProtocolServer] Agent registered: ${agentName} (${agentId})`);
    
    // Send registration confirmation
    this.sendToConnection(connectionId, {
      id: uuidv4(),
      sender: 'server',
      recipient: message.sender,
      messageType: 'NOTIFICATION',
      content: {
        type: 'registration_confirmed',
        agentId,
        message: 'Agent registered successfully',
        activeAgents: this.connections.size
      },
      timestamp: Date.now(),
      correlationId: message.id
    });
    
    // Broadcast agent online notification to other agents
    this.broadcast({
      id: uuidv4(),
      sender: 'server',
      recipient: 'broadcast',
      messageType: 'NOTIFICATION',
      content: {
        type: 'agent_online',
        agentId,
        name: agentName,
        role: agentRole,
        capabilities
      },
      timestamp: Date.now()
    }, connectionId); // Exclude the sender from broadcast
  }
  
  /**
   * Handle client disconnection
   */
  private handleDisconnection(connectionId: string): void {
    const conn = this.connections.get(connectionId);
    if (!conn) return;
    
    // Remove from maps
    this.connections.delete(connectionId);
    if (conn.agentId) {
      this.agentToConnectionMap.delete(conn.agentId);
      
      // Broadcast agent offline notification
      this.broadcast({
        id: uuidv4(),
        sender: 'server',
        recipient: 'broadcast',
        messageType: 'NOTIFICATION',
        content: {
          type: 'agent_offline',
          agentId: conn.agentId,
          name: conn.name
        },
        timestamp: Date.now()
      });
    }
    
    this.stats.activeConnections--;
  }
  
  /**
   * Route a message to its intended recipient
   */
  private routeMessage(message: AgentMessage): void {
    // Handle broadcast messages
    if (
      message.recipient === 'broadcast' || 
      (typeof message.recipient === 'string' && message.recipient === 'broadcast')
    ) {
      this.broadcast(message);
      return;
    }
    
    // Get the recipient's connection ID
    const recipientId = typeof message.recipient === 'string'
      ? message.recipient
      : message.recipient.id;
      
    const connectionId = this.agentToConnectionMap.get(recipientId);
    if (!connectionId) {
      console.warn(`[AgentProtocolServer] Recipient not found: ${recipientId}`);
      
      // Get sender's connection to send error
      const senderId = typeof message.sender === 'string'
        ? message.sender
        : message.sender.id;
        
      const senderConnId = this.agentToConnectionMap.get(senderId);
      if (senderConnId) {
        this.sendToConnection(senderConnId, {
          id: uuidv4(),
          sender: 'server',
          recipient: message.sender,
          messageType: 'ERROR',
          content: {
            error: 'Recipient not found',
            recipientId
          },
          timestamp: Date.now(),
          correlationId: message.id
        });
      }
      return;
    }
    
    // Forward the message to the recipient
    this.sendToConnection(connectionId, message);
  }
  
  /**
   * Send a message to a specific connection
   */
  private sendToConnection(connectionId: string, message: AgentMessage): boolean {
    const conn = this.connections.get(connectionId);
    if (!conn || conn.socket.readyState !== WebSocket.OPEN) {
      return false;
    }
    
    try {
      conn.socket.send(JSON.stringify(message));
      this.stats.messagesSent++;
      return true;
    } catch (error) {
      console.error(`[AgentProtocolServer] Error sending message to ${connectionId}:`, error);
      return false;
    }
  }
  
  /**
   * Broadcast a message to all connected agents
   */
  private broadcast(message: AgentMessage, excludeConnectionId?: string): void {
    this.connections.forEach((conn, id) => {
      if (excludeConnectionId && id === excludeConnectionId) {
        return;
      }
      
      this.sendToConnection(id, message);
    });
  }
  
  /**
   * Start the server
   */
  public start(): Promise<void> {
    return new Promise((resolve) => {
      this.server.listen(this.options.port, () => {
        console.log(`[AgentProtocolServer] Server started on port ${this.options.port}`);
        console.log(`[AgentProtocolServer] WebSocket endpoint: ws://localhost:${this.options.port}${this.options.path}`);
        resolve();
      });
    });
  }
  
  /**
   * Stop the server
   */
  public stop(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Close all connections
      this.connections.forEach((conn) => {
        conn.socket.terminate();
      });
      this.connections.clear();
      this.agentToConnectionMap.clear();
      
      // Close the server
      this.server.close((err) => {
        if (err) {
          reject(err);
          return;
        }
        console.log('[AgentProtocolServer] Server stopped');
        resolve();
      });
    });
  }
  
  /**
   * Get server statistics
   */
  public getStats(): ServerStats {
    return { ...this.stats };
  }
  
  /**
   * Get the number of connected agents
   */
  public getConnectionCount(): number {
    return this.connections.size;
  }
  
  /**
   * Get the list of connected agents
   */
  public getConnectedAgents(): Array<{
    id: string;
    name: string;
    role: string;
    capabilities: string[];
    lastSeen: number;
  }> {
    return Array.from(this.connections.values()).map(conn => ({
      id: conn.agentId,
      name: conn.name,
      role: conn.role,
      capabilities: conn.capabilities,
      lastSeen: conn.lastSeen
    }));
  }
}
