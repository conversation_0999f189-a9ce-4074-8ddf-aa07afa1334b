import express from 'express';
import http from 'http';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Remove duplicate declarations
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files from the client build directory
import path from 'path';

// Serve static files from the client build directory
app.use(express.static(path.join(__dirname, '../../client/dist')));

// Serve index.html for all routes to support client-side routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../../client/dist/index.html')));


// Serve index.html for all routes to support client-side routing
// Serve index.html for all routes to support client-side routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../../client/dist/index.html'));
});




// Create HTTP server
const server = http.createServer(app);


// Initialize Agent Protocol Server
import { AgentProtocolServer } from './services/agent-protocol-server/agent-protocol-server';
const agentProtocolServer = new AgentProtocolServer({
  port: parseInt(process.env.AGENT_PROTOCOL_PORT || '8080', 10),
  path: process.env.AGENT_WS_PATH || '/agent-protocol',
  allowCors: true,
});

  port: parseInt(process.env.AGENT_PROTOCOL_PORT || '8080', 10),
  path: process.env.AGENT_WS_PATH || '/agent-protocol',
  allowCors: true,
});

  port: parseInt(process.env.AGENT_PROTOCOL_PORT || '8080', 10),
  path: process.env.AGENT_WS_PATH || '/agent-protocol',
  allowCors: true,
});

  port: parseInt(process.env.AGENT_PROTOCOL_PORT || '8080', 10),
  path: process.env.AGENT_WS_PATH || '/agent-protocol',
  allowCors: true,
});

  port: parseInt(process.env.AGENT_PROTOCOL_PORT || '8080', 10),
  path: process.env.AGENT_WS_PATH || '/agent-protocol',
  allowCors: true,
});

async function startServer() {
  try {
    // Start the agent protocol server
    await agentProtocolServer.start();
    console.log('✅ Agent Protocol Server initialized');

    // Start the main HTTP server
    const PORT = process.env.PORT || 3000;
    server.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      console.log('Shutting down server...');
      await agentProtocolServer.stop();
      server.close(() => {
        console.log('Server shut down');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
    process.exit(1);
  }
    process.exit(1);
  }
    process.exit(1);
  }
    process.exit(1);
  }
    process.exit(1);
  }
}

// Run the server
startServer();
