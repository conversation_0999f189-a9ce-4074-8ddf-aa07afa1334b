<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Brand Creation Color Extraction</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-center;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .preview-container {
            margin-top: 20px;
            text-align: center;
        }
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .color-palette {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        .color-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .color-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        .color-swatch {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        .color-info {
            font-size: 14px;
        }
        .color-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .color-hex {
            color: #666;
            font-size: 12px;
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            border: 2px dashed #007bff;
            border-radius: 10px;
            background-color: #f0f8ff;
        }
        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success-message {
            color: #28a745;
            margin-top: 10px;
            font-weight: bold;
        }
        .error-message {
            color: #dc3545;
            margin-top: 10px;
            font-weight: bold;
        }
        .feature-list {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .feature-list h3 {
            margin-top: 0;
            color: #495057;
        }
        .primary-secondary-colors {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .color-field {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .color-field h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: bold;
        }
        .color-display {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .color-preview {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .extracted-badge {
            font-size: 10px;
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="feature-list">
            <h3>🎨 Brand Creation Color Extraction Enhancement</h3>
            <p><strong>Status:</strong> ✅ Color extraction functionality has been implemented!</p>
            <ul>
                <li><strong>✅ Automatic Color Extraction:</strong> Colors are extracted automatically when logo is uploaded</li>
                <li><strong>✅ Dominant Color Detection:</strong> Uses the same algorithm as Color Palette Generator</li>
                <li><strong>✅ Auto-Apply Colors:</strong> Primary and secondary colors are automatically updated</li>
                <li><strong>✅ Visual Feedback:</strong> Shows extraction progress and extracted colors</li>
                <li><strong>✅ Interactive Colors:</strong> Click extracted colors to apply them</li>
                <li><strong>✅ Format Support:</strong> Works with PNG, JPG, and WebP formats</li>
                <li><strong>✅ Graceful Fallback:</strong> Handles extraction failures appropriately</li>
            </ul>
        </div>

        <h2>🧪 Test Color Extraction</h2>
        <p>Upload a logo image to test the color extraction functionality:</p>
        
        <div class="upload-area" id="uploadArea">
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
            <div id="uploadContent">
                <div style="font-size: 48px; margin-bottom: 16px;">🎨</div>
                <h3>Upload Logo for Color Extraction</h3>
                <p>Click here or drag a logo image to test color extraction</p>
                <button type="button" style="padding: 8px 16px; border: 1px solid #ccc; border-radius: 4px; background: white; cursor: pointer;">Select Logo</button>
                <p style="font-size: 12px; color: #666; margin-top: 8px;">PNG, JPG, WebP up to 30MB</p>
            </div>
        </div>

        <div class="preview-container" id="previewContainer" style="display: none;">
            <img id="previewImage" class="preview-image" alt="Logo Preview">
            <div class="success-message">✅ Logo cargado exitosamente</div>
        </div>

        <div id="loadingContainer" class="loading" style="display: none;">
            <div class="spinner"></div>
            <span>Extrayendo colores del logo...</span>
        </div>

        <div id="colorsContainer" style="display: none;">
            <h3>🎨 Colores Extraídos</h3>
            <div class="color-palette" id="colorPalette"></div>
            <p style="font-size: 12px; color: #666; margin-top: 10px;">
                💡 Haz clic en un color para aplicarlo como primario o secundario
            </p>
        </div>

        <div class="primary-secondary-colors">
            <div class="color-field">
                <h4>Color Primario <span id="primaryBadge" class="extracted-badge" style="display: none;">Extraído</span></h4>
                <div class="color-display">
                    <div id="primaryPreview" class="color-preview" style="background-color: #3018ef;"></div>
                    <div>
                        <div id="primaryHex">#3018ef</div>
                        <div id="primaryName" style="font-size: 12px; color: #666;">Azul</div>
                    </div>
                </div>
            </div>
            <div class="color-field">
                <h4>Color Secundario <span id="secondaryBadge" class="extracted-badge" style="display: none;">Extraído</span></h4>
                <div class="color-display">
                    <div id="secondaryPreview" class="color-preview" style="background-color: #dd3a5a;"></div>
                    <div>
                        <div id="secondaryHex">#dd3a5a</div>
                        <div id="secondaryName" style="font-size: 12px; color: #666;">Rojo</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="errorMessage" class="error-message" style="display: none;"></div>
    </div>

    <div class="test-container">
        <h3>📋 Manual Testing Instructions</h3>
        <ol>
            <li>Navigate to <a href="http://localhost:3002/dashboard/marca/crear" target="_blank">Brand Creation Page</a></li>
            <li>Click "Siguiente" to reach Step 2 (Visual Identity)</li>
            <li>Upload a logo image (PNG, JPG, or WebP)</li>
            <li>✅ Verify "Extrayendo colores del logo..." message appears</li>
            <li>✅ Verify extracted colors are displayed below the logo</li>
            <li>✅ Verify primary and secondary colors are auto-updated</li>
            <li>✅ Verify "Extraído del logo" badges appear on color fields</li>
            <li>✅ Test clicking on extracted colors to apply them</li>
            <li>✅ Test with different logo images to verify consistency</li>
        </ol>
    </div>

    <script>
        let extractedColors = [];
        let primaryColor = '#3018ef';
        let secondaryColor = '#dd3a5a';

        // Simulate the same color extraction logic as implemented
        function extractColorsFromImage(file) {
            return new Promise((resolve) => {
                const img = new Image();
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                img.onload = () => {
                    try {
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);

                        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
                        const colors = extractDominantColors(imageData, canvas.width, canvas.height, 5);
                        
                        resolve({
                            success: true,
                            colors: colors.map((hex, index) => ({
                                hex,
                                name: getColorName(hex),
                                frequency: colors.length - index
                            }))
                        });
                    } catch (error) {
                        resolve({ success: false, colors: [], error: error.message });
                    }
                };

                img.onerror = () => resolve({ success: false, colors: [], error: 'Error loading image' });
                img.src = URL.createObjectURL(file);
            });
        }

        function extractDominantColors(imageData, width, height, colorCount) {
            const colorMap = {};
            const sampleRate = 10;

            for (let y = 0; y < height; y += sampleRate) {
                for (let x = 0; x < width; x += sampleRate) {
                    const idx = (y * width + x) * 4;
                    let r = imageData[idx];
                    let g = imageData[idx + 1];
                    let b = imageData[idx + 2];
                    const a = imageData[idx + 3];

                    if (a < 128) continue;

                    r = Math.round(r / 16) * 16;
                    g = Math.round(g / 16) * 16;
                    b = Math.round(b / 16) * 16;

                    const rgbValue = ((r << 16) | (g << 8) | b) >>> 0;
                    const hex = `#${rgbValue.toString(16).padStart(6, "0")}`;
                    colorMap[hex] = (colorMap[hex] || 0) + 1;
                }
            }

            return Object.entries(colorMap)
                .sort((a, b) => b[1] - a[1])
                .map(entry => entry[0])
                .slice(0, colorCount);
        }

        function getColorName(hex) {
            // Simplified color naming
            const colors = {
                '#ff0000': 'Rojo', '#00ff00': 'Verde', '#0000ff': 'Azul',
                '#ffff00': 'Amarillo', '#ff00ff': 'Magenta', '#00ffff': 'Cian'
            };
            return colors[hex.toLowerCase()] || 'Color';
        }

        async function handleFileUpload(file) {
            document.getElementById('uploadContent').style.display = 'none';
            document.getElementById('previewContainer').style.display = 'block';
            document.getElementById('loadingContainer').style.display = 'block';
            document.getElementById('colorsContainer').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';

            // Show preview
            const previewUrl = URL.createObjectURL(file);
            document.getElementById('previewImage').src = previewUrl;

            // Extract colors
            const result = await extractColorsFromImage(file);

            document.getElementById('loadingContainer').style.display = 'none';

            if (result.success && result.colors.length > 0) {
                extractedColors = result.colors;
                displayExtractedColors(result.colors);
                
                // Auto-apply primary and secondary colors
                if (result.colors[0]) {
                    primaryColor = result.colors[0].hex;
                    updateColorField('primary', result.colors[0]);
                }
                if (result.colors[1]) {
                    secondaryColor = result.colors[1].hex;
                    updateColorField('secondary', result.colors[1]);
                }
            } else {
                document.getElementById('errorMessage').textContent = result.error || 'No se pudieron extraer colores';
                document.getElementById('errorMessage').style.display = 'block';
            }
        }

        function displayExtractedColors(colors) {
            const palette = document.getElementById('colorPalette');
            palette.innerHTML = '';

            colors.forEach((color, index) => {
                const colorItem = document.createElement('div');
                colorItem.className = 'color-item';
                colorItem.onclick = () => applyColor(color, index);
                
                colorItem.innerHTML = `
                    <div class="color-swatch" style="background-color: ${color.hex};"></div>
                    <div class="color-info">
                        <div class="color-name">${color.name}</div>
                        <div class="color-hex">${color.hex}</div>
                    </div>
                `;
                
                palette.appendChild(colorItem);
            });

            document.getElementById('colorsContainer').style.display = 'block';
        }

        function applyColor(color, index) {
            if (index === 0) {
                primaryColor = color.hex;
                updateColorField('primary', color);
            } else {
                secondaryColor = color.hex;
                updateColorField('secondary', color);
            }
        }

        function updateColorField(type, color) {
            const preview = document.getElementById(`${type}Preview`);
            const hex = document.getElementById(`${type}Hex`);
            const name = document.getElementById(`${type}Name`);
            const badge = document.getElementById(`${type}Badge`);

            preview.style.backgroundColor = color.hex;
            hex.textContent = color.hex;
            name.textContent = color.name;
            badge.style.display = 'inline';
        }

        function resetUpload() {
            document.getElementById('uploadContent').style.display = 'block';
            document.getElementById('previewContainer').style.display = 'none';
            document.getElementById('loadingContainer').style.display = 'none';
            document.getElementById('colorsContainer').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('fileInput').value = '';
        }

        // Event listeners
        document.getElementById('uploadArea').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        document.getElementById('fileInput').addEventListener('change', (e) => {
            const file = e.target.files?.[0];
            if (file) handleFileUpload(file);
        });

        // Drag and drop
        document.getElementById('uploadArea').addEventListener('dragover', (e) => {
            e.preventDefault();
            e.currentTarget.style.borderColor = '#007bff';
        });

        document.getElementById('uploadArea').addEventListener('dragleave', (e) => {
            e.currentTarget.style.borderColor = '#ccc';
        });

        document.getElementById('uploadArea').addEventListener('drop', (e) => {
            e.preventDefault();
            e.currentTarget.style.borderColor = '#ccc';
            const file = e.dataTransfer.files?.[0];
            if (file) handleFileUpload(file);
        });
    </script>
</body>
</html>
