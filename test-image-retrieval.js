#!/usr/bin/env node

/**
 * Test Image Retrieval from Supabase Storage
 * Based on the actual file paths from backend logs
 */

console.log('🖼️ TESTING IMAGE RETRIEVAL FROM SUPABASE STORAGE');
console.log('================================================');

const SUPABASE_URL = "https://pthewpjbegkgomvyhkin.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aGV3cGpiZWdrZ29tdnloa2luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MjM1NDMsImV4cCI6MjA2NDI5OTU0M30.bskxkyZ9meYb2cpZZGmS_FAS2Wyjs4j_lOPnJqh1s0k";

// Test file paths from the backend logs
const testFilePaths = [
  "8142a43b-add3-46ce-9eda-2d9b1dc81f56/1752195727002_6b598488_Ads_Central.png",
  "8142a43b-add3-46ce-9eda-2d9b1dc81f56/1752195754449_64c528d5_DAE78D45-1C24-45E7-9A80-507D0FFB90B7.png"
];

async function testImageRetrieval() {
  console.log('🔍 Testing different image retrieval methods...\n');

  for (const filePath of testFilePaths) {
    console.log(`📁 Testing file: ${filePath.substring(0, 50)}...`);
    
    // Method 1: Public URL (should fail for private bucket)
    console.log('  Method 1: Public URL');
    try {
      const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/design-analysis-images/${filePath}`;
      const publicResponse = await fetch(publicUrl, { method: 'HEAD' });
      console.log(`    Status: ${publicResponse.status} (${publicResponse.ok ? 'SUCCESS' : 'FAILED - Expected for private bucket'})`);
    } catch (error) {
      console.log(`    Error: ${error.message}`);
    }

    // Method 2: Authenticated download (should work with proper auth)
    console.log('  Method 2: Authenticated Download');
    try {
      const downloadUrl = `${SUPABASE_URL}/storage/v1/object/design-analysis-images/${filePath}`;
      const downloadResponse = await fetch(downloadUrl, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      });
      console.log(`    Status: ${downloadResponse.status} (${downloadResponse.ok ? 'SUCCESS' : 'FAILED - Needs user auth'})`);
      
      if (downloadResponse.ok) {
        const contentType = downloadResponse.headers.get('content-type');
        const contentLength = downloadResponse.headers.get('content-length');
        console.log(`    Content-Type: ${contentType}`);
        console.log(`    Content-Length: ${contentLength} bytes`);
      }
    } catch (error) {
      console.log(`    Error: ${error.message}`);
    }

    // Method 3: Signed URL creation (should work with service role)
    console.log('  Method 3: Signed URL (requires service role)');
    try {
      const signedUrlEndpoint = `${SUPABASE_URL}/storage/v1/object/sign/design-analysis-images/${filePath}`;
      const signedResponse = await fetch(signedUrlEndpoint, {
        method: 'POST',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          expiresIn: 3600 // 1 hour
        })
      });
      console.log(`    Status: ${signedResponse.status} (${signedResponse.ok ? 'SUCCESS' : 'FAILED - Needs service role'})`);
      
      if (signedResponse.ok) {
        const signedData = await signedResponse.json();
        console.log(`    Signed URL created: ${signedData.signedURL ? 'YES' : 'NO'}`);
        
        if (signedData.signedURL) {
          // Test the signed URL
          const testSignedResponse = await fetch(signedData.signedURL, { method: 'HEAD' });
          console.log(`    Signed URL test: ${testSignedResponse.status} (${testSignedResponse.ok ? 'SUCCESS' : 'FAILED'})`);
        }
      }
    } catch (error) {
      console.log(`    Error: ${error.message}`);
    }

    console.log('');
  }
}

async function testBackendImageService() {
  console.log('🔧 Testing Backend Image Service...\n');

  // Test if backend has image retrieval endpoint
  try {
    const backendImageUrl = 'http://localhost:8001/api/image/test';
    const backendResponse = await fetch(backendImageUrl);
    console.log(`Backend image endpoint: ${backendResponse.status} (${backendResponse.ok ? 'AVAILABLE' : 'NOT AVAILABLE'})`);
  } catch (error) {
    console.log(`Backend image endpoint: ERROR - ${error.message}`);
  }

  // Test design analysis image retrieval
  try {
    const designAnalysisImageUrl = 'http://localhost:8001/api/design-analysis/image/test';
    const designResponse = await fetch(designAnalysisImageUrl);
    console.log(`Design analysis image endpoint: ${designResponse.status} (${designResponse.ok ? 'AVAILABLE' : 'NOT AVAILABLE'})`);
  } catch (error) {
    console.log(`Design analysis image endpoint: ERROR - ${error.message}`);
  }

  console.log('');
}

async function testSupabaseStorageInfo() {
  console.log('📊 Testing Supabase Storage Information...\n');

  try {
    // Get bucket info
    const bucketUrl = `${SUPABASE_URL}/storage/v1/bucket/design-analysis-images`;
    const bucketResponse = await fetch(bucketUrl, {
      headers: {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      }
    });
    
    console.log(`Bucket info: ${bucketResponse.status}`);
    if (bucketResponse.ok) {
      const bucketData = await bucketResponse.json();
      console.log(`Bucket public: ${bucketData.public}`);
      console.log(`Bucket file size limit: ${bucketData.file_size_limit}`);
      console.log(`Bucket allowed mime types: ${bucketData.allowed_mime_types?.join(', ') || 'All'}`);
    }

    // Try to list objects (will fail without proper auth)
    const listUrl = `${SUPABASE_URL}/storage/v1/object/list/design-analysis-images`;
    const listResponse = await fetch(listUrl, {
      method: 'POST',
      headers: {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        limit: 5,
        offset: 0
      })
    });
    
    console.log(`Object listing: ${listResponse.status} (${listResponse.ok ? 'SUCCESS' : 'REQUIRES AUTH'})`);
    
  } catch (error) {
    console.log(`Storage info test error: ${error.message}`);
  }

  console.log('');
}

async function generateRecommendations() {
  console.log('💡 RECOMMENDATIONS FOR IMAGE DISPLAY ISSUES');
  console.log('===========================================');
  
  console.log('Based on the test results, here are the likely causes and solutions:');
  console.log('');
  
  console.log('🔍 LIKELY CAUSES:');
  console.log('1. Private bucket requires authenticated requests');
  console.log('2. Frontend needs proper image URL resolution');
  console.log('3. Tldraw may need custom asset resolver');
  console.log('4. CORS configuration for image requests');
  console.log('');
  
  console.log('🔧 SOLUTIONS:');
  console.log('1. Implement backend endpoint for image retrieval with authentication');
  console.log('2. Use signed URLs for temporary image access');
  console.log('3. Configure tldraw with custom asset resolver');
  console.log('4. Ensure proper CORS headers for image requests');
  console.log('');
  
  console.log('🚀 IMMEDIATE ACTIONS:');
  console.log('1. Create /api/image/{file_path} endpoint in backend');
  console.log('2. Implement signed URL generation for images');
  console.log('3. Update frontend to use backend image URLs');
  console.log('4. Test with actual user authentication');
  console.log('');
}

// Run all tests
async function runAllTests() {
  await testSupabaseStorageInfo();
  await testImageRetrieval();
  await testBackendImageService();
  await generateRecommendations();
  
  console.log('🏁 IMAGE RETRIEVAL TESTING COMPLETE');
  console.log('===================================');
}

runAllTests();
