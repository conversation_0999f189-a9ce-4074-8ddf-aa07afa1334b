/**
 * Final test script to verify the corrected mood board image persistence
 * This script tests the corrected TLAssetStore implementation
 */

console.log('🎯 Final Image Persistence Test');
console.log('===============================\n');

class FinalImagePersistenceTest {
  constructor() {
    this.testResults = [];
    this.originalConsoleLog = console.log;
    this.assetStoreLogs = [];
  }

  async runFinalTest() {
    console.log('🚀 Starting final image persistence test...\n');
    
    try {
      await this.step1_CheckAssetStoreConfiguration();
      await this.step2_MonitorAssetStoreActivity();
      await this.step3_TestImageUploadFlow();
      await this.step4_VerifyDatabasePersistence();
      await this.step5_TestImageRetrieval();
      
      this.displayFinalResults();
    } catch (error) {
      console.error('❌ Final test failed:', error);
    }
  }

  async step1_CheckAssetStoreConfiguration() {
    console.log('📋 Step 1: Checking Asset Store Configuration');
    
    try {
      // Check if the corrected asset store is working
      const tldrawCanvas = document.querySelector('canvas');
      if (tldrawCanvas) {
        console.log('✅ Tldraw canvas found');
        this.testResults.push({ test: 'Canvas Found', result: 'PASS' });
      } else {
        console.log('❌ Tldraw canvas not found');
        this.testResults.push({ test: 'Canvas Found', result: 'FAIL' });
      }

      // Set up monitoring for asset store logs
      console.log = (...args) => {
        const message = args.join(' ');
        if (message.includes('MoodBoard Asset Store') || 
            message.includes('MoodBoard Editor: Creating asset store')) {
          this.assetStoreLogs.push({
            timestamp: new Date().toISOString(),
            message: message
          });
          console.log('📡 Asset Store Activity:', message);
        }
        this.originalConsoleLog.apply(console, args);
      };

      console.log('✅ Asset store monitoring enabled');
      this.testResults.push({ test: 'Monitoring Setup', result: 'PASS' });

    } catch (error) {
      console.error('❌ Error in step 1:', error);
      this.testResults.push({ test: 'Configuration Check', result: 'ERROR', details: error.message });
    }
  }

  async step2_MonitorAssetStoreActivity() {
    console.log('\n📋 Step 2: Monitoring Asset Store Activity');
    
    try {
      // Wait for component to fully mount and asset store to initialize
      await new Promise(resolve => setTimeout(resolve, 3000));

      console.log(`ℹ️ Captured ${this.assetStoreLogs.length} asset store logs so far`);
      
      if (this.assetStoreLogs.length > 0) {
        console.log('✅ Asset store initialization detected');
        this.assetStoreLogs.forEach((log, index) => {
          console.log(`   ${index + 1}. [${log.timestamp}] ${log.message}`);
        });
        this.testResults.push({ test: 'Asset Store Initialization', result: 'PASS' });
      } else {
        console.log('⚠️ No asset store initialization logs detected yet');
        this.testResults.push({ test: 'Asset Store Initialization', result: 'PENDING' });
      }

    } catch (error) {
      console.error('❌ Error in step 2:', error);
      this.testResults.push({ test: 'Activity Monitoring', result: 'ERROR', details: error.message });
    }
  }

  async step3_TestImageUploadFlow() {
    console.log('\n📋 Step 3: Testing Image Upload Flow');
    
    try {
      // Create a test image for upload simulation
      const testFile = await this.createTestImageFile();
      console.log(`✅ Test image created: ${testFile.name} (${testFile.size} bytes)`);

      // Try to simulate drag and drop
      const canvas = document.querySelector('canvas');
      if (canvas) {
        console.log('🔄 Simulating image drag and drop...');
        
        // Create drag and drop event with the test file
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(testFile);
        
        const dragEnterEvent = new DragEvent('dragenter', {
          bubbles: true,
          cancelable: true,
          dataTransfer: dataTransfer
        });
        
        const dragOverEvent = new DragEvent('dragover', {
          bubbles: true,
          cancelable: true,
          dataTransfer: dataTransfer
        });
        
        const dropEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          dataTransfer: dataTransfer
        });

        // Dispatch events in sequence
        canvas.dispatchEvent(dragEnterEvent);
        await new Promise(resolve => setTimeout(resolve, 100));
        canvas.dispatchEvent(dragOverEvent);
        await new Promise(resolve => setTimeout(resolve, 100));
        canvas.dispatchEvent(dropEvent);
        
        console.log('✅ Drop events dispatched');
        
        // Wait for potential asset store upload activity
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check for upload-related logs
        const uploadLogs = this.assetStoreLogs.filter(log => 
          log.message.includes('Uploading asset') || 
          log.message.includes('Upload successful') ||
          log.message.includes('Upload failed')
        );
        
        if (uploadLogs.length > 0) {
          console.log('✅ Upload activity detected');
          uploadLogs.forEach(log => {
            console.log(`   📤 ${log.message}`);
          });
          this.testResults.push({ test: 'Image Upload Flow', result: 'PASS' });
        } else {
          console.log('⚠️ No upload activity detected');
          this.testResults.push({ test: 'Image Upload Flow', result: 'PENDING' });
        }

      } else {
        console.log('❌ Canvas not available for drop simulation');
        this.testResults.push({ test: 'Image Upload Flow', result: 'FAIL' });
      }

    } catch (error) {
      console.error('❌ Error in step 3:', error);
      this.testResults.push({ test: 'Upload Flow Test', result: 'ERROR', details: error.message });
    }
  }

  async step4_VerifyDatabasePersistence() {
    console.log('\n📋 Step 4: Verifying Database Persistence');
    
    try {
      // Get current mood board ID from URL
      const currentUrl = window.location.href;
      const boardId = currentUrl.split('/').pop();
      
      if (boardId && boardId !== 'new') {
        console.log(`ℹ️ Checking mood board ${boardId} for persistence...`);
        
        // Fetch current mood board data
        const response = await fetch(`/api/moodboard/${boardId}`);
        if (response.ok) {
          const data = await response.json();
          const moodboard = data.data;
          
          if (moodboard && moodboard.tldraw_data) {
            console.log('✅ Mood board data found');
            
            // Check for image shapes in tldraw data
            const imageShapes = this.findImageShapes(moodboard.tldraw_data);
            console.log(`ℹ️ Found ${imageShapes.length} image shapes`);
            
            let validUrls = 0;
            let nullUrls = 0;
            
            imageShapes.forEach((shape, index) => {
              const src = shape.props?.src;
              if (src && src !== 'null' && src !== null) {
                validUrls++;
                const urlType = this.getUrlType(src);
                console.log(`✅ Image ${index + 1}: ${urlType} (${src.substring(0, 50)}...)`);
              } else {
                nullUrls++;
                console.log(`❌ Image ${index + 1}: NULL or missing src`);
              }
            });
            
            if (nullUrls === 0 && validUrls > 0) {
              console.log('🎉 All images have valid URLs - persistence working!');
              this.testResults.push({ test: 'Database Persistence', result: 'PASS' });
            } else if (nullUrls > 0) {
              console.log(`⚠️ Found ${nullUrls} images with null URLs - persistence issue remains`);
              this.testResults.push({ test: 'Database Persistence', result: 'FAIL' });
            } else {
              console.log('ℹ️ No images found to test persistence');
              this.testResults.push({ test: 'Database Persistence', result: 'PENDING' });
            }
            
          } else {
            console.log('⚠️ No tldraw data in mood board');
            this.testResults.push({ test: 'Database Persistence', result: 'PENDING' });
          }
        } else {
          console.log('❌ Failed to fetch mood board data');
          this.testResults.push({ test: 'Database Persistence', result: 'FAIL' });
        }
      } else {
        console.log('ℹ️ This is a new mood board, no persistence data to check');
        this.testResults.push({ test: 'Database Persistence', result: 'PENDING' });
      }

    } catch (error) {
      console.error('❌ Error in step 4:', error);
      this.testResults.push({ test: 'Database Persistence', result: 'ERROR', details: error.message });
    }
  }

  async step5_TestImageRetrieval() {
    console.log('\n📋 Step 5: Testing Image Retrieval');
    
    try {
      // Check if images in the DOM are loading correctly
      const images = document.querySelectorAll('img');
      let loadedImages = 0;
      let failedImages = 0;
      
      for (const img of images) {
        if (img.complete && img.naturalWidth > 0) {
          loadedImages++;
          console.log(`✅ Image loading correctly: ${img.src.substring(0, 50)}...`);
        } else if (img.src && img.src !== '') {
          failedImages++;
          console.log(`❌ Image failed to load: ${img.src.substring(0, 50)}...`);
        }
      }
      
      console.log(`📊 Image Loading Summary: ${loadedImages} loaded, ${failedImages} failed, ${images.length} total`);
      
      if (failedImages === 0 && loadedImages > 0) {
        this.testResults.push({ test: 'Image Retrieval', result: 'PASS' });
      } else if (failedImages > 0) {
        this.testResults.push({ test: 'Image Retrieval', result: 'FAIL' });
      } else {
        this.testResults.push({ test: 'Image Retrieval', result: 'PENDING' });
      }

    } catch (error) {
      console.error('❌ Error in step 5:', error);
      this.testResults.push({ test: 'Image Retrieval', result: 'ERROR', details: error.message });
    }
  }

  // Helper methods
  async createTestImageFile() {
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    // Create a colorful test pattern
    const gradient = ctx.createLinearGradient(0, 0, 200, 200);
    gradient.addColorStop(0, '#ff6b6b');
    gradient.addColorStop(0.5, '#4ecdc4');
    gradient.addColorStop(1, '#45b7d1');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 200, 200);
    
    // Add text
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('FINAL', 100, 90);
    ctx.fillText('TEST', 100, 120);
    
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    return new File([blob], 'final-test-image.png', { type: 'image/png' });
  }

  findImageShapes(tldrawData) {
    const imageShapes = [];
    
    if (tldrawData && tldrawData.store) {
      for (const [shapeId, shape] of Object.entries(tldrawData.store)) {
        if (shape && shape.type === 'image') {
          imageShapes.push(shape);
        }
      }
    }
    
    return imageShapes;
  }

  getUrlType(url) {
    if (url.startsWith('data:')) return 'Data URL';
    if (url.startsWith('blob:')) return 'Blob URL';
    if (url.includes('supabase')) return 'Supabase URL';
    if (url.startsWith('http')) return 'HTTP URL';
    return 'Unknown';
  }

  displayFinalResults() {
    console.log('\n🎯 FINAL IMAGE PERSISTENCE TEST RESULTS');
    console.log('========================================');
    
    // Restore original console.log
    console.log = this.originalConsoleLog;
    
    const passedTests = this.testResults.filter(test => test.result === 'PASS').length;
    const failedTests = this.testResults.filter(test => test.result === 'FAIL').length;
    const pendingTests = this.testResults.filter(test => test.result === 'PENDING').length;
    const errorTests = this.testResults.filter(test => test.result === 'ERROR').length;
    
    this.testResults.forEach(test => {
      const emoji = test.result === 'PASS' ? '✅' : 
                   test.result === 'FAIL' ? '❌' : 
                   test.result === 'PENDING' ? '⏳' : '⚠️';
      console.log(`${emoji} ${test.test}: ${test.result}`);
      if (test.details) {
        console.log(`   Details: ${test.details}`);
      }
    });
    
    console.log(`\n📊 Test Summary: ${passedTests} passed, ${failedTests} failed, ${pendingTests} pending, ${errorTests} errors`);
    console.log(`📊 Asset Store Logs: ${this.assetStoreLogs.length} captured`);
    
    console.log('\n🔧 Fix Summary:');
    console.log('✅ Corrected TLAssetStore implementation to match tldraw API');
    console.log('✅ Fixed upload method to return { src: string } instead of TLAsset');
    console.log('✅ Fixed resolve method to return string directly');
    console.log('✅ Used correct "assets" prop in Tldraw component');
    
    if (passedTests >= 3) {
      console.log('\n🎉 SUCCESS: Image persistence fix appears to be working!');
      console.log('💡 The corrected implementation should now:');
      console.log('   - Upload images to Supabase Storage');
      console.log('   - Save proper URLs instead of null values');
      console.log('   - Persist images correctly across sessions');
    } else {
      console.log('\n⚠️ PARTIAL SUCCESS: Some tests are still pending or failed');
      console.log('💡 Try adding an image manually to test the upload flow');
    }
    
    console.log('\n🧪 Manual Test Instructions:');
    console.log('1. Drag and drop an image onto the mood board canvas');
    console.log('2. Wait for the upload to complete');
    console.log('3. Save the mood board');
    console.log('4. Refresh the page');
    console.log('5. Verify the image loads correctly');
  }
}

// Auto-run the test if we're on the mood board editor page
if (window.location.href.includes('mood-board/editor')) {
  const finalTest = new FinalImagePersistenceTest();
  finalTest.runFinalTest();
} else {
  console.log('ℹ️ Please run this test on a mood board editor page');
  console.log('ℹ️ Navigate to: /dashboard/herramientas/mood-board/editor/[board-id]');
}
