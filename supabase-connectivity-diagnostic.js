#!/usr/bin/env node

/**
 * Comprehensive Supabase Connectivity Diagnostic Tool
 * Tests all aspects of Supabase connectivity affecting image display and storage
 */

console.log('🔍 SUPABASE CONNECTIVITY DIAGNOSTIC TOOL');
console.log('=========================================');
console.log('Testing Supabase connectivity, authentication, and image storage...\n');

// Configuration from the codebase
const SUPABASE_URL = "https://pthewpjbegkgomvyhkin.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aGV3cGpiZWdrZ29tdnloa2luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MjM1NDMsImV4cCI6MjA2NDI5OTU0M30.bskxkyZ9meYb2cpZZGmS_FAS2Wyjs4j_lOPnJqh1s0k";
const BACKEND_URL = "http://localhost:8001";

class SupabaseConnectivityDiagnostic {
  constructor() {
    this.results = {};
    this.errors = [];
    this.warnings = [];
  }

  async runFullDiagnostic() {
    console.log('🚀 Starting comprehensive Supabase diagnostic...\n');

    try {
      // Test 1: Backend connectivity
      await this.testBackendConnectivity();
      
      // Test 2: Supabase direct connectivity
      await this.testSupabaseDirectConnectivity();
      
      // Test 3: Authentication flow
      await this.testAuthenticationFlow();
      
      // Test 4: Storage bucket access
      await this.testStorageBucketAccess();
      
      // Test 5: Image retrieval methods
      await this.testImageRetrievalMethods();
      
      // Test 6: MoodBoard specific tests
      await this.testMoodBoardImageHandling();
      
      // Test 7: Visual Complexity Analyzer tests
      await this.testVisualComplexityAnalyzer();
      
      // Generate final report
      this.generateFinalReport();
      
    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      this.errors.push(`Diagnostic failed: ${error.message}`);
    }
  }

  async testBackendConnectivity() {
    console.log('📡 Test 1: Backend Connectivity');
    console.log('-------------------------------');

    try {
      // Test health endpoint
      const healthResponse = await fetch(`${BACKEND_URL}/api/health`);
      if (healthResponse.ok) {
        console.log('✅ Backend health check: PASSED');
        this.results.backendHealth = true;
      } else {
        console.log('❌ Backend health check: FAILED');
        this.results.backendHealth = false;
        this.errors.push(`Backend health check failed: ${healthResponse.status}`);
      }

      // Test CORS
      const corsHeaders = healthResponse.headers;
      const corsOrigin = corsHeaders.get('access-control-allow-origin');
      if (corsOrigin) {
        console.log(`✅ CORS configured: ${corsOrigin}`);
        this.results.corsConfigured = true;
      } else {
        console.log('⚠️ CORS headers not found');
        this.warnings.push('CORS headers not found in response');
      }

    } catch (error) {
      console.log('❌ Backend connectivity test failed:', error.message);
      this.errors.push(`Backend connectivity failed: ${error.message}`);
      this.results.backendHealth = false;
    }

    console.log('');
  }

  async testSupabaseDirectConnectivity() {
    console.log('🔗 Test 2: Supabase Direct Connectivity');
    console.log('---------------------------------------');

    try {
      // Test Supabase REST API directly
      const supabaseHealthUrl = `${SUPABASE_URL}/rest/v1/`;
      const response = await fetch(supabaseHealthUrl, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      });

      if (response.ok || response.status === 404) { // 404 is expected for root endpoint
        console.log('✅ Supabase REST API: ACCESSIBLE');
        this.results.supabaseRestApi = true;
      } else {
        console.log(`❌ Supabase REST API: FAILED (${response.status})`);
        this.results.supabaseRestApi = false;
        this.errors.push(`Supabase REST API failed: ${response.status}`);
      }

      // Test Supabase Auth API
      const authUrl = `${SUPABASE_URL}/auth/v1/user`;
      const authResponse = await fetch(authUrl, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      });

      if (authResponse.status === 401) { // Expected for unauthenticated request
        console.log('✅ Supabase Auth API: ACCESSIBLE');
        this.results.supabaseAuthApi = true;
      } else {
        console.log(`⚠️ Supabase Auth API: Unexpected response (${authResponse.status})`);
        this.warnings.push(`Supabase Auth API unexpected response: ${authResponse.status}`);
      }

      // Test Supabase Storage API
      const storageUrl = `${SUPABASE_URL}/storage/v1/bucket`;
      const storageResponse = await fetch(storageUrl, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      });

      if (storageResponse.ok || storageResponse.status === 401) {
        console.log('✅ Supabase Storage API: ACCESSIBLE');
        this.results.supabaseStorageApi = true;
      } else {
        console.log(`❌ Supabase Storage API: FAILED (${storageResponse.status})`);
        this.results.supabaseStorageApi = false;
        this.errors.push(`Supabase Storage API failed: ${storageResponse.status}`);
      }

    } catch (error) {
      console.log('❌ Supabase direct connectivity test failed:', error.message);
      this.errors.push(`Supabase direct connectivity failed: ${error.message}`);
    }

    console.log('');
  }

  async testAuthenticationFlow() {
    console.log('🔐 Test 3: Authentication Flow');
    console.log('------------------------------');

    try {
      // Check if we can get user info from backend
      const userResponse = await fetch(`${BACKEND_URL}/api/auth/user`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (userResponse.status === 401) {
        console.log('✅ Authentication required: CORRECT (401 expected)');
        this.results.authenticationRequired = true;
      } else if (userResponse.ok) {
        console.log('✅ User authenticated: ACTIVE SESSION');
        this.results.userAuthenticated = true;
      } else {
        console.log(`⚠️ Authentication endpoint: Unexpected response (${userResponse.status})`);
        this.warnings.push(`Authentication endpoint unexpected response: ${userResponse.status}`);
      }

    } catch (error) {
      console.log('❌ Authentication flow test failed:', error.message);
      this.errors.push(`Authentication flow test failed: ${error.message}`);
    }

    console.log('');
  }

  async testStorageBucketAccess() {
    console.log('🗄️ Test 4: Storage Bucket Access');
    console.log('--------------------------------');

    try {
      // Test design-analysis-images bucket access
      const bucketUrl = `${SUPABASE_URL}/storage/v1/bucket/design-analysis-images`;
      const bucketResponse = await fetch(bucketUrl, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      });

      if (bucketResponse.ok) {
        console.log('✅ design-analysis-images bucket: ACCESSIBLE');
        this.results.designAnalysisBucket = true;
      } else if (bucketResponse.status === 401) {
        console.log('⚠️ design-analysis-images bucket: REQUIRES AUTHENTICATION');
        this.results.designAnalysisBucket = 'auth_required';
        this.warnings.push('design-analysis-images bucket requires authentication');
      } else {
        console.log(`❌ design-analysis-images bucket: FAILED (${bucketResponse.status})`);
        this.results.designAnalysisBucket = false;
        this.errors.push(`design-analysis-images bucket failed: ${bucketResponse.status}`);
      }

      // Test bucket listing
      const listUrl = `${SUPABASE_URL}/storage/v1/object/list/design-analysis-images`;
      const listResponse = await fetch(listUrl, {
        method: 'POST',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          limit: 1,
          offset: 0
        })
      });

      if (listResponse.ok) {
        console.log('✅ Bucket listing: ACCESSIBLE');
        this.results.bucketListing = true;
      } else {
        console.log(`⚠️ Bucket listing: REQUIRES AUTHENTICATION (${listResponse.status})`);
        this.results.bucketListing = 'auth_required';
      }

    } catch (error) {
      console.log('❌ Storage bucket access test failed:', error.message);
      this.errors.push(`Storage bucket access test failed: ${error.message}`);
    }

    console.log('');
  }

  async testImageRetrievalMethods() {
    console.log('🖼️ Test 5: Image Retrieval Methods');
    console.log('----------------------------------');

    try {
      // Test backend image retrieval endpoint
      const imageTestUrl = `${BACKEND_URL}/api/design-analysis/image-test`;
      const imageTestResponse = await fetch(imageTestUrl);

      if (imageTestResponse.ok) {
        console.log('✅ Backend image retrieval endpoint: AVAILABLE');
        this.results.backendImageRetrieval = true;
      } else if (imageTestResponse.status === 404) {
        console.log('⚠️ Backend image retrieval endpoint: NOT IMPLEMENTED');
        this.results.backendImageRetrieval = 'not_implemented';
        this.warnings.push('Backend image retrieval endpoint not implemented');
      } else {
        console.log(`❌ Backend image retrieval endpoint: FAILED (${imageTestResponse.status})`);
        this.results.backendImageRetrieval = false;
      }

      // Test public URL generation
      const testImagePath = 'test/sample.png';
      const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/design-analysis-images/${testImagePath}`;
      
      try {
        const publicResponse = await fetch(publicUrl, { method: 'HEAD' });
        if (publicResponse.ok) {
          console.log('✅ Public URL access: WORKING');
          this.results.publicUrlAccess = true;
        } else {
          console.log(`⚠️ Public URL access: FAILED (${publicResponse.status}) - Expected for private bucket`);
          this.results.publicUrlAccess = false;
        }
      } catch (error) {
        console.log('⚠️ Public URL access: FAILED - Expected for private bucket');
        this.results.publicUrlAccess = false;
      }

    } catch (error) {
      console.log('❌ Image retrieval methods test failed:', error.message);
      this.errors.push(`Image retrieval methods test failed: ${error.message}`);
    }

    console.log('');
  }

  async testMoodBoardImageHandling() {
    console.log('🎨 Test 6: MoodBoard Image Handling');
    console.log('-----------------------------------');

    try {
      // Test moodboard list endpoint
      const moodboardListUrl = `${BACKEND_URL}/api/moodboard/list?page=1&limit=1`;
      const moodboardResponse = await fetch(moodboardListUrl);

      if (moodboardResponse.status === 401) {
        console.log('✅ MoodBoard endpoint: REQUIRES AUTHENTICATION (Correct)');
        this.results.moodboardEndpoint = 'auth_required';
      } else if (moodboardResponse.ok) {
        console.log('✅ MoodBoard endpoint: ACCESSIBLE');
        this.results.moodboardEndpoint = true;
        
        const moodboardData = await moodboardResponse.json();
        console.log(`📊 MoodBoards found: ${moodboardData.data?.length || 0}`);
        
        if (moodboardData.data && moodboardData.data.length > 0) {
          const firstMoodboard = moodboardData.data[0];
          if (firstMoodboard.tldraw_data) {
            console.log('✅ MoodBoard tldraw_data: PRESENT');
            this.results.moodboardTldrawData = true;
            
            // Analyze tldraw data for images
            const tldrawData = firstMoodboard.tldraw_data;
            let imageCount = 0;
            if (tldrawData.store) {
              for (const [shapeId, shape] of Object.entries(tldrawData.store)) {
                if (shape.type === 'image' && shape.props?.src) {
                  imageCount++;
                }
              }
            }
            console.log(`📊 Images in MoodBoard: ${imageCount}`);
            this.results.moodboardImageCount = imageCount;
          } else {
            console.log('⚠️ MoodBoard tldraw_data: MISSING');
            this.results.moodboardTldrawData = false;
          }
        }
      } else {
        console.log(`❌ MoodBoard endpoint: FAILED (${moodboardResponse.status})`);
        this.results.moodboardEndpoint = false;
      }

    } catch (error) {
      console.log('❌ MoodBoard image handling test failed:', error.message);
      this.errors.push(`MoodBoard image handling test failed: ${error.message}`);
    }

    console.log('');
  }

  async testVisualComplexityAnalyzer() {
    console.log('🔬 Test 7: Visual Complexity Analyzer');
    console.log('-------------------------------------');

    try {
      // Test design analysis endpoint
      const analysisUrl = `${BACKEND_URL}/api/design-analysis/list?page=1&limit=1`;
      const analysisResponse = await fetch(analysisUrl);

      if (analysisResponse.status === 401) {
        console.log('✅ Design Analysis endpoint: REQUIRES AUTHENTICATION (Correct)');
        this.results.designAnalysisEndpoint = 'auth_required';
      } else if (analysisResponse.ok) {
        console.log('✅ Design Analysis endpoint: ACCESSIBLE');
        this.results.designAnalysisEndpoint = true;
        
        const analysisData = await analysisResponse.json();
        console.log(`📊 Design Analyses found: ${analysisData.data?.length || 0}`);
        
        if (analysisData.data && analysisData.data.length > 0) {
          const firstAnalysis = analysisData.data[0];
          if (firstAnalysis.file_url) {
            console.log('✅ Design Analysis file_url: PRESENT');
            console.log(`📁 File URL format: ${firstAnalysis.file_url.substring(0, 50)}...`);
            this.results.designAnalysisFileUrl = true;
          } else {
            console.log('⚠️ Design Analysis file_url: MISSING');
            this.results.designAnalysisFileUrl = false;
          }
        }
      } else {
        console.log(`❌ Design Analysis endpoint: FAILED (${analysisResponse.status})`);
        this.results.designAnalysisEndpoint = false;
      }

    } catch (error) {
      console.log('❌ Visual Complexity Analyzer test failed:', error.message);
      this.errors.push(`Visual Complexity Analyzer test failed: ${error.message}`);
    }

    console.log('');
  }

  generateFinalReport() {
    console.log('📋 FINAL DIAGNOSTIC REPORT');
    console.log('==========================');
    
    console.log('\n✅ SUCCESSFUL TESTS:');
    Object.entries(this.results).forEach(([test, result]) => {
      if (result === true) {
        console.log(`  ✅ ${test}`);
      }
    });
    
    console.log('\n⚠️ WARNINGS:');
    if (this.warnings.length === 0) {
      console.log('  No warnings found');
    } else {
      this.warnings.forEach(warning => console.log(`  ⚠️ ${warning}`));
    }
    
    console.log('\n❌ ERRORS:');
    if (this.errors.length === 0) {
      console.log('  No errors found');
    } else {
      this.errors.forEach(error => console.log(`  ❌ ${error}`));
    }
    
    console.log('\n🔧 RECOMMENDATIONS:');
    this.generateRecommendations();
    
    console.log('\n🏁 DIAGNOSTIC COMPLETE');
    console.log('======================');
  }

  generateRecommendations() {
    const recommendations = [];
    
    if (!this.results.backendHealth) {
      recommendations.push('Start the backend server on port 8001');
    }
    
    if (this.results.designAnalysisBucket === 'auth_required') {
      recommendations.push('Ensure proper authentication for Supabase Storage access');
    }
    
    if (!this.results.designAnalysisFileUrl) {
      recommendations.push('Check image upload and file_url generation in Visual Complexity Analyzer');
    }
    
    if (this.results.moodboardImageCount === 0) {
      recommendations.push('Test MoodBoard with actual images to verify image handling');
    }
    
    if (recommendations.length === 0) {
      console.log('  🎉 No specific recommendations - system appears healthy!');
      console.log('  💡 If images still appear as yellow squares, check:');
      console.log('     - Browser console for JavaScript errors');
      console.log('     - Network tab for failed image requests');
      console.log('     - Tldraw asset resolver configuration');
    } else {
      recommendations.forEach(rec => console.log(`  🔧 ${rec}`));
    }
  }
}

// Run the diagnostic
const diagnostic = new SupabaseConnectivityDiagnostic();
diagnostic.runFullDiagnostic();
