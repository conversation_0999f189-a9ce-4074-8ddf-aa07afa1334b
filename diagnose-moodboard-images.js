#!/usr/bin/env node

/**
 * Diagnóstico específico para problemas de imágenes en MoodBoard
 * Verifica el asset handling de tldraw y la resolución de URLs de imágenes
 */

console.log('🎨 EMMA STUDIO MOODBOARD IMAGE DIAGNOSTIC');
console.log('==========================================');
console.log('Diagnosing image display issues in MoodBoard...\n');

async function diagnoseMoodBoardImages() {
  try {
    // Step 1: Check backend connectivity
    console.log('📡 Step 1: Testing backend connectivity...');
    
    const healthResponse = await fetch('http://localhost:8001/api/health');
    if (!healthResponse.ok) {
      console.log('❌ Backend not accessible on port 8001');
      return;
    }
    console.log('✅ Backend is accessible');

    // Step 2: Check authentication
    console.log('\n🔐 Step 2: Checking authentication...');
    
    // Try to get auth token from localStorage (if running in browser context)
    let token = null;
    if (typeof window !== 'undefined' && window.localStorage) {
      token = localStorage.getItem('supabase.auth.token') || 
              localStorage.getItem('auth_token') ||
              sessionStorage.getItem('auth_token');
    }
    
    if (!token) {
      console.log('⚠️ No authentication token found. Testing with mock auth...');
      // For server-side testing, we'll proceed without auth for basic checks
    } else {
      console.log('✅ Authentication token found');
    }

    // Step 3: Get moodboards
    console.log('\n📋 Step 3: Fetching moodboards...');
    
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const moodboardsResponse = await fetch('http://localhost:8001/api/moodboard/list?page=1&limit=10', {
      headers
    });

    if (!moodboardsResponse.ok) {
      if (moodboardsResponse.status === 401) {
        console.log('⚠️ Authentication required. Please log in to the app first.');
        console.log('💡 To test: Open http://localhost:3002, log in, then run this script in browser console.');
        return;
      }
      console.log('❌ Failed to fetch moodboards:', moodboardsResponse.status);
      return;
    }

    const moodboardsData = await moodboardsResponse.json();
    const moodboards = moodboardsData.data || [];
    
    console.log(`✅ Found ${moodboards.length} moodboards`);

    if (moodboards.length === 0) {
      console.log('ℹ️ No moodboards found. Create a moodboard with images to test.');
      return;
    }

    // Step 4: Analyze moodboard data structure
    console.log('\n🔍 Step 4: Analyzing moodboard data structure...');
    
    for (const moodboard of moodboards.slice(0, 3)) { // Test first 3 moodboards
      console.log(`\n📊 Analyzing moodboard: "${moodboard.title}"`);
      
      if (!moodboard.tldraw_data) {
        console.log('⚠️ No tldraw_data found');
        continue;
      }

      console.log('✅ tldraw_data exists');

      // Analyze the tldraw_data structure
      const tldrawData = moodboard.tldraw_data;
      const dataSize = JSON.stringify(tldrawData).length;
      console.log(`📏 Data size: ${(dataSize / 1024).toFixed(2)} KB`);

      // Look for image shapes
      let imageCount = 0;
      let totalImageSize = 0;
      const imageFormats = new Set();
      const imageIssues = [];

      if (tldrawData.store) {
        for (const [shapeId, shape] of Object.entries(tldrawData.store)) {
          if (shape.type === 'image' && shape.props?.src) {
            imageCount++;
            const src = shape.props.src;
            const imageSize = src.length;
            totalImageSize += imageSize;

            // Detect image format and potential issues
            if (src.startsWith('data:image/')) {
              const format = src.split(';')[0].split('/')[1];
              imageFormats.add(format.toUpperCase());
              
              // Check if data URL is complete
              if (!src.includes('base64,')) {
                imageIssues.push(`Shape ${shapeId}: Invalid data URL format`);
              } else {
                const base64Data = src.split('base64,')[1];
                if (!base64Data || base64Data.length < 100) {
                  imageIssues.push(`Shape ${shapeId}: Data URL appears truncated or empty`);
                }
              }
            } else if (src.startsWith('http')) {
              imageFormats.add('HTTP_URL');
              // Test if HTTP URL is accessible
              try {
                const testResponse = await fetch(src, { method: 'HEAD' });
                if (!testResponse.ok) {
                  imageIssues.push(`Shape ${shapeId}: HTTP URL not accessible (${testResponse.status})`);
                }
              } catch (error) {
                imageIssues.push(`Shape ${shapeId}: HTTP URL failed to load (${error.message})`);
              }
            } else if (src.startsWith('blob:')) {
              imageFormats.add('BLOB_URL');
              imageIssues.push(`Shape ${shapeId}: Blob URL detected - may not persist across sessions`);
            } else {
              imageFormats.add('UNKNOWN');
              imageIssues.push(`Shape ${shapeId}: Unknown image source format: ${src.substring(0, 50)}...`);
            }
          }
        }
      }

      console.log(`📊 Summary:`);
      console.log(`  - Total images: ${imageCount}`);
      console.log(`  - Total image data: ${(totalImageSize / 1024).toFixed(2)} KB`);
      console.log(`  - Image formats: ${Array.from(imageFormats).join(', ') || 'None'}`);

      if (imageIssues.length > 0) {
        console.log(`⚠️ Potential issues found:`);
        imageIssues.forEach(issue => console.log(`    - ${issue}`));
      } else if (imageCount > 0) {
        console.log('✅ No obvious image issues detected');
      }

      if (imageCount === 0) {
        console.log('ℹ️ No images found in this moodboard');
      }
    }

    // Step 5: Check tldraw asset handling
    console.log('\n🎨 Step 5: Checking tldraw asset handling...');
    
    console.log('💡 Common causes of yellow squares in tldraw:');
    console.log('  1. Data URLs that are corrupted or incomplete');
    console.log('  2. HTTP URLs that are not accessible (CORS issues)');
    console.log('  3. Blob URLs that have been revoked');
    console.log('  4. Missing asset resolver configuration');
    console.log('  5. Browser security restrictions');

    // Step 6: Recommendations
    console.log('\n🔧 Step 6: Recommendations...');
    
    console.log('✅ Recommended fixes:');
    console.log('  1. Ensure images are properly converted to data URLs when added');
    console.log('  2. Implement proper asset resolver in tldraw configuration');
    console.log('  3. Add CORS headers for external image URLs');
    console.log('  4. Consider storing images in Supabase Storage with signed URLs');
    console.log('  5. Add error handling for failed image loads');

    console.log('\n🏁 DIAGNOSIS COMPLETE');
    console.log('====================');

  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

// Run the diagnostic
if (typeof window !== 'undefined') {
  // Running in browser
  diagnoseMoodBoardImages();
} else {
  // Running in Node.js with ES modules
  import('node-fetch').then(({ default: fetch }) => {
    global.fetch = fetch;
    diagnoseMoodBoardImages();
  }).catch(() => {
    // If node-fetch is not available, use built-in fetch (Node 18+)
    diagnoseMoodBoardImages();
  });
}
