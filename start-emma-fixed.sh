#!/bin/bash

# Emma Studio - Script de inicio con puertos fijos
# Frontend: 3002, Backend: 8001

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}Emma Studio - Inicio con Puertos Fijos${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
echo -e "${GREEN}Frontend: http://localhost:3002${NC}"
echo -e "${GREEN}Backend:  http://localhost:8001${NC}"
echo -e "${GREEN}API Docs: http://localhost:8001/docs${NC}"
echo ""

# Función para limpiar procesos al salir
cleanup() {
    echo ""
    echo -e "${YELLOW}Deteniendo servicios...${NC}"
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo -e "${GREEN}✅ Backend detenido${NC}"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo -e "${GREEN}✅ Frontend detenido${NC}"
    fi
    echo -e "${BLUE}Emma Studio detenido correctamente${NC}"
    exit 0
}

# Configurar trap para cleanup
trap cleanup SIGINT SIGTERM

# Función AGRESIVA para limpiar puertos
force_kill_port() {
    local port=$1
    local service=$2
    echo -e "${YELLOW}🔪 FORZANDO limpieza del puerto $port ($service)...${NC}"

    # Método 1: lsof
    lsof -ti :$port | xargs kill -9 2>/dev/null || true

    # Método 2: netstat + kill
    netstat -tulpn 2>/dev/null | grep ":$port " | awk '{print $7}' | cut -d'/' -f1 | xargs kill -9 2>/dev/null || true

    # Método 3: fuser (si está disponible)
    fuser -k $port/tcp 2>/dev/null || true

    sleep 2

    # Verificar que el puerto esté libre
    if lsof -i :$port >/dev/null 2>&1; then
        echo -e "${RED}⚠️  Puerto $port AÚN está en uso, intentando método más agresivo...${NC}"
        pkill -f ":$port" 2>/dev/null || true
        sleep 2
    fi

    echo -e "${GREEN}✅ Puerto $port liberado${NC}"
}

# FORZAR limpieza de puertos - SIN PIEDAD
echo -e "${YELLOW}🔍 FORZANDO limpieza de puertos...${NC}"
force_kill_port 3002 "Frontend"
force_kill_port 8001 "Backend"

# Configurar variables de entorno FORZADAS
export PORT=8001
export HOST=0.0.0.0
export UVICORN_PORT=8001
export UVICORN_HOST=0.0.0.0
export VITE_PORT=3002
export VITE_STRICT_PORT=true

# Iniciar Backend
echo ""
echo -e "${BLUE}🐍 INICIANDO BACKEND (Puerto 8001)...${NC}"
echo -e "${BLUE}=====================================${NC}"

cd backend

# Verificar que el archivo main.py existe
if [ ! -f "app/main.py" ]; then
    echo -e "${RED}❌ app/main.py no encontrado${NC}"
    exit 1
fi

# Iniciar backend en background
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001 > backend.log 2>&1 &
BACKEND_PID=$!

echo -e "${GREEN}🚀 Backend iniciado (PID: $BACKEND_PID)${NC}"
echo -e "${GREEN}📝 Logs en: $(pwd)/backend.log${NC}"

# Esperar que el backend esté listo
echo -e "${YELLOW}⏳ Esperando que el backend esté listo...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:8001/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend funcionando correctamente${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ Backend falló al iniciar. Revisando logs...${NC}"
        tail -20 backend.log
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    sleep 1
done

# Iniciar Frontend
echo ""
echo -e "${BLUE}⚛️  INICIANDO FRONTEND (Puerto 3002)...${NC}"
echo -e "${BLUE}======================================${NC}"

cd ../client

# Verificar que package.json existe
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ package.json no encontrado${NC}"
    exit 1
fi

# Iniciar frontend FORZANDO puerto 3002
npm run dev-clean > frontend.log 2>&1 &
FRONTEND_PID=$!

echo -e "${GREEN}🚀 Frontend iniciado (PID: $FRONTEND_PID)${NC}"
echo -e "${GREEN}📝 Logs en: $(pwd)/frontend.log${NC}"

# Esperar que el frontend esté listo
echo -e "${YELLOW}⏳ Esperando que el frontend esté listo...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:3002 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Frontend funcionando correctamente${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ Frontend falló al iniciar. Revisando logs...${NC}"
        tail -20 frontend.log
        cleanup
        exit 1
    fi
    sleep 1
done

# Todo listo
echo ""
echo -e "${GREEN}🎉 EMMA STUDIO INICIADO CORRECTAMENTE${NC}"
echo -e "${GREEN}====================================${NC}"
echo ""
echo -e "${GREEN}🌐 Frontend: http://localhost:3002${NC}"
echo -e "${GREEN}🔧 Backend:  http://localhost:8001${NC}"
echo -e "${GREEN}📚 API Docs: http://localhost:8001/docs${NC}"
echo ""
echo -e "${YELLOW}Presiona Ctrl+C para detener todos los servicios${NC}"

# Mantener el script corriendo
wait
