#!/bin/bash

echo "🧪 Testing Buyer Persona API with curl"
echo "======================================"

# Test data that matches what the frontend sends
curl -X POST http://127.0.0.1:8001/api/generate-buyer-personas \
  -H "Content-Type: application/json" \
  -H "X-Request-ID: test_curl_$(date +%s)" \
  -d '{
    "product_description": "Una plataforma de marketing digital con IA para empresas pequeñas y medianas",
    "num_personas": 2,
    "industry": "Tecnología",
    "target_market": "Pequeñas empresas",
    "business_goals": "Aumentar ventas y mejorar engagement",
    "competitors": "HubSpot, Mailchimp",
    "target_countries": ["España", "México"],
    "request_timestamp": '$(date +%s000)',
    "request_id": "test_curl_'$(date +%s)'",
    "unique_seed": "test_seed_'$(date +%s)'",
    "generation_context": "Test from curl"
  }' \
  --max-time 60 \
  --verbose
