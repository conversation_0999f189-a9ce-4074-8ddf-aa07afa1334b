#!/usr/bin/env node

/**
 * Complete Image Solution Test
 * Tests the entire image pipeline from upload to display
 */

console.log('🎯 COMPLETE IMAGE SOLUTION TEST');
console.log('===============================');
console.log('Testing the complete image pipeline for both Visual Complexity Analyzer and MoodBoard');
console.log('');

const BACKEND_URL = "http://localhost:8001";

class CompleteImageSolutionTest {
  constructor() {
    this.results = {};
    this.errors = [];
    this.warnings = [];
  }

  async runCompleteTest() {
    console.log('🚀 Starting complete image solution test...\n');

    try {
      // Test 1: Backend endpoints
      await this.testBackendEndpoints();
      
      // Test 2: Image retrieval endpoints
      await this.testImageRetrievalEndpoints();
      
      // Test 3: Visual Complexity Analyzer
      await this.testVisualComplexityAnalyzer();
      
      // Test 4: MoodBoard functionality
      await this.testMoodBoardFunctionality();
      
      // Test 5: Authentication flow
      await this.testAuthenticationFlow();
      
      // Generate final report
      this.generateFinalReport();
      
    } catch (error) {
      console.error('❌ Complete test failed:', error);
      this.errors.push(`Complete test failed: ${error.message}`);
    }
  }

  async testBackendEndpoints() {
    console.log('🔧 Test 1: Backend Endpoints');
    console.log('----------------------------');

    try {
      // Test health endpoint
      const healthResponse = await fetch(`${BACKEND_URL}/api/health`);
      if (healthResponse.ok) {
        console.log('✅ Backend health: OPERATIONAL');
        this.results.backendHealth = true;
      } else {
        console.log('❌ Backend health: FAILED');
        this.results.backendHealth = false;
        this.errors.push(`Backend health failed: ${healthResponse.status}`);
      }

      // Test new image endpoints (should require auth)
      const imageTestResponse = await fetch(`${BACKEND_URL}/api/test-image-access`);
      if (imageTestResponse.status === 401) {
        console.log('✅ Image access endpoint: REQUIRES AUTH (Correct)');
        this.results.imageEndpointAuth = true;
      } else {
        console.log(`⚠️ Image access endpoint: Unexpected response (${imageTestResponse.status})`);
        this.warnings.push(`Image access endpoint unexpected response: ${imageTestResponse.status}`);
      }

      // Test image retrieval endpoint structure
      const imageRetrievalResponse = await fetch(`${BACKEND_URL}/api/image/test-path`);
      if (imageRetrievalResponse.status === 401) {
        console.log('✅ Image retrieval endpoint: REQUIRES AUTH (Correct)');
        this.results.imageRetrievalAuth = true;
      } else {
        console.log(`⚠️ Image retrieval endpoint: Unexpected response (${imageRetrievalResponse.status})`);
        this.warnings.push(`Image retrieval endpoint unexpected response: ${imageRetrievalResponse.status}`);
      }

    } catch (error) {
      console.log('❌ Backend endpoints test failed:', error.message);
      this.errors.push(`Backend endpoints test failed: ${error.message}`);
    }

    console.log('');
  }

  async testImageRetrievalEndpoints() {
    console.log('🖼️ Test 2: Image Retrieval Endpoints');
    console.log('------------------------------------');

    try {
      // Test the new image retrieval endpoints
      const endpoints = [
        '/api/image/test-path',
        '/api/image-url/test-path',
        '/api/test-image-access'
      ];

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(`${BACKEND_URL}${endpoint}`);
          
          if (response.status === 401) {
            console.log(`✅ ${endpoint}: REQUIRES AUTH (Correct)`);
          } else if (response.status === 404) {
            console.log(`⚠️ ${endpoint}: NOT FOUND (May need registration)`);
            this.warnings.push(`Endpoint ${endpoint} returned 404`);
          } else {
            console.log(`⚠️ ${endpoint}: Unexpected response (${response.status})`);
            this.warnings.push(`Endpoint ${endpoint} unexpected response: ${response.status}`);
          }
        } catch (error) {
          console.log(`❌ ${endpoint}: ERROR (${error.message})`);
          this.errors.push(`Endpoint ${endpoint} error: ${error.message}`);
        }
      }

      this.results.imageRetrievalEndpoints = true;

    } catch (error) {
      console.log('❌ Image retrieval endpoints test failed:', error.message);
      this.errors.push(`Image retrieval endpoints test failed: ${error.message}`);
    }

    console.log('');
  }

  async testVisualComplexityAnalyzer() {
    console.log('🔬 Test 3: Visual Complexity Analyzer');
    console.log('-------------------------------------');

    try {
      // Test design analysis endpoints
      const analysisResponse = await fetch(`${BACKEND_URL}/api/analyses`);
      
      if (analysisResponse.status === 401) {
        console.log('✅ Design analysis endpoint: REQUIRES AUTH (Correct)');
        this.results.designAnalysisAuth = true;
      } else if (analysisResponse.ok) {
        console.log('✅ Design analysis endpoint: ACCESSIBLE');
        this.results.designAnalysisAccessible = true;
        
        const analysisData = await analysisResponse.json();
        console.log(`📊 Found ${analysisData.data?.length || 0} design analyses`);
        
        if (analysisData.data && analysisData.data.length > 0) {
          const firstAnalysis = analysisData.data[0];
          if (firstAnalysis.file_url) {
            console.log('✅ Design analysis has file_url: PRESENT');
            console.log(`📁 File URL format: ${firstAnalysis.file_url.substring(0, 50)}...`);
            this.results.designAnalysisFileUrl = true;
          } else {
            console.log('⚠️ Design analysis file_url: MISSING');
            this.results.designAnalysisFileUrl = false;
          }
        }
      } else {
        console.log(`❌ Design analysis endpoint: FAILED (${analysisResponse.status})`);
        this.results.designAnalysisAuth = false;
      }

    } catch (error) {
      console.log('❌ Visual Complexity Analyzer test failed:', error.message);
      this.errors.push(`Visual Complexity Analyzer test failed: ${error.message}`);
    }

    console.log('');
  }

  async testMoodBoardFunctionality() {
    console.log('🎨 Test 4: MoodBoard Functionality');
    console.log('----------------------------------');

    try {
      // Test moodboard endpoints
      const moodboardResponse = await fetch(`${BACKEND_URL}/api/moodboard/list?page=1&limit=1`);
      
      if (moodboardResponse.status === 401) {
        console.log('✅ MoodBoard endpoint: REQUIRES AUTH (Correct)');
        this.results.moodboardAuth = true;
      } else if (moodboardResponse.ok) {
        console.log('✅ MoodBoard endpoint: ACCESSIBLE');
        this.results.moodboardAccessible = true;
        
        const moodboardData = await moodboardResponse.json();
        console.log(`📊 Found ${moodboardData.data?.length || 0} moodboards`);
        
        if (moodboardData.data && moodboardData.data.length > 0) {
          const firstMoodboard = moodboardData.data[0];
          if (firstMoodboard.tldraw_data) {
            console.log('✅ MoodBoard tldraw_data: PRESENT');
            this.results.moodboardTldrawData = true;
            
            // Analyze tldraw data for images
            const tldrawData = firstMoodboard.tldraw_data;
            let imageCount = 0;
            if (tldrawData.store) {
              for (const [shapeId, shape] of Object.entries(tldrawData.store)) {
                if (shape.type === 'image' && shape.props?.src) {
                  imageCount++;
                }
              }
            }
            console.log(`📊 Images in MoodBoard: ${imageCount}`);
            this.results.moodboardImageCount = imageCount;
          } else {
            console.log('⚠️ MoodBoard tldraw_data: MISSING');
            this.results.moodboardTldrawData = false;
          }
        }
      } else {
        console.log(`❌ MoodBoard endpoint: FAILED (${moodboardResponse.status})`);
        this.results.moodboardAuth = false;
      }

    } catch (error) {
      console.log('❌ MoodBoard functionality test failed:', error.message);
      this.errors.push(`MoodBoard functionality test failed: ${error.message}`);
    }

    console.log('');
  }

  async testAuthenticationFlow() {
    console.log('🔐 Test 5: Authentication Flow');
    console.log('------------------------------');

    try {
      // Test auth endpoints
      const authResponse = await fetch(`${BACKEND_URL}/api/auth/user`);
      
      if (authResponse.status === 401) {
        console.log('✅ Auth endpoint: REQUIRES LOGIN (Correct)');
        this.results.authRequired = true;
      } else if (authResponse.ok) {
        console.log('✅ Auth endpoint: USER LOGGED IN');
        this.results.userLoggedIn = true;
      } else {
        console.log(`⚠️ Auth endpoint: Unexpected response (${authResponse.status})`);
        this.warnings.push(`Auth endpoint unexpected response: ${authResponse.status}`);
      }

    } catch (error) {
      console.log('❌ Authentication flow test failed:', error.message);
      this.errors.push(`Authentication flow test failed: ${error.message}`);
    }

    console.log('');
  }

  generateFinalReport() {
    console.log('📋 COMPLETE IMAGE SOLUTION REPORT');
    console.log('=================================');
    
    console.log('\n✅ SUCCESSFUL COMPONENTS:');
    Object.entries(this.results).forEach(([test, result]) => {
      if (result === true) {
        console.log(`  ✅ ${test}`);
      }
    });
    
    console.log('\n⚠️ WARNINGS:');
    if (this.warnings.length === 0) {
      console.log('  No warnings found');
    } else {
      this.warnings.forEach(warning => console.log(`  ⚠️ ${warning}`));
    }
    
    console.log('\n❌ ERRORS:');
    if (this.errors.length === 0) {
      console.log('  No errors found');
    } else {
      this.errors.forEach(error => console.log(`  ❌ ${error}`));
    }
    
    console.log('\n🎯 SOLUTION STATUS:');
    this.generateSolutionStatus();
    
    console.log('\n🔧 NEXT STEPS:');
    this.generateNextSteps();
    
    console.log('\n🏁 COMPLETE TEST FINISHED');
    console.log('=========================');
  }

  generateSolutionStatus() {
    const hasBackend = this.results.backendHealth;
    const hasImageEndpoints = this.results.imageEndpointAuth || this.results.imageRetrievalAuth;
    const hasAuth = this.results.authRequired || this.results.userLoggedIn;
    
    if (hasBackend && hasImageEndpoints && hasAuth) {
      console.log('🎉 SOLUTION STATUS: READY FOR TESTING');
      console.log('   ✅ Backend operational');
      console.log('   ✅ Image endpoints available');
      console.log('   ✅ Authentication working');
      console.log('   🎯 Ready for frontend integration');
    } else {
      console.log('⚠️ SOLUTION STATUS: NEEDS ATTENTION');
      if (!hasBackend) console.log('   ❌ Backend not operational');
      if (!hasImageEndpoints) console.log('   ❌ Image endpoints not available');
      if (!hasAuth) console.log('   ❌ Authentication not working');
    }
  }

  generateNextSteps() {
    const nextSteps = [];
    
    if (!this.results.backendHealth) {
      nextSteps.push('Start the backend server on port 8001');
    }
    
    if (this.warnings.some(w => w.includes('404'))) {
      nextSteps.push('Verify image retrieval endpoints are properly registered');
    }
    
    if (this.results.moodboardImageCount === 0) {
      nextSteps.push('Test MoodBoard with actual images using new image service');
    }
    
    if (!this.results.designAnalysisFileUrl) {
      nextSteps.push('Test Visual Complexity Analyzer with new API key');
    }
    
    if (nextSteps.length === 0) {
      console.log('🎯 READY FOR FRONTEND TESTING:');
      console.log('   1. Open http://localhost:3002');
      console.log('   2. Log in to the application');
      console.log('   3. Test Visual Complexity Analyzer');
      console.log('   4. Test MoodBoard with images');
      console.log('   5. Verify images display correctly (no yellow squares)');
    } else {
      nextSteps.forEach(step => console.log(`   🔧 ${step}`));
    }
  }
}

// Run the complete test
const test = new CompleteImageSolutionTest();
test.runCompleteTest();
