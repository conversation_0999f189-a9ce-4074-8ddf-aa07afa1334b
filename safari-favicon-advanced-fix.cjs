#!/usr/bin/env node

/**
 * Safari Favicon Advanced Fix
 * Basado en problemas conocidos de Safari con favicons
 */

const fs = require('fs');
const path = require('path');

console.log('🍎 SAFARI FAVICON - DIAGNÓSTICO Y SOLUCIÓN AVANZADA');
console.log('=====================================================\n');

// Verificar si estamos en desarrollo local
const isLocalDev = process.cwd().includes('emma-studio');
const baseUrl = isLocalDev ? 'http://localhost:3000' : 'https://tu-dominio.com';

console.log(`🌐 URL base detectada: ${baseUrl}\n`);

// Problemas conocidos de Safari con favicons
const safariKnownIssues = [
  {
    issue: 'Cache agresivo de favicon',
    solution: 'Eliminar ~/Library/Safari/Favicon Cache',
    severity: 'CRÍTICO'
  },
  {
    issue: 'No reconoce cambios en favicon.ico',
    solution: 'Usar parámetros de cache-busting (?v=X)',
    severity: 'ALTO'
  },
  {
    issue: 'Prefiere archivos en root sobre public/',
    solution: 'Colocar favicon.ico en directorio raíz',
    severity: 'MEDIO'
  },
  {
    issue: 'Problemas con ICO mal formateados',
    solution: 'Usar ICO real, no PNG renombrado',
    severity: 'ALTO'
  },
  {
    issue: 'Delay en mostrar favicons nuevos',
    solution: 'Esperar 5-10 minutos después de limpiar cache',
    severity: 'MEDIO'
  }
];

console.log('🔍 PROBLEMAS CONOCIDOS DE SAFARI CON FAVICONS:\n');
safariKnownIssues.forEach((item, index) => {
  const severityIcon = item.severity === 'CRÍTICO' ? '🚨' : 
                      item.severity === 'ALTO' ? '⚠️' : '💡';
  console.log(`${severityIcon} ${index + 1}. ${item.issue}`);
  console.log(`   Solución: ${item.solution}\n`);
});

// Verificar archivos críticos
const criticalFiles = [
  { file: 'favicon.ico', location: 'root', desc: 'Favicon principal (root)' },
  { file: 'client/public/favicon-proper.ico', location: 'public', desc: 'Favicon ICO real' },
  { file: 'apple-touch-icon.png', location: 'root', desc: 'Apple Touch Icon (root)' },
];

console.log('📁 VERIFICANDO ARCHIVOS CRÍTICOS:\n');

let hasRootFavicon = false;
let hasProperIco = false;

criticalFiles.forEach(({ file, location, desc }) => {
  const filePath = path.join(__dirname, file);
  
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    const sizeKB = (stats.size / 1024).toFixed(2);
    console.log(`✅ ${desc}: ${file} (${sizeKB} KB)`);
    
    if (file === 'favicon.ico') hasRootFavicon = true;
    if (file.includes('favicon-proper.ico')) hasProperIco = true;
    
    // Verificar si es ICO real o PNG disfrazado
    if (file.includes('.ico')) {
      const buffer = fs.readFileSync(filePath);
      const isRealIco = buffer[0] === 0x00 && buffer[1] === 0x00;
      const isPng = buffer[0] === 0x89 && buffer[1] === 0x50;
      
      if (isRealIco) {
        console.log(`   ✅ Es un archivo ICO real`);
      } else if (isPng) {
        console.log(`   ❌ Es un PNG disfrazado como ICO (Safari lo rechaza)`);
      } else {
        console.log(`   ⚠️  Formato desconocido`);
      }
    }
  } else {
    console.log(`❌ ${desc}: ${file} - NO ENCONTRADO`);
  }
});

// Generar soluciones específicas
console.log('\n' + '='.repeat(60));
console.log('🔧 SOLUCIONES ESPECÍFICAS PARA TU CASO:\n');

if (!hasRootFavicon) {
  console.log('❌ PROBLEMA: No hay favicon.ico en el directorio raíz');
  console.log('   SOLUCIÓN: cp client/public/favicon-proper.ico favicon.ico\n');
}

if (!hasProperIco) {
  console.log('❌ PROBLEMA: No se encontró favicon ICO real');
  console.log('   SOLUCIÓN: Crear ICO real con herramientas apropiadas\n');
}

// Comandos específicos para Safari
console.log('🍎 COMANDOS ESPECÍFICOS PARA SAFARI:\n');

console.log('1. 🗑️  LIMPIAR CACHE DE FAVICON (MÉTODO NUCLEAR):');
console.log('   ./safari-favicon-nuclear-fix.sh\n');

console.log('2. 🔄 LIMPIAR CACHE MANUALMENTE:');
console.log('   - Cierra Safari completamente');
console.log('   - Abre Terminal y ejecuta:');
console.log('     rm -rf ~/Library/Safari/Favicon\\ Cache');
console.log('     rm ~/Library/Safari/WebpageIcons.db');
console.log('   - Abre Safari y visita tu sitio\n');

console.log('3. 🧪 PROBAR EN SAFARI PRIVADO:');
console.log('   - Cmd+Shift+N para nueva ventana privada');
console.log('   - Visita tu sitio en modo privado');
console.log('   - Si funciona en privado, es problema de cache\n');

console.log('4. 🔗 FORZAR DESCARGA DE FAVICON:');
console.log(`   - Visita directamente: ${baseUrl}/favicon.ico`);
console.log(`   - Visita directamente: ${baseUrl}/favicon-proper.ico`);
console.log('   - Debería descargar el archivo\n');

console.log('5. 📑 AGREGAR A FAVORITOS:');
console.log('   - Agrega tu sitio a favoritos de Safari');
console.log('   - Esto fuerza a Safari a descargar el favicon\n');

// Test de conectividad
console.log('🌐 TEST DE CONECTIVIDAD:\n');

if (isLocalDev) {
  console.log('📍 DESARROLLO LOCAL DETECTADO');
  console.log('   - Asegúrate de que el servidor esté ejecutándose');
  console.log('   - Prueba: curl -I http://localhost:3000/favicon.ico');
  console.log('   - Debería devolver 200 OK\n');
} else {
  console.log('🌍 PRODUCCIÓN DETECTADA');
  console.log('   - Verifica que los archivos estén desplegados');
  console.log('   - Prueba la URL directa del favicon\n');
}

// Timing específico de Safari
console.log('⏰ TIMING ESPECÍFICO DE SAFARI:\n');
console.log('- Safari puede tardar 5-10 MINUTOS en mostrar favicons nuevos');
console.log('- Después de limpiar cache, espera al menos 5 minutos');
console.log('- Safari tiene bugs conocidos con favicons en macOS');
console.log('- En casos extremos, reiniciar macOS puede ser necesario\n');

console.log('🎯 ORDEN DE EJECUCIÓN RECOMENDADO:\n');
console.log('1. Ejecutar: ./safari-favicon-nuclear-fix.sh');
console.log('2. Esperar 2-3 minutos');
console.log('3. Abrir Safari y visitar tu sitio');
console.log('4. Hard refresh con Cmd+Shift+R');
console.log('5. Agregar sitio a favoritos');
console.log('6. Esperar 5-10 minutos');
console.log('7. Si no funciona, reiniciar macOS\n');

console.log('💡 NOTA: Safari tiene problemas conocidos con favicons');
console.log('   que no están documentados oficialmente por Apple.');
console.log('   Estos métodos están basados en la experiencia de la comunidad.');
