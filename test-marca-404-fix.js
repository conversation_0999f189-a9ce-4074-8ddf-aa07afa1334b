/**
 * Test script to debug and fix the 404 error in brand creation
 */

console.log('🔧 Testing Marca 404 Fix...');

async function testMarcaCreation() {
  try {
    console.log('\n🔍 Step 1: Testing Supabase Configuration...');
    
    // Import the supabase client
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      console.log('💡 Please sign in to test marca creation');
      return false;
    }
    
    console.log('✅ User authenticated:', { id: user.id, email: user.email });
    
    console.log('\n🗄️ Step 2: Testing Database Table Access...');
    
    // Test if marcas table exists and is accessible
    const { data: testQuery, error: queryError } = await supabase
      .from('marcas')
      .select('id, brand_name, created_at')
      .limit(1);
    
    if (queryError) {
      console.error('❌ Marcas table query failed:', {
        message: queryError.message,
        code: queryError.code,
        details: queryError.details,
        hint: queryError.hint
      });
      
      // Check if it's a schema issue
      if (queryError.message.includes('PGRST106') || queryError.message.includes('schema')) {
        console.error('🚨 Schema configuration issue detected!');
        return false;
      }
      
      // Check if it's a table not found issue
      if (queryError.message.includes('relation') && queryError.message.includes('does not exist')) {
        console.error('🚨 Marcas table does not exist!');
        return false;
      }
      
      return false;
    }
    
    console.log('✅ Marcas table accessible:', {
      recordsFound: testQuery?.length || 0,
      sampleRecord: testQuery?.[0] || 'No existing records'
    });
    
    console.log('\n🧪 Step 3: Testing Brand Creation...');
    
    // Test brand creation with minimal data
    const testBrandData = {
      brand_name: `Test Brand ${Date.now()}`,
      industry: 'Tecnología',
      target_audience: 'Desarrolladores',
      tone: 'Profesional',
      description: 'Una marca de prueba para verificar la funcionalidad',
      unique_value: 'Soluciones innovadoras',
      primary_color: '#3B82F6',
      secondary_color: '#8B5CF6',
      personality: ['Innovador', 'Confiable'],
      examples: 'Marketing profesional',
      user_id: user.id,
      status: 'draft',
      campaigns_count: 0,
      assets_count: 0
    };
    
    console.log('📤 Attempting to create brand with data:', {
      brand_name: testBrandData.brand_name,
      industry: testBrandData.industry,
      user_id: testBrandData.user_id
    });
    
    const { data: createdBrand, error: createError } = await supabase
      .from('marcas')
      .insert([testBrandData])
      .select()
      .single();
    
    if (createError) {
      console.error('❌ Brand creation failed:', {
        message: createError.message,
        code: createError.code,
        details: createError.details,
        hint: createError.hint
      });
      
      // Analyze specific error types
      if (createError.code === 'PGRST116') {
        console.error('🚨 Authentication error - user not properly authenticated');
      } else if (createError.code === '23505') {
        console.error('🚨 Duplicate brand name error');
      } else if (createError.code === '23502') {
        console.error('🚨 Missing required fields error');
      } else if (createError.message.includes('404')) {
        console.error('🚨 404 error - table or endpoint not found');
      }
      
      return false;
    }
    
    console.log('✅ Brand created successfully:', {
      id: createdBrand.id,
      brand_name: createdBrand.brand_name,
      created_at: createdBrand.created_at
    });
    
    console.log('\n🔍 Step 4: Testing Brand Retrieval...');
    
    // Test retrieving the created brand
    const { data: retrievedBrand, error: retrieveError } = await supabase
      .from('marcas')
      .select('*')
      .eq('id', createdBrand.id)
      .single();
    
    if (retrieveError) {
      console.error('❌ Brand retrieval failed:', retrieveError);
      return false;
    }
    
    console.log('✅ Brand retrieved successfully:', {
      id: retrievedBrand.id,
      brand_name: retrievedBrand.brand_name,
      personality: retrievedBrand.personality
    });
    
    console.log('\n🧹 Step 5: Cleanup Test Data...');
    
    // Clean up test data
    const { error: deleteError } = await supabase
      .from('marcas')
      .delete()
      .eq('id', createdBrand.id);
    
    if (deleteError) {
      console.warn('⚠️ Failed to cleanup test data:', deleteError);
    } else {
      console.log('✅ Test data cleaned up successfully');
    }
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed with exception:', error);
    return false;
  }
}

async function testMarcaService() {
  try {
    console.log('\n🔧 Step 6: Testing MarcaService...');
    
    // Import the marca service
    const { MarcaService } = await import('/src/services/marca-service.ts');
    
    // Test service method
    const testServiceData = {
      brand_name: `Service Test Brand ${Date.now()}`,
      industry: 'Tecnología',
      target_audience: 'Desarrolladores',
      tone: 'Profesional',
      description: 'Una marca de prueba del servicio',
      unique_value: 'Soluciones del servicio',
      primary_color: '#3B82F6',
      secondary_color: '#8B5CF6',
      personality: ['Innovador', 'Confiable'],
      examples: 'Marketing del servicio'
    };
    
    console.log('📤 Testing MarcaService.createMarca...');
    
    const createdBrand = await MarcaService.createMarca(testServiceData);
    
    console.log('✅ MarcaService.createMarca successful:', {
      id: createdBrand.id,
      brand_name: createdBrand.brand_name
    });
    
    // Test retrieval
    console.log('📥 Testing MarcaService.getMarcas...');
    
    const brands = await MarcaService.getMarcas();
    
    console.log('✅ MarcaService.getMarcas successful:', {
      totalBrands: brands.length,
      testBrandFound: brands.some(b => b.id === createdBrand.id)
    });
    
    // Cleanup
    await MarcaService.deleteMarca(createdBrand.id);
    console.log('✅ Test brand cleaned up');
    
    return true;
    
  } catch (error) {
    console.error('❌ MarcaService test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running Marca 404 Fix Tests...\n');
  
  const directTest = await testMarcaCreation();
  const serviceTest = await testMarcaService();
  
  console.log('\n📊 Test Results:');
  console.log(`✅ Direct Database Test: ${directTest ? 'PASSED' : 'FAILED'}`);
  console.log(`✅ Service Layer Test: ${serviceTest ? 'PASSED' : 'FAILED'}`);
  
  if (directTest && serviceTest) {
    console.log('\n🎉 All tests passed! The 404 error should be fixed.');
    console.log('\n📋 What was fixed:');
    console.log('• Supabase client configured to use correct schema (public)');
    console.log('• Marcas table is accessible and functional');
    console.log('• Brand creation and retrieval working properly');
    console.log('• MarcaService methods functioning correctly');
    
    console.log('\n🧪 Next Steps:');
    console.log('1. Test the brand creation form in the UI');
    console.log('2. Verify no 404 errors in browser console');
    console.log('3. Check that brands appear in dashboard after creation');
  } else {
    console.log('\n⚠️ Some tests failed. Please review the errors above.');
    
    if (!directTest) {
      console.log('\n🔧 Direct Database Issues:');
      console.log('• Check Supabase configuration');
      console.log('• Verify marcas table exists');
      console.log('• Ensure user authentication is working');
    }
    
    if (!serviceTest) {
      console.log('\n🔧 Service Layer Issues:');
      console.log('• Check MarcaService implementation');
      console.log('• Verify error handling in service methods');
      console.log('• Ensure data transformation is correct');
    }
  }
  
  return { directTest, serviceTest };
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests, testMarcaCreation, testMarcaService };
} else {
  // Run tests immediately if in browser
  runAllTests();
}
