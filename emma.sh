#!/bin/bash

# Emma Studio - PUERTOS FORZADOS 3002 y 8001
# Script ULTRA SIMPLE que SIEMPRE usa los puertos correctos

echo "🚀 Emma Studio - PUERTOS FORZADOS"
echo "Frontend: 3002 | Backend: 8001"
echo ""

# MATAR TODO lo que esté en esos puertos
echo "🔪 Matando procesos en puertos 3002 y 8001..."
lsof -ti :3002 | xargs kill -9 2>/dev/null || true
lsof -ti :8001 | xargs kill -9 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true
pkill -f "uvicorn" 2>/dev/null || true
pkill -f ":3002" 2>/dev/null || true
pkill -f ":8001" 2>/dev/null || true

sleep 2

# Variables de entorno FORZADAS
export PORT=8001
export UVICORN_PORT=8001
export VITE_PORT=3002

echo "🐍 Iniciando backend en puerto 8001..."
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001 &
BACKEND_PID=$!

sleep 3

echo "⚛️  Iniciando frontend en puerto 3002..."
cd ../client
npm run dev-clean &
FRONTEND_PID=$!

echo ""
echo "✅ Emma Studio iniciado:"
echo "   Frontend: http://localhost:3002"
echo "   Backend:  http://localhost:8001"
echo ""
echo "Presiona Ctrl+C para detener"

# Cleanup al salir
trap 'echo ""; echo "🛑 Deteniendo..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' SIGINT SIGTERM

wait
