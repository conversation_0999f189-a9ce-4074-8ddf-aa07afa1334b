#!/usr/bin/env node

/**
 * SEO Validation Script for Emma Studio
 * Validates SEO implementation and provides recommendations
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CLIENT_DIR = path.join(__dirname, '../client');
const PUBLIC_DIR = path.join(CLIENT_DIR, 'public');
const SRC_DIR = path.join(CLIENT_DIR, 'src');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Validation functions
function validateRobotsTxt() {
  const robotsPath = path.join(PUBLIC_DIR, 'robots.txt');
  
  if (!fs.existsSync(robotsPath)) {
    return { status: 'error', message: 'robots.txt not found' };
  }
  
  const content = fs.readFileSync(robotsPath, 'utf8');
  const checks = {
    hasUserAgent: content.includes('User-agent:'),
    hasSitemap: content.includes('Sitemap:'),
    hasDisallow: content.includes('Disallow:'),
    hasAllow: content.includes('Allow:')
  };
  
  const passed = Object.values(checks).filter(Boolean).length;
  const total = Object.keys(checks).length;
  
  return {
    status: passed === total ? 'success' : 'warning',
    message: `robots.txt validation: ${passed}/${total} checks passed`,
    details: checks
  };
}

function validateSitemaps() {
  const sitemapFiles = [
    'sitemap.xml',
    'sitemap-herramientas.xml',
    'sitemap-agents.xml',
    'sitemap-blog.xml',
    'sitemap-index.xml'
  ];
  
  const results = [];
  
  sitemapFiles.forEach(filename => {
    const filepath = path.join(PUBLIC_DIR, filename);
    if (fs.existsSync(filepath)) {
      const content = fs.readFileSync(filepath, 'utf8');
      const urlCount = (content.match(/<url>/g) || []).length;
      results.push({
        file: filename,
        exists: true,
        urlCount,
        valid: content.includes('<?xml') && content.includes('<urlset')
      });
    } else {
      results.push({
        file: filename,
        exists: false,
        urlCount: 0,
        valid: false
      });
    }
  });
  
  const existingFiles = results.filter(r => r.exists).length;
  const validFiles = results.filter(r => r.valid).length;
  
  return {
    status: validFiles >= 1 ? 'success' : 'error',
    message: `Sitemaps: ${existingFiles}/${sitemapFiles.length} exist, ${validFiles} valid`,
    details: results
  };
}

function validateManifest() {
  const manifestPath = path.join(PUBLIC_DIR, 'manifest.json');
  
  if (!fs.existsSync(manifestPath)) {
    return { status: 'error', message: 'manifest.json not found' };
  }
  
  try {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    const requiredFields = ['name', 'short_name', 'start_url', 'display', 'theme_color', 'icons'];
    const missingFields = requiredFields.filter(field => !manifest[field]);
    
    return {
      status: missingFields.length === 0 ? 'success' : 'warning',
      message: `manifest.json: ${requiredFields.length - missingFields.length}/${requiredFields.length} required fields`,
      details: { missingFields, hasIcons: manifest.icons?.length > 0 }
    };
  } catch (error) {
    return { status: 'error', message: 'manifest.json is invalid JSON' };
  }
}

function validateIndexHtml() {
  const indexPath = path.join(CLIENT_DIR, 'index.html');
  
  if (!fs.existsSync(indexPath)) {
    return { status: 'error', message: 'index.html not found' };
  }
  
  const content = fs.readFileSync(indexPath, 'utf8');
  const checks = {
    hasTitle: content.includes('<title>'),
    hasDescription: content.includes('name="description"'),
    hasKeywords: content.includes('name="keywords"'),
    hasOgTags: content.includes('property="og:'),
    hasTwitterCards: content.includes('property="twitter:'),
    hasCanonical: content.includes('rel="canonical"'),
    hasLangAttribute: content.includes('lang="es"'),
    hasViewport: content.includes('name="viewport"'),
    hasThemeColor: content.includes('name="theme-color"')
  };
  
  const passed = Object.values(checks).filter(Boolean).length;
  const total = Object.keys(checks).length;
  
  return {
    status: passed >= 7 ? 'success' : passed >= 5 ? 'warning' : 'error',
    message: `index.html SEO: ${passed}/${total} checks passed`,
    details: checks
  };
}

function validateSEOComponents() {
  const seoDir = path.join(SRC_DIR, 'components', 'seo');
  const requiredComponents = [
    'SEOHead.tsx',
    'StructuredData.tsx',
    'OptimizedImage.tsx'
  ];
  
  const results = requiredComponents.map(component => {
    const componentPath = path.join(seoDir, component);
    return {
      component,
      exists: fs.existsSync(componentPath)
    };
  });
  
  const existingComponents = results.filter(r => r.exists).length;
  
  return {
    status: existingComponents === requiredComponents.length ? 'success' : 'warning',
    message: `SEO Components: ${existingComponents}/${requiredComponents.length} implemented`,
    details: results
  };
}

function validateImageOptimization() {
  const publicImages = fs.readdirSync(PUBLIC_DIR)
    .filter(file => /\.(jpg|jpeg|png|webp|svg)$/i.test(file));
  
  const largeImages = [];
  const missingAltImages = [];
  
  // Check for large images (basic file size check)
  publicImages.forEach(image => {
    const imagePath = path.join(PUBLIC_DIR, image);
    const stats = fs.statSync(imagePath);
    if (stats.size > 500 * 1024) { // 500KB threshold
      largeImages.push({ file: image, size: Math.round(stats.size / 1024) + 'KB' });
    }
  });
  
  return {
    status: largeImages.length === 0 ? 'success' : 'warning',
    message: `Images: ${publicImages.length} found, ${largeImages.length} potentially oversized`,
    details: { totalImages: publicImages.length, largeImages }
  };
}

// Main validation function
function runSEOValidation() {
  log('🔍 Running SEO Validation for Emma Studio...', 'blue');
  log('', 'reset');
  
  const validations = [
    { name: 'robots.txt', fn: validateRobotsTxt },
    { name: 'Sitemaps', fn: validateSitemaps },
    { name: 'Web App Manifest', fn: validateManifest },
    { name: 'HTML Meta Tags', fn: validateIndexHtml },
    { name: 'SEO Components', fn: validateSEOComponents },
    { name: 'Image Optimization', fn: validateImageOptimization }
  ];
  
  const results = [];
  
  validations.forEach(({ name, fn }) => {
    try {
      const result = fn();
      results.push({ name, ...result });
      
      const icon = result.status === 'success' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
      const color = result.status === 'success' ? 'green' : result.status === 'warning' ? 'yellow' : 'red';
      
      log(`${icon} ${name}: ${result.message}`, color);
      
      if (result.details && process.argv.includes('--verbose')) {
        console.log('   Details:', result.details);
      }
    } catch (error) {
      results.push({ name, status: 'error', message: error.message });
      log(`❌ ${name}: Error - ${error.message}`, 'red');
    }
  });
  
  log('', 'reset');
  
  // Summary
  const successful = results.filter(r => r.status === 'success').length;
  const warnings = results.filter(r => r.status === 'warning').length;
  const errors = results.filter(r => r.status === 'error').length;
  
  log('📊 SEO Validation Summary:', 'bold');
  log(`   ✅ Passed: ${successful}`, 'green');
  log(`   ⚠️  Warnings: ${warnings}`, 'yellow');
  log(`   ❌ Errors: ${errors}`, 'red');
  
  if (errors === 0 && warnings === 0) {
    log('🎉 All SEO validations passed!', 'green');
  } else if (errors === 0) {
    log('👍 SEO implementation is good with minor improvements needed.', 'yellow');
  } else {
    log('🚨 Critical SEO issues found. Please address errors before deployment.', 'red');
  }
  
  log('', 'reset');
  log('💡 For detailed analysis, run: npm run seo:validate -- --verbose', 'blue');
  
  return { successful, warnings, errors, results };
}

// Run if called directly
if (import.meta.url.startsWith('file:') && process.argv[1] && import.meta.url.includes(process.argv[1])) {
  const summary = runSEOValidation();
  process.exit(summary.errors > 0 ? 1 : 0);
}

export { runSEOValidation };
