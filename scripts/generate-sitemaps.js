#!/usr/bin/env node

/**
 * Sitemap Generator Script for Emma Studio
 * Generates XML sitemaps for better SEO indexing
 * Run this script during build process
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const BASE_URL = process.env.SITE_URL || 'https://emmastudio.ai';
const OUTPUT_DIR = path.join(__dirname, '../client/public');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Current date for lastmod
const currentDate = new Date().toISOString().split('T')[0];

// Main pages configuration
const MAIN_PAGES = [
  {
    loc: '/',
    changefreq: 'weekly',
    priority: 1.0,
    lastmod: currentDate
  },
  {
    loc: '/emma-ai',
    changefreq: 'weekly',
    priority: 0.9,
    lastmod: currentDate
  },
  {
    loc: '/profesionales-ia',
    changefreq: 'weekly',
    priority: 0.8,
    lastmod: currentDate
  },
  {
    loc: '/soluciones-negocio',
    changefreq: 'weekly',
    priority: 0.8,
    lastmod: currentDate
  },
  {
    loc: '/blog',
    changefreq: 'daily',
    priority: 0.7,
    lastmod: currentDate
  }
];

// Marketing tools pages
const HERRAMIENTAS_PAGES = [
  {
    loc: '/dashboard/herramientas/seo-analyzer',
    changefreq: 'monthly',
    priority: 0.7,
    lastmod: currentDate
  },
  {
    loc: '/dashboard/herramientas/seo-gpt-optimizer',
    changefreq: 'monthly',
    priority: 0.7,
    lastmod: currentDate
  },
  {
    loc: '/dashboard/herramientas/title-analyzer',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  },
  {
    loc: '/dashboard/herramientas/content-builder',
    changefreq: 'monthly',
    priority: 0.7,
    lastmod: currentDate
  },
  {
    loc: '/dashboard/herramientas/image-generator',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  },
  {
    loc: '/dashboard/herramientas/photography-studio',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  },
  {
    loc: '/dashboard/herramientas/product-placement',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  }
];

// Agent pages
const AGENT_PAGES = [
  {
    loc: '/agents/hunter-pro',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  },
  {
    loc: '/agents/community-manager-ai',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  },
  {
    loc: '/agents/lead-agent',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  },
  {
    loc: '/agents/sales-support',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  },
  {
    loc: '/agents/email-customer-service',
    changefreq: 'monthly',
    priority: 0.6,
    lastmod: currentDate
  }
];

// Generate XML sitemap
function generateSitemapXML(urls) {
  const urlElements = urls.map(url => {
    const fullUrl = BASE_URL + url.loc;
    return `  <url>
    <loc>${fullUrl}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`;
}

// Generate sitemap index
function generateSitemapIndex() {
  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${BASE_URL}/sitemap.xml</loc>
    <lastmod>${currentDate}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${BASE_URL}/sitemap-herramientas.xml</loc>
    <lastmod>${currentDate}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${BASE_URL}/sitemap-agents.xml</loc>
    <lastmod>${currentDate}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${BASE_URL}/sitemap-blog.xml</loc>
    <lastmod>${currentDate}</lastmod>
  </sitemap>
</sitemapindex>`;
}

// Generate blog sitemap (placeholder for now)
function generateBlogSitemap() {
  // This would be populated with actual blog posts
  const blogPosts = [
    {
      loc: '/blog/como-automatizar-marketing-con-ia',
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.6
    },
    {
      loc: '/blog/guia-completa-seo-2025',
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.6
    }
  ];

  return generateSitemapXML(blogPosts);
}

// Main execution
function generateAllSitemaps() {
  console.log('🚀 Generating sitemaps for Emma Studio...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📁 Output directory: ${OUTPUT_DIR}`);

  try {
    // Generate main sitemap
    const mainSitemap = generateSitemapXML([...MAIN_PAGES, ...HERRAMIENTAS_PAGES, ...AGENT_PAGES]);
    fs.writeFileSync(path.join(OUTPUT_DIR, 'sitemap.xml'), mainSitemap);
    console.log('✅ Generated sitemap.xml');

    // Generate herramientas sitemap
    const herramientasSitemap = generateSitemapXML(HERRAMIENTAS_PAGES);
    fs.writeFileSync(path.join(OUTPUT_DIR, 'sitemap-herramientas.xml'), herramientasSitemap);
    console.log('✅ Generated sitemap-herramientas.xml');

    // Generate agents sitemap
    const agentsSitemap = generateSitemapXML(AGENT_PAGES);
    fs.writeFileSync(path.join(OUTPUT_DIR, 'sitemap-agents.xml'), agentsSitemap);
    console.log('✅ Generated sitemap-agents.xml');

    // Generate blog sitemap
    const blogSitemap = generateBlogSitemap();
    fs.writeFileSync(path.join(OUTPUT_DIR, 'sitemap-blog.xml'), blogSitemap);
    console.log('✅ Generated sitemap-blog.xml');

    // Generate sitemap index
    const sitemapIndex = generateSitemapIndex();
    fs.writeFileSync(path.join(OUTPUT_DIR, 'sitemap-index.xml'), sitemapIndex);
    console.log('✅ Generated sitemap-index.xml');

    console.log('🎉 All sitemaps generated successfully!');
    console.log(`📊 Total URLs: ${MAIN_PAGES.length + HERRAMIENTAS_PAGES.length + AGENT_PAGES.length}`);

  } catch (error) {
    console.error('❌ Error generating sitemaps:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url.startsWith('file:') && process.argv[1] && import.meta.url.includes(process.argv[1])) {
  generateAllSitemaps();
}

export {
  generateAllSitemaps,
  generateSitemapXML,
  generateSitemapIndex
};
