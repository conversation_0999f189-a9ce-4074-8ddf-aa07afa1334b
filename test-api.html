<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Connection</title>
</head>
<body>
    <h1>Test Emma Studio API</h1>
    <button onclick="testAPI()">Test Free Generation API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const formData = new FormData();
                formData.append('prompt', 'test product');
                formData.append('platform', 'instagram');
                formData.append('size', '1024x1024');
                formData.append('num_images', '1');

                const response = await fetch('http://localhost:8000/api/v1/ad-creator-agent/free-generation', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    resultDiv.innerHTML = `
                        <h3>Success!</h3>
                        <p><strong>Status:</strong> ${result.status}</p>
                        <p><strong>Message:</strong> ${result.message}</p>
                        <p><strong>Image URL:</strong> <a href="${result.image_url}" target="_blank">View Image</a></p>
                        <p><strong>Number Generated:</strong> ${result.num_generated}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <h3>Error ${response.status}</h3>
                        <p>${errorText}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Network Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
