#!/bin/bash

# Emma Studio - Verificador de Puertos Correctos
# Verifica que TODOS los archivos usen los puertos correctos: 3002 y 8001

echo "🔍 VERIFICANDO PUERTOS EN EMMA STUDIO"
echo "====================================="
echo "✅ Puertos correctos: Frontend 3002, Backend 8001"
echo "❌ Puertos incorrectos: 3000, 5173, 8000"
echo ""

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

errors=0

# Función para buscar puertos incorrectos
check_wrong_ports() {
    local file=$1
    local wrong_ports=("3000" "5173" "8000")
    
    for port in "${wrong_ports[@]}"; do
        if grep -q "localhost:$port\|:$port\|port.*$port" "$file" 2>/dev/null; then
            echo -e "${RED}❌ $file contiene puerto incorrecto: $port${NC}"
            grep -n "localhost:$port\|:$port\|port.*$port" "$file" | head -3
            echo ""
            ((errors++))
        fi
    done
}

# Función para verificar puertos correctos
check_correct_ports() {
    local file=$1
    local correct_ports=("3002" "8001")
    local found_correct=false
    
    for port in "${correct_ports[@]}"; do
        if grep -q "localhost:$port\|:$port\|port.*$port" "$file" 2>/dev/null; then
            found_correct=true
            break
        fi
    done
    
    if [ "$found_correct" = true ]; then
        echo -e "${GREEN}✅ $file usa puertos correctos${NC}"
    fi
}

echo "🔍 Verificando archivos de configuración..."
echo ""

# Archivos críticos a verificar
critical_files=(
    "client/vite.config.ts"
    "client/package.json"
    "client/.env"
    "backend/.env"
    "backend/app/core/config.py"
    "docker-compose.yml"
    "docker-compose.poetry.yml"
    "start-emma.sh"
    "start-emma-fixed.sh"
    "emma.sh"
    "stop-emma.sh"
    ".env.example"
    ".env.development"
)

# Verificar archivos críticos
for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        check_wrong_ports "$file"
        check_correct_ports "$file"
    else
        echo -e "${YELLOW}⚠️  $file no encontrado${NC}"
    fi
done

echo ""
echo "🔍 Verificando archivos del frontend..."

# Verificar archivos del frontend
find client/src -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | while read file; do
    check_wrong_ports "$file"
done

echo ""
echo "🔍 Verificando archivos del backend..."

# Verificar archivos del backend
find backend -name "*.py" -o -name "*.html" | while read file; do
    check_wrong_ports "$file"
done

echo ""
echo "📊 RESUMEN"
echo "=========="

if [ $errors -eq 0 ]; then
    echo -e "${GREEN}🎉 ¡PERFECTO! Todos los archivos usan los puertos correctos${NC}"
    echo -e "${GREEN}   Frontend: 3002 ✅${NC}"
    echo -e "${GREEN}   Backend:  8001 ✅${NC}"
else
    echo -e "${RED}❌ Se encontraron $errors archivos con puertos incorrectos${NC}"
    echo -e "${YELLOW}   Revisa los archivos marcados arriba${NC}"
fi

echo ""
echo "🌐 URLs correctas:"
echo "   Frontend: http://localhost:3002"
echo "   Backend:  http://localhost:8001"
echo "   API Docs: http://localhost:8001/docs"
