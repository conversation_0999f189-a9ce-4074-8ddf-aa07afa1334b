/**
 * Comprehensive diagnostic script to investigate inconsistent mood board image persistence
 * This script compares working vs non-working mood boards to identify the root cause
 */

console.log('🔍 Mood Board Consistency Diagnostic');
console.log('====================================\n');

class MoodBoardConsistencyDiagnostic {
  constructor() {
    this.workingMoodBoardId = '2659ce54-f3ea-4926-bbe9-a605cff2c50d';
    this.nonWorkingMoodBoardId = 'f7bbb990-3f6c-413f-b721-f0fdc4a52a62';
    this.currentMoodBoardId = this.getCurrentMoodBoardId();
    this.diagnosticResults = [];
    this.assetStoreLogs = [];
    this.originalConsoleLog = console.log;
  }

  async runCompleteDiagnostic() {
    console.log('🚀 Starting comprehensive mood board consistency diagnostic...\n');
    
    try {
      await this.step1_IdentifyCurrentMoodBoard();
      await this.step2_CompareBasicMoodBoardData();
      await this.step3_AnalyzeTldrawDataStructure();
      await this.step4_TestAssetStoreInitialization();
      await this.step5_MonitorAssetStoreActivity();
      await this.step6_TestImageUploadFlow();
      await this.step7_AnalyzeConsistencyFactors();
      
      this.displayDiagnosticResults();
    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
    }
  }

  async step1_IdentifyCurrentMoodBoard() {
    console.log('📋 Step 1: Identifying Current Mood Board');
    
    try {
      console.log(`ℹ️ Current mood board ID: ${this.currentMoodBoardId}`);
      
      if (this.currentMoodBoardId === this.workingMoodBoardId) {
        console.log('✅ Currently on WORKING mood board');
        this.diagnosticResults.push({ test: 'Current Board Type', result: 'WORKING' });
      } else if (this.currentMoodBoardId === this.nonWorkingMoodBoardId) {
        console.log('⚠️ Currently on NON-WORKING mood board');
        this.diagnosticResults.push({ test: 'Current Board Type', result: 'NON_WORKING' });
      } else {
        console.log('ℹ️ Currently on different mood board');
        this.diagnosticResults.push({ test: 'Current Board Type', result: 'OTHER' });
      }

    } catch (error) {
      console.error('❌ Error in step 1:', error);
      this.diagnosticResults.push({ test: 'Board Identification', result: 'ERROR', details: error.message });
    }
  }

  async step2_CompareBasicMoodBoardData() {
    console.log('\n📋 Step 2: Comparing Basic Mood Board Data');
    
    try {
      // Fetch data for both mood boards
      const workingData = await this.fetchMoodBoardData(this.workingMoodBoardId);
      const nonWorkingData = await this.fetchMoodBoardData(this.nonWorkingMoodBoardId);
      
      if (workingData && nonWorkingData) {
        console.log('✅ Both mood boards data fetched successfully');
        
        // Compare basic properties
        const comparison = {
          working: {
            id: workingData.id,
            title: workingData.title,
            created_at: workingData.created_at,
            updated_at: workingData.updated_at,
            status: workingData.status,
            has_tldraw_data: !!workingData.tldraw_data,
            tldraw_data_size: workingData.tldraw_data ? JSON.stringify(workingData.tldraw_data).length : 0
          },
          nonWorking: {
            id: nonWorkingData.id,
            title: nonWorkingData.title,
            created_at: nonWorkingData.created_at,
            updated_at: nonWorkingData.updated_at,
            status: nonWorkingData.status,
            has_tldraw_data: !!nonWorkingData.tldraw_data,
            tldraw_data_size: nonWorkingData.tldraw_data ? JSON.stringify(nonWorkingData.tldraw_data).length : 0
          }
        };
        
        console.log('📊 Mood Board Comparison:');
        console.table(comparison);
        
        // Check for significant differences
        const createdAtDiff = new Date(nonWorkingData.created_at) - new Date(workingData.created_at);
        const daysDiff = Math.abs(createdAtDiff) / (1000 * 60 * 60 * 24);
        
        console.log(`ℹ️ Creation time difference: ${daysDiff.toFixed(2)} days`);
        
        if (daysDiff > 1) {
          console.log('⚠️ Significant creation time difference detected');
          this.diagnosticResults.push({ test: 'Creation Time Difference', result: 'SIGNIFICANT', details: `${daysDiff.toFixed(2)} days` });
        } else {
          this.diagnosticResults.push({ test: 'Creation Time Difference', result: 'MINIMAL', details: `${daysDiff.toFixed(2)} days` });
        }
        
        this.diagnosticResults.push({ test: 'Basic Data Comparison', result: 'SUCCESS', details: comparison });
        
      } else {
        console.log('❌ Failed to fetch mood board data');
        this.diagnosticResults.push({ test: 'Basic Data Comparison', result: 'FAIL' });
      }

    } catch (error) {
      console.error('❌ Error in step 2:', error);
      this.diagnosticResults.push({ test: 'Basic Data Comparison', result: 'ERROR', details: error.message });
    }
  }

  async step3_AnalyzeTldrawDataStructure() {
    console.log('\n📋 Step 3: Analyzing Tldraw Data Structure');
    
    try {
      const workingData = await this.fetchMoodBoardData(this.workingMoodBoardId);
      const nonWorkingData = await this.fetchMoodBoardData(this.nonWorkingMoodBoardId);
      
      if (workingData?.tldraw_data && nonWorkingData?.tldraw_data) {
        const workingStructure = this.analyzeTldrawStructure(workingData.tldraw_data, 'WORKING');
        const nonWorkingStructure = this.analyzeTldrawStructure(nonWorkingData.tldraw_data, 'NON_WORKING');
        
        console.log('📊 Tldraw Data Structure Comparison:');
        console.table({
          working: workingStructure,
          nonWorking: nonWorkingStructure
        });
        
        // Check for structural differences
        const structuralDifferences = this.compareStructures(workingStructure, nonWorkingStructure);
        
        if (structuralDifferences.length > 0) {
          console.log('⚠️ Structural differences detected:');
          structuralDifferences.forEach(diff => {
            console.log(`   - ${diff}`);
          });
          this.diagnosticResults.push({ test: 'Tldraw Structure Analysis', result: 'DIFFERENCES', details: structuralDifferences });
        } else {
          console.log('✅ No significant structural differences');
          this.diagnosticResults.push({ test: 'Tldraw Structure Analysis', result: 'SIMILAR' });
        }
        
      } else {
        console.log('⚠️ Missing tldraw data in one or both mood boards');
        this.diagnosticResults.push({ test: 'Tldraw Structure Analysis', result: 'MISSING_DATA' });
      }

    } catch (error) {
      console.error('❌ Error in step 3:', error);
      this.diagnosticResults.push({ test: 'Tldraw Structure Analysis', result: 'ERROR', details: error.message });
    }
  }

  async step4_TestAssetStoreInitialization() {
    console.log('\n📋 Step 4: Testing Asset Store Initialization');
    
    try {
      // Set up console monitoring
      console.log = (...args) => {
        const message = args.join(' ');
        if (message.includes('MoodBoard') && (message.includes('Asset Store') || message.includes('asset store'))) {
          this.assetStoreLogs.push({
            timestamp: new Date().toISOString(),
            message: message,
            boardId: this.currentMoodBoardId
          });
        }
        this.originalConsoleLog.apply(console, args);
      };
      
      // Wait for initialization
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const initLogs = this.assetStoreLogs.filter(log => 
        log.message.includes('Creating asset store') || 
        log.message.includes('Tldraw mounted with asset store')
      );
      
      if (initLogs.length > 0) {
        console.log('✅ Asset store initialization detected');
        initLogs.forEach(log => {
          console.log(`   📡 ${log.message}`);
        });
        this.diagnosticResults.push({ test: 'Asset Store Initialization', result: 'SUCCESS' });
      } else {
        console.log('⚠️ No asset store initialization logs detected');
        this.diagnosticResults.push({ test: 'Asset Store Initialization', result: 'NO_LOGS' });
      }

    } catch (error) {
      console.error('❌ Error in step 4:', error);
      this.diagnosticResults.push({ test: 'Asset Store Initialization', result: 'ERROR', details: error.message });
    }
  }

  async step5_MonitorAssetStoreActivity() {
    console.log('\n📋 Step 5: Monitoring Asset Store Activity');
    
    try {
      // Continue monitoring for asset store activity
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const activityLogs = this.assetStoreLogs.filter(log => 
        log.message.includes('Uploading asset') || 
        log.message.includes('Upload successful') ||
        log.message.includes('Upload failed') ||
        log.message.includes('Resolving asset')
      );
      
      console.log(`ℹ️ Asset store activity logs: ${activityLogs.length}`);
      
      if (activityLogs.length > 0) {
        console.log('✅ Asset store activity detected');
        activityLogs.forEach(log => {
          console.log(`   📤 ${log.message}`);
        });
        this.diagnosticResults.push({ test: 'Asset Store Activity', result: 'ACTIVE' });
      } else {
        console.log('⚠️ No asset store activity detected');
        this.diagnosticResults.push({ test: 'Asset Store Activity', result: 'INACTIVE' });
      }

    } catch (error) {
      console.error('❌ Error in step 5:', error);
      this.diagnosticResults.push({ test: 'Asset Store Activity', result: 'ERROR', details: error.message });
    }
  }

  async step6_TestImageUploadFlow() {
    console.log('\n📋 Step 6: Testing Image Upload Flow');
    
    try {
      // Create a test image
      const testFile = await this.createTestImageFile();
      console.log(`✅ Test image created: ${testFile.name}`);
      
      // Try to simulate image upload
      const canvas = document.querySelector('canvas');
      if (canvas) {
        console.log('🔄 Simulating image upload...');
        
        // Create drop event
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(testFile);
        
        const dropEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          dataTransfer: dataTransfer
        });
        
        // Monitor for upload activity
        const beforeUploadLogs = this.assetStoreLogs.length;
        
        canvas.dispatchEvent(dropEvent);
        
        // Wait for upload processing
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const afterUploadLogs = this.assetStoreLogs.length;
        const newLogs = afterUploadLogs - beforeUploadLogs;
        
        if (newLogs > 0) {
          console.log(`✅ Upload triggered ${newLogs} new asset store logs`);
          this.diagnosticResults.push({ test: 'Image Upload Flow', result: 'TRIGGERED', details: `${newLogs} logs` });
        } else {
          console.log('⚠️ Upload did not trigger asset store activity');
          this.diagnosticResults.push({ test: 'Image Upload Flow', result: 'NO_ACTIVITY' });
        }
        
      } else {
        console.log('❌ Canvas not found for upload test');
        this.diagnosticResults.push({ test: 'Image Upload Flow', result: 'NO_CANVAS' });
      }

    } catch (error) {
      console.error('❌ Error in step 6:', error);
      this.diagnosticResults.push({ test: 'Image Upload Flow', result: 'ERROR', details: error.message });
    }
  }

  async step7_AnalyzeConsistencyFactors() {
    console.log('\n📋 Step 7: Analyzing Consistency Factors');
    
    try {
      // Analyze potential factors causing inconsistency
      const factors = {
        currentBoardType: this.currentMoodBoardId === this.workingMoodBoardId ? 'WORKING' : 
                         this.currentMoodBoardId === this.nonWorkingMoodBoardId ? 'NON_WORKING' : 'OTHER',
        hasCanvas: !!document.querySelector('canvas'),
        hasAssetStoreLogs: this.assetStoreLogs.length > 0,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      };
      
      console.log('📊 Consistency Factors:');
      console.table(factors);
      
      this.diagnosticResults.push({ test: 'Consistency Factors', result: 'ANALYZED', details: factors });

    } catch (error) {
      console.error('❌ Error in step 7:', error);
      this.diagnosticResults.push({ test: 'Consistency Factors', result: 'ERROR', details: error.message });
    }
  }

  // Helper methods
  getCurrentMoodBoardId() {
    const url = window.location.href;
    const parts = url.split('/');
    const editorIndex = parts.findIndex(part => part === 'editor');
    return editorIndex !== -1 && editorIndex + 1 < parts.length ? parts[editorIndex + 1] : null;
  }

  async fetchMoodBoardData(boardId) {
    try {
      const response = await fetch(`/api/moodboard/${boardId}`);
      if (response.ok) {
        const data = await response.json();
        return data.data;
      }
      return null;
    } catch (error) {
      console.error(`Error fetching mood board ${boardId}:`, error);
      return null;
    }
  }

  analyzeTldrawStructure(tldrawData, label) {
    const structure = {
      hasStore: !!tldrawData.store,
      storeKeys: tldrawData.store ? Object.keys(tldrawData.store).length : 0,
      hasSchema: !!tldrawData.schema,
      imageShapes: 0,
      totalShapes: 0,
      hasAssets: false,
      assetCount: 0
    };

    if (tldrawData.store) {
      for (const [key, value] of Object.entries(tldrawData.store)) {
        if (value && value.type) {
          structure.totalShapes++;
          if (value.type === 'image') {
            structure.imageShapes++;
          }
        }
        if (key.startsWith('asset:')) {
          structure.hasAssets = true;
          structure.assetCount++;
        }
      }
    }

    return structure;
  }

  compareStructures(working, nonWorking) {
    const differences = [];
    
    for (const key in working) {
      if (working[key] !== nonWorking[key]) {
        differences.push(`${key}: working=${working[key]}, nonWorking=${nonWorking[key]}`);
      }
    }
    
    return differences;
  }

  async createTestImageFile() {
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    
    // Create a distinctive test pattern
    ctx.fillStyle = '#ff4757';
    ctx.fillRect(0, 0, 50, 50);
    ctx.fillStyle = '#2ed573';
    ctx.fillRect(50, 0, 50, 50);
    ctx.fillStyle = '#3742fa';
    ctx.fillRect(0, 50, 50, 50);
    ctx.fillStyle = '#ffa502';
    ctx.fillRect(50, 50, 50, 50);
    
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    return new File([blob], 'consistency-test.png', { type: 'image/png' });
  }

  displayDiagnosticResults() {
    console.log('\n🎯 MOOD BOARD CONSISTENCY DIAGNOSTIC RESULTS');
    console.log('=============================================');
    
    // Restore original console.log
    console.log = this.originalConsoleLog;
    
    // Display results
    this.diagnosticResults.forEach(result => {
      const emoji = result.result === 'SUCCESS' || result.result === 'WORKING' ? '✅' :
                   result.result === 'FAIL' || result.result === 'NON_WORKING' ? '❌' :
                   result.result === 'ERROR' ? '🚨' : '⚠️';
      
      console.log(`${emoji} ${result.test}: ${result.result}`);
      if (result.details) {
        if (typeof result.details === 'object') {
          console.log('   Details:', result.details);
        } else {
          console.log(`   Details: ${result.details}`);
        }
      }
    });
    
    console.log(`\n📊 Total Asset Store Logs: ${this.assetStoreLogs.length}`);
    console.log(`📊 Current Board: ${this.currentMoodBoardId}`);
    
    // Provide recommendations
    console.log('\n💡 DIAGNOSTIC RECOMMENDATIONS:');
    
    const hasAssetStoreActivity = this.assetStoreLogs.length > 0;
    const isWorkingBoard = this.currentMoodBoardId === this.workingMoodBoardId;
    const isNonWorkingBoard = this.currentMoodBoardId === this.nonWorkingMoodBoardId;
    
    if (isWorkingBoard && hasAssetStoreActivity) {
      console.log('✅ This is the working board and asset store is active');
      console.log('💡 Try uploading an image to test the fix');
    } else if (isNonWorkingBoard && !hasAssetStoreActivity) {
      console.log('❌ This is the non-working board with no asset store activity');
      console.log('💡 This confirms the inconsistent behavior');
    } else if (isNonWorkingBoard && hasAssetStoreActivity) {
      console.log('🔄 Non-working board now shows asset store activity');
      console.log('💡 The fix may have resolved the issue');
    }
    
    console.log('\n🔍 NEXT STEPS:');
    console.log('1. Test both mood boards with the same image upload');
    console.log('2. Compare the asset store logs between boards');
    console.log('3. Check if the issue is related to board creation time');
    console.log('4. Verify tldraw data structure consistency');
  }
}

// Auto-run the diagnostic
if (window.location.href.includes('mood-board/editor')) {
  const diagnostic = new MoodBoardConsistencyDiagnostic();
  diagnostic.runCompleteDiagnostic();
} else {
  console.log('ℹ️ Please run this diagnostic on a mood board editor page');
  console.log('ℹ️ Navigate to one of the mood board URLs to test');
}

// Export for manual testing
window.MoodBoardDiagnostic = MoodBoardConsistencyDiagnostic;
