#!/bin/bash
# Emma Studio - Script de parada limpia
# Detiene todos los procesos de Emma Studio de forma segura

echo "🛑 EMMA STUDIO - PARADA LIMPIA"
echo "=============================="

PROJECT_ROOT="$(dirname "$0")"

# Función para matar procesos por PID
kill_by_pid() {
    local pid_file=$1
    local service_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "🔄 Deteniendo $service_name (PID: $pid)..."
            kill "$pid" 2>/dev/null
            sleep 2
            
            # Si aún está corriendo, forzar
            if kill -0 "$pid" 2>/dev/null; then
                echo "⚠️  Forzando parada de $service_name..."
                kill -9 "$pid" 2>/dev/null
            fi
            
            echo "✅ $service_name detenido"
        else
            echo "ℹ️  $service_name ya estaba detenido"
        fi
        rm -f "$pid_file"
    else
        echo "ℹ️  No se encontró archivo PID para $service_name"
    fi
}

# Detener usando archivos PID si existen
kill_by_pid "$PROJECT_ROOT/.backend.pid" "Backend"
kill_by_pid "$PROJECT_ROOT/.frontend.pid" "Frontend"

# Limpiar procesos por puerto (backup)
echo ""
echo "🧹 Limpiando procesos por puerto..."

# Puerto 8001 (Backend)
backend_pids=$(lsof -ti:8001 2>/dev/null)
if [ -n "$backend_pids" ]; then
    echo "🔄 Deteniendo procesos en puerto 8001..."
    echo "$backend_pids" | xargs kill -9 2>/dev/null || true
    echo "✅ Puerto 8001 liberado"
else
    echo "✅ Puerto 8001 ya estaba libre"
fi

# Puerto 3002 (Frontend)
frontend_pids=$(lsof -ti:3002 2>/dev/null)
if [ -n "$frontend_pids" ]; then
    echo "🔄 Deteniendo procesos en puerto 3002..."
    echo "$frontend_pids" | xargs kill -9 2>/dev/null || true
    echo "✅ Puerto 3002 liberado"
else
    echo "✅ Puerto 3002 ya estaba libre"
fi

# Limpiar procesos por nombre (backup)
echo ""
echo "🧹 Limpiando procesos por nombre..."

# Uvicorn
uvicorn_pids=$(pgrep -f "uvicorn" 2>/dev/null)
if [ -n "$uvicorn_pids" ]; then
    echo "🔄 Deteniendo procesos uvicorn..."
    echo "$uvicorn_pids" | xargs kill -9 2>/dev/null || true
    echo "✅ Procesos uvicorn detenidos"
fi

# Vite
vite_pids=$(pgrep -f "vite" 2>/dev/null)
if [ -n "$vite_pids" ]; then
    echo "🔄 Deteniendo procesos vite..."
    echo "$vite_pids" | xargs kill -9 2>/dev/null || true
    echo "✅ Procesos vite detenidos"
fi

# Node (solo los relacionados con vite)
node_vite_pids=$(pgrep -f "node.*vite" 2>/dev/null)
if [ -n "$node_vite_pids" ]; then
    echo "🔄 Deteniendo procesos node+vite..."
    echo "$node_vite_pids" | xargs kill -9 2>/dev/null || true
    echo "✅ Procesos node+vite detenidos"
fi

# Limpiar archivos temporales
echo ""
echo "🧹 Limpiando archivos temporales..."

# Limpiar logs si existen
if [ -f "$PROJECT_ROOT/backend/backend.log" ]; then
    rm -f "$PROJECT_ROOT/backend/backend.log"
    echo "✅ Log del backend eliminado"
fi

if [ -f "$PROJECT_ROOT/client/frontend.log" ]; then
    rm -f "$PROJECT_ROOT/client/frontend.log"
    echo "✅ Log del frontend eliminado"
fi

# Limpiar archivos PID
rm -f "$PROJECT_ROOT/.backend.pid" "$PROJECT_ROOT/.frontend.pid"

# Verificación final
echo ""
echo "🔍 Verificación final..."

if lsof -ti:8001 >/dev/null 2>&1; then
    echo "⚠️  Advertencia: Aún hay procesos en puerto 8001"
else
    echo "✅ Puerto 8001 completamente libre"
fi

if lsof -ti:3002 >/dev/null 2>&1; then
    echo "⚠️  Advertencia: Aún hay procesos en puerto 3002"
else
    echo "✅ Puerto 3002 completamente libre"
fi

echo ""
echo "🎉 EMMA STUDIO DETENIDO COMPLETAMENTE"
echo "===================================="
echo "✅ Todos los procesos han sido detenidos"
echo "✅ Todos los puertos han sido liberados"
echo "✅ Archivos temporales eliminados"
echo ""
echo "💡 Para reiniciar Emma Studio ejecuta: ./start-emma.sh"
