#!/bin/bash

echo "🧪 Test simple de sketch-to-image"

# Crear un archivo de imagen simple (1x1 pixel PNG)
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > test_image.png

echo "📤 Enviando request..."

curl -X POST http://localhost:8001/api/v1/sketches/generate \
  -F "image=@test_image.png" \
  -F "prompt=a simple drawing" \
  -F "control_strength=0.7" \
  -F "output_format=png" \
  -F "style_preset=enhance" \
  --max-time 120

echo ""
echo "🧹 Limpiando archivos temporales..."
rm -f test_image.png

echo "✅ Test completado"
