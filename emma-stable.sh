#!/bin/bash

# Emma Studio - STABLE PRODUCTION MODE
# NO AUTO-RELOAD = NO CRASHES = STABLE BACKEND

echo "🚀 Emma Studio - MODO ESTABLE (SIN AUTO-RELOAD)"
echo "Frontend: 3002 | Backend: 8001"
echo ""

# MATAR TODO lo que esté en esos puertos
echo "🔪 Matando procesos en puertos 3002 y 8001..."
lsof -ti :3002 | xargs kill -9 2>/dev/null || true
lsof -ti :8001 | xargs kill -9 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true
pkill -f "uvicorn" 2>/dev/null || true
pkill -f ":3002" 2>/dev/null || true
pkill -f ":8001" 2>/dev/null || true

sleep 2

# Variables de entorno FORZADAS
export PORT=8001
export UVICORN_PORT=8001
export VITE_PORT=3002

echo "🐍 Iniciando backend ESTABLE en puerto 8001..."
cd backend
# ⚡ SIN --reload = SIN CRASHES
python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 &
BACKEND_PID=$!

sleep 5

echo "⚛️  Iniciando frontend en puerto 3002..."
cd ../client
npm run dev-clean &
FRONTEND_PID=$!

echo ""
echo "✅ Emma Studio ESTABLE iniciado:"
echo "   Frontend: http://localhost:3002"
echo "   Backend:  http://localhost:8001"
echo ""
echo "🛡️  MODO ESTABLE ACTIVADO:"
echo "   ✅ Sin auto-reload (sin crashes)"
echo "   ✅ Servicios estables"
echo "   ✅ Recursos limpios"
echo ""
echo "💡 Para cambios de código: Ctrl+C y reinicia"
echo "Presiona Ctrl+C para detener"

# Cleanup al salir
trap 'echo ""; echo "🛑 Deteniendo..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' SIGINT SIGTERM

wait
