/**
 * Simultaneous Upload Threshold Test
 * Identifies the exact number of simultaneous image uploads that causes save failures
 */

console.log('🧪 Simultaneous Upload Threshold Test');
console.log('====================================\n');

class SimultaneousUploadThresholdTest {
  constructor() {
    this.testResults = [];
    this.uploadLogs = [];
    this.originalConsoleLog = console.log;
    this.originalConsoleError = console.error;
    this.maxTestImages = 10; // Test up to 10 simultaneous uploads
  }

  async runThresholdTest() {
    console.log('🚀 Starting simultaneous upload threshold test...\n');
    
    try {
      this.setupLogging();
      await this.testSequentialUploads();
      await this.testSimultaneousUploads();
      this.displayThresholdResults();
    } catch (error) {
      console.error('❌ Threshold test failed:', error);
    } finally {
      this.restoreLogging();
    }
  }

  setupLogging() {
    const self = this;
    
    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('Asset Store') || message.includes('Upload')) {
        self.uploadLogs.push({
          type: 'log',
          timestamp: new Date().toISOString(),
          message: message
        });
      }
      self.originalConsoleLog.apply(console, args);
    };

    console.error = function(...args) {
      const message = args.join(' ');
      self.uploadLogs.push({
        type: 'error',
        timestamp: new Date().toISOString(),
        message: message
      });
      self.originalConsoleError.apply(console, args);
    };
  }

  async testSequentialUploads() {
    console.log('📋 Phase 1: Testing Sequential Uploads (Baseline)');
    
    try {
      const testImages = await this.createTestImages(3);
      console.log(`✅ Created ${testImages.length} test images`);
      
      // Test sequential uploads (one at a time)
      for (let i = 0; i < testImages.length; i++) {
        console.log(`🔄 Sequential upload ${i + 1}/${testImages.length}`);
        
        const beforeLogs = this.uploadLogs.length;
        await this.simulateImageUpload(testImages[i]);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait between uploads
        
        const afterLogs = this.uploadLogs.length;
        const uploadActivity = afterLogs - beforeLogs;
        
        this.testResults.push({
          test: `Sequential Upload ${i + 1}`,
          result: uploadActivity > 0 ? 'SUCCESS' : 'NO_ACTIVITY',
          details: `${uploadActivity} logs generated`
        });
      }
      
      console.log('✅ Sequential upload baseline completed');

    } catch (error) {
      console.error('❌ Error in sequential upload test:', error);
    }
  }

  async testSimultaneousUploads() {
    console.log('\n📋 Phase 2: Testing Simultaneous Uploads');
    
    for (let batchSize = 2; batchSize <= this.maxTestImages; batchSize++) {
      console.log(`\n🔄 Testing ${batchSize} simultaneous uploads...`);
      
      try {
        const testImages = await this.createTestImages(batchSize);
        const beforeLogs = this.uploadLogs.length;
        
        // Simulate simultaneous uploads
        const uploadPromises = testImages.map((image, index) => 
          this.simulateImageUpload(image, `batch-${batchSize}-image-${index + 1}`)
        );
        
        // Wait for all uploads to complete (or timeout)
        const results = await Promise.allSettled(uploadPromises);
        
        // Wait additional time for processing
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const afterLogs = this.uploadLogs.length;
        const totalActivity = afterLogs - beforeLogs;
        
        // Analyze results
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        
        // Check for error logs
        const errorLogs = this.uploadLogs.slice(beforeLogs).filter(log => log.type === 'error');
        
        const testResult = {
          test: `${batchSize} Simultaneous Uploads`,
          result: failed === 0 && errorLogs.length === 0 ? 'SUCCESS' : 'PARTIAL_FAILURE',
          details: {
            successful,
            failed,
            errors: errorLogs.length,
            totalActivity,
            errorMessages: errorLogs.map(log => log.message)
          }
        };
        
        this.testResults.push(testResult);
        
        console.log(`   📊 Results: ${successful} successful, ${failed} failed, ${errorLogs.length} errors`);
        
        if (errorLogs.length > 0) {
          console.log(`   ⚠️ Threshold likely reached at ${batchSize} simultaneous uploads`);
        }

      } catch (error) {
        console.error(`❌ Error testing ${batchSize} simultaneous uploads:`, error);
        this.testResults.push({
          test: `${batchSize} Simultaneous Uploads`,
          result: 'ERROR',
          details: error.message
        });
      }
    }
  }

  async createTestImages(count) {
    const images = [];
    
    for (let i = 0; i < count; i++) {
      const canvas = document.createElement('canvas');
      canvas.width = 100;
      canvas.height = 100;
      const ctx = canvas.getContext('2d');
      
      // Create unique pattern for each image
      const hue = (i * 360 / count) % 360;
      ctx.fillStyle = `hsl(${hue}, 70%, 50%)`;
      ctx.fillRect(0, 0, 100, 100);
      
      // Add number
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 24px Arial';
      ctx.textAlign = 'center';
      ctx.fillText((i + 1).toString(), 50, 60);
      
      const blob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'image/png');
      });
      
      images.push(new File([blob], `test-image-${i + 1}.png`, { type: 'image/png' }));
    }
    
    return images;
  }

  async simulateImageUpload(file, identifier = '') {
    return new Promise((resolve, reject) => {
      try {
        const canvas = document.querySelector('canvas');
        if (!canvas) {
          reject(new Error('Canvas not found'));
          return;
        }
        
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        
        const dropEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          dataTransfer: dataTransfer
        });
        
        // Add identifier to track this specific upload
        if (identifier) {
          dropEvent.uploadIdentifier = identifier;
        }
        
        canvas.dispatchEvent(dropEvent);
        
        // Resolve after a short delay to simulate async upload
        setTimeout(() => resolve(), 500);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  displayThresholdResults() {
    console.log('\n🎯 SIMULTANEOUS UPLOAD THRESHOLD TEST RESULTS');
    console.log('==============================================');
    
    this.restoreLogging();
    
    // Display all test results
    this.testResults.forEach(result => {
      const emoji = result.result === 'SUCCESS' ? '✅' :
                   result.result === 'PARTIAL_FAILURE' ? '⚠️' :
                   result.result === 'ERROR' ? '❌' : '🔄';
      
      console.log(`${emoji} ${result.test}: ${result.result}`);
      
      if (result.details) {
        if (typeof result.details === 'object' && result.details.successful !== undefined) {
          const details = result.details;
          console.log(`   📊 Success: ${details.successful}, Failed: ${details.failed}, Errors: ${details.errors}`);
          if (details.errorMessages && details.errorMessages.length > 0) {
            console.log(`   🚨 Error messages: ${details.errorMessages.slice(0, 2).join(', ')}`);
          }
        } else {
          console.log(`   Details: ${result.details}`);
        }
      }
    });
    
    // Analyze threshold
    console.log('\n🔍 THRESHOLD ANALYSIS:');
    
    const simultaneousTests = this.testResults.filter(r => r.test.includes('Simultaneous'));
    const firstFailure = simultaneousTests.find(r => r.result === 'PARTIAL_FAILURE' || r.result === 'ERROR');
    
    if (firstFailure) {
      const failureNumber = parseInt(firstFailure.test.match(/(\d+) Simultaneous/)[1]);
      console.log(`🚨 THRESHOLD IDENTIFIED: ${failureNumber} simultaneous uploads cause failures`);
      console.log(`💡 RECOMMENDATION: Limit simultaneous uploads to ${failureNumber - 1} images`);
    } else {
      console.log('✅ No threshold reached within test range');
      console.log('💡 RECOMMENDATION: Consider testing with larger batch sizes');
    }
    
    console.log('\n📋 UPLOAD ACTIVITY SUMMARY:');
    console.log(`📊 Total upload logs captured: ${this.uploadLogs.length}`);
    
    const errorLogs = this.uploadLogs.filter(log => log.type === 'error');
    if (errorLogs.length > 0) {
      console.log(`🚨 Total errors detected: ${errorLogs.length}`);
      console.log('🔍 Recent error messages:');
      errorLogs.slice(-3).forEach(log => {
        console.log(`   ❌ ${log.message}`);
      });
    }
    
    console.log('\n💡 NEXT STEPS:');
    console.log('1. Implement upload queue management system');
    console.log('2. Limit simultaneous uploads to safe threshold');
    console.log('3. Add progress feedback for batch uploads');
    console.log('4. Test with real image persistence to database');
  }

  restoreLogging() {
    console.log = this.originalConsoleLog;
    console.error = this.originalConsoleError;
  }
}

// Auto-run the test
if (window.location.href.includes('mood-board/editor')) {
  const thresholdTest = new SimultaneousUploadThresholdTest();
  thresholdTest.runThresholdTest();
} else {
  console.log('ℹ️ Please run this test on a mood board editor page');
  console.log('ℹ️ Navigate to: /dashboard/herramientas/mood-board/editor/[board-id]');
}

// Export for manual testing
window.SimultaneousUploadThresholdTest = SimultaneousUploadThresholdTest;
