#!/usr/bin/env node

/**
 * Diagnostic script for Emma Studio tools issues
 * Checks Visual Complexity Analyzer auto-save and MoodBoard image loading
 */

const API_BASE = 'http://localhost:8001/api';

async function makeAuthenticatedRequest(endpoint, options = {}) {
  // Get a real token from the frontend localStorage or use the one from browser
  // For now, let's try without authentication first to test basic connectivity
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  const response = await fetch(`${API_BASE}${endpoint}`, {
    ...options,
    headers
  });

  return response;
}

async function diagnoseMoodBoardImages() {
  console.log('\n🔍 DIAGNOSING MOODBOARD IMAGE ISSUES');
  console.log('=====================================');

  try {
    // 1. Check if moodboards are loading
    console.log('\n📋 Step 1: Checking moodboard list...');
    const listResponse = await makeAuthenticatedRequest('/moodboard/list?page=1&limit=5');
    
    if (!listResponse.ok) {
      console.log(`❌ Failed to load moodboards: ${listResponse.status} ${listResponse.statusText}`);
      return;
    }

    const listData = await listResponse.json();
    console.log(`✅ Found ${listData.data?.length || 0} moodboards`);

    if (!listData.data || listData.data.length === 0) {
      console.log('ℹ️ No moodboards found to test');
      return;
    }

    // 2. Check specific moodboard with images
    const moodboard = listData.data[0];
    console.log(`\n🖼️ Step 2: Analyzing moodboard "${moodboard.title}"...`);
    console.log(`ID: ${moodboard.id}`);

    const detailResponse = await makeAuthenticatedRequest(`/moodboard/${moodboard.id}`);
    if (!detailResponse.ok) {
      console.log(`❌ Failed to load moodboard details: ${detailResponse.status}`);
      return;
    }

    const detailData = await detailResponse.json();
    const tldrawData = detailData.data?.tldraw_data;

    if (!tldrawData) {
      console.log('⚠️ No tldraw_data found in moodboard');
      return;
    }

    console.log('✅ tldraw_data exists');

    // 3. Analyze image content
    let imageCount = 0;
    let totalImageSize = 0;
    const imageFormats = new Set();
    const imageIssues = [];

    if (tldrawData.store) {
      console.log('\n🔍 Step 3: Analyzing image content...');
      
      for (const [shapeId, shape] of Object.entries(tldrawData.store)) {
        if (shape.type === 'image' && shape.props?.src) {
          imageCount++;
          const src = shape.props.src;
          const imageSize = src.length;
          totalImageSize += imageSize;

          // Check image format and validity
          if (src.startsWith('data:image/')) {
            const format = src.split(';')[0].split('/')[1];
            imageFormats.add(format.toUpperCase());
            
            // Check if base64 is valid
            try {
              const base64Data = src.split(',')[1];
              if (!base64Data || base64Data.length < 100) {
                imageIssues.push(`Image ${shapeId}: Base64 data too short`);
              }
            } catch (e) {
              imageIssues.push(`Image ${shapeId}: Invalid base64 format`);
            }
          } else if (src.startsWith('http')) {
            imageFormats.add('URL');
            // Test if URL is accessible
            try {
              const imgResponse = await fetch(src, { method: 'HEAD' });
              if (!imgResponse.ok) {
                imageIssues.push(`Image ${shapeId}: URL not accessible (${imgResponse.status})`);
              }
            } catch (e) {
              imageIssues.push(`Image ${shapeId}: URL fetch failed`);
            }
          } else {
            imageIssues.push(`Image ${shapeId}: Unknown image format`);
          }
        }
      }
    }

    console.log(`\n📊 Image Analysis Results:`);
    console.log(`  - Total images: ${imageCount}`);
    console.log(`  - Total image data: ${(totalImageSize / 1024).toFixed(2)} KB`);
    console.log(`  - Image formats: ${Array.from(imageFormats).join(', ') || 'None'}`);
    console.log(`  - Issues found: ${imageIssues.length}`);

    if (imageIssues.length > 0) {
      console.log('\n⚠️ Image Issues:');
      imageIssues.forEach(issue => console.log(`  - ${issue}`));
    } else if (imageCount > 0) {
      console.log('\n✅ All images appear to be properly stored');
    }

  } catch (error) {
    console.error('❌ MoodBoard diagnosis failed:', error);
  }
}

async function diagnoseVisualComplexityAutoSave() {
  console.log('\n🔍 DIAGNOSING VISUAL COMPLEXITY AUTO-SAVE');
  console.log('==========================================');

  try {
    // 1. Check if analysis history endpoint works
    console.log('\n📊 Step 1: Checking analysis history...');
    const historyResponse = await makeAuthenticatedRequest('/analyses?limit=5');
    
    if (!historyResponse.ok) {
      console.log(`❌ Failed to load analysis history: ${historyResponse.status} ${historyResponse.statusText}`);
      const errorText = await historyResponse.text();
      console.log(`Error details: ${errorText}`);
      return;
    }

    const historyData = await historyResponse.json();
    console.log(`✅ Found ${historyData.data?.length || 0} analysis records`);

    if (historyData.data && historyData.data.length > 0) {
      const latest = historyData.data[0];
      console.log(`\n📋 Latest analysis:`);
      console.log(`  - ID: ${latest.id}`);
      console.log(`  - Created: ${latest.created_at}`);
      console.log(`  - Score: ${latest.complexity_score}`);
      console.log(`  - Has image: ${!!latest.image_path}`);
      console.log(`  - Auto-saved: ${latest.is_auto_saved || 'unknown'}`);
    }

    // 2. Test a simple analysis to see if auto-save works
    console.log('\n🧪 Step 2: Testing analysis endpoint...');
    
    // Create a simple test image (1x1 pixel PNG)
    const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    const formData = new FormData();
    const blob = await fetch(testImageData).then(r => r.blob());
    formData.append('file', blob, 'test.png');

    const analysisResponse = await makeAuthenticatedRequest('/analyze-design', {
      method: 'POST',
      body: formData,
      headers: {} // Don't set Content-Type for FormData
    });

    if (!analysisResponse.ok) {
      console.log(`❌ Analysis failed: ${analysisResponse.status} ${analysisResponse.statusText}`);
      const errorText = await analysisResponse.text();
      console.log(`Error details: ${errorText}`);
      
      // Check if it's an API key issue
      if (errorText.includes('API key not valid')) {
        console.log('\n💡 ISSUE IDENTIFIED: Invalid Gemini API key');
        console.log('   - The Visual Complexity Analyzer requires a valid Gemini API key');
        console.log('   - Current key appears to be a test key: "test_key_gemini"');
        console.log('   - Auto-save may fail because analysis fails');
      }
      return;
    }

    const analysisData = await analysisResponse.json();
    console.log(`✅ Analysis completed successfully`);
    console.log(`  - Analysis ID: ${analysisData.analysis_id || 'Not provided'}`);
    console.log(`  - Saved to DB: ${analysisData.saved_to_database || false}`);
    console.log(`  - Score: ${analysisData.score || 'Not provided'}`);

    // 3. Check if the analysis was auto-saved
    console.log('\n🔄 Step 3: Verifying auto-save...');
    const newHistoryResponse = await makeAuthenticatedRequest('/analyses?limit=1');
    
    if (newHistoryResponse.ok) {
      const newHistoryData = await newHistoryResponse.json();
      if (newHistoryData.data && newHistoryData.data.length > 0) {
        const newest = newHistoryData.data[0];
        console.log(`✅ Latest analysis in history:`);
        console.log(`  - ID: ${newest.id}`);
        console.log(`  - Created: ${newest.created_at}`);
        console.log(`  - Auto-saved: ${newest.is_auto_saved || 'unknown'}`);
        
        if (analysisData.analysis_id === newest.id) {
          console.log('✅ Auto-save is working correctly!');
        } else {
          console.log('⚠️ Auto-save may have issues - IDs don\'t match');
        }
      }
    }

  } catch (error) {
    console.error('❌ Visual Complexity diagnosis failed:', error);
  }
}

async function main() {
  console.log('🚀 EMMA STUDIO TOOLS DIAGNOSTIC');
  console.log('================================');
  console.log('Checking backend connection and tool functionality...\n');

  // Test basic connectivity
  try {
    const healthResponse = await fetch(`${API_BASE}/health`);
    if (healthResponse.ok) {
      console.log('✅ Backend is responding');
    } else {
      console.log('❌ Backend health check failed');
      return;
    }
  } catch (error) {
    console.log('❌ Cannot connect to backend');
    return;
  }

  await diagnoseMoodBoardImages();
  await diagnoseVisualComplexityAutoSave();

  console.log('\n🏁 DIAGNOSIS COMPLETE');
  console.log('====================');
}

// Run the diagnostic
main().catch(console.error);
