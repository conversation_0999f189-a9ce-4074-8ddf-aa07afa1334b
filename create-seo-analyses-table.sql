-- Create SEO Analyses Table and Related Infrastructure
-- This script creates the seo_analyses table with proper schema, indexes, and RLS policies
-- Following the established patterns from design_analyses and headline_analyses tables

-- Create the seo_analyses table
CREATE TABLE IF NOT EXISTS seo_analyses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información de la URL analizada
  url TEXT NOT NULL,
  analysis_mode TEXT NOT NULL DEFAULT 'page' CHECK (analysis_mode IN ('page', 'website')),
  
  -- Parámetros de análisis
  tool_type TEXT NOT NULL DEFAULT 'seo_analyzer',
  analysis_version TEXT DEFAULT '1.0',

  -- Resultados del análisis
  overall_score INTEGER NOT NULL,
  basic_info JSONB NOT NULL, -- {title, title_length, meta_description, h1_tags, etc.}
  content_analysis JSONB NOT NULL, -- {word_count, images, links, headings_structure, top_keywords, ranking_keywords}
  seo_checks JSONB NOT NULL, -- {has_title, has_meta_description, has_h1, etc.}
  recommendations JSONB NOT NULL, -- Array de recomendaciones categorizadas
  achievements JSONB DEFAULT '[]'::jsonb, -- Array de logros obtenidos
  open_graph JSONB DEFAULT '{}'::jsonb, -- Datos de Open Graph
  twitter_card JSONB DEFAULT '{}'::jsonb, -- Datos de Twitter Card
  preview_data JSONB DEFAULT '{}'::jsonb, -- Datos de preview (title, description, etc.)
  performance_metrics JSONB, -- Core Web Vitals y métricas de rendimiento (opcional)

  -- Metadata adicional
  analysis_duration_ms INTEGER,
  status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('processing', 'completed', 'failed')),
  error_message TEXT,
  ai_enhanced BOOLEAN DEFAULT FALSE,

  -- Gestión de favoritos y organización
  is_favorite BOOLEAN DEFAULT FALSE,
  custom_name TEXT, -- Nombre personalizado asignado por el usuario
  tags TEXT[] DEFAULT '{}',
  notes TEXT,

  -- Estadísticas de uso
  view_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMP WITH TIME ZONE,
  regeneration_count INTEGER DEFAULT 0 -- Número de veces que se ha regenerado
);

-- Create indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_seo_analyses_user_id ON seo_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_created_at ON seo_analyses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_tool_type ON seo_analyses(tool_type);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_status ON seo_analyses(status);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_is_favorite ON seo_analyses(user_id, is_favorite) WHERE is_favorite = true;
CREATE INDEX IF NOT EXISTS idx_seo_analyses_overall_score ON seo_analyses(overall_score);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_view_count ON seo_analyses(view_count);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_last_viewed_at ON seo_analyses(last_viewed_at DESC);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_url ON seo_analyses(url);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_analysis_mode ON seo_analyses(analysis_mode);

-- Create trigger for automatic updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER IF NOT EXISTS update_seo_analyses_updated_at
    BEFORE UPDATE ON seo_analyses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE seo_analyses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user data isolation
CREATE POLICY "Users can view their own seo analyses" ON seo_analyses
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own seo analyses" ON seo_analyses
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own seo analyses" ON seo_analyses
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own seo analyses" ON seo_analyses
    FOR DELETE USING (auth.uid()::text = user_id);

-- Grant necessary permissions
GRANT ALL ON seo_analyses TO postgres, anon, authenticated, service_role;

-- Verify the table was created successfully
SELECT 'SEO Analyses table created successfully with RLS policies and indexes' as status;

-- Show table structure
\d seo_analyses;

-- Show RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'seo_analyses';
