#!/usr/bin/env node

/**
 * Comprehensive Buyer Persona Avatar Upload Test
 * Verifies all critical fixes are working correctly
 */

console.log('🧪 BUYER PERSONA AVATAR UPLOAD VERIFICATION TEST');
console.log('================================================');
console.log('Testing all critical VCA pattern implementations...\n');

// Test configuration
const TEST_CONFIG = {
  supabaseUrl: "https://pthewpjbegkgomvyhkin.supabase.co",
  supabaseAnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aGV3cGpiZWdrZ29tdnloa2luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MjM1NDMsImV4cCI6MjA2NDI5OTU0M30.bskxkyZ9meYb2cpZZGmS_FAS2Wyjs4j_lOPnJqh1s0k",
  testPersonaName: "Maria_Test_Avatar",
  testDataUrl: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
};

class AvatarUploadTester {
  constructor() {
    this.results = {
      codeIntegrity: false,
      fileObjectCreation: false,
      vcaUploadPattern: false,
      errorHandling: false,
      authentication: false,
      storageAccess: false,
      endToEndUpload: false
    };
    this.errors = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const prefix = {
      'info': '📋',
      'success': '✅',
      'error': '❌',
      'warn': '⚠️'
    }[type] || '📋';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  async testCodeIntegrity() {
    this.log('Testing code integrity...', 'info');
    
    try {
      // Import the service to check for compilation errors
      const fs = require('fs');
      const path = require('path');
      
      const servicePath = path.join(process.cwd(), 'client/src/services/buyerPersonaService.ts');
      
      if (!fs.existsSync(servicePath)) {
        throw new Error('buyerPersonaService.ts not found');
      }
      
      const serviceContent = fs.readFileSync(servicePath, 'utf8');
      
      // Check for critical VCA patterns
      const patterns = [
        'new File([blob], fileName, {',
        'type: \'image/png\'',
        '// NO contentType - let Supabase auto-detect (VCA pattern)',
        'console.error(\'❌ Supabase Storage upload error (VCA pattern):\''
      ];
      
      let foundPatterns = 0;
      patterns.forEach(pattern => {
        if (serviceContent.includes(pattern)) {
          foundPatterns++;
          this.log(`Found pattern: ${pattern}`, 'success');
        } else {
          this.log(`Missing pattern: ${pattern}`, 'error');
        }
      });
      
      if (foundPatterns === patterns.length) {
        this.results.codeIntegrity = true;
        this.log('Code integrity check PASSED', 'success');
      } else {
        throw new Error(`Only ${foundPatterns}/${patterns.length} patterns found`);
      }
      
    } catch (error) {
      this.errors.push(`Code integrity: ${error.message}`);
      this.log(`Code integrity check FAILED: ${error.message}`, 'error');
    }
  }

  async testFileObjectCreation() {
    this.log('Testing File object creation pattern...', 'info');
    
    try {
      // Simulate the File object creation from the service
      const testBlob = new Blob(['test'], { type: 'application/octet-stream' });
      const fileName = 'test_avatar.png';
      
      // This is the exact pattern from the service
      const file = new File([testBlob], fileName, {
        type: 'image/png',  // Force correct MIME type
        lastModified: Date.now()
      });
      
      if (file.type === 'image/png' && file.name === fileName) {
        this.results.fileObjectCreation = true;
        this.log('File object creation PASSED', 'success');
        this.log(`Created file: ${file.name}, type: ${file.type}, size: ${file.size}`, 'info');
      } else {
        throw new Error(`File creation failed: type=${file.type}, name=${file.name}`);
      }
      
    } catch (error) {
      this.errors.push(`File object creation: ${error.message}`);
      this.log(`File object creation FAILED: ${error.message}`, 'error');
    }
  }

  async testVCAUploadPattern() {
    this.log('Testing VCA upload pattern compliance...', 'info');
    
    try {
      // Check that the upload pattern matches VCA exactly
      const fs = require('fs');
      const path = require('path');
      
      const servicePath = path.join(process.cwd(), 'client/src/services/buyerPersonaService.ts');
      const serviceContent = fs.readFileSync(servicePath, 'utf8');
      
      // Look for the exact VCA upload pattern
      const uploadPatternRegex = /supabase\.storage\s*\.from\('design-analysis-images'\)\s*\.upload\(avatarFilePath,\s*file,\s*\{\s*cacheControl:\s*'3600',\s*upsert:\s*false\s*\/\/\s*NO contentType/s;
      
      if (uploadPatternRegex.test(serviceContent)) {
        this.results.vcaUploadPattern = true;
        this.log('VCA upload pattern PASSED', 'success');
      } else {
        throw new Error('VCA upload pattern not found or incorrect');
      }
      
    } catch (error) {
      this.errors.push(`VCA upload pattern: ${error.message}`);
      this.log(`VCA upload pattern FAILED: ${error.message}`, 'error');
    }
  }

  async testErrorHandling() {
    this.log('Testing enhanced error handling...', 'info');
    
    try {
      const fs = require('fs');
      const path = require('path');
      
      const servicePath = path.join(process.cwd(), 'client/src/services/buyerPersonaService.ts');
      const serviceContent = fs.readFileSync(servicePath, 'utf8');
      
      // Check for enhanced error handling patterns
      const errorPatterns = [
        'console.error(\'❌ Supabase Storage upload error (VCA pattern):\'',
        'error.message.includes(\'Duplicate\')',
        'error.message.includes(\'Policy\')',
        'error.message.includes(\'RLS\')',
        'throw new Error(`RLS Policy Error: ${error.message}`)'
      ];
      
      let foundErrorPatterns = 0;
      errorPatterns.forEach(pattern => {
        if (serviceContent.includes(pattern)) {
          foundErrorPatterns++;
        }
      });
      
      if (foundErrorPatterns === errorPatterns.length) {
        this.results.errorHandling = true;
        this.log('Enhanced error handling PASSED', 'success');
      } else {
        throw new Error(`Only ${foundErrorPatterns}/${errorPatterns.length} error patterns found`);
      }
      
    } catch (error) {
      this.errors.push(`Error handling: ${error.message}`);
      this.log(`Error handling FAILED: ${error.message}`, 'error');
    }
  }

  async generateReport() {
    this.log('\n📊 VERIFICATION REPORT', 'info');
    this.log('===================', 'info');
    
    const totalTests = Object.keys(this.results).length;
    const passedTests = Object.values(this.results).filter(Boolean).length;
    
    Object.entries(this.results).forEach(([test, passed]) => {
      this.log(`${test}: ${passed ? 'PASSED' : 'FAILED'}`, passed ? 'success' : 'error');
    });
    
    this.log(`\nOverall: ${passedTests}/${totalTests} tests passed`, passedTests === totalTests ? 'success' : 'error');
    
    if (this.errors.length > 0) {
      this.log('\n🚨 ERRORS FOUND:', 'error');
      this.errors.forEach(error => this.log(`  - ${error}`, 'error'));
    }
    
    if (passedTests === totalTests) {
      this.log('\n🎉 ALL TESTS PASSED! Avatar upload functionality is ready.', 'success');
      this.log('✅ No "mime type application/json" errors should occur.', 'success');
      this.log('✅ VCA pattern implementation is complete and correct.', 'success');
    } else {
      this.log('\n⚠️ Some tests failed. Please review the errors above.', 'warn');
    }
  }

  async runAllTests() {
    this.log('Starting comprehensive avatar upload verification...\n', 'info');
    
    await this.testCodeIntegrity();
    await this.testFileObjectCreation();
    await this.testVCAUploadPattern();
    await this.testErrorHandling();
    
    await this.generateReport();
  }
}

// Run the tests
const tester = new AvatarUploadTester();
tester.runAllTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
