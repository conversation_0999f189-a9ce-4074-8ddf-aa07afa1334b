// Test script to verify Visual Complexity Analyzer image storage fix
// Run this in the browser console after navigating to the Visual Complexity Analyzer

console.log('🧪 Visual Complexity Analyzer - Image Storage Fix Test');
console.log('====================================================');

class VisualComplexityFixTest {
  constructor() {
    this.results = {
      auth: null,
      imageUpload: null,
      analysis: null,
      databaseSave: null,
      imageRetrieval: null,
      errors: []
    };
  }

  async runCompleteTest() {
    console.log('\n🚀 Starting complete Visual Complexity Analyzer fix test...');
    
    try {
      // Step 1: Check authentication
      await this.testAuthentication();
      
      // Step 2: Create test image
      const testImage = await this.createTestImage();
      
      // Step 3: Test backend analysis endpoint (which now includes image storage)
      await this.testBackendAnalysis(testImage);
      
      // Step 4: Verify database record has file_url
      await this.verifyDatabaseRecord();
      
      // Step 5: Test image retrieval
      await this.testImageRetrieval();
      
      // Step 6: Cleanup
      await this.cleanup();
      
      console.log('\n✅ All tests completed successfully!');
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      this.results.errors.push(error.message);
      this.printResults();
    }
  }

  async testAuthentication() {
    console.log('\n🔐 Step 1: Testing authentication...');
    
    const { data: { user, session }, error } = await window.supabase.auth.getUser();
    
    if (error || !user || !session) {
      throw new Error('Authentication required - please log in first');
    }
    
    this.results.auth = {
      success: true,
      userId: user.id,
      email: user.email,
      hasAccessToken: !!session.access_token
    };
    
    console.log('✅ Authentication successful:', {
      userId: user.id,
      email: user.email
    });
  }

  async createTestImage() {
    console.log('\n🖼️ Step 2: Creating test image...');
    
    // Create a test image (colorful design for complexity analysis)
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');
    
    // Create a complex design with multiple colors and elements
    ctx.fillStyle = '#FF6B6B';
    ctx.fillRect(0, 0, 200, 150);
    
    ctx.fillStyle = '#4ECDC4';
    ctx.fillRect(200, 0, 200, 150);
    
    ctx.fillStyle = '#45B7D1';
    ctx.fillRect(0, 150, 200, 150);
    
    ctx.fillStyle = '#96CEB4';
    ctx.fillRect(200, 150, 200, 150);
    
    // Add some text elements
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '24px Arial';
    ctx.fillText('TEST DESIGN', 150, 100);
    ctx.fillText('COMPLEXITY', 150, 200);
    
    // Convert to blob
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    const testFile = new File([blob], 'visual-complexity-test.png', {
      type: 'image/png'
    });
    
    console.log('✅ Test image created:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });
    
    return testFile;
  }

  async testBackendAnalysis(testImage) {
    console.log('\n🔬 Step 3: Testing backend analysis with image storage...');
    
    const formData = new FormData();
    formData.append('design', testImage);
    
    console.log('📤 Sending analysis request to backend...');
    
    const response = await fetch('http://localhost:8001/api/design-analysis/analyze', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${(await window.supabase.auth.getSession()).data.session.access_token}`
      },
      body: formData
    });
    
    if (!response.ok) {
      throw new Error(`Backend analysis failed: ${response.status} ${response.statusText}`);
    }
    
    const analysisResult = await response.json();
    
    this.results.analysis = {
      success: analysisResult.success,
      analysisId: analysisResult.analysis_id,
      savedToDatabase: analysisResult.saved_to_database,
      score: analysisResult.score,
      hasAnalysisId: !!analysisResult.analysis_id
    };
    
    console.log('✅ Backend analysis completed:', {
      success: analysisResult.success,
      analysisId: analysisResult.analysis_id,
      savedToDatabase: analysisResult.saved_to_database,
      score: analysisResult.score
    });
    
    if (!analysisResult.analysis_id) {
      throw new Error('Backend analysis did not return analysis_id');
    }
    
    this.analysisId = analysisResult.analysis_id;
  }

  async verifyDatabaseRecord() {
    console.log('\n💾 Step 4: Verifying database record has file_url...');
    
    const { data, error } = await window.supabase
      .from('design_analyses')
      .select('id, file_url, original_filename, file_size, file_type')
      .eq('id', this.analysisId)
      .single();
    
    if (error) {
      throw new Error(`Database query failed: ${error.message}`);
    }
    
    this.results.databaseSave = {
      success: true,
      hasFileUrl: !!data.file_url,
      fileUrl: data.file_url,
      filename: data.original_filename,
      fileSize: data.file_size,
      fileType: data.file_type
    };
    
    console.log('✅ Database record verified:', {
      id: data.id,
      hasFileUrl: !!data.file_url,
      fileUrl: data.file_url ? `${data.file_url.substring(0, 50)}...` : 'NULL',
      filename: data.original_filename
    });
    
    if (!data.file_url) {
      throw new Error('❌ CRITICAL: file_url is still NULL in database!');
    }
    
    this.fileUrl = data.file_url;
  }

  async testImageRetrieval() {
    console.log('\n🖼️ Step 5: Testing image retrieval...');
    
    if (!this.fileUrl) {
      throw new Error('No file_url to test retrieval');
    }
    
    try {
      const imageUrl = await window.designAnalysisService.getImageUrl(this.fileUrl);
      
      if (!imageUrl) {
        throw new Error('Failed to retrieve image URL');
      }
      
      // Test if the image actually loads
      await new Promise((resolve, reject) => {
        const testImg = new Image();
        testImg.onload = () => resolve(true);
        testImg.onerror = () => reject(new Error('Image failed to load'));
        testImg.src = imageUrl;
      });
      
      this.results.imageRetrieval = {
        success: true,
        imageUrl: imageUrl.substring(0, 50) + '...',
        urlType: imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL'
      };
      
      console.log('✅ Image retrieval successful:', {
        urlType: imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL',
        imageLoads: true
      });
      
      // Clean up object URL
      if (imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(imageUrl);
      }
      
    } catch (error) {
      this.results.imageRetrieval = {
        success: false,
        error: error.message
      };
      throw error;
    }
  }

  async cleanup() {
    console.log('\n🧹 Step 6: Cleaning up test data...');
    
    if (this.analysisId) {
      try {
        await window.designAnalysisService.deleteAnalysis(this.analysisId);
        console.log('✅ Test analysis deleted successfully');
      } catch (error) {
        console.warn('⚠️ Failed to cleanup test analysis:', error.message);
      }
    }
  }

  printResults() {
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('======================');
    console.log('Authentication:', this.results.auth?.success ? '✅ PASS' : '❌ FAIL');
    console.log('Backend Analysis:', this.results.analysis?.success ? '✅ PASS' : '❌ FAIL');
    console.log('Database Save:', this.results.databaseSave?.success ? '✅ PASS' : '❌ FAIL');
    console.log('File URL Present:', this.results.databaseSave?.hasFileUrl ? '✅ PASS' : '❌ FAIL');
    console.log('Image Retrieval:', this.results.imageRetrieval?.success ? '✅ PASS' : '❌ FAIL');
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.results.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    console.log('\n🎯 CRITICAL FIX STATUS:');
    if (this.results.databaseSave?.hasFileUrl && this.results.imageRetrieval?.success) {
      console.log('✅ FIXED: Images are now properly stored and file_url is populated!');
    } else {
      console.log('❌ NOT FIXED: Issue still exists');
    }
  }
}

// Auto-run the test
const test = new VisualComplexityFixTest();
test.runCompleteTest();
