/**
 * Prueba Simple del Analizador de Complejidad Visual
 * Verifica el flujo básico de análisis y guardado
 */

console.log('🧪 PRUEBA SIMPLE - Analizador de Complejidad Visual');

async function testSimpleAnalysis() {
  try {
    // 1. Verificar autenticación
    console.log('🔐 Verificando autenticación...');
    const { supabase } = await import('./client/src/lib/supabase.js');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('❌ No autenticado');
      return;
    }
    
    console.log('✅ Usuario autenticado:', user.id);

    // 2. Crear imagen de prueba simple
    console.log('🖼️ Creando imagen de prueba...');
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    ctx.fillStyle = '#ff6b6b';
    ctx.fillRect(0, 0, 200, 200);
    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial';
    ctx.fillText('TEST', 80, 100);
    
    const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
    const testFile = new File([blob], 'test-simple.png', { type: 'image/png' });
    
    console.log('✅ Imagen creada:', testFile.name, testFile.size, 'bytes');

    // 3. Enviar al backend
    console.log('🚀 Enviando al backend...');
    const formData = new FormData();
    formData.append('design', testFile);

    const response = await fetch('/api/analyze-design', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
      },
      body: formData
    });

    if (!response.ok) {
      console.error('❌ Error en backend:', response.status, response.statusText);
      return;
    }

    const result = await response.json();
    console.log('✅ Respuesta del backend:', {
      success: result.success,
      hasAnalysisId: !!result.analysis_id,
      savedToDatabase: result.saved_to_database,
      hasFileUrl: !!result.file_url,
      fileUrl: result.file_url
    });

    // 4. Verificar en base de datos
    if (result.analysis_id) {
      console.log('🔍 Verificando en base de datos...');
      
      const { data: dbRecord, error: dbError } = await supabase
        .schema('api')
        .from('design_analyses')
        .select('*')
        .eq('id', result.analysis_id)
        .single();

      if (dbError) {
        console.error('❌ Error en DB:', dbError);
      } else {
        console.log('✅ Registro en DB:', {
          id: dbRecord.id,
          hasFileUrl: !!dbRecord.file_url,
          fileUrl: dbRecord.file_url,
          score: dbRecord.overall_score
        });

        // 5. Probar acceso a imagen
        if (dbRecord.file_url) {
          console.log('🖼️ Probando acceso a imagen...');
          
          try {
            const imageResponse = await fetch(`/api/image/${dbRecord.file_url}`, {
              headers: {
                'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
              }
            });

            console.log('📊 Respuesta de imagen:', {
              status: imageResponse.status,
              contentType: imageResponse.headers.get('content-type'),
              contentLength: imageResponse.headers.get('content-length')
            });

            if (imageResponse.ok) {
              console.log('✅ ¡Imagen accesible!');
            } else {
              console.error('❌ Imagen no accesible');
            }
          } catch (imgError) {
            console.error('❌ Error accediendo imagen:', imgError);
          }
        }

        // 6. Limpiar
        console.log('🧹 Limpiando...');
        try {
          const { error: deleteError } = await supabase
            .schema('api')
            .from('design_analyses')
            .delete()
            .eq('id', result.analysis_id);

          if (deleteError) {
            console.warn('⚠️ No se pudo limpiar:', deleteError);
          } else {
            console.log('✅ Limpieza completada');
          }
        } catch (cleanError) {
          console.warn('⚠️ Error en limpieza:', cleanError);
        }
      }
    }

    console.log('\n🎯 PRUEBA COMPLETADA');

  } catch (error) {
    console.error('💥 Error en prueba:', error);
  }
}

// Ejecutar prueba
testSimpleAnalysis();
