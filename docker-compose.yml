services:
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    image: backend:latest
    ports:
      - "8001:8001"
    environment:
      - STABILITY_API_KEY=${STABILITY_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./backend:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 5s
      retries: 3
    labels:
      org.opencontainers.image.source: https://github.com/Agents4Work/emma-studio-
      org.opencontainers.image.security.scan-date: "${DATE:-date +%Y-%m-%d}"
    # Security enhancements
    user: appuser
    read_only: true
    # Temporary writable directories needed for functionality
    tmpfs:
      - /tmp:exec,size=256M
    security_opt:
      - no-new-privileges:true
    # Apply resource constraints to prevent DoS
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  frontend:
    build:
      context: .
      dockerfile: client/Dockerfile
    image: frontend:latest
    ports:
      - "3002:80"
    depends_on:
      - backend
    environment:
      - VITE_API_BASE=http://backend:8001
    volumes:
      - ./client:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 5s
      retries: 3
    # Security enhancements
    user: nginx-user
    read_only: true
    # Temporary writable directories needed for functionality
    tmpfs:
      - /var/cache/nginx:exec
      - /var/run:exec
    security_opt:
      - no-new-privileges:true
    # Apply resource constraints to prevent DoS
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  security-scanner:
    image: docker/scout:latest
    command: cves backend frontend --format json --output /reports/security-scan-results.json
    volumes:
      - ./security-reports:/reports
    depends_on:
      - backend
      - frontend
    profiles:
      - security-scan
    # Run weekly security scans
    # This service won't start with regular docker-compose up
    # To run a scan: docker-compose --profile security-scan up security-scanner
