# 🎯 Emma Studio - Puertos Fijos Solucionados

## ✅ PROBLEMA RESUELTO

**El problema:** Emma Studio iniciaba en puertos aleatorios en lugar de los puertos fijos configurados (3002 frontend, 8001 backend).

**La solución:** Scripts mejorados y configuración forzada de puertos.

---

## 🚀 FORMAS DE INICIAR EMMA STUDIO

### 1. <PERSON>ript ULTRA SIMPLE (Recomendado) 🔥
```bash
./emma.sh
```
- ✅ MATA TODO en puertos 3002 y 8001
- ✅ FUERZA los puertos correctos SIEMPRE
- ✅ Variables de entorno forzadas
- ✅ Inicio súper rápido
- ✅ Cero configuración

### 2. <PERSON><PERSON>t Completo (Detallado)
```bash
./start-emma-fixed.sh
```
- ✅ Limpieza AGRESIVA de puertos
- ✅ Verifica que los servicios estén funcionando
- ✅ Logs detallados
- ✅ Manejo de errores
- ✅ Cleanup automático al salir

### 3. <PERSON><PERSON>t Simple
```bash
./start-emma.sh
```
- ✅ Limpia puertos automáticamente
- ✅ Inicio rápido
- ✅ Menos verbose

### 4. Frontend Solo (FORZADO)
```bash
cd client
npm run dev        # SIEMPRE mata procesos en 3002 y fuerza el puerto
npm run dev-force  # Igual que dev
npm run dev-clean  # Solo mata procesos en 3002
```
- ✅ TODOS los scripts npm FUERZAN puerto 3002
- ✅ Mata procesos automáticamente
- ✅ `--strictPort` para no negociar

### 5. Backend Solo (FORZADO)
```bash
cd backend
python start_backend_forced.py  # Script Python que FUERZA puerto 8001
# O manualmente:
python -m uvicorn app.main:app --reload --port 8001
```

---

## 🔧 CONFIGURACIÓN FIJA DE PUERTOS

### Frontend (Puerto 3002)
- **Archivo:** `client/vite.config.ts`
- **Configuración:** `port: 3002, strictPort: true`
- **Script npm:** `"dev": "vite --port 3002 --host"`

### Backend (Puerto 8001)
- **Archivo:** `backend/app/core/config.py`
- **Configuración:** `PORT: int = 8001`
- **Proxy:** `client/vite.config.ts` → `target: 'http://127.0.0.1:8001'`

---

## 🌐 URLs DE ACCESO

| Servicio | URL | Descripción |
|----------|-----|-------------|
| **Frontend** | http://localhost:3002 | Aplicación React de Emma Studio |
| **Backend** | http://localhost:8001 | API FastAPI |
| **API Docs** | http://localhost:8001/docs | Documentación interactiva de la API |
| **Health Check** | http://localhost:8001/api/v1/ad-creator-agent/health | Verificar estado del backend |

---

## 🛠️ SOLUCIÓN DE PROBLEMAS

### Error: "Port 3002 is already in use"
```bash
# Opción 1: Usar script con limpieza automática
npm run dev-force

# Opción 2: Limpiar manualmente
lsof -ti :3002 | xargs kill -9
npm run dev
```

### Error: "Port 8001 is already in use"
```bash
# Limpiar puerto del backend
lsof -ti :8001 | xargs kill -9
cd backend
python -m uvicorn app.main:app --reload --port 8001
```

### Verificar qué está usando los puertos
```bash
# Ver procesos en puerto 3002
lsof -i :3002

# Ver procesos en puerto 8001
lsof -i :8001

# Ver todos los procesos de Emma Studio
ps aux | grep -E "(vite|uvicorn|node|python)" | grep -v grep
```

---

## 📝 CAMBIOS REALIZADOS

### 1. Scripts ULTRA AGRESIVOS
- ✅ `emma.sh` - Script ULTRA SIMPLE que MATA TODO y FUERZA puertos
- ✅ `start-emma-fixed.sh` - Script completo con limpieza AGRESIVA
- ✅ `start-emma.sh` - Script actualizado con limpieza de puertos
- ✅ `backend/start_backend_forced.py` - Script Python que FUERZA backend

### 2. Package.json FORZADO
- ✅ `"dev"` - SIEMPRE mata procesos en 3002 y fuerza puerto
- ✅ `"dev-force"` - Igual que dev
- ✅ `"dev-clean"` - Solo mata procesos en 3002
- ✅ Todos usan `--strictPort` para NO negociar

### 3. Variables de Entorno FORZADAS
- ✅ `client/.env` - VITE_PORT=3002, PORT=3002 (FORZADOS)
- ✅ `backend/.env` - PORT=8001, UVICORN_PORT=8001 (FORZADOS)
- ✅ Scripts exportan variables de entorno para FORZAR puertos

### 4. Configuración BLINDADA
- ✅ `client/vite.config.ts` - Puerto 3002 con `strictPort: true` NO NEGOCIABLE
- ✅ `backend/app/core/config.py` - Puerto 8001 fijo
- ✅ Proxy configurado correctamente

---

## 🎉 RESULTADO

**Antes:** Puertos aleatorios, conflictos, frustración 😡
**Ahora:** Puertos FORZADOS 3002 y 8001, scripts AGRESIVOS, CERO negociación 💪

## 🔥 MODO BESTIA ACTIVADO

Emma Studio ahora usa **FUERZA BRUTA** para garantizar los puertos correctos:
- 🔪 MATA todos los procesos en 3002 y 8001
- 🛡️ Variables de entorno BLINDADAS
- ⚡ Scripts que NO aceptan un NO por respuesta
- 🎯 Puerto 3002 y 8001 SIEMPRE, sin excepciones

**¡PROBLEMA RESUELTO PARA SIEMPRE!** 🚀
