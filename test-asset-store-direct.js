/**
 * Direct test of the MoodboardAssetStore functionality
 * This script will test the asset store upload and resolve methods directly
 */

console.log('🧪 Direct Asset Store Test');
console.log('==========================\n');

class DirectAssetStoreTest {
  constructor() {
    this.testResults = [];
  }

  async runDirectTest() {
    console.log('🚀 Starting direct asset store test...\n');
    
    try {
      await this.testAssetStoreUpload();
      await this.testImageServiceUpload();
      await this.testSupabaseStorageAccess();
      await this.testNetworkRequests();
      
      this.displayResults();
    } catch (error) {
      console.error('❌ Direct test failed:', error);
    }
  }

  async testAssetStoreUpload() {
    console.log('📋 Test 1: Direct Asset Store Upload');
    
    try {
      // Create a test image
      const testFile = await this.createTestImageFile();
      
      // Try to access the asset store directly
      // Since it's instantiated in the component, we'll simulate the upload process
      console.log('ℹ️ Simulating asset store upload process...');
      
      // Check if we can access the MoodboardImageService
      const uploadResult = await this.simulateImageUpload(testFile);
      
      if (uploadResult.success) {
        console.log('✅ Simulated upload successful');
        this.testResults.push({ test: 'Asset Store Upload', result: 'PASS', details: uploadResult });
      } else {
        console.log('❌ Simulated upload failed');
        this.testResults.push({ test: 'Asset Store Upload', result: 'FAIL', details: uploadResult });
      }
      
    } catch (error) {
      console.error('❌ Asset store upload test failed:', error);
      this.testResults.push({ test: 'Asset Store Upload', result: 'ERROR', details: error.message });
    }
  }

  async testImageServiceUpload() {
    console.log('\n📋 Test 2: Image Service Upload');
    
    try {
      // Test the image service upload functionality
      const testFile = await this.createTestImageFile();
      
      // Try to upload using fetch to the backend
      const formData = new FormData();
      formData.append('file', testFile);
      
      console.log('🔄 Testing backend image upload...');
      
      // This would normally go through the MoodboardImageService
      // but we'll test the backend endpoint directly
      const response = await fetch('/api/v1/images/upload-temp', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Backend upload test successful');
        this.testResults.push({ test: 'Image Service Upload', result: 'PASS', details: result });
      } else {
        console.log('❌ Backend upload test failed');
        this.testResults.push({ test: 'Image Service Upload', result: 'FAIL', details: `HTTP ${response.status}` });
      }
      
    } catch (error) {
      console.error('❌ Image service upload test failed:', error);
      this.testResults.push({ test: 'Image Service Upload', result: 'ERROR', details: error.message });
    }
  }

  async testSupabaseStorageAccess() {
    console.log('\n📋 Test 3: Supabase Storage Access');
    
    try {
      // Test if we can access Supabase storage
      console.log('🔄 Testing Supabase storage access...');
      
      // Try to get a signed URL for a test file
      const response = await fetch('/api/v1/images/signed-url/test-file.png');
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Supabase storage access test successful');
        this.testResults.push({ test: 'Supabase Storage Access', result: 'PASS', details: result });
      } else {
        console.log('❌ Supabase storage access test failed');
        this.testResults.push({ test: 'Supabase Storage Access', result: 'FAIL', details: `HTTP ${response.status}` });
      }
      
    } catch (error) {
      console.error('❌ Supabase storage access test failed:', error);
      this.testResults.push({ test: 'Supabase Storage Access', result: 'ERROR', details: error.message });
    }
  }

  async testNetworkRequests() {
    console.log('\n📋 Test 4: Network Request Monitoring');
    
    try {
      // Monitor network requests for image uploads
      console.log('🔄 Monitoring network requests...');
      
      // Set up network monitoring
      const originalFetch = window.fetch;
      const networkRequests = [];
      
      window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && (url.includes('image') || url.includes('upload') || url.includes('storage'))) {
          networkRequests.push({
            url: url,
            method: args[1]?.method || 'GET',
            timestamp: new Date().toISOString()
          });
          console.log(`📡 Network request detected: ${args[1]?.method || 'GET'} ${url}`);
        }
        return originalFetch.apply(this, args);
      };
      
      // Wait for a bit to capture any ongoing requests
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Restore original fetch
      window.fetch = originalFetch;
      
      console.log(`✅ Monitored ${networkRequests.length} image-related network requests`);
      this.testResults.push({ test: 'Network Request Monitoring', result: 'PASS', details: networkRequests });
      
    } catch (error) {
      console.error('❌ Network request monitoring failed:', error);
      this.testResults.push({ test: 'Network Request Monitoring', result: 'ERROR', details: error.message });
    }
  }

  async createTestImageFile() {
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    
    // Draw a simple test pattern
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(0, 0, 50, 50);
    ctx.fillStyle = '#00ff00';
    ctx.fillRect(50, 0, 50, 50);
    ctx.fillStyle = '#0000ff';
    ctx.fillRect(0, 50, 50, 50);
    ctx.fillStyle = '#ffff00';
    ctx.fillRect(50, 50, 50, 50);
    
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    return new File([blob], 'direct-test-image.png', { type: 'image/png' });
  }

  async simulateImageUpload(file) {
    try {
      // Simulate the MoodboardImageService upload process
      console.log('🔄 Simulating MoodboardImageService.uploadImage()...');
      
      // Create a data URL as fallback
      const dataUrl = await this.fileToDataUrl(file);
      
      // Try to upload to backend
      const formData = new FormData();
      formData.append('image', file);
      
      const response = await fetch('/api/v1/images/upload', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        const result = await response.json();
        return {
          success: true,
          method: 'backend_upload',
          result: result,
          dataUrl: dataUrl.substring(0, 100) + '...'
        };
      } else {
        return {
          success: false,
          method: 'backend_upload',
          error: `HTTP ${response.status}`,
          fallback_dataUrl: dataUrl.substring(0, 100) + '...'
        };
      }
      
    } catch (error) {
      return {
        success: false,
        method: 'simulation',
        error: error.message
      };
    }
  }

  fileToDataUrl(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  displayResults() {
    console.log('\n🎯 DIRECT ASSET STORE TEST RESULTS');
    console.log('===================================');
    
    this.testResults.forEach(test => {
      const status = test.result === 'PASS' ? '✅' : test.result === 'FAIL' ? '❌' : '⚠️';
      console.log(`${status} ${test.test}: ${test.result}`);
      
      if (test.details && typeof test.details === 'object') {
        console.log(`   Details:`, test.details);
      } else if (test.details) {
        console.log(`   Details: ${test.details}`);
      }
    });
    
    const passedTests = this.testResults.filter(test => test.result === 'PASS').length;
    const totalTests = this.testResults.length;
    
    console.log(`\n📊 Overall Score: ${passedTests}/${totalTests} tests passed`);
    
    console.log('\n🔍 Diagnostic Summary:');
    console.log('- This test checks the asset store functionality directly');
    console.log('- It simulates the image upload process');
    console.log('- It monitors network requests for debugging');
    console.log('- It tests backend connectivity');
    
    console.log('\n💡 Troubleshooting Tips:');
    console.log('1. Check browser console for detailed error messages');
    console.log('2. Verify backend is running and accessible');
    console.log('3. Check Supabase configuration and permissions');
    console.log('4. Monitor network tab for failed requests');
    console.log('5. Test with different image formats and sizes');
  }
}

// Auto-run the test
const directTest = new DirectAssetStoreTest();
directTest.runDirectTest();
